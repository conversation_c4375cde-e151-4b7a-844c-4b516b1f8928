import { Component, Input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'ifp-db-chart-props',
    imports: [TranslateModule],
    templateUrl: './ifp-db-chart-props.component.html',
    styleUrl: './ifp-db-chart-props.component.scss'
})
export class IfpDbChartPropsComponent {
  @Input() propName: string = '';
  @Input() isDots: boolean= false;
}
