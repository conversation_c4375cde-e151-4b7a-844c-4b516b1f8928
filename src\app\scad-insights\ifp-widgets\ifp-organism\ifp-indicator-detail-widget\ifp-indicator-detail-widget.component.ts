import { QuotRemove } from 'src/app/scad-insights/core/pipes/quotsRemove.pipe';

import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AfterViewChecked, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnChanges, OnDestroy, OnInit, output, Renderer2, ViewChild } from '@angular/core';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { distinctUntilChanged } from 'rxjs';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { IndicatorListData, Security } from 'src/app/scad-insights/core/interface/indicator.interface';
import { DownloadService } from 'src/app/scad-insights/core/services/download.service';
import { selectIndicatorGetById } from 'src/app/scad-insights/store/incidicator-list/incidicator-list.selector';
import { SubSink } from 'subsink';
import { IfpButtonComponent } from '../../ifp-atoms/ifp-button/ifp-button.component';
import { IFPHighChartsComponent } from '../../charts/ifp-highcharts.component';
import { IfpCardLoaderComponent } from '../../ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { buttonClass, buttonColor } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpMonthSelectorComponent } from '../../ifp-atoms/ifp-month-selector/ifp-month-selector.component';
import { IfpRatingComponent } from '../../ifp-atoms/ifp-rating/ifp-rating.component';
import { IfpDropdownComponent } from '../../ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { FilterService } from 'src/app/scad-insights/core/services/filter/filter.service';
import { AnalyticalService } from 'src/app/scad-insights/core/services/analytical.service';
import { IfpAnalyticLineChartComponent } from '../../ifp-molecules/ifp-analytic-line-chart/ifp-analytic-line-chart.component';
import { selectGetMappedData } from 'src/app/scad-insights/store/notification/notification.selector';
import { selectGetMyAppsMappedData } from 'src/app/scad-insights/store/myApps/myAppsGlobal.selector';
import { setMyAppsUpdate, unsubscribeMyAppsUpdate } from 'src/app/scad-insights/store/myApps/myAppsGlobal.action';
import { IfpToggleButtonComponent } from '../../ifp-atoms/ifp-toggle-button/ifp-toggle-button.component';
import { cloneDeep, groupBy } from 'lodash';
import { CustomNumberPipe } from '../../../core/pipes/customNumber.pipe';
import { IfpTableComponent } from '../../ifp-molecules/ifp-table/ifp-table.component';
import { LabelData } from 'src/app/scad-insights/core/interface/atom/ifp-category-label.interface';
import { IfpTabComponent } from '../../ifp-molecules/ifp-tab/ifp-tab.component';
import { IfpCommentsComponent } from '../ifp-comments/ifp-comments.component';
import { IfpModalComponent } from '../ifp-modal/ifp-modal.component';
import { IfpPdfTemplateComponent } from '../../ifp-molecules/ifp-pdf-template/ifp-pdf-template.component';
import { chartConstants, chartResponse, chartTypeHide, compareSeries, totalValues } from 'src/app/scad-insights/core/constants/chart.constants';
import { selectInsightResponse } from 'src/app/scad-insights/store/chart-insights/chart-insights.selector';
import { getInsights } from 'src/app/scad-insights/store/chart-insights/chart-insights.action';
import { InsightsService } from 'src/app/scad-insights/core/services/chart-insight/chart-insight.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { CommonApiService } from 'src/app/scad-insights/core/services/common-api/commonApi.service';
import { IfpCheckBoxComponent } from '../../ifp-atoms/ifp-check-box/ifp-check-box.component';
import { IfpTncModalComponent } from '../ifp-tnc-modal/ifp-tnc-modal.component';
import { IfpNotificationSettingsComponent } from '../../ifp-molecules/ifp-notification-settings/ifp-notification-settings.component';
import { ActivatedRoute } from '@angular/router';
import { CustomLetter } from 'src/app/scad-insights/core/pipes/firstLetter.pipe';
import { IfpIconTextComponent } from '../../ifp-molecules/ifp-icon-text/ifp-icon-text.component';
import { analyticsClasses } from 'src/app/scad-insights/core/constants/analytics-class.constants';
import { contentTypeDashboard, indicatorType } from 'src/app/scad-insights/core/constants/contentType.constants';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IfpCompareListComponent } from '../../ifp-molecules/ifp-compare-list/ifp-compare-list.component';
import { classifications } from 'src/app/scad-insights/core/constants/domain.constants';
import { ChartToolsService } from 'src/app/scad-insights/core/services/chart-tools/chart-tools.service';
import { PieChartSeries, SunburstChartData } from 'src/app/scad-insights/core/interface/page.interface';
import { IfpPieChartComponent } from '../../ifp-molecules/ifp-pie-chart/ifp-pie-chart.component';
import { IfpCircularBarChartComponent } from '../../ifp-molecules/ifp-circular-bar-chart/ifp-circular-bar-chart.component';
import { IfpSunburstChartComponent } from '../../ifp-molecules/ifp-sunburst-chart/ifp-sunburst-chart.component';
import { Title } from '@angular/platform-browser';
import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';
import { IfpCustomXlFilterComponent } from '../../ifp-molecules/ifp-custom-xl-filter/ifp-custom-xl-filter.component';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';
import { UsageDashboardLogService } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard.constants';
import { IfpTagComponent } from '../../../../ifp-analytics/atom/ifp-tag/ifp-tag.component';
import { IfpIndicatorCardService } from 'src/app/scad-insights/core/services/indicator-card/ifp-indicator-card.service';
import { environment } from 'src/environments/environment';
import { title } from 'src/app/scad-insights/core/constants/header.constants';
import { IfpChartInsightComponent, InsightList } from '../ifp-chart-insight/ifp-chart-insight.component';
import { IfpIndicatorDisclaimerComponent } from "../../ifp-molecules/ifp-indicator-disclaimer/ifp-indicator-disclaimer.component";


@Component({
  selector: 'app-ifp-indicator-detail-widget',
  templateUrl: './ifp-indicator-detail-widget.component.html',
  styleUrls: ['./ifp-indicator-detail-widget.component.scss'],
  providers: [QuotRemove],
  imports: [CommonModule, FormsModule, TranslateModule, IfpButtonComponent, IfpCardLoaderComponent, IfpMonthSelectorComponent, IfpRatingComponent, IfpDropdownComponent, IfpAnalyticLineChartComponent, IfpToggleButtonComponent, CustomNumberPipe, IfpTableComponent, IfpTabComponent, IfpModalComponent, IfpPdfTemplateComponent, IfpCommentsComponent, IfpCheckBoxComponent, IfpTncModalComponent, IfpNotificationSettingsComponent, IfpIconTextComponent, IfpTooltipDirective, CustomLetter, QuotRemove, IfpCompareListComponent, IfpCircularBarChartComponent, IfpPieChartComponent, IfpSunburstChartComponent,
    IfpCustomXlFilterComponent, IfpTagComponent, IfpChartInsightComponent, IfpIndicatorDisclaimerComponent]
})
export class IfpIndicatorDetailWidgetComponent implements OnInit, AfterViewChecked, OnDestroy, OnChanges {

  @ViewChild('chartRef') chartRef!: IFPHighChartsComponent;
  @ViewChild('chartComponent') chartComponent!: IfpAnalyticLineChartComponent;
  @ViewChild('modalSla', { static: true }) modalSla!: IfpModalComponent;
  @ViewChild('tncModal') tncModal!: IfpModalComponent;
  @ViewChild('fullScreen') fullScreen: ElementRef | undefined;
  @ViewChild('filterModal') filterModal!: IfpModalComponent;
  @ViewChild('downloadPrint') downloadPrint!: ElementRef;

  @Input() id!: string;
  @Input() contentType: string = 'scad_official_indicator';
  @Input() value: string = '28000';
  @Input() domain: any[] = [];
  @Input() classification: string = '';
  @Input() isDashboardCard: boolean = false;
  @Input() title!: string;
  pageDataEmit = output<any>();
  relatedProductDropdownSelected = output<string>();

  public colors = ifpColors;
  public subs = new SubSink();
  public metaValue !: any;
  public comparison!: string;
  public comparisonList: any[] = [];
  public downloadTypes: any = [];
  public format!: string;
  public isYearly: boolean = false;
  public autoSwitch: boolean = false;
  public notificationSelector !: boolean;
  // public chart = false;
  public chartData: any = [];
  public initialFilterData: any = [];
  public tableData: any = [];
  public error = false;
  public isToolTip: boolean = true;
  public isPreciseValue: boolean = false;
  public ratingValues: any = [];
  public myAppsStatus = false;
  public type = '';
  public chartType: string = 'line';
  public domains: any[] = [];
  public isVisble: boolean = true;
  public change = false;
  public name: string = '';
  public isDatalabel: boolean = true;
  public isZoomoutDisabled: boolean = true;
  public yAxisLabel!: string;
  public contentClassification = '';
  public loader: undefined | boolean = true;
  public data: IndicatorListData | any = {};
  public loaderChart: undefined | boolean = true;
  public buttonClass = buttonClass;
  public subtitle: string = '';
  public range: any;
  public chartConstants = chartConstants;
  public filterKeys: any = [];
  public filterPanel: any = true;
  public filterItems: any[] = ['Region', 'Indicator', 'Industry'];
  public seriesMeta!: any;
  public filteredData!: any;
  public toolbarAction: string = 'relatedProducts';
  public optionsLimit = 1000;
  public isRangeSelector: boolean = false;
  public buttonColor = buttonColor;
  public isNotificationEnabled!: boolean;
  public isEmailEnabled!: boolean;
  public iEmailEnabled = false;
  public insightData: any = [];
  public isSubscNotifOpen: boolean = false;
  public viewCheck: boolean = true;
  public tncState!: boolean;
  public foracstTooltip: any = [];
  public queryParams: any = [];
  public toolTipFormat: string = 'number_1.0-0';
  public quarterTooltipLabel: string = '';
  public yearTooltipLabel: string = '';
  public isTncModalOpen: boolean = false;
  public analyticsClasses = analyticsClasses;
  public indicatorType = indicatorType;
  public classifications = classifications;
  public xAxisLabelType: string = chartConstants.xAxisDateTime;
  public forcastData: any = [];
  public chartCatogory: any = [];
  public isuarterData: boolean = false;
  public selectedPeriod: any;
  public benchMark: any = [];
  public compareSelected: any = [];
  public maxYaxisValue: any;
  public circularChartData: any = [];
  public customTimePeriodOptions: any = [];
  public pieSelectedPeriod: any;
  public pieFilterIndex: any;
  public pieChartSeriesData: any = [];
  public previousSelectedFilter: any = [];
  public isShowDropDown: boolean = false;
  public disclaimerDetails: any = [];
  public sunburstSeriesData: SunburstChartData[] = [];
  private isSunBurst: boolean = false;
  public previousChartType!: string;
  public isDashboardFilter: boolean = false;
  public isCoi: boolean = false;
  public invertColor: boolean = false;
  public removePercentageCalculation: boolean = false;
  public actualValue: { format: string, label: string, path: string } | undefined = { format: '', label: '', path: '' };
  public security!: Security;
  public insightList!: InsightList;
  public hidingChartTypes: string[] = [];
  public timeUnit!: string;
  public enableDateFilter: boolean = false;
  public xAxisLabelsUpdateString: Record<string, any> = {};
  public relatedProductsSearchTerm: string = '';
  public relatedSV: { id: string | number; tittle?: string; title?: string; name?: string; label?: string; tittle_ar?: string; title_ar?: string; }[] = [];
  public filteredRelatedProducts: { id: string | number; tittle?: string; title?: string; name?: string; label?: string; tittle_ar?: string; title_ar?: string; }[] = [];

  private cntType!: string;


  public comparisonValues: Record<string, { change: string, compare: string }> = {
    Yearly: {
      change: 'yearlyChangeValue',
      compare: 'yearlyCompareValue'
    },
    Monthly: {
      change: 'monthlyChangeValue',
      compare: 'monthlyCompareValue'
    },
    Quarterly: {
      change: 'quarterlyChangeValue',
      compare: 'quarterlyCompareValue'
    }
  };

  public notificationOptions!: any;

  tabData: LabelData[] = [
    {
      iconClass: 'ifp-icon-graph-line',
      name: 'line',
      machineName: 'All'
    },
    {
      iconClass: 'ifp-icon-table',
      name: 'table',
      machineName: 'All'
    }
  ];

  private sessionId!: string | undefined;
  private downloadSessionId!: string | undefined;

  constructor(private store: Store, private _cdr: ChangeDetectorRef, private downloadService: DownloadService, private _filterService: FilterService, public analyticalService: AnalyticalService, public insightService: InsightsService, private _toasterService: ToasterService, private _commonApiService: CommonApiService, private _translateService: TranslateService,
    private route: ActivatedRoute, private customLetter: CustomLetter, private el: ElementRef, private _renderer: Renderer2, private datePipe: DatePipe, private _quotRemove: QuotRemove, private chartToolService: ChartToolsService, private _titleService: Title
    , private _msalService: IFPMsalService, private _dashboardService: DashboardService, private log: UsageDashboardLogService, private _cardService: IfpIndicatorCardService) {
    this.route.queryParams.subscribe(val => {
      this.queryParams = val;
    });


    // for dashboard settings change //

    this._dashboardService.settingsChanged.subscribe(resp => {
      if ((resp.type == chartConstants.OFFICIAL_STATISTICS || resp.type == chartConstants.EXPERIMENTAL_STATISTICS) && this.id === resp.id && !this._dashboardService.dashboardTools.includes(resp.tools)) {
        this.isDashboardFilter = false;
        if (resp.tools == this._dashboardService.cardFilter) {
          this.isDashboardFilter = true;
          this.filterKeys = this._dashboardService.chartSettings[resp.type]?.find((x: { id: string; }) => x.id == resp.id).selectedFilter;
        }
        this.ngOnInit();
      }
    });
  }

  ngOnInit() {
    this.subs.add(this.route.params.subscribe(val => {
      this.benchMark = [];
      if (val['id']) {
        this.id = val['id'];
      }
      const subs = this.store.select(selectIndicatorGetById(this.id))
        .pipe()
        .subscribe((data) => {
          this.relatedSV = data?.body?.indicatorVisualizations.visualizationsMeta[0].relatedSV || [];
          this.filteredRelatedProducts = [...this.relatedSV];
          this.cntType = data.body?.type != 'coi' ? data?.body?.content_classification_key : contentTypeDashboard['analytical-apps'];
          this.isCoi = false;
          if (this.sessionId) {
            this.log.logEnds(this.sessionId, this.log.currentTime);
            this.sessionId = undefined;
          }
          if (data.body && data.body != null) {
            this.sessionId = this.log.createUUid;
            if (data?.body?.content_classification_key === classifications.innovativeStatistics) {
              this.log.logStart(this.sessionId, logType.indicator, this.log.currentTime, data.body?.subdomain_id ?? +this.id);
            } else {
              this.log.logStart(this.sessionId, logType.indicator, this.log.currentTime, +this.id);
            }
          }
          if (this.isDashboardCard) {
            if (this._dashboardService.chartSettings[this.cntType]?.find((x: { id: string; }) => x.id == this.id)?.selectedFilter) {
              this.filterKeys = this._dashboardService.chartSettings[this.cntType]?.find((x: { id: string; }) => x.id == this.id).selectedFilter;
              this.isDashboardFilter = true;
            }
          }
          this.chartType = !this.isDashboardCard ? 'line' : this._dashboardService.getChartType(this.cntType, this.id);
          if (data?.body && !this.isDashboardCard) {
            this._titleService.setTitle(`${title.bayaan} | ${data?.body?.component_title}`);
            if (data?.body?.component_title) {
              (window as any)?.dataLayer?.push({
                'event': 'page_load',
                'page_title_var': data.body.component_title,
                'userId': this._msalService.convertEmail(this._msalService.getLoginData.account.username)
              });
            }
          }
          this.loaderChart = data.loader;
          if (data.status) {

            this.selectedPeriod = {
              id: 'Latest-Readings',
              label: 'Recent',
              unit: chartConstants.RECENT_LABEL,
              value: '',
              isSelected: true
            };
            this.isPreciseValue = false;
            this.ratingValues = [];
            this.metaValue = data?.body?.indicatorValues?.overviewValuesMeta?.[0];
            this.hidingChartTypes = data.body.indicatorVisualizations.visualizationsMeta?.[0]?.hideChart;
            this.subtitle = data?.body?.component_subtitle.trim();
            this.data = cloneDeep(data.body);
            this.invertColor = data?.body?.indicatorVisualizations?.visualizationsMeta[0]?.invertColor;
            this.removePercentageCalculation = data?.body?.indicatorVisualizations?.visualizationsMeta[0]?.isPercentageValue;
            this.disclaimerDetails = this.data?.domain_details?.policy_guide;
            this.benchMark = this.data.benchmarks;
            this.toolbarAction = this.data?.enableCompare == 'true' ? 'compare' : (this.relatedSV.length ? 'relatedProducts' : 'settings');
            this.store.dispatch(getInsights({ nodeId: this.data.id }));
            if (this.data.indicatorValues?.valuesMeta?.length > 0) {
              this.ratingValues = cloneDeep(this.data.indicatorValues?.valuesMeta);
            }
            this.emitPageData();
            this.yAxisLabel = data?.body?.indicatorVisualizations?.visualizationsMeta?.[0].yAxisLabel;
            this.toolTipFormat = data?.body?.indicatorVisualizations?.visualizationsMeta?.[0].tooltipValueFormat;
            this.quarterTooltipLabel = data?.body?.indicatorVisualizations?.visualizationsMeta?.[0].quarterOnQuarterLabel;
            this.yearTooltipLabel = data?.body?.indicatorVisualizations?.visualizationsMeta?.[0].yearOnYearLabel;
            this.actualValue = undefined;
            if (data?.body?.indicatorVisualizations?.visualizationsMeta?.[0]?.tooltipValues?.[0]) {
              this.actualValue = data?.body?.indicatorVisualizations?.visualizationsMeta?.[0].tooltipValues[0];
            }
            if (!this.isDashboardFilter) {
              this.filterKeys = [];
              if (this.data?.filterPanel?.properties?.length > 0) {
                this.data?.filterPanel.properties.forEach((element: any, index: number) => {
                  element.isDisabled = false;
                  const obj = {
                    label: element.label,
                    value: [element.options[0]],
                    index: index,
                    path: element.path
                  };
                  this.filterKeys.push(obj);
                });
              }
            }
            if (data.body?.indicatorTools && data.body.indicatorTools.length > 0) {
              data.body.indicatorTools.forEach((element: { disabled: any; label: any; }) => {
                if (!element.disabled) {
                  this.downloadTypes.push(element.label);
                }
              });
            }
            if (this.data.indicatorFilters) {
              this.data.indicatorFilters[0].options.map((x: { isSelected: any; }) => x.isSelected = false);
              this.data.indicatorFilters[0].options.push(this.selectedPeriod);
            }
            if (!data.loader) {
              this.loader = data.loader;
            }
            this.loaderChart = data.loader;
            if (!data.loader && !data.status) {
              this.error = true;
            }
            this.contentClassification = this.data?.content_classification;
            this.value = this.metaValue?.value;
            if (this.data?.component_title) {
              this.name = this._quotRemove.transform(this.data?.component_title?.trim());
            }
            this.comparison = this.metaValue?.compareFilters?.[0];
            this.comparisonList = this.metaValue?.compareFilters;
            this.type = this.data?.type;
            this.seriesMeta = this.data.type != 'coi' ? this.data.indicatorVisualizations.visualizationsMeta?.[0]?.seriesMeta?.[0] : this.data.indicatorVisualizations.visualizationsMeta?.[0]?.seriesMeta;
            this.maxYaxisValue = this.seriesMeta.yMax;
            if (this.comparison) {
              this.format = this.metaValue?.valueFormat.split('_')[1];
            }
            this.chartType = this.data?.indicatorVisualizations?.visualizationsMeta?.[0].type == chartConstants.SUNBURST_CHART ? chartConstants.SUNBURST_TYPE : this.chartType;
            this.isSunBurst = this.chartType == chartConstants.SUNBURST_TYPE ? true : false;
            if (this.data?.thresholdValue) {
              this.disclaimerDetails = ['censesDesclimer'];
            }
            if (this.data?.showMaritalDisclaimer) {
              this.disclaimerDetails.push('censusMartialDesclimer');
            }
            if (this.chartType != chartConstants.SUNBURST_TYPE) {
              this.filterData();
            }
            if (this.chartType == 'circular') {
              this.createCircularBarChart();
            }
            if (this.chartType == chartConstants.SUNBURST_TYPE) {
              this.createSunburstSeries();
            }
            if (this.chartType == 'table' && this.data.type != 'coi') {
              this.tableData = this.setTableData();
            }
            if (this.chartType == 'pie' || this.chartType == 'doughnut') {
              this.previousSelectedFilter = cloneDeep(this.filterKeys);
              const filterWithoutTimePeriod = this.filterKeys.filter((x: { path: string; }) => x.path != chartConstants.TIME_PERIOD);
              if (filterWithoutTimePeriod.every((x: { path: string; value: string | any[]; }) => x.value?.length == 1)) {
                this.pieFilterIndex = this.data.filterPanel?.properties[0].path == chartConstants.TIME_PERIOD ? 1 : 0;
                this.filterKeys[this.pieFilterIndex].value = this.data.filterPanel?.properties[this.pieFilterIndex].options;
              }
              this.createPieChartData();
            }
            if (this.data?.security && environment.env !== 'demo') {
              this.security = this._cardService.setSecurity(this.data.security);
            }
            this._cdr.detectChanges();
          }
          if (data.body && data.body != null) {
            subs?.unsubscribe();
          }
        });
      // this.subs.add(this.store.select(selectGetMappedData(this.data.id)).pipe(distinctUntilChanged((prev, curr) => prev === curr)).subscribe(data => {
      //   this.isNotificationEnabled = data[this.data.id].isNotification;
      //   this.isEmailEnabled = data[this.data.id].isEmail;
      //   this._cdr.detectChanges();
      // }));
      this.subs.add(this.store.select(selectGetMyAppsMappedData(this.id)).pipe(distinctUntilChanged((prev, curr) => prev === curr)).subscribe(myapps => {
        this.myAppsStatus = myapps;
        this._cdr.detectChanges();
      }));
      this.subs.add(
        this.store.select(selectInsightResponse).subscribe((chartInsight: any) => {
          this.insightData = chartInsight;
          this._cdr.detectChanges();
        })
      );
      this.subs.add(this._commonApiService.getDownloadTermsStatus(this.id).subscribe((res: any) => {
        if (res) {
          if (res.status) {
            this.tncState = res.status;
          }
        }
      }));

      // this.insightService.getChartInsightList(this.id, false);
      this.notificationOptions = {
        contentType: this.queryParams.contentType ? this.queryParams.contentType : this.contentType,
        id: this.data.id,
        appType: this.queryParams.contentType ? this.queryParams.contentType : this.contentType,
        compare: false,
        emailStatus: false
      };
    }));
  }




  ngOnChanges() {
    this.subs.add(this.store.select(selectGetMappedData(this.id)).pipe(distinctUntilChanged((prev, curr) => prev === curr)).subscribe(data => {
      this.isNotificationEnabled = data[this.data.id]?.isNotification ? data[this.data.id]?.isNotification : false;
      this.isEmailEnabled = data[this.data.id]?.isEmail ? data[this.data.id]?.isEmail : false;
      this._cdr.detectChanges();
    }));
  }

  filter() {
    if (this.ratingValues?.length > 0) {
      this.ratingValues.forEach((element: { title: any; value: number; percentage: any; color: string; }) => {
        const comparisonValue = this.comparisonValues[this.comparison];
        element.title = this.formatRange(this.metaValue);
        element.value = this.metaValue?.[comparisonValue.compare];
        element.percentage = this.metaValue?.[comparisonValue.change];
        element.color = element.value > 0 ? this.colors.greenDark : this.colors.red;
      });
    }
  }

  formatRange(data: any) {
    return this._filterService.formatingTitle(data);
  }

  removeTooltip() {
    if (this.chartRef) {
      this.chartRef.removeTooltip();
    }
  }

  // download documents ;
  download(type: string) {
    if (type != 'XL') {
      this._renderer.addClass(document.body, 'ifp-light-theme');
      this.modalSla.createElement();
    }
    if (type == 'pdf') {
      setTimeout(() => {
        const offsetHeight = this.downloadPrint.nativeElement.offsetHeight;
        const offsetWidth = this.downloadPrint.nativeElement.offsetWidth;
        this.downloadService.downloadPdf(this.downloadPrint.nativeElement, this.name, offsetHeight, offsetWidth, this.chartType).then(_resp => {
          if (_resp) {
            this._renderer.removeClass(document.body, 'ifp-light-theme');
            this.modalSla.removeModal();
          }
        });
      }, 1000);
    }

    if (type == 'XL') {
      const downloadData: any = [];
      // if (this.data.type != 'coi' && this.chartType != chartConstants.SUNBURST_TYPE) {
      //   if (this.initialFilterData?.length > 0) {
      //     this.initialFilterData.forEach((element: any) => {
      //       if (element?.length > 0) {
      //         downloadData.push(...element);
      //       }
      //     });
      //   }
      // }
      // if (this.data.type == 'coi' && this.chartType != chartConstants.SUNBURST_TYPE) {
      this.tableData.forEach((element: any) => {
        downloadData.push(...element);
      });
      // }
      this.downloadService.exportToExcel(this.chartType != chartConstants.SUNBURST_TYPE ? downloadData : this.sunburstSeriesData, this.name.replace(/[*?:/\\[\]]/g, "_"));
    }

    if (type == 'png') {
      setTimeout(() => {
        this.downloadService.downloadElementAsPNG(this.downloadPrint.nativeElement, this.name.replace(/[*?:/\\[\]]/g, "_"));
        this.modalSla.removeModal();
        this._renderer.removeClass(document.body, 'ifp-light-theme');
      }, 1000);
    }
    // if (type == 'ppt') {
    //   setTimeout(() => {
    //     this.downloadService.downloadAsPPT(document.getElementById('downloadPrint'), this.name);
    //     this.modalSla.removeModal();
    //   }, 1000);
    // }
    if (this.downloadSessionId) {
      this.log.logEnds(this.downloadSessionId, this.log.currentTime);
    }
    this.downloadSessionId = this.log.createUUid;
    this.log.logStart(this.downloadSessionId, logType.download, this.log.currentTime, +this.id, this.name, type);
  }



  // FilterPanel apply filter
  applyFilter(event: any, label: string) {
    if (!event || event?.length <= 0) {
      event = [this.data.filterPanel.properties.find((x: { label: string; }) => x.label == label).default];
    }
    this.autoSwitch = true;
    this.filterKeys.find((x: { label: any; }) => x.label == label).value = Array.isArray(event) ? event : [event];
    if (this.chartType == 'line' || this.chartType == 'column' || this.chartType == 'bar' || this.chartType == 'table') {
      this.filterData();
    } else if (this.chartType == 'circular') {
      this.createCircularBarChart();
    } else if (this.chartType == 'pie') {
      this.createPieChartData();
    }
  }

  getSelectedFilters() {
    this.data?.indicatorFilters?.[0]?.options;
  }

  // Apply period filter
  applyPeriodFilter(event: any) {
    if (event.id == 'All') {
      this.isYearly = false;
    }
    this.selectedPeriod = event;
    if (this.compareSelected?.length > 0) {
      this.addOrRemoveCompareData();
    } else if (this.chartType == 'line' || this.chartType == 'column' || this.chartType == 'bar' || this.chartType == 'table') {
      this.filterData();
    } else if (this.chartType == 'circular') {
      this.createCircularBarChart();
    }

  }



  filterData() {
    this.isCoi = false;
    if (this.filterKeys?.length > 0) {
      if (this.filterKeys.some((z: { value: string | any[]; }) => z.value.length > 1)) {
        this.filterKeys.map((x: any) => {
          x.checkbox = x.value.length > 1 ? true : false;
        });
      }
      if (!this.filterKeys.some((z: { value: string | any[]; }) => z.value.length > 1)) {
        this.filterKeys.forEach((element: any, index: number) => {
          element.checkbox = index == 0 ? true : false;
        });
      }
    }
    this.checkDefaultFilter();
    if (this.data.type != 'coi') {
      this.isCoi = false;
      this.getInsightData();
      if ((this.benchMark?.length <= 0 || !this.benchMark) && this.filterKeys?.length > 0) {
        this.tabData = [
          {
            iconClass: 'ifp-icon-graph-line',
            name: 'line',
            machineName: 'All',
            hide: chartTypeHide('line', this.hidingChartTypes)
          },
          {
            iconClass: 'ifp-icon-bar-chart',
            name: 'column',
            machineName: 'All',
            hide: chartTypeHide('column', this.hidingChartTypes)
          },
          {
            iconClass: 'ifp-icon-horizontal-bar',
            name: 'bar',
            machineName: 'All',
            hide: chartTypeHide('bar', this.hidingChartTypes)
          },
          {
            iconClass: 'ifp-icon-radial-bar',
            name: 'circular',
            machineName: 'All',
            hide: chartTypeHide('circular', this.hidingChartTypes)
          },
          {
            iconClass: 'ifp-icon-pie-chart',
            name: 'pie',
            machineName: 'All',
            hide: chartTypeHide('pie', this.hidingChartTypes)
          },
          {
            iconClass: 'ifp-icon-table',
            name: 'table',
            machineName: 'All',
            hide: chartTypeHide('table', this.hidingChartTypes)
          }
        ];
      } else if ((this.benchMark?.length <= 0 || !this.benchMark) && this.filterKeys?.length <= 0) {
        this.tabData = [
          {
            iconClass: 'ifp-icon-graph-line',
            name: 'line',
            machineName: 'All',
            hide: chartTypeHide('line', this.hidingChartTypes)
          },
          {
            iconClass: 'ifp-icon-bar-chart',
            name: 'column',
            machineName: 'All',
            hide: chartTypeHide('column', this.hidingChartTypes)
          },
          {
            iconClass: 'ifp-icon-horizontal-bar',
            name: 'bar',
            machineName: 'All',
            hide: chartTypeHide('bar', this.hidingChartTypes)
          },
          {
            iconClass: 'ifp-icon-radial-bar',
            name: 'circular',
            machineName: 'All',
            hide: chartTypeHide('circular', this.hidingChartTypes)
          },
          {
            iconClass: 'ifp-icon-table',
            name: 'table',
            machineName: 'All',
            hide: chartTypeHide('table', this.hidingChartTypes)
          }
        ];
      } else if (this.benchMark?.length > 0 || this.benchMark) {
        this.tabData = [
          {
            iconClass: 'ifp-icon-graph-line',
            name: 'line',
            machineName: 'All',
            hide: chartTypeHide('line', this.hidingChartTypes)
          },
          {
            iconClass: 'ifp-icon-bar-chart',
            name: 'column',
            machineName: 'All',
            hide: chartTypeHide('column', this.hidingChartTypes)
          },
          {
            iconClass: 'ifp-icon-table',
            name: 'table',
            machineName: 'All',
            hide: chartTypeHide('table', this.hidingChartTypes)
          }
        ];
      }

    }


    if (this.data.type == 'coi') {
      this.isCoi = true;
      this.getForcastData();
    }

  }


  getInsightData() {
    this.initialFilterData = [];
    this.foracstTooltip = [];
    if (this.isDashboardCard) {
      this.selectedPeriod = this._dashboardService.getRecentValue(this.cntType, this.id);
    }
    this.initialFilterData = this._filterService.filterIndicatorDetail(this.seriesMeta, this.selectedPeriod, this.filterKeys);
    this.tableData = this.setTableData();
    this.chartData = [];
    this.isYearly = true;
    if (this.data.indicatorVisualizations.visualizationsMeta?.[0]?.timeUnit?.includes(chartConstants.MONTHLY)) {
      this.isYearly = false;
    }
    const isQuarter = this.data.indicatorVisualizations.visualizationsMeta?.[0]?.showQuarterlyIntervals;
    if ((this.data.indicatorVisualizations.visualizationsMeta?.[0]?.timeUnit?.includes('Quarterly') && this.selectedPeriod.id != 'All' && !this.data.indicatorVisualizations.visualizationsMeta?.[0]?.timeUnit?.includes('Monthly')) || isQuarter) {
      this.createQuarterData();
    } else if (this.initialFilterData[0]?.length < 5 && this.chartType == 'circular') {
      this.createDateCatogoryData();
    } else {
      this.createAutoYaxisLabelData();
    }

    this.isVisble = false;
    this._cdr.detectChanges();
    this.change = true;
  }



  createDateCatogoryData() {
    this.xAxisLabelType = chartConstants.xAxisCatogory;
    this.chartCatogory = [];
    this.initialFilterData.forEach((series: Record<string, any>[], index: number) => {
      const newChart: any[] = [];
      const catogory: (string | undefined)[] = [];
      series.forEach(element => {
        const catLabel: any = this.isYearly ? this.datePipe.transform(element['OBS_DT'], 'yyyy') : this.datePipe.transform(element['OBS_DT'], 'MMM yyyy');
        newChart.push(element['VALUE']);
        catogory.push(catLabel);
      });
      const seriesData: any = {
        color: !this.isDashboardCard ? this.getColor(index) : this._dashboardService.getColors(this.cntType, this.id, index),
        data: newChart,
        name: this.customLetter.transform(this.filterKeys.find((x: { checkbox: boolean; }) => x.checkbox)?.value[index] ? this.filterKeys.find((x: { checkbox: boolean; }) => x.checkbox)?.value[index] : this.data.indicatorVisualizations.visualizationsMeta?.[0].componentTitle),
        type: this.chartType,
        marker: {
          enabled: true
        }
      };
      if (this.isDashboardCard) {
        seriesData.spacing = this._dashboardService.gettingSpaceValues(this.cntType, this.id);
        seriesData.xAxisPositions = this._dashboardService.getXaxisPositions(this.cntType, this.id);
        seriesData.legendPositions = this._dashboardService.getLegendPositions(this.cntType, this.id);
        seriesData.isDatalabel = this._dashboardService.getChartSettings(this.cntType, this.id, chartConstants.DATALABEL);
        seriesData.isPrecise = this._dashboardService.getChartSettings(this.cntType, this.id, chartConstants.PRESICE_VALUE);
      }
      if (seriesData.data?.length > 0) {
        this.chartData.push(seriesData);
        this.chartCatogory.push(...catogory);
      }
      if (this.isDashboardCard) {
        this._dashboardService.setSeriesLength(this.cntType, this.id, this.chartData);
      }
    });
  }

  createAutoYaxisLabelData() {
    this.xAxisLabelsUpdateString = {};
    this.xAxisLabelType = chartConstants.xAxisDateTime;
    this.initialFilterData.forEach((series: Record<string, any>[], index: number) => {
      const newChart = series?.map((element: Record<string, any>) => {
        const splitDate = element['OBS_DT'].split('-');
        const maxObsDtFormat = element?.['OBS_DT_FORMAT'];
        if (maxObsDtFormat != null) {
          this.xAxisLabelsUpdateString[(Date.UTC(splitDate[0], splitDate[1] - 1, splitDate[2]))] = maxObsDtFormat
        }
        return [(Date.UTC(splitDate[0], splitDate[1] - 1, splitDate[2])), element['VALUE']];
      });
      const seriesData: any = {
        color: !this.isDashboardCard ? this.getColor(index) : this._dashboardService.getColors(this.cntType, this.id, index),
        data: newChart,
        name: this.customLetter.transform(this.filterKeys.find((x: { checkbox: boolean; }) => x.checkbox)?.value[index] ? this.filterKeys.find((x: { checkbox: boolean; }) => x.checkbox)?.value[index] : (this.data.indicatorVisualizations.visualizationsMeta?.[0].componentTitle ? this.data.indicatorVisualizations.visualizationsMeta?.[0].componentTitle : this.data.component_title)),
        type: this.chartType,
        marker: {
          enabled: true
        }
      };
      if (this.isDashboardCard) {
        seriesData.spacing = this._dashboardService.gettingSpaceValues(this.cntType, this.id);
        seriesData.xAxisPositions = this._dashboardService.getXaxisPositions(this.cntType, this.id);
        seriesData.legendPositions = this._dashboardService.getLegendPositions(this.cntType, this.id);
        seriesData.isDatalabel = this._dashboardService.getChartSettings(this.cntType, this.id, chartConstants.DATALABEL);
        seriesData.isPrecise = this._dashboardService.getChartSettings(this.cntType, this.id, chartConstants.PRESICE_VALUE);
      }
      if (seriesData.data?.length > 0) {
        this.chartData.push(seriesData);
      }
      series.forEach(element => {
        this.foracstTooltip.push({
          MonthLabel: element['CHANGE_QQ'],
          YearLabel: element['CHANGE_YY']
        });
      });
      if (this.isDashboardCard) {
        this._dashboardService.setSeriesLength(this.cntType, this.id, this.chartData);
      }
    });
    console.log("this.chartData", this.chartData);
  }

  createQuarterData() {
    this.xAxisLabelType = chartConstants.xAxisCatogory;
    this.chartCatogory = [];
    this.initialFilterData.forEach((series: Record<string, any>[], index: number) => {
      const newChart: any[] = [];
      const catogory: (string | undefined)[] = [];
      series.forEach(element => {
        newChart.push(element['VALUE']);
        catogory.push(this.convertDateToQuarter(element['OBS_DT']));
      });
      const seriesData: any = {
        color: !this.isDashboardCard ? this.getColor(index) : this._dashboardService.getColors(this.cntType, this.id, index),
        data: newChart,
        name: this.customLetter.transform(this.filterKeys.find((x: { checkbox: boolean; }) => x.checkbox)?.value[index] ? this.filterKeys.find((x: { checkbox: boolean; }) => x.checkbox)?.value[index] : this.data.component_title),
        type: this.chartType,
        marker: {
          enabled: true
        }
      };
      if (this.isDashboardCard) {
        seriesData.spacing = this._dashboardService.gettingSpaceValues(this.cntType, this.id);
        seriesData.xAxisPositions = this._dashboardService.getXaxisPositions(this.cntType, this.id);
        seriesData.legendPositions = this._dashboardService.getLegendPositions(this.cntType, this.id);
        seriesData.isDatalabel = this._dashboardService.getChartSettings(this.cntType, this.id, chartConstants.DATALABEL);
        seriesData.isPrecise = this._dashboardService.getChartSettings(this.cntType, this.id, chartConstants.PRESICE_VALUE);
      }
      if (seriesData.data?.length > 0) {
        this.chartData.push(seriesData);
        this.chartCatogory.push(...catogory);
      }
      series.forEach(element => {
        this.foracstTooltip.push({
          MonthLabel: element['CHANGE_QQ'],
          YearLabel: element['CHANGE_YY']
        });
      });
      if (this.isDashboardCard) {
        this._dashboardService.setSeriesLength(this.cntType, this.id, this.chartData);
      }
    });
  }

  convertDateToQuarter(date: any) {
    let label: string = '';
    if ((new Date(date).getMonth() + 1) <= 3 && (new Date(date).getMonth() + 1) > 0) {
      label = `Q1<br>${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if ((new Date(date).getMonth() + 1) > 3 && (new Date(date).getMonth() + 1) <= 6) {
      label = `Q2<br>${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if ((new Date(date).getMonth() + 1) > 6 && (new Date(date).getMonth() + 1) <= 9) {
      label = `Q3<br>${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if ((new Date(date).getMonth() + 1) > 9 && (new Date(date).getMonth() + 1) <= 12) {
      label = `Q4<br>${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if ((new Date(date).getMonth() + 1) == 0) {
      label = `Q1<br>${this.datePipe.transform(new Date(date), 'yyyy')}`;
    }
    return label;
  }

  getForcastData() {
    this.isYearly = true;
    if (this.data.indicatorVisualizations.visualizationsMeta?.[0]?.timeUnit?.includes(chartConstants.MONTHLY) || this.data.indicatorVisualizations.visualizationsMeta?.[0]?.showQuarterlyIntervals == 'true' || this.data.indicatorVisualizations.visualizationsMeta?.[0]?.showQuarterlyIntervals == true) {
      this.isYearly = false;
    }
    if (this.seriesMeta?.length > 0) {
      this.initialFilterData = [];
      this.foracstTooltip = [];
      this.tableData = [];
      const multiSeriesOptIndex = this.filterKeys.findIndex((x: { checkbox: boolean; }) => x.checkbox);
      const editableSerieData = cloneDeep(this.seriesMeta);
      const multiseriesData = this.data?.filterPanel?.properties?.length ? this._filterService.filterForcastwithOpts(editableSerieData, this.filterKeys, true, true) : this.seriesMeta;
      multiseriesData.forEach((element: any) => {
        this.initialFilterData.push(...this._filterService.filterIndicatorForcast(element, this.selectedPeriod));
      });
      const validationLength = this.data?.filterPanel?.properties?.length ? (this.filterKeys[multiSeriesOptIndex].value?.length * 2) : this.seriesMeta.length;
      this.tableData = this.setForcastTableData();
      this.chartData = this.analyticalService.creatingLineChartData(this.initialFilterData, this.id, this.cntType, this.actualValue, this.isYearly);
      if (this.initialFilterData?.length == validationLength) {
        this.initialFilterData.forEach((chart: { data: any; }) => {
          if (chart.data?.length > 0) {
            const toolTipData: any = [];
            chart.data.forEach((element: Record<string, number | string>) => {
              const toolTip: Record<string, number | string> = {
                MonthLabel: element['CHANGE_QQ'],
                YearLabel: element['CHANGE_YY']
              };
              if (this.actualValue) {
                toolTip[this.actualValue.label] = element[this.actualValue.path];
              }
              toolTipData.push(toolTip);
            });
            this.foracstTooltip.push(toolTipData);
          }
        });
      }
      this.xAxisLabelType = chartConstants.xAxisDateTime;
      if (this.data?.filterPanel?.properties?.length) {
        this.isVisble = false;
        this.change = true;
      }
      if (this.isDashboardCard) {
        this.chartData[0].spacing = this._dashboardService.gettingSpaceValues(this.cntType, this.id);
        this.chartData[0].legendPositions = this._dashboardService.getLegendPositions(this.cntType, this.id),
        this.chartData[0].xAxisPositions = this._dashboardService.getXaxisPositions(this.cntType, this.id);
        this.chartData[0].isDatalabel = this._dashboardService.getChartSettings(this.cntType, this.id, chartConstants.DATALABEL);
        this.chartData[0].isPrecise = this._dashboardService.getChartSettings(this.cntType, this.id, chartConstants.PRESICE_VALUE);
        this._dashboardService.setSeriesLength(this.cntType, this.id, this.chartData);
      }
      if ((this.data.indicatorVisualizations.visualizationsMeta?.[0]?.showQuarterlyIntervals == 'true' || this.data.indicatorVisualizations.visualizationsMeta?.[0]?.showQuarterlyIntervals == true) && this.selectedPeriod.id != 'All') {
        this.isVisble = false;
        this.xAxisLabelType = chartConstants.xAxisCatogory;
        this.createQuarterForcastData();
      }
    }
  }


  createQuarterForcastData() {
    this.chartCatogory = [];
    if (this.chartData?.length > 0) {
      this.chartData.forEach((element: any) => {
        // if (index <= 2) {
        this.chartCatogory.push(...this.convertUtcToDate(element.data));
        // }
        element.data = element.data.map((arr: string | any[]) => arr.slice(1));
      });
    }
    const groupedChartData = groupBy(this.chartData, (a: any) => a.identifier);
    for (const key in groupedChartData) {
      if (Object.hasOwn(groupedChartData, key)) {
        // eslint-disable-next-line no-loop-func
        groupedChartData[key].forEach((elementValues: any) => {
          if (elementValues.type == 'arearange' || elementValues.name?.includes('-forecast') || elementValues.name?.includes('-تكهن')) {
            elementValues.pointStart = groupedChartData[key][0].data.length - 1;
          }
        });
        this.change = true;
      }
    }
    this.chartCatogory = [...new Set(this.chartCatogory)];
  }


  convertUtcToDate(data: any) {
    const dates: any = [];
    let date: any;
    if (data?.length > 0) {
      for (let index = 0; index < data.length; index++) {
        const convertedDate = this.datePipe.transform(data[index][0], 'yyyy-MM-dd');
        date = this.convertDateToQuarter(convertedDate);
        dates.push(date);
      }
    }
    return dates;
  }

  getColor(index: number) {
    let color: any;
    switch (index) {
    case 0:
      color = ifpColors.green;
      break;
    case 1:
      color = ifpColors.blue;
      break;
    case 2:
      color = ifpColors.lightBlue;
      break;
    case 3:
      color = ifpColors.red;
      break;
    case 4:
      color = ifpColors.colorGreyBg;
      break;
    case 5:
      color = ifpColors.skyBlue;
      break;
    case 6:
      color = ifpColors.orange;
      break;
    case 7:
      color = ifpColors.yellow;
      break;
    case 8:
      color = ifpColors.chartBlue;
      break;
    case 9:
      color = ifpColors.chartGreen;
      break;
    case 10:
      color = ifpColors.chartVilot;
      break;
    case 11:
      color = ifpColors.chartBlack;
      break;
    case 12:
      color = ifpColors.chartOrange;
      break;
    default:
      color = ifpColors.grey;
      break;
    }
    return color;
  }

  checkValue() {
    let dropValue = true;
    if (this.filterKeys.length > 0) {
      if (this.filterKeys.some((x: { value: string | any[]; }) => x.value.length > 1)) {
        dropValue = false;
      }
    }
    return dropValue;
  }


  ngAfterViewChecked(): void {
    if (this.change) {
      this.isVisble = true;
      this.isShowDropDown = false;
      this.enableDateFilter = true;
      this._cdr.detectChanges();
      this.change = false;
      this.changeDataLabel(false);
      this.changeTooltip(false);
      this.changePreciseLabel(false);
    }
  }

  checkIndex() {
    return this.filterKeys.findIndex((x: { value: string | any[]; }) => x.value.length > 1);
  }

  zoom(type: string) {
    if (this.chartComponent) {
      this.chartComponent.zoom(type);
    }
  }


  fullscreen() {
    this.chartToolService.fullscreen(this.fullScreen, this.chartComponent, this.chartType);
  }

  print() {
    window.print();
  }

  // addNotification(current: boolean) {
  //   if (current) {
  //     this.store.dispatch(setNotificationUpdate({ id: this.data.id, contentType: 'analytical-apps', appType: this.data.type}));
  //   } else {
  //     this.store.dispatch(unsubscribeNotificationUpdate({ id: this.data.id }));
  //   }
  // }

  // setEmailNotifStatus(status: boolean) {
  //   if (status) {
  //     if (!this.isNotificationEnabled) {
  //       this.store.dispatch(setEmailWithNotificationSuccess({ id: this.data.id, contentType: 'analytical-apps', appType: this.data.type, emailStatus: true }));
  //     } else {
  //       this.store.dispatch(setEmailUpdate({ id: this.data.id }));
  //     }
  //   } else {
  //     this.store.dispatch(unsubscribeEmailUpdate({ id: this.data.id }));
  //   }
  // }

  addDataMyApps(status: boolean) {
    if (status) {
      this.store.dispatch(unsubscribeMyAppsUpdate({ id: this.data.id, title: this.title, contentType: this.queryParams.contentType ? this.queryParams.contentType : this.contentType }));
    } else {
      const data = {
        title: this.title,
        contentType: this.queryParams.contentType ? this.queryParams.contentType : this.contentType,
        id: this.data.id
      };
      this.store.dispatch(setMyAppsUpdate(data));

    }
  }

  emitPageData() {
    // let data: any;
    // if (this.data?.domain) {
    const data = {
      parent: this.data?.domain ??  this.queryParams?.topic,
      title: this.data.component_title,
      id: this.data?.domain_id ?? this.queryParams.domainId,
      content_classification: this.data.content_classification,
      content_classification_key: this.data.content_classification_key
    };
    // }
    // if (this.queryParams?.visa == 'true') {
    //   data = {
    //     parent: this.queryParams?.topic ? this.queryParams.topic : this.data.domain,
    //     title: this.data.component_title,
    //     id: this.queryParams.domainId
    //   };
    // }
    this.pageDataEmit.emit(data);
  }

  valueRangeUpdated(event: any) {
    if (event && event.flag == 'completed') {
      const data = {
        title: 'Selected Range Value',
        value: event.value,
        invertArrows: false
      };
      this.ratingValues = [];
      this.ratingValues.push(data);
    }
    if (event && event.flag == 'inprogress') {
      this.ratingValues = [];
    }
  }

  getRangeSelector(event: any) {
    this.ratingValues = [];
    this.isRangeSelector = event;
    if (!this.isRangeSelector) {
      this.ratingValues = cloneDeep(this.data.indicatorValues?.valuesMeta);
    }
  }

  formatTitle(data: any) {
    return this._filterService.formatingTitle(data);
  }

  changeChart(type: string) {
    this.chartType = type;
    if (type != 'pie') {
      if (!this.autoSwitch && this.previousChartType == 'pie') {
        this.filterKeys = this.previousSelectedFilter;
        this.isShowDropDown = true;
      }
      this.previousSelectedFilter = [];
      this.isVisble = false;
      this.change = true;
    }
    if (type == 'line' || type == 'column' || type == 'bar' || type == 'table') {
      if (this.data.type != 'coi') {
        this.chartData.forEach((element: { type: string; }) => {
          element.type = type;
        });
      }
      if (!this.isSunBurst) {
        this.filterData();
      }
      this.isVisble = false;
      this.change = true;
    } else if (type == 'circular') {
      this.createCircularBarChart();
    } else if (this.chartType == 'pie') {
      this.pieSelectedPeriod = undefined;
      this.previousSelectedFilter = cloneDeep(this.filterKeys);
      const filterWithoutTimePeriod = this.filterKeys.filter((x: { path: string; }) => x.path != chartConstants.TIME_PERIOD);
      if (filterWithoutTimePeriod.every((x: { path: string; value: string | any[]; }) => x.value?.length == 1)) {
        this.pieFilterIndex = this.data.filterPanel?.properties[0].path == chartConstants.TIME_PERIOD ? 1 : 0;
        this.filterKeys[this.pieFilterIndex].value = this.data.filterPanel?.properties[this.pieFilterIndex].options;
      }
      this.createPieChartData();
    }
    this.getRangeSelector(false);
    this.previousChartType = type;
  }


  createCircularBarChart() {
    this.initialFilterData = this._filterService.filterIndicatorDetail(this.seriesMeta, this.selectedPeriod, this.filterKeys);
    this.tableData = this.setTableData();
    this.circularChartData = [];
    this.isYearly = true;
    if (this.data.indicatorVisualizations.visualizationsMeta?.[0]?.timeUnit?.includes(chartConstants.MONTHLY)) {
      this.isYearly = false;
    }
    this.chartCatogory = [];
    if (this.initialFilterData?.length > 0) {
      const seriesData: any = {
        name: '',
        type: 'column',
        data: [],
        color: ''
      };
      this.initialFilterData.forEach((element: any[], index: number) => {
        seriesData.data = [];
        seriesData.name = '';
        seriesData.name = this.customLetter.transform(this.filterKeys.find((x: { checkbox: boolean; }) => x.checkbox)?.value[index] ? this.filterKeys.find((x: { checkbox: boolean; }) => x.checkbox)?.value[index] : (this.data.indicatorVisualizations.visualizationsMeta?.[0].componentTitle ? this.data.indicatorVisualizations.visualizationsMeta?.[0].componentTitle : this.data.component_title));
        if (element?.length > 0) {
          element.forEach(series => {
            this.chartCatogory.push(this.isYearly ? series.YEAR : series.OBS_DT);
            seriesData.data.push(series.VALUE);
            seriesData.color = !this.isDashboardCard ? this.getColor(index) : this._dashboardService.getColors(this.cntType, this.id, index);
            if (this.isDashboardCard) {
              seriesData.spacing = this._dashboardService.gettingSpaceValues(this.cntType, this.id),
              seriesData.xAxisPositions = this._dashboardService.getXaxisPositions(this.cntType, this.id),
              seriesData.legendPositions = this._dashboardService.getLegendPositions(this.cntType, this.id);
              seriesData.isDatalabel = this._dashboardService.getChartSettings(this.cntType, this.id, chartConstants.DATALABEL);
              seriesData.isPrecise = this._dashboardService.getChartSettings(this.cntType, this.id, chartConstants.PRESICE_VALUE);
            }
          });
          this.circularChartData.push(cloneDeep(seriesData));
          if (this.isDashboardCard) {
            this._dashboardService.setSeriesLength(this.cntType, this.id, this.circularChartData);
          }
        }
      });
      this.isVisble = false;
      this._cdr.detectChanges();
      this.change = true;
    }
  }


  disableZoomout(event: any) {
    this.isZoomoutDisabled = event;
    this._cdr.detectChanges();
  }

  addInsight(value: string) {
    const data = {
      nodeId: this.id,
      nodeTitle: this.name,
      nodeLink: this.chartData.NODE_LINK,
      insight: value
    };
    this.subs.add(
      this.insightService.addInsights(data).subscribe((res: any) => {
        this.store.dispatch(getInsights({ nodeId: this.data.id }));
      })
    );
  }

  updateInsight(event: { item: any, newVal: string }) {
    const data = {
      nodeId: this.id,
      nodeTitle: this.name,
      nodeLink: this.chartData.NODE_LINK,
      insight: event.newVal
    };
    this.subs.add(
      this.insightService.editInsights(data, event.item.ID).subscribe((res: any) => {
        if (res.status === 'success') {
          this._toasterService.success('Insight Updated successfully');
          this.store.dispatch(getInsights({ nodeId: this.data.id }));
        } else {
          this._toasterService.error('Failed to update insight');
        }
        this._cdr.detectChanges();
      })
    );
  }

  deleteInsight(insightId: string) {
    this.subs.add(
      this.insightService.deleteInsights(insightId).subscribe((res: any) => {
        if (res.status === 'success') {
          this._toasterService.success('Insight deleted successfully');
          this.store.dispatch(getInsights({ nodeId: this.data.id }));
        } else {
          this._toasterService.error('Failed to delete insight');
        }
        this._cdr.detectChanges();
      })
    );
  }

  isTncChecked(event: boolean) {
    this.tncState = event;
  }

  showTnC() {
    if (this.isTncModalOpen) {
      return;
    }
    this.tncModal.createElement();
    this.isTncModalOpen = true;
  }

  termsResponse(response: boolean) {
    if (response) {
      this.subs.add(this._commonApiService.setDownloadTermsStatus(this.id).subscribe((res: any) => {
        if (res) {
          this.tncState = true;
        }
      }));
    }
    // else {
    //   this.tncState = false;
    // }
    if (this.isTncModalOpen) {
      this.tncModal.removeModal();
      this.isTncModalOpen = false;
    }
  }

  setTableData() {
    let dynamicObject: any = {};
    let indexData: any = [];
    this.tableData = [];
    const timeUnits = this.data?.indicatorVisualizations?.visualizationsMeta?.[0]?.timeUnit;
    const showQuarterly = this.data.indicatorVisualizations.visualizationsMeta?.[0]?.showQuarterlyIntervals;
    this.timeUnit = timeUnits?.includes('Monthly') ? 'Monthly' : ((timeUnits?.includes('Quarterly') || showQuarterly) ? 'Quarterly' : 'Yearly');
    if (this.initialFilterData?.length > 0) {
      this.initialFilterData.forEach((element: any) => {
        dynamicObject = {};
        indexData = [];
        element.forEach((value: Record<string, any>) => {
          if (this.data.tableFields?.length > 0) {
            this.data.tableFields.forEach((cell: { label: any; path: any; }) => {
              // if (value[cell.path]) {
              //   dynamicObject[cell.label] = value[cell.path];
              // }
              if (!this.timeUnit) {
                this.timeUnit = 'Yearly';
              }
              if (value[cell.path] !== undefined) {
                let key = cell.label;
                if (cell.label === this._translateService.instant('DATE')) {
                  key = this.timeUnit === 'Yearly' ? this._translateService.instant('Year') : (this.timeUnit === 'Quarterly' ? this._translateService.instant('Quarter') : 'Date');
                }
                dynamicObject[key] = cell.path === 'OBS_DT' && (this.timeUnit === 'Yearly' || this.timeUnit === 'y') ? value['YEAR'] : (cell.path === 'OBS_DT' && this.timeUnit === 'Quarterly' ? this._filterService.convertDateToQuarter(value[cell.path], 'table') : value[cell.path]);
              }
            });
          }
          indexData.push(cloneDeep(dynamicObject));
        });
        this.tableData.push(indexData);
      });
    }
    return this.tableData;
  }

  setForcastTableData() {
    let dynamicObject: any = {};
    let indexData: any = [];
    this.tableData = [];
    if (this.initialFilterData?.length > 0) {
      this.initialFilterData.forEach((element: any) => {
        dynamicObject = {};
        indexData = [];
        element.data?.forEach((value: Record<string, any>) => {
          if (this.data.tableFields?.length > 0) {
            this.data.tableFields.forEach((cell: { label: any; path: any; }) => {
              if (value[cell.path]) {
                dynamicObject[cell.label] = value[cell.path];
              }
            });
          }
          if (this.data.type == 'coi' && chartConstants.POPULATION_DOMAIN.includes(this.data.domain)) {
            dynamicObject['Citizenship'] = element.label;
          }
          indexData.push(cloneDeep(dynamicObject));
        });
        this.tableData.push(indexData);
      });
    }
    return this.tableData;
  }

  changeDataLabel(event: any) {
    if (event) {
      this.isDatalabel = event?.target?.checked;
    }
    this.chartComponent?.toggleDataLabel(this.isDatalabel);
  }

  changeTooltip(event: any) {
    if (event) {
      this.isToolTip = event?.target?.checked;
    }
    this.chartComponent?.toggleToolTip(this.isToolTip);
  }

  getFormat(value: string) {
    return value?.split('_')[1];
  }

  changePreciseLabel(event: any) {
    if (event) {
      this.isPreciseValue = event?.target?.checked;
    }
    this.chartComponent?.showFullValue(this.isPreciseValue);
  }

  downloadMetaData() {
    const metaData: any = {};
    if (this.data.metaData?.length) {
      this.data.metaData.forEach((element: { label: string | number; value: any; }) => {
        metaData[element.label] = element.value;
      });
    }
    this.downloadService.downloadSingleObjectXl(metaData, 'Meta data & Methodology');
  }

  selectedCompare(event: any) {
    if (this.compareSelected.find((x: { country_id: any; }) => x.country_id == event.country_id)) {
      this.compareSelected.splice(this.compareSelected.findIndex((x: { country_id: any; }) => x.country_id == event.country_id), 1);
    } else {
      this.compareSelected.push(event);
    }
    this.addOrRemoveCompareData();
  }

  addOrRemoveCompareData() {
    const selectedData: any = [];
    if (this.compareSelected?.length > 0) {
      this.compareSelected.forEach((element: { country_id: string; id: string; }) => {
        selectedData.push(compareSeries.find(x => x.id.toLowerCase() == element.country_id.toLowerCase()));
      });
    }
    selectedData.push(this.seriesMeta);
    this.initialFilterData = this._filterService.filterCompareDetail(selectedData, this.selectedPeriod, this.filterKeys);
    this.createCompareChart();
    this.isVisble = false;
    this.change = true;
  }

  createCompareChart() {
    this.chartData = [];
    this.xAxisLabelType = chartConstants.xAxisDateTime;
    this.initialFilterData.forEach((series: any, index: number) => {
      const newChart = series?.data.map((element: Record<string, any>) => {
        const splitDate = element['OBS_DT'].split('-');
        return [(Date.UTC(splitDate[0], splitDate[1] - 1, splitDate[2])), element['VALUE']];
      });
      const seriesData = {
        color: !this.isDashboardCard ? this.getColor(index) : this._dashboardService.getColors(this.cntType, this.id, index),
        data: newChart,
        name: series.label,
        type: this.chartType,
        marker: {
          enabled: true
        }
      };
      if (seriesData.data?.length > 0) {
        this.chartData.push(seriesData);
      }
      series.data.forEach((element: Record<string, any>) => {
        this.foracstTooltip.push({
          MonthLabel: element['CHANGE_QQ'],
          YearLabel: element['CHANGE_YY']
        });
      });
    });
  }

  setReset(event: any) {
    this.compareSelected = [];
    this.getInsightData();
  }

  getFormatedDate(publication_date: any) {
    try {
      return publication_date ? this.datePipe.transform(new Date(publication_date), 'dd/MM/yyyy') : publication_date;
    } catch (error) {
      return publication_date;
    }
  }


  createPieChartData() {
    this.initialFilterData = [];
    this.foracstTooltip = [];
    if (this.filterKeys?.length > 0) {
      if (this.filterKeys.some((z: { value: string | any[]; }) => z.value.length > 1)) {
        this.filterKeys.map((x: any) => {
          x.checkbox = x.value.length > 1 ? true : false;
        });
      }
      if (!this.filterKeys.some((z: { value: string | any[]; }) => z.value.length > 1)) {
        this.filterKeys.forEach((element: any, index: number) => {
          element.checkbox = index == 0 ? true : false;
        });
      }
    }

    this.initialFilterData = this._filterService.filterIndicatorDetail(this.seriesMeta, this.selectedPeriod, this.filterKeys);
    this.tableData = this.setTableData();
    this.chartData = [];
    this.isYearly = true;
    if (this.data.indicatorVisualizations.visualizationsMeta?.[0]?.timeUnit?.includes(chartConstants.MONTHLY)) {
      this.isYearly = false;
    }
    if (this.data.indicatorVisualizations.visualizationsMeta?.[0]?.timeUnit?.includes('Quarterly') && this.selectedPeriod.id != 'All' && !this.data.indicatorVisualizations.visualizationsMeta?.[0]?.timeUnit?.includes('Monthly')) {
      this.isYearly = false;
    }

    this.customTimePeriodOptions = [];
    if (this.initialFilterData[0].length > 0 && this.customTimePeriodOptions?.length <= 0) {
      this.initialFilterData[0].forEach((element: { YEAR: any; OBS_DT: any; }) => {
        this.customTimePeriodOptions.push(this.isYearly ? element.YEAR : element.OBS_DT);
      });
      if (!this.pieSelectedPeriod || this.customTimePeriodOptions.find((x: any) => x != this.pieSelectedPeriod)) {
        this.enableDateFilter = false;
        this.pieSelectedPeriod = this.customTimePeriodOptions[0];
        this.customTimePeriodOptions = cloneDeep(this.customTimePeriodOptions);
        this.change = true;
      }
    }
    this.createPieSeries();
  }

  createPieSeries() {
    this.pieChartSeriesData = [];
    const chartSeries: PieChartSeries = {
      type: 'pie',
      data: []
    };
    this.pieChartSeriesData.type = 'pie';
    this.initialFilterData.forEach((val: any, index: number) => {
      const data: any = {
        name: this.filterKeys.find((x: { checkbox: any; }) => x.checkbox) ? this.filterKeys.find((x: { checkbox: any; }) => x.checkbox).value[index] : this.name,
        y: val.find((x: any) => x[this.isYearly ? 'YEAR' : 'OBS_DT'] == this.pieSelectedPeriod)?.VALUE,
        color: !this.isDashboardCard ? this.getColor(index) : this._dashboardService.getColors(this.cntType, this.id, index)
      };
      if (this.isDashboardCard) {
        data.spacing = this._dashboardService.gettingSpaceValues(this.cntType, this.id);
        data.xAxisPositions = this._dashboardService.getXaxisPositions(this.cntType, this.id);
        data.legendPositions = this._dashboardService.getLegendPositions(this.cntType, this.id);
        data.isDatalabel = this._dashboardService.getChartSettings(this.cntType, this.id, chartConstants.DATALABEL);
        data.isPrecise = this._dashboardService.getChartSettings(this.cntType, this.id, chartConstants.PRESICE_VALUE);
      }
      const isNotContained = !totalValues.some(keyword => data.name.includes(keyword));
      if (isNotContained) {
        chartSeries.data.push(data);
      }

    });
    this.pieChartSeriesData.push(chartSeries);
    if (this.isDashboardCard) {
      this._dashboardService.setSeriesLength(this.cntType, this.id, this.pieChartSeriesData);
    }
    this.isVisble = false;
    this.change = true;
  }

  changePeriod(event: any) {
    this.pieSelectedPeriod = event;
    this.createPieChartData();
  }

  createSunburstSeries() {
    this.tabData = [
      {
        iconClass: 'ifp-icon-radial-bar',
        name: 'sunburst',
        machineName: 'All'
      },
      {
        iconClass: 'ifp-icon-table',
        name: 'table',
        machineName: 'All'
      }
    ];
    this.seriesMeta = this.data.indicatorVisualizations.visualizationsMeta[0].seriesMeta;
    this.sunburstSeriesData = [];
    this.seriesMeta.forEach((element: { id: any; parentId: any; label: any; data: { CONTRIBUTION: any; CHANGE: any; VALUE: any; }[]; color: any; }, index: number) => {
      const series: any = {
        id: element?.id,
        parent: element?.parentId,
        name: element?.label,
        value: Math.abs(element?.data[0]?.VALUE),
        color: !this.isDashboardCard ? element?.color : this._dashboardService.getColors(this.cntType, this.id, index),
        change: element?.data[0]?.CHANGE,
        contribution: element?.data[0]?.CONTRIBUTION,
        ogValue: element?.data[0]?.VALUE
      };
      if (this.isDashboardCard) {
        series.spacing = this._dashboardService.gettingSpaceValues(this.cntType, this.id);
        series.xAxisPositions = this._dashboardService.getXaxisPositions(this.cntType, this.id);
        series.legendPositions = this._dashboardService.getLegendPositions(this.cntType, this.id);
        series.isDatalabel = this._dashboardService.getChartSettings(this.cntType, this.id, chartConstants.DATALABEL);
        series.isPrecise = this._dashboardService.getChartSettings(this.cntType, this.id, chartConstants.PRESICE_VALUE);
      }
      if (series?.value) {
        this.sunburstSeriesData.push(series);
      }
    });
    if (this.isDashboardCard) {
      this._dashboardService.setSeriesLength(this.cntType, this.id, this.sunburstSeriesData);
    }
    if (this.sunburstSeriesData?.length) {
      this.tableData.push(this.sunburstSeriesData);
    }
  }

  checkDefaultFilter() {
    // let isDefault = true;
    this.autoSwitch = false;
    if (this.filterKeys?.length > 0) {
      this.filterKeys.forEach((element: any, index: number) => {
        if (element.value?.length > 1 || element.value.toString() != this.data?.filterPanel?.properties[index].default) {
          // isDefault = false;
          this.autoSwitch = true;
        }
      });
    }
    // this.selectedPeriod = {
    //   id: !isDefault ? 'All' : 'Latest-Readings',
    //   isSelected: !isDefault ? false : true,
    //   label: !isDefault ? 'ALL' : 'Recent',
    //   unit: !isDefault ? null : chartConstants.RECENT_LABEL,
    //   value: ''
    // };

  }


  downloadCustomXl() {
    this.filterModal.createElement();
  }

  closeFilterModel(_event: any) {
    this.filterModal.removeModal();
  }

  // Related Products dropdown methods
  onRelatedProductsSearch() {
    if (!this.relatedProductsSearchTerm.trim()) {
      this.filteredRelatedProducts = [...this.relatedSV];
    } else {
      const searchTerm = this.relatedProductsSearchTerm.toLowerCase();
      this.filteredRelatedProducts = this.relatedSV.filter(product => {
        const title = product.tittle || product.title || product.name || product.label || '';
        const titleAr = product.tittle_ar || product.title_ar || '';
        return title.toLowerCase().includes(searchTerm) ||
          titleAr.toLowerCase().includes(searchTerm);
      });
    }
  }

  selectRelatedProduct(product: { id: string | number; tittle?: string; title?: string; name?: string; label?: string; tittle_ar?: string; title_ar?: string; }) {
    const productTitle = product.tittle || product.title || product.name || product.label || 'Selected Product';
    this.relatedProductsSearchTerm = productTitle;
    this.relatedProductDropdownSelected.emit(String(product.id));
  }



  ngOnDestroy() {
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
    this.subs.unsubscribe();
  }
}
