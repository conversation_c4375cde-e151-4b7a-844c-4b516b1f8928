import { <PERSON><PERSON><PERSON><PERSON><PERSON>ni<PERSON>, ChangeDetector<PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, WritableSignal, signal } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { IfpInfoComponent } from '../../../../scad-insights/ifp-widgets/ifp-molecules/ifp-heading-with-info/ifp-info.component';
import { IfpButtonComponent } from '../../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpModalComponent } from '../../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpPrepSaveModalComponent } from '../../../organism/ifp-prep-save-modal/ifp-prep-save-modal.component';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { Router } from '@angular/router';
import { loadCreateNodeClear, loadCreateNodeDestination, loadNode, loadRemoveConnectionUploadUpdateName } from '../store/node-store/node-store.actions';
import { Store } from '@ngrx/store';
import { IfpPrepService } from '../ifp-prep-service';
import { SubSink } from 'subsink';
import { workFlowState } from '../constants/ifp-state.contants';
import { IfpPrepPopupComponent } from '../../../organism/ifp-prep-popup/ifp-prep-popup.component';
import { CardData } from 'src/app/shared/molecule/ifp-tool-card/ifp-tool-card.component';
import { environment } from 'src/environments/environment';
import { DownLoadService } from 'src/app/scad-insights/core/services/download-service/download.service';
import { prepsApiEndpoints } from '../constants/if-preps.constants';
import { NgClass } from '@angular/common';
import { ScUploadModelComponent } from '../../../../dashboard-builder/molecule/sc-upload-model/sc-upload-model.component';
import { IfpSpinnerComponent } from '../../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { slaService } from 'src/app/scad-insights/core/services/sla/sla.service';


@Component({
    selector: 'ifp-data-prep-save',
    templateUrl: './ifp-data-prep-save.component.html',
    styleUrl: './ifp-data-prep-save.component.scss',
    imports: [TranslateModule, IfpInfoComponent, IfpButtonComponent, IfpModalComponent, IfpPrepSaveModalComponent, IfpPrepPopupComponent, NgClass, ScUploadModelComponent, IfpSpinnerComponent]
})
export class IfpDataPrepSaveComponent implements OnDestroy, AfterViewInit {
  @ViewChild('modalSucess') modalSucess!: IfpModalComponent;
  @ViewChild('saveDataModal') saveDataModal!: IfpModalComponent;
  @ViewChild('dashboardModel') dashboardModel!: IfpModalComponent;



  public buttonClass = buttonClass;
  public currentToolList = [
    {
      key: 'advanceAnalytics',
      title: 'To Advance Analytics Tool',
      description: 'Effortlessly generate powerful machine learning models with advanced automation and optimization',
      icon: 'ifp-icon-advanced-analytics',
      tagColor: ifpColors.purple,
      disable: false,
      route: 'analytics/auto-ml/run'
    },
    {
      key: 'dataExploration',
      title: 'To Data Exploration Tool',
      description: 'Investigate relationships between variables through correlation analysis, gaining deeper insights into your data',
      icon: 'ifp-icon-desktop-chart',
      tagColor: ifpColors.yellow,
      disable: false,
      route: 'analytics/exploratory/data-analysis'
    },

    {
      key: 'dashboard',
      title: 'To Dashboard Builder',
      description: 'Effortlessly create personalized dashboards by integrating saved indicators from My Bookmarks or the platform.',
      icon: 'ifp-icon-builder',
      tagColor: ifpColors.lightGreen,
      disable: false,
      route: 'store/dashboard-builder-basic'
    }
  ];

  public toolsList: {
    key: string;
    title: string;
    description: string;
    icon: string;
    tagColor: string;
    disable: boolean;
    route: string;
  }[] = [];

  public download = false;
  public loaderProcess = signal(false);
  public subs = new SubSink();
  public isLoder: WritableSignal<boolean> = signal(false);
  public nameModal = false;
  public tabData = [
    {
      name: 'Instructions'
    }
  ];

  public toolForward = false;
  public currentTool!:  {key : string; route: string};
  public name = '';

  constructor(private _toaster: ToasterService, private _router: Router, private _store: Store, public _prepService: IfpPrepService, private _downlaod: DownLoadService, private _translate: TranslateService,
    private _cdr: ChangeDetectorRef, public _preService: IfpPrepService, private _sla: slaService
  ) {
    if (!this._prepService.uploadedFileResponse()) {
      this._router.navigate(['analytics/data-preparation/upload-data']);
    }
    this.toolsList = [];
    if (this._sla.permission().autoML) {
      this.toolsList.push(this.currentToolList[0]);
    }
    if (this._sla.permission().dataExploration) {
      this.toolsList.push(this.currentToolList[1]);
    }
    if (this._sla.permission().dashBoardBuilder) {
      this.toolsList.push(this.currentToolList[2]);
    }
    this._prepService.showProcessBtn.set(false);
  }

  downloadData() {
    // Download code
  }

  ngAfterViewInit(): void {
    if (this._prepService.uploadedFileResponse()) {
      // this.modalSucess.createElement();
      this._cdr.detectChanges();
    }
  }

  openSaveModal(download: boolean = false) {
    this.download = download;
    this.nameModal = true;
    this.saveDataModal.createElement();

  }

  saveToLibrary(event: { name: string, type: string }) {
    this.name = event.name;
    if (this.toolForward) {
      this.openRoute(this.currentTool);
    } else if (this.download) {
      this.processAndDownload(event.name);
    } else {
      this.process(event.name);
    }
    this.saveDataModal.removeModal();
    this.nameModal = false;
  }

  process(name: string, download?: boolean) {
    this._prepService.processActive.set(true);
    this._prepService.disableSidebar.set(true);
    this._store.dispatch(loadNode[loadCreateNodeDestination]({
      config: {
        type: 'csv',
        name: name,
        is_download: this.download
      }
    }));
    this.loaderProcess.set(true);
    const subs = this._prepService.processStatus.subscribe(data => {
      if (data?.workflow_status === workFlowState.completed) {
        this.loaderProcess.set(false);
        if (download) {
          this.downloadFile('csv', name);
        } else {
          this._prepService.processActive.set(false);
          this._store.dispatch(loadNode[loadRemoveConnectionUploadUpdateName]({ objectId: this._prepService.currentNodeId() ?? '' }));
          this._toaster.success(this._translate.instant('Saved to the library!'));
        }
        subs.unsubscribe();
      } else if (data?.workflow_status === workFlowState.error) {
        this._prepService.disableSidebar.set(false);
        this.loaderProcess.set(false);
        this._prepService.processActive.set(false);
        this._store.dispatch(loadNode[loadRemoveConnectionUploadUpdateName]({ objectId: this._prepService.currentNodeIdBeforeWorkFlow() ?? '' }));
        subs.unsubscribe();
      }
    });
  }

  checkRemoveStatus() {
    const subs = this._prepService.processStatus.subscribe(data => {
      if (data?.workflow_status === workFlowState.completed) {
        this._prepService.disableSidebar.set(false);
        subs.unsubscribe();
      }
    });
  }

  downloadFile(file: string, name: string = '') {
    // this._toaster.success(this._translate.instant('Download started!'));
    const subscribe = this._prepService.getDownloadRequest(`${environment.prepbaseUrl}${prepsApiEndpoints.datasetDownlaod}${this._prepService.currentNodeId()}/download/${file}/`).subscribe({
      next: data => {
        const matches = (data.headers.get('Content-Disposition'))?.match(/"(.*?)"/);
        // const nameValue = matches ? matches[1] : '.xlsx';
        this._downlaod.downloadFiles(data.body, name);
        this._toaster.success(`${name} ${this._translate.instant('Download completed successfully!')}`);
        this._prepService.processActive.set(false);
        this._store.dispatch(loadNode[loadRemoveConnectionUploadUpdateName]({ objectId: this._prepService.currentNodeId() ?? '' }));
        this._prepService.disableSidebar.set(false);
        subscribe.unsubscribe();
      },
      error: err => {
        const error = err?.error;
        this.errorHandler(error);
        this._prepService.processActive.set(true);
        this._store.dispatch(loadNode[loadRemoveConnectionUploadUpdateName]({ objectId: this._prepService.currentNodeId() ?? '' }));
        this._toaster.error(`${this._translate.instant('Download failed!')}`);
      }
    });
  }

  errorHandler(error: any) {
    if (typeof error === 'string') {
      if (error !== '') {
        this._toaster.error(error);
      }
    } else {
      for (const key in error) {
        if (Object.prototype.hasOwnProperty.call(error, key)) {
          const element = error[key];
          let data = '';
          if (typeof element === 'string') {
            data = element;
          } else {
            element.forEach((elementValue: string) => {
              data = `${data} ${elementValue}`;
            });
          }
          if (data !== '') {
            this._toaster.error(data);
          }
        }
      }
    }
  }

  processAndDownload(name: string) {
    this.process(name, true);
  }

  closeSucessModal() {
    this.modalSucess.removeModal();
  }

  buttonClick(event: CardData) {
    this.modalSucess?.removeModal();
    if (event.key === 'continuesPrep') {
      this._router.navigate(['analytics/data-preparation/upload-data']);
    }
  }

  closeModal(event: string) {
    if (this.toolForward && event === 'cancel') {
      this.openRoute(this.currentTool, true);
    }
    this.saveDataModal.removeModal();
    this.nameModal = false;
  }

  goTOTool(tool: {key : string; route: string}) {
    this.currentTool = tool;
    this.nameModal = true;
    this.download = false;
    this.toolForward = true;
    this.saveDataModal?.createElement();
  }

  openRoute(tool: {key : string; route: string}, save: boolean = false) {
    // added null check because of blockerx

    const columnKeyength = Object.keys(this._prepService.previewTableData?.data?.[0] ?? {});
    if (tool.key == 'dashboard' && (this._preService.previewTableData?.data?.length > 200 || columnKeyength.length > 25) ) {
      this.dashboardModel.createElement();
      return;
    }
    if (tool.route && tool.key == 'dashboard' && save) {
      const key = tool.key == 'dashboard' ? 'prepId' : 'id';
      const queryParams = { [key]: this._preService.currentNodeId() };
      this._router.navigate([tool.route], { queryParams: queryParams });
    } else {
      this.openTools(tool, save, this._preService.currentNodeId());
    }

  }

  async openTools(tool: {key : string; route: string}, save: boolean = false, currentNodeId: string| null) {
    const dataSetId = await this.getDataSet();
    if (dataSetId && dataSetId != null) {
      this._router.navigate([tool.route], { queryParams: { id: dataSetId } });
      return;
    }
    this._prepService.processActive.set(true);
    this._prepService.disableSidebar.set(true);
    this.isLoder.set(true);
    this._store.dispatch(loadNode[loadCreateNodeDestination]({
      config: {
        type: 'csv',
        name: this.name && this.name !== '' ? this.name :`Untitled${Date.now()}`,
        is_download: save
      }
    }));
    this.loaderProcess.set(true);
    const subsValue = this._prepService.processStatus.subscribe({
      next: async data => {
        if (data?.workflow_status === workFlowState.completed) {
          subsValue?.unsubscribe();
          this.isLoder.set(false);
          const dataId = await this.getDataSet();
          this._preService.clearAllValues();
          this._store.dispatch(loadNode[loadCreateNodeClear]({}));
          if (dataId && dataId != null) {
            if (tool.route && tool.key == 'dashboard') {
              const key = tool.key == 'dashboard' ? 'prepId' : 'id';
              const queryParams = { [key]: currentNodeId };
              this._router.navigate([tool.route], { queryParams: queryParams });
            } else {
              this._router.navigate([tool.route], { queryParams: { id: dataId, basic: 'y' } });
            }

            this.nameModal = false;

          }
        } else   if (data?.workflow_status === workFlowState.error) {
          this.isLoder.set(false);
          this.download = false;
          this.loaderProcess.set(false);
          this.nameModal = false;
          this._store.dispatch(loadNode[loadRemoveConnectionUploadUpdateName]({ objectId: this._prepService.currentNodeIdBeforeWorkFlow()  ?? '' }));
        }
      },
      error: _error => {
        this._prepService.disableSidebar.set(false);
        this.loaderProcess.set(false);
        this.isLoder.set(false);
        this._prepService.processActive.set(false);
        this._store.dispatch(loadNode[loadRemoveConnectionUploadUpdateName]({ objectId: this._prepService.currentNodeId() ?? '' }));
      }
    });
  }

  getDataSet() {
    return new Promise(resolve => {
      this._prepService.getDataSet(`${prepsApiEndpoints.getDataSet}${this._prepService.currentNodeId()}/getdataset/`).subscribe({
        next: next => {
          resolve(next.dataset_id);
        },
        error: _error => {
          resolve(null);
        }
      });
    });
  }

  closeDashBordModel(_event: boolean) {
    this.dashboardModel.removeModal();
  }

  ngOnDestroy(): void {
    this.modalSucess?.removeModal();
    this.saveDataModal?.removeModal();
    this.subs.unsubscribe();
    this._prepService.showProcessBtn.set(true);
  }
}
