<div class="ifp-byn-db">
  <div class="ifp-byn-db__tab-wrapper">
    <div class="ifp-container ifp-byn-db__tab-wrapper-inner">
      <ifp-pills-tab [tabData]="mainTabs" [selectedTab]="selectedMainTab" (selectTabItem)="onSelectMainTab($event)" class="ifp-byn-db__tab ifp-byn-db__tab--main"></ifp-pills-tab>
    </div>
  </div>

  <div class="ifp-container">
    <p class="ifp-byn-db__sub-head">{{"Here, you'll find KPIs highlighting user engagement and platform activity, offering insights to drive strategic decisions and enhance user experience" | translate}}</p>
    @if (selectedMainTab === 'userOverview') {
      <ifp-user-overview 
        (downloadUsersAndEntities)="downloadUsersAndEntitiesReport($event.downloadType, $event.data, $event.selectedTab)"
        [overViewList]="userCount().total" class="ifp-byn-db__user-overview"></ifp-user-overview>
    }

    <div class="ifp-byn-db__head-wrapper">
      @if (selectedMainTab === 'utilization') {
      <div class="ifp-byn-db__setting">
        <ifp-panel-dropdown [enableSelectAll]="true"  [multiSelect]="true"
          [enableSearch]="true" [isBoxType]="true" class="ifp-byn-db__dropdown" [options]="columListEntityDropdown"
          [changeCheckedDataValue]="true" (multiSelected)="multiSelection($event)" [key]="'value'"
          [label]="'Select the entity'" ></ifp-panel-dropdown>
      </div>

      <div class="ifp-byn-db__picker-wrapper">
        <div class="ifp-byn-db__date-wrapper">
          <p class="ifp-byn-db__date-title">{{'Select the date'}}</p>
          <div class="ifp-byn-db__picker-badge" (click)="picker.open()" (keyup)="picker.open()"
            [appIfpTooltip]="'Date  Selector'" [placement]="'topLeft'">
            <em class="ifp-icon ifp-icon-calender ifp-byn-db__calender"></em>
            <span>
              {{startDate.value | date : 'MMM d, y' }} - {{endDate.value | date : 'MMM d, y' }}
            </span>

            <mat-date-range-input [min]="minDate" [max]="maxDate" [rangePicker]="picker" class="ifp-byn-db__picker">
              <input matStartDate [formControl]="startDate" placeholder="Start date">
              <input matEndDate [formControl]="endDate" placeholder="End date">
            </mat-date-range-input>
          </div>
          <mat-date-range-picker #picker (closed)="dateChange()"></mat-date-range-picker>
        </div>

        <div class="ifp-byn-db__toggle-wrapper">
          <p class="ifp-byn-db__date-title">{{'Select the classification level' | translate}}</p>
          <app-ifp-tab [tabData]="platforms" [isSmall]="true" (selectedTabEvent)="onSelectPlatform($event.event)"
            [selectedTab]="selectedPlatform" [selectionType]="'name'" class="ifp-byn-db__switch-platform"></app-ifp-tab>
        </div>
      </div>
      }
    </div>
    @if (selectedMainTab === 'utilization') {
    <!-- Utilization dashboard start -->
    <div class="ifp-byn-db__overview">
      <div class="ifp-byn-db__active-inactive-wrapper">
        <div class="ifp-byn-db__total-card ifp-byn-db__overview-card ifp-byn-db__overview-card--66">

          <div class="ifp-byn-db__user-count-wrapper">
            <p class="ifp-byn-db__users-title">{{'Total Users' | translate}}</p>
            <ifp-total-user-donut [loader]="userCountLoader()" [userCount]="activeUsers" [heading]="'Total Users'"
              (tabSelect)="selectedUserType = $event" class="ifp-byn-db__total-user-donut" [showTabs]="false"></ifp-total-user-donut>
          </div>

          <div class="ifp-byn-db__user-monthly-split">
          <ifp-user-card-bar class="ifp-byn-db__total-item" [loader]="userCountLoader()"
            [tooltipLabel]="'Users'" [multiBarData]="activeUsers.monthly_users_data" [circle]="false" [name]="'Total Active Users'" (downloadData)="downloadUserStatus()"></ifp-user-card-bar>
          </div>
        </div>

        <div class="ifp-byn-db__overview-card ifp-byn-db__active-entities-wrapper">
          <ifp-user-card-bar [loader]="callTotalEntityLoader" [tooltipLabel]="'Entities'" [cardData]="totalEntities" [circle]="false" class="ifp-byn-db__active-entities" [name]="'Total Active Entities'" [chartName]="'areaChart'" (downloadData)="downloadEntitiesStatus()"
          [tickIntervell]="1"></ifp-user-card-bar>
        </div>
      </div>
      <div class="ifp-byn-db__top-wrapper">
        <div class="ifp-byn-db__top-usage-wrapper">
          <div class="ifp-byn-db__tab-outer-wrapper">
            <p class="ifp-byn-db__users-title">{{'Top 5 Performers' | translate}}</p>
            <div class="ifp-drop-download" [class.ifp-drop-download--disabled]="(selectedTopKey == 'users' && topUsersTableData.length===0) || (selectedTopKey != 'users' && topEntitiesTableData.length===0)">
              <app-ifp-dropdown
                [disableDropdown] = "(selectedTopKey == 'users' && topUsersTableData.length===0) || (selectedTopKey != 'users' && topEntitiesTableData.length===0)"
                (dropDownItemClicked)="downloadReport($event, selectedTopKey == 'users' ? topUsersTableData : topEntitiesTableData, getTopPerformersFileName())"
                [isInline]="true" [dropDownItems]="downloadTypes" [isDownload]="true"
                [key]="'label'"></app-ifp-dropdown>
            </div>
          </div>
          <!-- <ifp-top-usage-tabs [loader]="callTopEntitiesLoader" [loaderUser]="callTopUserUsageLoader" [totalUser]="topUsers"
            [totalEntity]="topEntities" class="ifp-byn-db__peak-items"></ifp-top-usage-tabs> -->

            <div class="ifp-byn-db__top-action-wrapper">
              <ifp-pills-tab [tabData]="tabData" (selectTabItem)="selectedKey($event)"
              class="ifp-byn-db__top-tab" [isLarge]="false"></ifp-pills-tab>

              <ifp-panel-dropdown   [multiSelect]="true"
              [enableSearch]="true" [isBoxType]="true" class="ifp-byn-db__entity-dropdown" [options]="selectedTopKey == 'users' ? topUserTableTitles :topEntityTableTitles "
              [changeCheckedDataValue]="true" (multiSelected)="selectTopTableColumn($event)" [key]="'value'"
              [label]="'Columns'"></ifp-panel-dropdown>
            </div>
            <ifp-data-table class="ifp-byn-db__user-table" [loader]="(selectedTopKey == 'users' && callTopUserUsageLoader) || (selectedTopKey == 'entity' && callTopEntitiesLoader)" [tableHead]="selectedTopKey == 'users' ? topUsersTableHeader : topEntitiesTableHeader" [tableData]="selectedTopKey == 'users' ? topUsersTableData : topEntitiesTableData"></ifp-data-table>
        </div>
        <div class="ifp-byn-db__peak-time-wrapper">
          <ifp-peak-time-usage [unit]="averageUsage.unit" [averageTime]="averageUsage.value"
            [loader]="callPeakValueUsageLoader" [barData]="peakValueChart"
            class="ifp-byn-db__peak-items ifp-byn-db__peak-items--flex"></ifp-peak-time-usage>
        </div>
      </div>

      <div class="ifp-byn-db__indicator-table-wrapper">
        <div class="ifp-byn-db__tab-outer-wrapper">
          <p class="ifp-byn-db__users-title">{{'Statistical Product Visits' | translate}}</p>
          <div class="ifp-drop-download" [class.ifp-drop-download--disabled]="(indicatorTableData.length===0)">
              <app-ifp-dropdown
              [disableDropdown] = "indicatorTableData.length===0"
              (dropDownItemClicked)="downloadReport($event, indicatorTableData, 'Statistical Product Visits For ' + (selectedIndicatorTab == 'Official' ? indicatorTabData[0].key : selectedIndicatorTab))"
              [isInline]="true" [dropDownItems]="downloadTypes" [isDownload]="true"
              [key]="'label'"></app-ifp-dropdown>
            </div>
        </div>
        <div class="ifp-byn-db__tab-outer-wrapper">
          <ifp-pills-tab [tabData]="indicatorTabData" (selectTabItem)="selectIndicatorType($event)"
            class="ifp-byn-db__top-tab" [isLarge]="false"></ifp-pills-tab>
          </div>
          <ifp-data-table [loader]="callDomainsLoader" class="ifp-byn-db__user-table"
          [tableHead]="indicatorTableHead"
          [tableData]="indicatorTableData" (colEvent)="productVisitsClicked($event)"></ifp-data-table>
      </div>


      <div class="ifp-byn-db__tools-table-wrapper">
        <div class="ifp-byn-db__tab-outer-wrapper">
          <p class="ifp-byn-db__users-title">{{'Tools Utilization' | translate}}</p>
          <div class="ifp-drop-download" [class.ifp-drop-download--disabled]="(toolsTableData.length===0)">
            <app-ifp-dropdown
            [disableDropdown] = "toolsTableData.length===0"
            (dropDownItemClicked)="downloadtoolsTableReport($event, (selectedIndicatorTab == 'Official' ? toolsTab[0].key : selectedIndicatorTab) + ' Tools Utilization')"
            [isInline]="true" [dropDownItems]="downloadTypes" [isDownload]="true"
            [key]="'label'"></app-ifp-dropdown>
          </div>
        </div>
        <div class="ifp-byn-db__tab-outer-wrapper">
          <ifp-pills-tab [tabData]="toolsTab" (selectTabItem)="selectIndicatorType($event)" class="ifp-byn-db__top-tab"
            [isLarge]="false" (selectTabItem)="selectToolsTab($event)"></ifp-pills-tab>
        </div>

        <ifp-data-table class="ifp-byn-db__tools-table" [tableHead]="toolsTableHead"
          [tableData]="toolsTableData" [loader]="loaderUser"></ifp-data-table>

        <div class="ifp-byn-db__user-pagination">
          <app-pagination [offset]="toolsOffset" [limit]="toolsPer_page" [size]="userListSize"
            (pageChange)="toolsPageChange($event)"
            (limitChange)="toolslimitChanged($event)"></app-pagination>
        </div>
      </div>



        <!-- <ifp-domain-cards [loader]="callDomainsomainsLoader" [products]="overallDomains" class="ifp-byn-db__indicator-progress-item " [name]="'Domain Insights - View'" [subNames]="'Overall Domain'" [multi]="false"></ifp-domain-cards> -->
        <!-- <ifp-domain-cards [loader]="callDomainsomainsLoader"  [domainDetails]="domain" class="ifp-byn-db__indicator-progress-item " [tab]="true" [subNames]="'Domain'" [subTitle]="'Product'"></ifp-domain-cards> -->

        <div class="ifp-byn-db__download-analysis">
          <div class="ifp-byn-db__download-inner">
            <ifp-download-insight-analysis
              (downloadIndicatorsReport)="downloadReport($event.downloadType, $event.data, 'Most Downloaded Indicators as ' + $event.selectedTab)"
              (getUsersDetails)="usersModalClicked({downloadType: $event.downloadType}, $event.indicatorName, 'Most Downloaders for ', userApi.mostIndicatorsDownloaders)"
              [loader]="downloadInsightsLoader()" [title]="'Most Downloaded Indicators'"
              [tabData]="downloadInsightsTabData" [nodeData]="downloadInsights"
              class="ifp-byn-db__download-card"></ifp-download-insight-analysis>
            <ifp-download-insight-analysis
              (downloadIndicatorsReport)="downloadReport($event.downloadType, $event.data, 'Most Subscribed Indicators')"
              (getUsersDetails)="usersModalClicked({nodeId: $event.nodeId}, $event.indicatorName, 'Most Subscribers for ', userApi.mostSubscribedUsers)"
              [loader]="notificationEnabledInsightsLoader()" [title]="'Most Subscribed Indicators'"
              [nodeData]="notificationEnabledInsights" class="ifp-byn-db__download-card"></ifp-download-insight-analysis>
          </div>
        </div>
    </div>
    <!-- Utilization dashboard end -->
    } @else {
    <!-- User overview start -->

    <!-- Entity Users Type -->
      <div class="ifp-byn-db__entity-user-type-wrapper">
        <div class="ifp-byn-db__tab-outer-wrapper">
          <p class="ifp-byn-db__users-title">{{'Entity Users Type'}}</p>
          
          <div class="ifp-drop-download" [class.ifp-drop-download--disabled]="(userDetailTableData.length===0)">
            <app-ifp-dropdown
            [disableDropdown] = "userDetailTableData.length===0"
            (dropDownItemClicked)="downloadReport($event, userDetailTableData, 'UserDetails')"
            [isInline]="true" [dropDownItems]="downloadTypes" [isDownload]="true"
            [key]="'label'"></app-ifp-dropdown>
          </div>
        </div>

        <div class="ifp-byn-db__users-type-wrapper">

            <ifp-panel-dropdown [enableSelectAll]="true"  [multiSelect]="true"
              [enableSearch]="true" [isBoxType]="true" class="ifp-byn-db__entity-dropdown" [options]="overviewColumListEntityDropdown"
              [changeCheckedDataValue]="true" (multiSelected)="entitiesMultiSelection($event)" [key]="'value'"
              [label]="'Select the entity'" ></ifp-panel-dropdown>
        

            <ifp-panel-dropdown   [multiSelect]="true"
            [enableSearch]="true" [isBoxType]="true" class="ifp-byn-db__entity-dropdown" [options]="userTableTitleData"
            [changeCheckedDataValue]="true" (multiSelected)="selectUserColumns($event)" [key]="'value'"
            [label]="'Columns'"></ifp-panel-dropdown>

            <ifp-search class="ifp-search-filter__tools-item ifp-byn-db__entity-dropdown" [isKeypress]="true" (searchEvent)="onSearch($event)"
              [onSearch]="search" [boxType]="boxTypeSearch" [placeholderText]="searchPlaceHolder"
              [ngClass]="{'ifp-search-filter__search' : !isSort}">
            </ifp-search>

            <ifp-pills-tab [tabData]="userStatusTab" [isLarge]="false" (selectTabItem)="onSelectUserStatusTab($event)" class="ifp-byn-db__user-tab"></ifp-pills-tab>
        </div>
        <!-- @if(userDesignationSummary() && userDesignationSummary().length > 0){ -->
          <ifp-user-desgination-summary [userDesignationSummary]="userDesignationSummary()"></ifp-user-desgination-summary>
        <!-- } -->
       
        <div class="ifp-byn-db__users-wrapper">
          <ifp-total-user-donut [loader]="userCountLoader()" [userCount]="userCount().entity_count" [heading]="'Total Users'" (tabSelect)="selectedUserType = $event" class="ifp-byn-db__user-overview-card"></ifp-total-user-donut>

          <ifp-data-table class="ifp-byn-db__user-table ifp-byn-db__user-table--80" [headerSettings]="headingSettings" [tableHead]="userTableHead" [tableData]="filteredUserDetailTableData" [loader]="userDetailLoader()" (sortEvent)="sortEvent($event)"></ifp-data-table>
        </div>
      </div>

      <!-- Entity Data Classification -->
      <div class="ifp-byn-db__data-classification-wrapper">
        <p class="ifp-byn-db__users-title">{{'Entity Data Classification'}}</p>
        <div class="ifp-byn-db__data-classification-overview">
          <div class="ifp-byn-db__data-classification-action-wrapper">

            <app-ifp-dropdown [showTitle]="true" [key]="'value'" class="ifp-byn-db__entity-dropdown"
            (dropDownItemClicked)="changeDataEntity($event)" [dropDownItems]="columListEntityDropdown" [searchEnable]="true"
            [title]="'Select the entity'"   [selectedValue]="userTypeEntity" [singleDefaultSelect]="false"></app-ifp-dropdown>

            @if (dataClassificationOverview() && dataClassificationOverview().classification.length) {
            <div class="ifp-byn-db__acc-level-sec">
              <p class="ifp-byn-db__input-title">{{'Select the classification level' | translate}}</p>
              <ul class="ifp-byn-db__level-tab">
                @for (classification of dataClassificationOverview().classification; track $index) {
                  <li class="ifp-byn-db__level-tab-item">
                    <input type="radio" name="classification" [id]="'edc_'+classification.key" [checked]="classification.value.toLowerCase() === (selectedEntityClassificationLevel.toLowerCase() | translate)" class="ifp-byn-db__level-radio" (change)="changeDataClassification(classification)">
                    <label [for]="'edc_'+classification.key" class="ifp-byn-db__level-label">{{classification.value}}</label>
                  </li>
                }
              </ul>
            </div>
            }

          </div>
          <!-- @if (dataClassificationOverview() && dataClassificationOverview().domain && dataClassificationOverview().domain.length) { -->
          <ifp-stats-db-navigation-menu #navigationMenu [classificationData]="dataClassificationOverview()" [loader]="dataClassificationOverviewLoader"
          (triggerItem)="changeDomainOrProduct($event)"></ifp-stats-db-navigation-menu>
          <!-- } -->
        </div>

      </div>

     <div class="ifp-byn-db__overview ifp-byn-db__overview--user">
      <!-- @if (userOverviewTableData && userOverviewTableData.length) { -->
      <div class="ifp-byn-db__users-overview-wrapper">
        <div class="ifp-byn-db__tab-outer-wrapper">
          <p class="ifp-byn-db__users-title">{{'Entity Users Overview'}}</p>
          
          <div class="ifp-drop-download" 
            [class.ifp-drop-download--disabled]="(selectedUserTypeItem.key === 'entity' && userOverviewEntityTableData.length===0) || (selectedUserTypeItem.key != 'entity' && userOverviewTableData.length===0)">
            <app-ifp-dropdown
            [disableDropdown] = "(selectedUserTypeItem.key === 'entity' && userOverviewEntityTableData.length===0) || (selectedUserTypeItem.key != 'entity' && userOverviewTableData.length===0)"
            (dropDownItemClicked)="downloadOverviewReport($event, 'UserOverview')"
            [isInline]="true" [dropDownItems]="downloadTypes" [isDownload]="true"
            [key]="'label'"></app-ifp-dropdown>
          </div>
        </div>
        <div class="ifp-byn-db__user-overview-table-wrapper">
          <div class="ifp-byn-db__users-type-wrapper">
            <ifp-panel-dropdown [multiSelect]="true"
              [enableSearch]="true" [isBoxType]="true" class="ifp-byn-db__entity-dropdown" [options]="selectedUserTypeItem.key === 'entity' ? entityTableColumns : entityTableUserListColumns"
              [changeCheckedDataValue]="true" (multiSelected)="selectEntityColumns($event)" [key]="'value'"
              [label]="'Columns'"></ifp-panel-dropdown>
              @if (selectedUserTypeItem.key === 'external') {
                <app-ifp-dropdown [showTitle]="true" [key]="'value'" class="ifp-byn-db__entity-dropdown"
                (dropDownItemClicked)="changeEntityUsersOverview($event)" [dropDownItems]="externalEntitiesOptions"
                [title]="'Select the entity'" [searchEnable]="true" [selectedValue]="userTypeEntity" [singleDefaultSelect]="false"></app-ifp-dropdown>
              }
            <ifp-pills-tab [tabData]="userOverviewTabData" class="ifp-byn-db__tab" [isLarge]="false" (selectTabItem)="userOverviewTabSelection($event)" class="ifp-byn-db__user-tab"></ifp-pills-tab>
          </div>
          <div class="ifp-byn-db__user-sub-table-wrapper">
            <p class="ifp-byn-db__users-sub-title">{{selectedUserTypeItem.value | translate}}</p>
            <ifp-data-table class="ifp-byn-db__user-overview-table" [tableHead]="selectedUserTypeItem.key === 'entity' ? userOverviewEntityTableHead : userOverviewTableHead" [loader]="userAndEntityListLoader"
              [tableData]="selectedUserTypeItem.key === 'entity' ? userOverviewEntityTableData : userOverviewTableData"></ifp-data-table>
            <div class="ifp-byn-db__user-pagination">
              <app-pagination [offset]="userOverviewOffset" [limit]="userOverviewPerPage" [size]="userOverviewTotalRow"
                (pageChange)="onUserOverviewPageChange($event)" (limitChange)="onUserOverviewlimitChanged($event)"></app-pagination>
            </div>
          </div>
        </div>
      </div>
    <!-- } -->
     </div>

     <!-- user group details start -->
     <ifp-usage-dashboard-group-list [loader]="userGroupLoader()"  (pageChangeEvent)="pageChangeUserGroup($event)" [offset]="userGroupListOffset" [size]="userGroupListSize" class="ifp-byn-db__group-list" (arrowClickEvent)="arrowClickEvent($event)" [table]="userListTableData" >
      <div class="ifp-user-data-list__header-actions">
        <div class="ifp-drop-download" [class.ifp-drop-download--disabled]="(userDetailTableData.length===0)">
          <app-ifp-dropdown
          [disableDropdown] = "userListTableData.length===0"
          (dropDownItemClicked)="downloadGroupListReport($event, 'UserList')"
          [isInline]="true" [dropDownItems]="downloadTypes" [isDownload]="true"
          [key]="'label'"></app-ifp-dropdown>
        </div>
      </div>
      <div class="ifp-byn-db__users-type-wrapper">
        <app-ifp-dropdown [showTitle]="true" [key]="'value'" class="ifp-byn-db__entity-dropdown"
        (dropDownItemClicked)="onSelectEntityUserGroup($event)" [dropDownItems]="columListEntityDropdown" [searchEnable]="true"
        [title]="'Select the entity'" [selectedValue]="userTypeEntity" [singleDefaultSelect]="false"></app-ifp-dropdown>

        <ifp-panel-dropdown [multiSelect]="true" [enableSearch]="true" [isBoxType]="true" class="ifp-byn-db__entity-dropdown" [options]="userListTableTitles" [changeCheckedDataValue]="true" (multiSelected)="selectUserListColumns($event)" [key]="'value'" [label]="'Columns'"></ifp-panel-dropdown>

    </div>
     </ifp-usage-dashboard-group-list>
     <!-- user group details start -->

    <!-- User overview end -->
    }
  </div>
</div>
<app-ifp-modal #modal [overlayType]="'transparent'">
  <ifp-usage-dashboard-userlist [subHeading]="'Assigned Group List'" [email]="userGroups().email" [heading]="userGroups().name ?? ''" [loader]="userGroupLoader()" [size]="geoSpecialUserSize" [userGroup]="userGroups().group ?? []" (closeEvent)="closeModal()"></ifp-usage-dashboard-userlist>
</app-ifp-modal>


<app-ifp-modal #usersDetailesModal [overlayType]="'transparent'" [modalClass]="'ifp-modal__visits-custom'">
   @if (isUsersDetailsModalOpen) {
   <app-users-visits 
      [isModal]="true"
      [userTableData]="usersTableData" 
      [userTableHead]="usersTableHead"
      [title]="modalTitle"
      (closeEvent)="closeUsersDetailesModal()">
   </app-users-visits>
   }
</app-ifp-modal>
