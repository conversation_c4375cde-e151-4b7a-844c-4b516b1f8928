<div class="ifp-whats-new-card ifp-whats-new-card--dxp"
     [ngClass]="{
       'ifp-whats-new-card--small': small(),
       'ifp-whats-new-card--expanded': !small()
     }" #card>
     @if (data()?.sourceProductSubscription?.hasSubscription === false) {
        <div class="ifp-whats-new-card__overlay"  >
            <p class="ifp-whats-new-card__overlay-desc">{{'You don’t have a subscription for this product.' | translate}}</p>
            <ifp-button [buttonClass]="buttonClass.primary+' '+ buttonClass.normalAplabetic" [label]="'View Product'" (ifpClick)="gotTOProduct(data()?.sourceProductSubscription?.productUrl ?? '')"></ifp-button>
        </div>
    }
    <div class="ifp-whats-new-card__header">
      <div class="ifp-whats-new-card__icon-wrapper">
        <em [class]="'ifp-whats-new-card__icon ifp-icon ' + data()?.icon"></em>
      </div>
      <div class="ifp-whats-new-card__buttons">
        <div class="ifp-whats-new-card__rect ifp-whats-new-card__rect--small {{analyticsClasses.collapsedCard}}"
          [ngClass]="{'ifp-whats-new-card__fill': small()}"
           (click)="resize(true)" id="card-small"></div>
        <div class="ifp-whats-new-card__rect ifp-whats-new-card__rect--large {{analyticsClasses.expandedCard}}"
          [ngClass]="{'ifp-whats-new-card__fill': !small()}"
           (click)="resize(false)" id="card-large"></div>
        <ifp-button class="ifp-whats-new-card__btn ifp-open-new" [buttonColor]="'black'"
          [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-link-curve'" [link]="false"
          (click)="openUrl('/dxp/admin-panel/'+(data()?.objectId ))" id="open-new"></ifp-button>
      </div>
    </div>
    <div class="ifp-whats-new-card__body" [ngClass]="{'ifp-whats-new-card__body--expanded': !small()}">
      <div class="ifp-whats-new-card__left">
        <div class="ifp-whats-new-card__value-range">

          <div class="ifp-whats-new-card__value" >
            <span class="ifp-whats-new-card__value-number">
            @if (data()?.overview?.value || +(data()?.overview?.value?? '0') === 0 ) {
              {{(data()?.overview?.value ?? 0) | shortNumber}}
            }
            </span>
            @if(data()?.overview?.unit){
            <span class="ifp-whats-new-card__unit">
              {{data()?.overview?.unit}}
            </span>
               }
          </div>


        </div>
        @if (data()?.title) {
           <div class="ifp-whats-new-card__name" [appIfpTooltip]="data()?.title ?? ''"
          [disableTooltip]="data()?.title ? (data()?.title?.length ?? 0) < textLimit : true" [delay]="3000">
          {{data()?.title && data()?.title?.length ? ((data()?.title?.charAt(0)?.toUpperCase() ?? '') + data()?.title?.slice(1)): '' | translate}}
        </div>
        }

        <!-- Component subtitle display -->
        <!-- <div class="ifp-whats-new-card__subtitle" *ngIf="subtitle" [appIfpTooltip]="subtitle"
          [disableTooltip]="subtitle.length < textLimit" [delay]="3000">
          {{subtitle | translate}}
        </div> -->
        @if (baseDate) {
        <div class="ifp-whats-new-card__txt-icon">
          <em class="ifp-icon ifp-icon-calender ifp-whats-new-card__calender" ></em>
          {{baseDate }}
          @if (isDxp()) {
            <ifp-sync-button [syncStatus]="syncStatus()" class="ifp-whats-new-card__sync"></ifp-sync-button>
          }
        </div>
        }

        <!-- Bubble Tags for Product and Entity -->
        @if (data()?.product?.displayName || data()?.createdBy?.entity?.name) {
        <div class="ifp-whats-new-card__bubble-tags">
          @if (data()?.product?.displayName) {
          <div class="ifp-whats-new-card__bubble-tag ifp-whats-new-card__bubble-tag--product"
               [appIfpTooltip]="data()?.product?.displayName!"
               [placement]="'topLeft'"
               [dynamic]="true"
               [fontSize]="14">
            <em class="ifp-icon ifp-icon-database-disk ifp-whats-new-card__bubble-icon"></em>
            <span class="ifp-whats-new-card__bubble-text">{{data()?.product?.displayName}}</span>
          </div>
          }
          @if (data()?.createdBy?.entity?.name) {
          <div class="ifp-whats-new-card__bubble-tag ifp-whats-new-card__bubble-tag--entity"
               [appIfpTooltip]="data()?.product?.organization?.name ?? ''"
               [placement]="'topLeft'"
               [dynamic]="true"
               [fontSize]="14">
            <em class="ifp-icon ifp-icon-connection ifp-whats-new-card__bubble-icon"></em>
            <span class="ifp-whats-new-card__bubble-text">{{data()?.product?.organization?.name ?? '-'}}</span>
          </div>
          }
        </div>
        }
      </div>
      @if (!small()) {
      <div class="ifp-whats-new-card__right">
         @if (data()?.title) {
         <div class="ifp-whats-new-card__name" [appIfpTooltip]="data()?.title ?? ''"
          [disableTooltip]="data()?.title ? (data()?.title?.length ?? 0) < textLimit : true" [delay]="3000">
          {{data()?.title && data()?.title?.length ? ((data()?.title?.charAt(0)?.toUpperCase() ?? '') + data()?.title?.slice(1)): '' | translate}}
        </div>
      }
        <!-- DXP Chart Display -->
        <div class="ifp-whats-new-card__chart-container" *ngIf="chart">
          @if( (!data() ||!data()?.series?.series?.length || data()?.series?.series?.length === 0)) {
           <div class="ifp-whats-new-card__no-chart">
            <p>{{'No chart data available' | translate}}</p>
          </div>

          }@else {

            <ifp-highcharts [donutCenterFunc]="centerValue()" [legends]="legends" [comparisonEnable]="false" [xAxis]="xAxis()" class="ifp-dxp-admin__chart"
            [chartType]="chartType[data()?.visualizationConfig?.chart_configuration?.default_chart_type ?? '' ]"
            [height]="150" [category]="
            data()?.series?.xAxis?.categories ?? []" [plotOptions]="plotOptions()"
             [yaxisLabel]="data()?.visualizationConfig?.chart_configuration?.y_axis?.label  + (data()?.visualizationConfig?.chart_configuration?.unit ? ' (' + data()?.visualizationConfig?.chart_configuration?.unit+ ')':'')"
            [data]="((chartType[data()?.visualizationConfig?.chart_configuration?.default_chart_type ?? '' ] === chartConst.pie ) ? [seriesDonutChart()]:data()?.series?.series) ?? []"
            [innerSize]="data()?.visualizationConfig?.chart_configuration?.default_chart_type === chartConst.pie? '0%': '80%'"
            [chartName]="
            (chartType[data()?.visualizationConfig?.chart_configuration?.default_chart_type ?? '' ] === chartConst.pie ) ? 'pieChart': 'lineChart'"
            ></ifp-highcharts>
          }


        </div>

      </div>
    }
    </div>



    <!-- Card Action Buttons -->
    @if (cardConfig && cardConfig.length > 0) {
      <div class="ifp-whats-new-card__actions">
        @if (cardConfig.length === 1) {
          <!-- Single button layout -->
          <div
            class="ifp-whats-new-card__action-btn ifp-whats-new-card__action-btn--single"
            (click)="onCardActionClick(cardConfig[0].value)"
            [title]="cardConfig[0].label">
            <em class="ifp-icon" [ngClass]="cardConfig[0].icon"></em>
            <span>{{ cardConfig[0].label | translate}}</span>
          </div>
        } @else if (cardConfig.length === 2) {
          <!-- Two div layout -->
          <div
            class="ifp-whats-new-card__action-btn ifp-whats-new-card__action-btn--left"
            (click)="onCardActionClick(cardConfig[0].value)"
            [title]="cardConfig[0].label">
            <em class="ifp-icon" [ngClass]="cardConfig[0].icon"></em>
            <span>{{ cardConfig[0].label | translate }}</span>
          </div>
          <div class="ifp-whats-new-card__action-divider"></div>
          <div
            class="ifp-whats-new-card__action-btn ifp-whats-new-card__action-btn--right"
            (click)="onCardActionClick(cardConfig[1].value)"
            [title]="cardConfig[1].label">
            <em class="ifp-icon" [ngClass]="cardConfig[1].icon"></em>
            <span>{{ cardConfig[1].label | translate }}</span>
          </div>
        }
      </div>
    }

</div>
