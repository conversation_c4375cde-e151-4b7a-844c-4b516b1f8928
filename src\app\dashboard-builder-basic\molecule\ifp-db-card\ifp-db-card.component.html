@if (!isRequestAccess) {
<div class="ifp-db-card"
  [ngClass]="{'ifp-db-card--selected' : selectedId === id, 'ifp-db-card--select-mode': isSelectMode, 'ifp-db-card--custom': isCustom}"
  (click)="setChecked($event, !cardData.checked)" (touchstart)="setChecked($event, !cardData.checked)">
  <div class="ifp-db-card__head-sec">
    <!-- <span class="ifp-db-card__icon" [ngStyle]="{'background-color': iconData.bgColor ? iconData.bgColor : 'transparent'}"><img [src]="iconData.url" alt=""></span> -->

    <div class="ifp-db-card__domain-wrapper">
      @if (!cardData.icon || !isDashboardCard) {
      <ifp-domain-icon [domainName]="cardData.domain"></ifp-domain-icon>
      } @else {
      <em class="ifp-db-card__ifp-icon {{cardData.icon}}"></em>
      }
      @if (isDashboardCard && mode =='edit') {
      <div class="ifp-db-card__action-icons">
        @if (showFilterOnCard) {
        <em class="ifp-icon ifp-icon-filter-2" (click)="openFilterPanel()"></em>
        }
        <em class="ifp-icon ifp-icon-round-cross" (mousedown)="deleteCard($event)" (touchstart)="deleteCard($event)"></em>
      </div>
      }
    </div>
    <div class="ifp-db-card__head-actions">
      @if(enableCheckbox) {
      <app-ifp-check-box [isSmall]="true" [checkedData]="cardData.checked"
        (click)="setChecked($event, !cardData.checked)" [disableFalse]="true"
        [disabled]="isCheckboxDisabled"></app-ifp-check-box>
      }
    </div>
  </div>

  @if (!isDashboardCard && mode == '') {
  <p class="ifp-db-card__title">{{cardData?.component_title}}</p>
  } @else if (mode == 'edit') {
  <div class="ifp-db-card__header-title-container"
    [ngClass]="{'ifp-db-card__header-title-container--focus': isTitleEdit}">
    <!-- <p #isTitleHead class="ifp-db-card__title" (mousedown)="changeHeadTitle($event)"
      [attr.contenteditable]="isTitleEdit" (keyup)="getTitle($event)"
      [ngStyle]="{'color': cardData.textColor ? cardData.textColor : null, 'fontSize' : cardData.textSize ? cardData.textSize+'px' : null  }">
      {{cardData?.title}}</p> -->
    <!-- <textarea #isTitleHead class="ifp-db-card__title" rows="1" (mousedown)="changeHeadTitle($event)"
      [attr.contenteditable]="isTitleEdit" (keyup)="getTitle(isTitleHead.value)"
      [ngStyle]="{'color': cardData.textColor ? cardData.textColor : null, 'fontSize' : cardData.textSize ? cardData.textSize+'px' : null}" [value]="cardData?.title"></textarea> -->
    @if(isTitleEdit) {
    <textarea #isTitleHead class="ifp-db-card__title" (mousedown)="changeHeadTitle($event)" (touchstart)="changeHeadTitle($event)"
      (keyup)="getTitle(isTitleHead.value)"
      [ngStyle]="{'color': cardData.textColor ? cardData.textColor : null, 'fontSize' : cardData.textSize ? cardData.textSize+'px' : null}"
      [value]="cardData?.title"></textarea>
    } @else {
    <p class="ifp-db-card__title" (mousedown)="changeHeadTitle($event)" (touchstart)="changeHeadTitle($event)"
      [ngStyle]="{'color': cardData.textColor ? cardData.textColor : null, 'fontSize' : cardData.textSize ? cardData.textSize+'px' : null  }">
      {{cardData?.title | translate}}</p>
    }
    <em class="ifp-icon ifp-icon-edit"></em>
  </div>
  } @else {
  <p class="ifp-db-card__title" [ngStyle]="{'color': cardData.textColor ? cardData.textColor : null, 'fontSize' : cardData.textSize ? cardData.textSize+'px' : null}">
    {{cardData?.title}}</p>
  }


  <!-- custom card start -->
  @if(isCustom) {
  <div class="ifp-db-card__cust">
    <!-- @if (!customChartType) {
    <div class="ifp-db-card__cust-inner">
      <p class="ifp-db-card__title">{{'Select a visualization' | translate}}</p>
      <div class="ifp-db-card__chart-set">
        @for (chart of customChartTypes; track chart.key; let i = $index) {
        <ifp-icon-selector [icon]="chart" (selectIcon)="getCustomChart($event)"
          class="ifp-db-card__chart-icon"></ifp-icon-selector>
        }
      </div>
    </div>
    }  -->
    <!-- @else { -->
    @if (customChartType) {
    <ifp-custom-dashboard-card [chartType]="customChartType" [cntType]="cntType" [selectId]="id"
      class="ifp-db-card__custom-card" [dashboardView]="dashboardView"></ifp-custom-dashboard-card>
    }

    <!-- } -->

  </div>
  <!-- custom card end -->
  }



  @if (!isCustom) {

  @if (cardData.content_classification_key && cardData.content_classification_key != classifications.analyticalApps &&
  cardData.type !== chartConstants.LIVEABILITY) {
  <p class="ifp-db-card__value">{{isNumeric(cardDataValues?.value) ? (cardDataValues?.value | number | shortNumber) :
    cardDataValues?.value}} <span class="ifp-db-card__value-unit">{{cardData?.unit}}</span>
    @if(isNumeric(rating.value) && cardData.content_classification_key && cardData.content_classification_key !=
    classifications.analyticalApps && cardData.type !== chartConstants.LIVEABILITY) {
    <span class="ifp-db-card__compared" [class]="'ifp-db-card__compared--'+(rating.value < 0 ? 'red' : 'green')">
      <em class="ifp-icon ifp-icon-up"></em><span>{{rating.value | number | shortNumber}} ({{ (rating.percentage |
        number: this.format)}}
        {{rating.value ? '%': ''}})</span>
    </span>
    } @else {
    <span style="visibility: hidden;">.</span>
    }
  </p>
  }@else {
  <ng-container *ngIf="currencyData.currency | shortNumber:numberType as currencyData">
    @if (currencyData.value && cardData.type !== chartConstants.LIVEABILITY && currencyData.value != 0) {
    <span class="ifp-db-card__value">{{currencyData.value}}{{currencyData?.key ? currencyData?.key : '' |
      translate}}<span class="ifp-db-card__value-unit">{{cardData?.unit}}</span></span>
    } @else {
    <p class="ifp-db-card__value" style="visibility: hidden;">.</p>
    }
  </ng-container>
  }

  @if (cardData?.publish) {
  <div class="ifp-db-card__txt-icon">
    <ifp-icon-text [icon]="'ifp-icon-calender'" [text]="cardData.publish " [key]="''"></ifp-icon-text>
  </div>
  }


  @if (isShowchart) {
  @if ((cardData.content_classification_key == classifications.innovativeStatistics ||
  cardData.content_classification_key == classifications.officialStatistics || cardData.type =='coi' || cardData.type
  =='scad') && id && cardData?.content_classification != chartConstants.COMPARE_STATISTICS_CNT) {
  <app-ifp-indicator-detail-widget [id]="id" [isDashboardCard]="true"></app-ifp-indicator-detail-widget>
  }

  @if ((!cardData.content_classification_key || cardData.content_classification_key == classifications.analyticalApps)
  && id) {
  @if (cardData.type == chartConstants.INSIGHT_DISCOVERY) {
  <app-ifp-insight-discovery-card [title]="visualizationData?.component_title" [filterPanel]="visualaizationfilterPanel"
    [periodFilter]="visualaizationFilters" [getChartData]="chartData" [chartValues]="visualaizationValues"
    [description]="cardData?.narrative" [appType]="cardData.type" [indicatorId]="id" [tableLabels]="tableData"
    [publishDate]="cardData?.publication_date ? cardData.publication_date : cardData?.updated"
    [source]="visualizationData?.data_source && visualizationData?.data_source !== '' ? visualizationData?.data_source : dataSource"
    [domain]="cardData?.domain" [isDashboardCard]="true"
    (filterChanged)="changeFilterData($event)"></app-ifp-insight-discovery-card>
  }

  @if (cardData.type == chartConstants.SCENARIO_TYPE) {
  <app-ifp-scenario-driver-card [analyticalId]="id" [isDashboardCard]="true"></app-ifp-scenario-driver-card>
  }

  @if (cardData.type === chartConstants.LIVEABILITY_DASHBOARD) {
  <app-ifp-geo-map [mapData]="cardData?.dashboardMapData?.data" [height]="280" [isDataLabel]="true"></app-ifp-geo-map>
  }

  }

  @if (cardData?.content_classification == chartConstants.COMPARE_STATISTICS_CNT) {
  <app-ifp-compare-chart-card *ngIf="cardData" [responseData]="cardData" [cloneResponse]="cardData"
    [isDashboardCard]="true"></app-ifp-compare-chart-card>
  }
  }

  }
  @if (mode == 'edit' && isDashboardCard){
  <div class="ifp-db-card__header-description-container" #isDescription
    [ngClass]="{'ifp-db-card__header-description-container--placeholder': !cardData?.cardDescription && !isDescriptionEdit}">
    @if (isDescriptionEdit || !cardData?.cardDescription && !isDescriptionEdit) {
    <textarea class="ifp-db-card__description" (mousedown)="changeDescription($event)" (touchstart)="changeDescription($event)" #inputField
      [attr.contenteditable]="isDescriptionEdit" (keyup)="updateDescription($event)"
      [ngStyle]="{'color': cardData.descriptionColor ? cardData.descriptionColor : null, 'fontSize' : cardData.descriptionFontSize ? cardData.descriptionFontSize+'px' : null  }"
      [value]="(cardData?.cardDescription || isDescriptionEdit )? cardData?.cardDescription : placeHolderText"></textarea>
    }

    @if (!isDescriptionEdit && cardData?.cardDescription) {
    <p class="ifp-db-card__description-preview" (mousedown)="changeDescription($event)" (touchstart)="changeDescription($event)"
      [ngStyle]="{'color': cardData.descriptionColor ? cardData.descriptionColor : null, 'fontSize' : cardData.descriptionFontSize ? cardData.descriptionFontSize+'px' : null  }">
      {{cardData?.cardDescription}}</p>
    }
    <em class="ifp-icon ifp-icon-edit"></em>
  </div>
  } @else if (mode !== 'edit' && isDashboardCard && cardData?.cardDescription) {
  <div class="ifp-richtext-viewer" [ngStyle]="{'color': cardData.descriptionColor ? cardData.descriptionColor : null, 'fontSize' : cardData.descriptionFontSize ? cardData.descriptionFontSize+'px' : null  }" [innerHTML]="cardData.cardDescription"></div>
  }

  @if (!cardData) { <app-ifp-card-loader class="ifp-loader" [type]="'chart'">
  </app-ifp-card-loader>
  }
</div>
<em class="ifp-icon ifp-icon-horizontal-arrows ifp-db-card__arrows ifp-db-card__arrows--h"></em>
<em class="ifp-icon ifp-icon-horizontal-arrows ifp-db-card__arrows ifp-db-card__arrows--h"></em>
<em class="ifp-icon ifp-icon-horizontal-arrows ifp-db-card__arrows ifp-db-card__arrows--v"></em>
<em class="ifp-icon ifp-icon-horizontal-arrows ifp-db-card__arrows ifp-db-card__arrows--v"></em>
} @else {
<div class="ifp-db-card">
  <ifp-unauthorized-card [nodeId]="id" [contentType]="cntType" [isPending]="accessPending"></ifp-unauthorized-card>
</div>
}
