import { NgClass } from '@angular/common';
import { Component, EventEmitter, HostListener, Input, OnChanges, OnInit, Output, SimpleChanges, forwardRef, On<PERSON><PERSON>roy, ViewChild, ElementRef, input, viewChild, inject, Renderer2, ChangeDetectorRef } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpCheckBoxComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { DbDropDown } from './ifp-db-dropdown.interface';
import { ControlValueAccessor, FormControl, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { SubSink } from 'subsink';
import { commonDataTypes } from 'src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-data-prep.constant';
import { PopService } from 'src/app/scad-insights/core/services/popperService/popper.service';
import { SearchListPipe } from 'src/app/scad-insights/core/pipes/searchList.pipe';


@Component({
  selector: 'app-ifp-db-dropdown',
  templateUrl: './ifp-db-dropdown.component.html',
  styleUrls: ['./ifp-db-dropdown.component.scss'],
  imports: [TranslateModule, NgClass, IfpCheckBoxComponent, SearchListPipe, ReactiveFormsModule],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: forwardRef(() => IfpDbDropdownComponent)
    }
  ]
})
export class IfpDbDropdownComponent implements OnChanges, ControlValueAccessor, OnInit, OnDestroy {

  @ViewChild('drop') drop!: ElementRef;
  public dropdownList = viewChild<ElementRef>('dropdownList');
  public dropdownListInner = viewChild<ElementRef>('dropdownListInner');
  @HostListener('window:pointerup', ['$event.target'])

  onOutsideClick(target: HTMLElement) {
    if (this.drop) {
      if (!this.drop.nativeElement.contains(target) && !this.bodyBind() && this.showList) {
        this.showList = false;
        this.close.emit();
      } else if (this.bodyBind() && this.showList && this.dropdownList && !this.dropdownList()?.nativeElement.contains(target) && !this.drop.nativeElement.contains(target)) {
        this.removeDropdown();
        this.close.emit();
        this.showList = false;
      }
    }
  }


  @Input() title!: string;
  @Input() isMultiSelect: boolean = true;
  @Input() options?: any[] = [];
  @Input() placeholder: string = 'Select';
  @Input() selectedSingleItem!: any;
  @Input() key!: string;
  @Input() defaultSelect: boolean = true;
  @Input() optionAlias: boolean = false;
  @Input() multipleSelectedItems: any = [];
  @Input() disableTranslation: boolean = false;
  @Input() iconEnable: boolean = false;
  @Input() formControls: FormControl = new FormControl();

  public limit  = input<number>();
  public minLimit  = input<number>();
  public bodyBind = input<boolean>(false);
  public autoCalculatePosition = input<boolean>(false);
  public searchEnable = input<boolean>(false);
  public searchOptions =[];
  public _renderer =  inject(Renderer2);
  public _cdr: ChangeDetectorRef =  inject(ChangeDetectorRef);
  public search: FormControl = new FormControl('');

  public instance!:{destroy : () => object, update: () => object, forceUpdate: () => object};
  @Output() singleSelected: EventEmitter<any> = new EventEmitter<any>();
  @Output() multiSelected: EventEmitter<any[]> = new EventEmitter<any[]>();
  @Output() multiSelectedValue: EventEmitter<any> = new EventEmitter<any>();
  @Output() close: EventEmitter<void> = new EventEmitter<void>();


  public showList: boolean = false;
  public childElement!: HTMLElement | undefined;
  public subs = new SubSink();
  public commmonDataTypes: any = commonDataTypes;

  onChange = (_value: string) => {
    // chnage event
  };

  onTouched = () => {
    // touch event
  };

  writeValue(value: string): void {
    this.selectedSingleItem = value;
    if ( this.options && this.options?.length > 0 && this.defaultSelect && !this.selectedSingleItem) {
      this.selectedSingleItem = this.options[0];
      if (this.isMultiSelect && this.multipleSelectedItems?.length <= 0) {
        this.selectedSingleItem.checked = true;
        this.multipleSelectedItems.push(this.selectedSingleItem);
      }
    }
    if (this.options?.length == 1 && this.defaultSelect) {
      this.selectedSingleItem = this.options[0];
    }
    if (this.selectedSingleItem) {
      this.onChange(this.selectedSingleItem);
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['options']) {
      if (this.options && this.options?.length > 0 && this.defaultSelect && !this.selectedSingleItem) {
        this.selectedSingleItem = this.options[0];
        this.formControls.setValue(this.selectedSingleItem);
        if (this.isMultiSelect && this.multipleSelectedItems?.length <= 0) {
          this.selectedSingleItem.checked = true;
          this.multipleSelectedItems.push(this.selectedSingleItem);
          this.formControls.setValue(this.multipleSelectedItems);
        }
      }
      if (this.options?.length == 1 && this.defaultSelect) {
        this.selectedSingleItem = this.options[0];
      }
    }
    // Handle external updates to multipleSelectedItems
    if (changes['multipleSelectedItems'] && this.isMultiSelect && this.options) {
      // Update the checked state of all options based on multipleSelectedItems
      this.options.forEach(option => {
        const isSelected = this.multipleSelectedItems?.some((selectedItem: any) => selectedItem[this.key] === option[this.key]
        );
        option['checked'] = isSelected || false;
      });

    }
  }

  setChecked(event: boolean, item: DbDropDown | any) {
    // Prevent selecting more than the max limit
    if (event && this.limit() && this.multipleSelectedItems?.length >= (this.limit() ?? 0)) {
      return;
    }
    // Prevent deselecting below the min limit
    if (!event && this.minLimit() && this.multipleSelectedItems?.length <= (this.minLimit() ?? 0)) {
      return;
    }
    item.checked = event;
    if (event) {
      this.multipleSelectedItems.push(item);
    } else {
      let index = -1;
      if (this.key) {
        index = this.multipleSelectedItems.findIndex((x: Record<string, string| undefined>) => x?.[this.key] == item?.[this.key]);
      } else {
        index = this.multipleSelectedItems.findIndex((x: { name: string | undefined; }) => x.name == item.name);
      }

      if (index >= 0) {
        this.multipleSelectedItems.splice(index, 1);
      }
    }
    this.onChange(this.multipleSelectedItems);
    this.onTouched();
    this.formControls.setValue(this.multipleSelectedItems);
    this.multiSelectedValue.emit(item);
    this.multiSelected.emit(this.multipleSelectedItems);
  }

  selectSingleItem(item: DbDropDown | any, index: number) {
    this.selectedSingleItem = item;
    item.index = index;
    this.onChange(item);
    this.onTouched();
    this.formControls.setValue(item);
    this.singleSelected.emit(item);
    this.showList = false;
    this.close.emit();
    if (this.bodyBind()) {
      this.removeDropdown();
    }
  }

  toggleDropdown() {
    const wasOpen = this.showList;
    this.showList = !this.showList;
    if (this.bodyBind()) {
      if (this.showList) {
        this.setDropdownPosition();
      } else {
        this.removeDropdown();
      }
    }
    this.search.setValue('');
    if (wasOpen && !this.showList) {
      this.close.emit();
    }
  }

  getMutiselectedItem(item: Record<string, any>) {
    let isSelected: boolean = false;
    if (this.key && this.multipleSelectedItems?.find((x: Record<string, any>) => x?.[this.key] == item?.[this.key])) {
      isSelected = true;
    }
    return isSelected;
  }

  ngOnInit(): void {
    this.subs.add(this.formControls
      .valueChanges.subscribe(
        (value) => {
          this.selectedSingleItem = value;
        }
      ));

  }

  removeDropdown() {
    if (this.instance) {
      this.instance?.destroy();
    }
    const container = document.fullscreenElement ? document.fullscreenElement : document.body;
    this._renderer.removeChild(container, this.dropdownList()?.nativeElement);
  }

  setDropdownPosition() {
    this._renderer.setStyle(this.dropdownList()?.nativeElement, 'max-width', `${this.drop.nativeElement.offsetWidth}px`);
    this._renderer.setStyle(this.dropdownList()?.nativeElement, 'width', '100%');
    const container = document.fullscreenElement ? document.fullscreenElement : document.body;
    this._renderer.appendChild(container, this.dropdownList()?.nativeElement);
    if (this.instance) {
      this.instance?.destroy();
    }


    // Check if there's enough space below the dropdown
    const dropdownRect = this.drop.nativeElement.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const spaceBelow = viewportHeight - dropdownRect.bottom;
    // If space below is less than 300px, position the dropdown above
    const placement = spaceBelow < 300 && this.autoCalculatePosition() ? 'top' : 'bottom';
    if (placement === 'top') {
      this._renderer.setStyle(this.dropdownListInner()?.nativeElement, 'transform-origin', 'bottom');
    }
    this.instance = new PopService().createPopper(this.drop.nativeElement, this.dropdownList()?.nativeElement, {
      placement: placement,
      modifiers: [
        {
          name: 'offset',
          options: {
            offset: [0, 5]
          }
        },
      ]
    });
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
    this.removeDropdown();
  }
}
