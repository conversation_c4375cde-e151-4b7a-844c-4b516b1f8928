import { LabelData } from 'src/app/scad-insights/core/interface/atom/ifp-category-label.interface';

export const tabMenu = [
  {
    key: 'chart',
    title_en: 'Charts',
    title_ar: 'الرسوم البيانية',
    icon: 'ifp-icon-pie-chart',
    disabled: false,
    content: [
      {
        title: 'Chart Types',
        key: 'chartsType',
        isExpand: true
      },
      {
        title: 'Chart style',
        key: 'chartStyle'
      },
      {
        title: 'Chart Settings',
        key: 'chartSettings'
      },
      {
        title: 'Legend',
        key: 'legend'
      }
      // {
      //   title: 'Text',
      //   key: 'text'
      // },
      // {
      //   title: 'Background',
      //   key: 'background'
      // }
    ]
  },
  {
    key: 'graphics',
    title_en: 'Graphics',
    title_ar: 'الرسومات',
    icon: 'ifp-icon-image-icon',
    disabled: false,
    content: [
      {
        title: 'Library',
        key: 'library',
        isExpand: true
      }
      // {
      //   title: 'Color',
      //   key: 'color'
      // }
    ]
  },
  {
    key: 'text',
    title_en: 'Text',
    title_ar: 'نص',
    icon: 'ifp-icon-font1',
    disabled: false,
    content: [
      {
        title: 'Title',
        key: 'cardTitle',
        isExpand: true
      },
      {
        title: 'Text Area',
        key: 'textArea'
      }
      // {
      //   title: 'Text Alignment',
      //   key: 'textAlignment'
      // },
      // {
      //   title: 'Text Format',
      //   key: 'textFormat'
      // }
    ]
  },
  {
    key: 'filter',
    title_en: 'Filter',
    title_ar: 'منقي',
    icon: 'ifp-icon-filter-2',
    disabled: true,
    content: []
  },
  {
    key: 'data',
    title_en: 'Data',
    title_ar: 'بيانات',
    icon: 'ifp-icon-data-file',
    disabled: false,
    content: [
      {
        title: 'Modify Data',
        key: 'data',
        isExpand: true
      }
    ]
  }
];

export const chartTypes = [
  {
    title: 'Line',
    key: 'line',
    icon: 'ifp-icon-graph-line',
    disabled: false,
    selected: false
  },
  {
    title: 'Column',
    key: 'column',
    icon: 'ifp-icon-bar-chart',
    disabled: false,
    selected: false
  },
  {
    title: 'Bar',
    key: 'bar',
    icon: 'ifp-icon-horizontal-bar',
    disabled: false,
    selected: false
  },
  {
    title: 'Circular',
    key: 'circular',
    icon: 'ifp-icon-radial-bar',
    disabled: false,
    selected: false
  },
  {
    title: 'Pie',
    key: 'pie',
    icon: 'ifp-icon-pie-chart',
    disabled: false,
    selected: false
  },
  {
    title: 'Donut',
    key: 'doughnut',
    icon: 'ifp-icon-doughnut-chart',
    disabled: false,
    selected: false
  },
  {
    title: 'Tree',
    key: 'tree',
    icon: 'ifp-icon-menu',
    disabled: false,
    selected: false
  },
  {
    title: 'Table',
    key: 'table',
    icon: 'ifp-icon-table',
    disabled: false,
    selected: false
  }
];

export const chartSettingsOptions = [
  {
    name: 'Data Label',
    key: 'dataLabel',
    value: true
  },
  {
    name: 'Precise Value',
    key: 'preciseValue',
    value: false
  },
  {
    name: 'Recent',
    key: 'recent',
    value: true
  }
];

export const dockOptions = [
  {
    key: 'left',
    icon: 'ifp-icon-dockside-left',
    name_en: 'Left',
    name_ar: 'يسار '
  },
  {
    key: 'right',
    icon: 'ifp-icon-dockside-right',
    name_en: 'Right',
    name_ar: 'يمين'
  },
  {
    key: 'drag',
    icon: 'ifp-icon-dockside-pop',
    name_en: 'Drag',
    name_ar: 'سحب'
  }
];

export const dbdlistTabs: LabelData[] = [
  {
    // iconClass: 'ifp-icon-list-view',
    name: 'Dashboards',
    badge: false
  },
  {
    // iconClass: 'ifp-icon-menu',
    name: 'Sent',
    badge: false
  },
  {
    // iconClass: 'ifp-icon-menu',
    name: 'Received',
    badge: true
  }
];

export const customChartTypes = [
  {
    title: 'Line',
    key: 'line',
    icon: 'ifp-icon-graph-line',
    disabled: false,
    selected: false
  },
  {
    title: 'Bar',
    key: 'bar',
    icon: 'ifp-icon-horizontal-bar',
    disabled: false,
    selected: false
  },
  {
    title: 'Column',
    key: 'column',
    icon: 'ifp-icon-bar-chart',
    disabled: false,
    selected: false
  },
  {
    title: 'Circular',
    key: 'circular',
    icon: 'ifp-icon-radial-bar',
    disabled: false,
    selected: false
  },
  {
    title: 'Pie',
    key: 'pie',
    icon: 'ifp-icon-pie-chart',
    disabled: false,
    selected: false
  },
  {
    title: 'Donut',
    key: 'doughnut',
    icon: 'ifp-icon-doughnut-chart',
    disabled: false,
    selected: false
  },
  {
    title: 'Table',
    key: 'table',
    icon: 'ifp-icon-table',
    disabled: false,
    selected: false
  }
];


export const dataExploratoryTypes = [


  {
    title: 'Box plot',
    key: 'box',
    icon: 'ifp-icon-box-plot',
    disabled: true,
    selected: false
  },
  {
    title: 'Tabular',
    key: 'table',
    icon: 'ifp-icon-table',
    disabled: true,
    selected: false
  },
  {
    title: 'Column',
    key: 'column',
    icon: 'ifp-icon-analysis',
    disabled: false,
    selected: false
  }

];


export const singleDimentionData = [
  {
    year: 2000,
    count: 10,
    age: 20
  },
  {
    year: 2001,
    count: 15,
    age: 30
  },
  {
    year: 2002,
    count: 12,
    age: 45
  },
  {
    year: 2003,
    count: 25,
    age: 60
  },
  {
    year: 2004,
    count: 17,
    age: 45
  }
];

export const multiDimentionData = [
  {
    count: 10,
    year: 2000,
    age: 20,
    wage: 500
  }
];

export const axisDropDownOptions = [
  {
    name: 'Year',
    key: 'year',
    checked: false,
    disabled: false
  },
  {
    name: 'Value',
    key: 'value',
    checked: true,
    disabled: false
  },
  {
    name: 'Age',
    key: 'age',
    checked: false,
    disabled: false
  }
];

export const axisDropDowns = [
  {
    title: 'Select X-Variable',
    key: 'x-axis',
    options: axisDropDownOptions,
    optKey: 'name',
    disableOption: false,
    multiSelect: false
  },
  {
    title: 'Select Y-Variable',
    key: 'y-axis',
    options: axisDropDownOptions,
    optKey: 'name',
    disableOption: true,
    multiSelect: true
  }
];

export const commonKeyword = {
  Yaxis: 'y-axis',
  Xaxis: 'x-axis',
  toolXaxis: 'Xaxis',
  toolYaxis: 'Yaxis'
};

