@use "../../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-share-modal {
  background-color: $ifp-color-white;
  border-radius: 24px;
  padding: $spacer-6 $spacer-5 $spacer-5;
  position: relative;
  box-shadow: 0 0 32px rgba(0, 0, 0, 0.16);
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacer-4;
  }
  &__title {
    font-size: $ifp-fs-6;
    font-weight: $fw-bold;
  }
  &__text,
  &__comment {
  padding: $spacer-2 $spacer-3;
  color: $ifp-color-black;
}
  &__text,
  &__comment,
  &__share-sec {
    background-color: $ifp-color-white;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 5px;
  }
  &__text {
    width: 100%;
    &--email {
      margin-top: $spacer-3;
    }
  }
  &__comment {
    display: block;
    width: 100%;
    resize: none;
  }
  &__email {
    border: none;
    background-color: transparent;
    color: $ifp-color-black;
  }
  &__field-wrapper {
    display: flex;
    margin: $spacer-0 (-$spacer-2) $spacer-4;
    &:last-child {
      margin-bottom: $spacer-0;
    }
    & > .ifp-icon {
      font-size: $ifp-fs-6;
      margin: $spacer-0 $spacer-2;
    }
  }
  &__field-item {
    margin: $spacer-0 $spacer-2;
    width: 100%;
  }
  &__share-sec {
    padding: $spacer-2;
    max-height: 300px;
    @include ifp-scroll-y($ifp-color-grey-11, $ifp-color-grey-1, 4px, 8px);
    padding-right: $spacer-2;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  &__share-tag {
    display: inline-flex;
    align-items: center;
    margin: $spacer-1;
    border-radius: 50px;
    border: 1px solid $ifp-color-blue-med;
    transition: 0.3s;
  }
  &__profile-img {
    width: 34px;
    min-width: 34px;
    height: 34px;
    border-radius: 50%;
  }
  &__username {
    white-space: nowrap;
    padding: $spacer-2 $spacer-3;
  }
  &__share-cancel {
    background-color: $ifp-color-violet-light;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.3s;
    cursor: pointer;
    .ifp-icon {
      color: $ifp-color-secondary-blue;
    }
    &:hover {
      background-color: $ifp-color-red-light;
      .ifp-icon {
        color: $ifp-color-red;
      }
    }
  }
  &__close{
    position: absolute;
    top: 15px;
    right: 16px;
    cursor: pointer;
    opacity: 0.7;
    transition: 0.3s;
    &:hover {
      opacity: 1;
    }
  }
  &__error {
    color: $ifp-color-red;
    margin-top: $spacer-1;
    font-size: $ifp-fs-2;
    display: flex;
    align-items: center;
    .ifp-icon {
      margin-right: $spacer-1;
    }
  }
  &__count {
    margin-bottom: $spacer-3
  }
}
