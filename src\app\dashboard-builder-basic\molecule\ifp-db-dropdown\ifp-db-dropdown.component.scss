@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-db-dropdown {
  position: relative;

  &__title {
    font-size: $ifp-fs-4;
    font-weight: $fw-semi-bold;
    margin-bottom: $spacer-2;
  }

  &__selected {
    background: $ifp-color-white;
    border: 1px solid $ifp-color-grey-8;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    padding: $spacer-2 $spacer-3;

    .ifp-icon {
      margin-left: $spacer-2;
    }

    &--disabled {
      background-color: $ifp-color-grey-7;
      pointer-events: none;
    }
  }

  &__list {
    transform: scaleY(0) translateX(-50%);
    transform-origin: top;
    position: absolute;
    top: 100%;
    width: 100%;
    left: 50%;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 5px;
    margin-top: $spacer-2;
    background-color: $ifp-color-white;
    z-index: 7;
    min-width: 200px;
    transition: 0.3s;
    @include ifp-scroll-y(transparent, $ifp-color-grey-1, 4px, 8px);
    max-height: 250px;
  }

  &__list-item {
    &--disabled {
      pointer-events: none;
    }
  }

  &__list-text {
    padding: $spacer-2 $spacer-3;
    transition: 0.3s;
    display: block;

    &:hover {
      background-color: $ifp-color-grey-7;
    }

    &--selected {
      background-color: $ifp-color-secondary-blue;
      color: $ifp-color-white-global;
      &:hover {
        background-color: $ifp-color-secondary-blue;
        color: $ifp-color-white-global;
      }
    }

    &--disabled {
      pointer-events: none;
      opacity: 0.3;
    }
  }

  &--show {
    .ifp-db-dropdown {
      &__list {
        transform: scaleY(1) translateX(-50%);
      }
    }
  }
  &__selected-icon {
    font-size: 1.6rem;
    margin-right: $spacer-2;
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-db-dropdown__list-text, .ifp-db-dropdown__selected-item{
       color:  $ifp-color-primary-grey;
    }
}

