@use "../../../../../assets/ifp-styles/abstracts/index" as *;
@use "../../../../../assets/ifp-styles/components/_whats-new-card.scss";
:host{
    background-color: $ifp-color-white;
  border-radius:10px;
  height: 100%;
  padding: $spacer-3 $spacer-3 $spacer-0;
  border: solid $ifp-color-grey-7 1px;
  overflow: hidden;
  display: block;
}

.ifp-whats-new-card {
  &__value-number {
    font-size: $ifp-fs-8;
    font-weight: $fw-bold;
  }
  &__unit {
    color: $ifp-color-grey-14;
    font-size: $ifp-fs-2;
    font-weight:$fw-regular;
  }
  &__name{
    font-size: $ifp-fs-4;
    padding-left: $spacer-0 !important;
  }
  &__icon {
       display:block;
       margin-inline-end: $spacer-3;
       font-size:$ifp-fs-11;
  }
  &--dxp {
    // Let parent container control height - remove conflicting constraints
    width: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    position: static;
    // Simplified state styles - let parent control height
    &.ifp-whats-new-card--small,
    &.ifp-whats-new-card--expanded {
      .ifp-card {
        height: 100%; // Fill parent container height
        transition: all 0.3s ease-in-out;
        display: flex;
        flex-direction: column;
        overflow: hidden; // Prevent content overflow
      }
    }

    .ifp-whats-new-card {
      &__overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        z-index: 10;
      }
      &__overlay-desc {
        font-size: $ifp-fs-4;
        font-weight: $fw-bold;
        margin-bottom: $spacer-3;
        max-width: 240px;
        text-align: center;
      }
      &__value-range {
        margin-bottom: $spacer-3;
      }
      &__remove {
        width: 100%;
        margin-top: $spacer-3;
      }
      &__txt-icon {
      color: $ifp-color-grey-14;
      display: flex;
      }
      // Header layout adjustments for checkbox and icon
      &__icon-wrapper {
        display: flex;
        align-items: center;
        gap: $spacer-2;
      }
      &__calender {
        margin-inline-end:$spacer-2 ;
      }

      // Checkbox styling in header
      &__header-checkbox {
        display: flex;
        align-items: center;
      }

      // Subtitle styling
      &__subtitle {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-top: $spacer-1;
        line-height: 1.4;
        font-weight: 400;
      }

      // Chart container styling
      &__chart-container {
        margin-top: $spacer-2;
        width: 100%;
        flex: 1; // Take remaining space
        min-height: 0; // Allow shrinking
      }

      // Left section sizing
      &__left {
        flex-shrink: 0; // Don't shrink the left content
        min-width: 120px; // Ensure minimum width for left content
        max-width: 100%;
      }
      // Body layout for flexible height
      &__body {
        flex: 1;
        display: flex;
        min-height: 0; // Allow shrinking
        overflow: hidden; // Prevent content overflow

        &--expanded {
          .ifp-whats-new-card__left {
             max-width: 300px;
          }
          .ifp-whats-new-card__right {
            display: flex;
            flex-direction: column;
            flex: 1;
            min-height: 0;
            overflow: hidden; // Prevent chart overflow
          }
        }
      }


      // Right section for expanded view
      &__right {
        flex: 1;
        min-height: 0;
        margin-left: $spacer-3;
        overflow: hidden; // Prevent overflow

        // Chart container in right section
        .ifp-whats-new-card__chart-container {
          overflow: hidden;
        }
      }

      // No chart data message
      &__no-chart {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100px;
        background-color: var(--background-light, #f8f9fa);
        border-radius: 4px;
        border: 1px dashed var(--border-color, #dee2e6);

        p {
          margin: 0;
          color: var(--text-muted, #6c757d);
          font-size: 0.875rem;
          font-style: italic;
        }
      }

      // Chart loading state
      &__chart-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100px;
        background-color: var(--background-light, #f8f9fa);
        border-radius: 4px;

        p {
          margin: $spacer-2 0 0 0;
          color: var(--text-muted, #6c757d);
          font-size: 0.875rem;
        }
      }

      // Loading spinner
      &__loading-spinner {
        width: 20px;
        height: 20px;
        border: 2px solid var(--border-color, #dee2e6);
        border-top: 2px solid var(--primary-color, #007bff);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      // Bubble Tags Styles
      &__bubble-tags {
        display: flex;
        flex-wrap: nowrap; // Keep in same row
        gap: $spacer-2;
        margin-top: $spacer-3;
        overflow: visible; // Allow content to be visible
        width: 100%; // Ensure full width utilization
        margin-bottom: $spacer-3;
      }

      &__bubble-tag {
        display: inline-flex;
        align-items: center;
        padding: 10px 12px; // Increased padding for better visual presence
        border-radius: 50px;
        font-size: $ifp-fs-2; // Slightly larger font size for better readability
        font-weight: $fw-medium;
        border: none; // Remove border for filled style
        background-color: $ifp-color-grey-table-bg; // Faded grey background
        color: $ifp-color-secondary-grey; // Dark grey text for good contrast
        transition: all 0.2s ease;
        flex-shrink: 1; // Allow shrinking
        min-width: 82px; // Allow shrinking below content size
        max-width: 90% !important; // Significantly increased to 90% to maximize text display

      }

      &__bubble-icon {
        font-size: $ifp-fs-3; // Optimized icon size to match larger bubble tags
        margin-inline-end: $spacer-2; // Maintained spacing for proper alignment
        display: flex;
        align-items: center;
        flex-shrink: 0; // Don't shrink the icon
      }

      &__bubble-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.3; // Slightly increased line height for better readability
        min-width: 0; // Allow shrinking
        font-weight: $fw-medium; // Ensure consistent font weight
      }
    }
  }

  &__actions {
    display: flex;
    width: calc(100% + #{$spacer-3 * 2}); // Extend beyond card padding (left + right)
    background-color: $ifp-color-grey-4; // Light grey background to match target design
    margin-top: auto; // Push to bottom of card
    margin-left: -$spacer-3; // Negative margin to extend to left edge
    margin-right: -$spacer-3; // Negative margin to extend to right edge
    border-radius: 0 0 8px 8px; // Rounded bottom corners to match card
    position: relative; // Ensure proper positioning
    z-index: 1; // Ensure buttons appear above other content
    overflow: hidden; // Ensure clean edges

    // Ensure proper height and alignment - keep flat design as shown in image
    min-height: 44px; // Slightly taller for better touch targets
    align-items: center;
  }

  &__action-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: $spacer-1;
    padding: $spacer-2 $spacer-3;
    background: transparent;
    border: none;
    color: $ifp-color-secondary-blue; // Use primary grey color for more subtle look
    font-size: $ifp-fs-3;
    font-weight: $fw-medium;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 40px;
    position: relative; // Ensure proper stacking

    // Ensure text and icon are properly aligned
    text-align: center;
    white-space: nowrap;

    &:hover {
      background-color: rgba(0, 0, 0, 0.04); // Very subtle grey hover effect like target design
      color: $ifp-color-primary-blue; // Blue color on hover for interaction feedback
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.08); // Slightly more visible active state
    }

    &:focus {
      outline: none;
      box-shadow: inset 0 0 0 2px $ifp-color-primary-blue;
    }

    // Single button - full width
    &--single {
      width: 100%;
      border-radius: 0 0 8px 8px; // Rounded bottom corners for single button
    }

    // Two button layout
    &--left {
      border-right: none;
      border-radius: 0 0 0 8px; // Rounded bottom-left corner
    }

    &--right {
      border-left: none;
      border-radius: 0 0 8px 0; // Rounded bottom-right corner
    }

    // Icon styling - ensure icons are visible and properly sized
    .ifp-icon {
      font-size: $ifp-fs-4;
      display: inline-block;
      vertical-align: middle;

      // Ensure icon font is loaded properly
      font-family: 'ifp-icons' !important;
      font-style: normal;
      font-weight: normal;
      font-variant: normal;
      text-transform: none;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    // Button text styling
    span {
      font-size: $ifp-fs-3;
      line-height: 1.2;
      display: inline-block;
      vertical-align: middle;
      font-weight: $fw-medium;
    }
  }

  &__action-divider {
    width: 1px;
    background-color: $ifp-color-grey-7; // Subtle divider line like target design
    flex-shrink: 0;
    height: 100%; // Full height divider to match design
    max-height: 22px; // Ensure minimum height matches button height
  }
}

// Spinner animation
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}





:host-context(.ifp-dark-theme) {
  .ifp-whats-new-card {
     &__bubble-tag {
        background-color: $ifp-color-section-white; // Faded grey background
      }
    }
}

:host::ng-deep {
  .ifp-whats-new-card__txt-icon {
    .ifp-ic-tx-holder__key,.ifp-ic-tx-holder__value {
      font-size: $ifp-fs-3;
    }
  }
}
