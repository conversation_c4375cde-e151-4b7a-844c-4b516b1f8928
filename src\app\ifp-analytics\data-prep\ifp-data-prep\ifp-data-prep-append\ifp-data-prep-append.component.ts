import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {  Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { IfpDbDropdownComponent } from 'src/app/dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.component';
import { FileData, IfpDbFileUploaderComponent } from 'src/app/dashboard-builder/molecule/ifp-db-file-uploader/ifp-db-file-uploader.component';
import { fileFormats } from 'src/app/dashboard-builder/molecule/ifp-db-file-uploader/ifp-db-file-uploader.constants';
import { IfpImportIndicatorsComponent } from 'src/app/dashboard-builder/organism/ifp-import-indicators/ifp-import-indicators.component';
import { IfpTagComponent } from 'src/app/ifp-analytics/atom/ifp-tag/ifp-tag.component';
import { IfpBackButtonComponent } from 'src/app/ifp-widgets/atoms/ifp-back-button/ifp-back-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { IfpButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { IfpDataPreviewComponent } from '../../../organism/ifp-data-preview/ifp-data-preview.component';
import { SubscriptionLike } from 'rxjs';
import { ApiStatus } from 'src/app/scad-insights/core/constants/api-status.constants';
import { FileResponePrep } from '../interface/ifp-data-prep.interface';
import { IfpPrepUploadService } from '../ifp-data-prep-upload/services/ifp-prep-upload.service';
import { IfpPrepService } from '../ifp-prep-service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { Store } from '@ngrx/store';
import { prepStatusStore, workFlowState } from '../constants/ifp-state.contants';
import { loadCreateConnectionName, loadCreateConnectionRemoveName, loadCreateNodeSuccessUnionCreate, loadCreateNodeSuccessUnionRemove, loadCreateNodeSuccessUnionUpdate, loadNode } from '../store/node-store/node-store.actions';
import { SubSink } from 'subsink';
import { connectionType, prepsApiEndpoints, storageType } from '../constants/if-preps.constants';
import { ColumnPrep, ColumnUpstream } from '../ifp-prep-select-data/interface/prep-selection.interface';
import { selectNodePrepResponse } from '../store/node-store/node-store.selectors';
import { FormArray, FormGroup, FormControl, ReactiveFormsModule } from '@angular/forms';


@Component({
    selector: 'ifp-data-prep-append',
    templateUrl: './ifp-data-prep-append.component.html',
    styleUrl: './ifp-data-prep-append.component.scss',
    imports: [TranslateModule, IfpDbFileUploaderComponent, IfpImportIndicatorsComponent, IfpBackButtonComponent, IfpDbDropdownComponent, IfpTagComponent, IfpButtonComponent, IfpDataPreviewComponent, ReactiveFormsModule],
    providers: [IfpPrepUploadService]
})

export class IfpDataPrepAppendComponent implements OnDestroy, OnInit{

  constructor(private _router: Router, private _prepService: IfpPrepUploadService, private _prepCommonService: IfpPrepService, private _toast: ToasterService, private _store: Store) {
    if (!this._prepCommonService.uploadedFileResponse()) {
      this._router.navigate(['analytics/data-preparation/upload-data']);
    } else { //  else

    }
  }

  public allowedExtensions: string[] = fileFormats.excelFormats;
  public step: 'upload' | 'browse' | 'detail' | '' = '';
  public color = ifpColors;
  public buttonClass = buttonClass;
  public loader = false;
  public disableProcesed = false;
  public uploadDataProgess = 0;
  public downloadSubscribe!:SubscriptionLike;
  public uploadResponse!: FileResponePrep;
  public dataColumn!:ColumnUpstream;
  public dataColumn2!:ColumnUpstream;
  public exisitingColumns: ColumnPrep[] = [];
  public formControl: FormGroup<{array: FormControl<FormArray<FormGroup>>}> | any = new FormGroup({array: new FormArray([])});
  public subs = new SubSink();
  public unionNode = '';
  public process = false;
  public objectIdData = '';
  public newColumns: ColumnPrep[] = [];
  private cancelUploads = false;


  ngOnInit(): void {
    this.callUpendColumn();
  }

  uploadFile(file: FileData) {
    const uploadData = new FormData();
    const nameFile = file.file[0].name;
    // Split the filename at the last dot (.)
    const parts = nameFile.split('.');
    // Extract the filename (without extension)
    const name = parts.slice(0, -1).join('.');
    // Get the extension (including the dot)
    const extension = parts.pop();

    // Format the date (adjust the format string as needed)
    const today = new Date().toISOString().replace(/-/g, '');

    // Combine the name, date, and extension
    const newFilename = `${name}-${today}.${extension}`;
    uploadData.append('name', newFilename);
    uploadData.append('storage_backend', storageType.s3);
    uploadData.append('file', file.file[0]);
    this.downloadSubscribe =this._prepService.getUploadData(uploadData).subscribe({next: data => {
      if (data?.type == 1) {
        this.uploadDataProgess =Math.floor((data?.loaded/ data?.total)*100);
      } else if (data?.type == 4 ) {
        if (data?.status == ApiStatus.created) {
          this.uploadResponse = data.body;
          this.disableProcesed = false;
        }

      }
    }, error: err => {
      const error = err?.error;
      this.errorHandler(error);
    }});
    // this.step = 'detail';
  }

  errorHandler(error: any) {
    if (typeof error === 'string' ) {
      if (error!== '') {
        this._toast.error(error);
      }
    } else {
      for (const key in error) {
        if (Object.prototype.hasOwnProperty.call(error, key)) {
          const element = error[key];
          let data = '';
          if (typeof element === 'string' ) {
            data = element;
          } else {
            element.forEach((elementValue:string) => {
              data =  `${data} ${elementValue}`;
            });
          }
          if (data!== '') {
            this._toast.error(data);
          }
        }
      }
    }
  }

  addAllIndicators(indicatorId: { id: string; cols: number; rows:  number; y:  number; x:  number; key: string; type: string; indicatorId: string;}) {
    this.cancelUploads = false;
    this.loader = true;
    this._prepCommonService.processActive.set(true);
    this._prepCommonService.processStatus.next(null);
    this._store.dispatch(loadNode[loadCreateConnectionName]({id: indicatorId?.indicatorId ?? '', earseData: false, typeData: connectionType.ifp }));
    const perpStatus = this._prepCommonService.processStatus.subscribe(data=> {

      if (this.cancelUploads) {
        perpStatus?.unsubscribe();
        return;
      }
      if (data?.workflow_status === workFlowState.completed) {
        this._prepCommonService.isShowPreview = true;
        this._prepCommonService.disableSidebar.set(false);
        this.step = 'detail';
        this.loader = false;
        this._store.dispatch(loadNode[loadCreateNodeSuccessUnionCreate]({config: {}}));
        perpStatus.unsubscribe();
      }  else if (data?.workflow_status === workFlowState.error) {
        this._prepCommonService.isShowPreview = false;
        this.loader = false;
        this._prepCommonService.disableSidebar.set(false);
        this._store.dispatch(loadNode[loadCreateConnectionRemoveName]({objectId: this.objectIdData}));
        this.process = true;
        perpStatus.unsubscribe();
      }
    });
  }

  selectColumn(item: any, dropdown: any[], index:number) {
    dropdown[index].selectedDataType = item.dataType;
  }

  onProceed() {
    const mapping: {
      source:  string;
      target: string;
    }[] = [];
    this.formControl.value.array.forEach((element : {
      name: string; type: string;
                  options: {name: string}[];
                  selectedValue: {name: string}
    }) => {
      if (element.selectedValue.name !== 'None') {
        mapping.push(  {
          source: element.name,
          target: element.selectedValue.name
        });
      }

    });
    this.loader = true;
    this._prepCommonService.processActive.set(true);
    this._prepCommonService.processStatus.next(null);
    this._store.dispatch(loadNode[loadCreateNodeSuccessUnionUpdate]({objectId: this.unionNode, config: {
      type: 'manual_mapping',
      mapping: mapping}}));
    const prepSubs = this._prepCommonService.processStatus.subscribe(data=> {
      if (data?.workflow_status === workFlowState.completed) {
        this._prepCommonService.isShowPreview = true;
        this.loader = true;
        this._prepCommonService.disableSidebar.set(false);
        prepSubs.unsubscribe();
        this._router.navigate(['analytics/data-preparation/add-column']);
        this.process = true;
      } else if (data?.workflow_status === workFlowState.error) {
        this._prepCommonService.isShowPreview = false;
        this.loader = false;
        this._prepCommonService.disableSidebar.set(false);
        this._store.dispatch(loadNode[loadCreateNodeSuccessUnionRemove]({ objectIdData: this.objectIdData,  objectIdUnion: this.unionNode}));
        prepSubs.unsubscribe();
        this.process = true;
      }
    });
  }

  cancelImport() {
    this.loader = false;
    this.cancelUploads = true;
    this._prepCommonService.uploadedFileResponse.set(null);
    this.step = '';
    this._store.dispatch(loadNode[loadCreateConnectionRemoveName]({objectId: this.objectIdData}));
  }


  callUpendColumn() {
    this.subs.add(
      this._store.select(selectNodePrepResponse).subscribe(dataState => {
        if (dataState.currentStatus === prepStatusStore.datasetAdd) {
          const nodeLength = dataState.data.nodes.length;
          const lastValue = dataState.data.nodes[nodeLength - 1];
          this.objectIdData = lastValue.object_id;
        }
        if (dataState.currentStatus === prepStatusStore.unionAdded ) {
          const nodeLength = dataState.data.nodes.length;
          const lastValue = dataState.data.nodes[nodeLength - 1];
          const lastValue1 = dataState.data.nodes[nodeLength - 2];
          const lastValue2 = dataState.data.nodes[nodeLength - 3];
          const controller = this.formControl.controls['array'];
          this.unionNode = lastValue.object_id;
          this.objectIdData = lastValue1.object_id;
          this.subs.add(
            this._prepCommonService.postMethodRequest(`${prepsApiEndpoints.selectNode}${lastValue.object_id}${prepsApiEndpoints.coloumUpstrem}`, dataState.data).subscribe((dataColumn: Record<string, ColumnUpstream>)=> {
              this.dataColumn  = dataColumn[lastValue1.object_id];
              this.dataColumn2  = dataColumn[lastValue2.object_id];
              this.newColumns = dataColumn[lastValue1.object_id].columns_1;
              this.exisitingColumns  = dataColumn[lastValue2.object_id].columns_1;
              this.exisitingColumns.forEach(exisitngColumn => {
                const formGroup: any =  new FormGroup({
                  name: new FormControl(exisitngColumn.name),
                  type: new FormControl(exisitngColumn.type),
                  options: new FormArray([new FormControl({name: 'None'})]),
                  selectedValue: new FormControl({name: 'None'})
                } );
                this.newColumns.forEach(newColumn => {
                  if (exisitngColumn.type === newColumn.type)  {
                    formGroup.controls['options'].push(new FormGroup({name: new FormControl(newColumn.name)}));
                  }
                });
                controller.push(formGroup);
              });
            } ));
        }
      })
    );
  }

  deleteFile() {
    this.downloadSubscribe?.unsubscribe();
    if (this.uploadResponse) {
      this.deleteLibrary(this.uploadResponse.id ?? '');
    }
    this.disableProcesed = true;
  }


  deleteLibrary(id: string) {
    this.subs.add(
      this._prepCommonService.getDeleteRequest(`${prepsApiEndpoints.libraryDelete}${id}`).subscribe({ next: () => {
        this._toast.success('File deleted successfully!');
      }, error: err => {
        const error = err?.error;
        this.errorHandler(error);
      }})
    );
  }

  onProceedInput() {
    this.loader = true;
    this._prepCommonService.processActive.set(true);
    this._prepCommonService.processStatus.next(null);
    this._store.dispatch(loadNode['[prepComponent]CreateConnection']({id: this.uploadResponse?.id ?? '', earseData: false}));
    const perpStatus = this._prepCommonService.processStatus.subscribe(data=> {

      if (data?.workflow_status === workFlowState.completed) {
        this._prepCommonService.isShowPreview = true;
        this._prepCommonService.disableSidebar.set(false);
        this.step = 'detail';
        this.loader = false;
        this._store.dispatch(loadNode[loadCreateNodeSuccessUnionCreate]({config: {}}));
        perpStatus.unsubscribe();
      } else if (data?.workflow_status === workFlowState.error) {
        this._store.dispatch(loadNode[loadCreateConnectionRemoveName]({objectId: this.objectIdData}));
      }
    });
  }


  ngOnDestroy(): void {
    if ( !this.process) {
      this._store.dispatch(loadNode[loadCreateNodeSuccessUnionRemove]({ objectIdData: this.objectIdData,  objectIdUnion: this.unionNode}));
    }
    this.subs.unsubscribe();
  }
}
