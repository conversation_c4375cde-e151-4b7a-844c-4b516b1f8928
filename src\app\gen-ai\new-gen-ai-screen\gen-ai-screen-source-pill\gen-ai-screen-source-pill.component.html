
@if (sourceKeys().length > 0) {
<div class="ifp-gen-ai-screen-source-pill" (click)="sourceClick.emit({keys: sourceKeys(), message: message()})">
  <div class="ifp-gen-ai-screen-source-pill__icon-wrapper">
    @for (item of sourceKeys(); track item) {
      @if (item === 'sv_hyperlink') {
        <div class="ifp-gen-ai-screen-source-pill__icon">
             <em class="ifp-icon ifp-icon-verifyed-tick ifp-gen-ai-screen-source-pill__sv-icon"></em>
    </div>
      } @else if(this.message()?.data?.related_sources?.[item] && this.message()?.data?.related_sources?.[item]?.at(0)?.url_icon) {
    <div class="ifp-gen-ai-screen-source-pill__icon">
      <img [src]="this.message()?.data?.related_sources?.[item]?.at(0)?.url_icon" class="ifp-gen-ai-screen-source-pill__image" />
    </div>
      }

    }

  </div>

   <span class="ifp-gen-ai-screen-source-pill__source">
        {{'Sources' | translate}}
    </span>
    <em class="ifp-icon ifp-icon-right-arrow ifp-gen-ai-screksen-source-pill__arrow"></em>
</div>
}
