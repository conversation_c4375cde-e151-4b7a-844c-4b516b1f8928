@use "../../../assets/ifp-styles/abstracts/index" as *;

.ifp-byn-db {
  padding: $spacer-6 $spacer-0;

  &__head {
    font-weight: $fw-bold;
    font-size: $ifp-fs-13;
    margin-bottom: $spacer-2;
  }


  &__users-wrapper {
    display: flex;
    justify-content: space-between;
    margin: $spacer-0 (-$spacer-3);
  }

  &__user-overview-card {
    display: block;
    background-color: $ifp-color-section-white;
    border-radius: 20px;
    padding: $spacer-4;
    width: calc(25% - (2 * $spacer-3));
    margin: $spacer-0 $spacer-3;
  }

  &__overview-card {
    display: block;
    // background-color: $ifp-color-section-white;

    &--transparent {
      background-color: transparent;
      padding: $spacer-0;
    }

    &--66 {
      width: calc(66.66% - (2 * $spacer-3));
    }

    &--100 {
      width: calc(100% - (2 * $spacer-3));
    }
  }

  &__users-title {
    font-weight: $fw-semi-bold;
    font-size: $ifp-fs-6;
    margin-bottom: $spacer-3;
  }

  &__users-sub-title {
    font-weight: $fw-semi-bold;
    font-size: $ifp-fs-4;
    margin-bottom: $spacer-3;
  }

  &__calender {
    position: relative;
    top: 1.5px;
  }

  &__peak-items {
    width: calc(100% - ($spacer-4));
    // margin: $spacer-5 $spacer-3 $spacer-0;

    &--flex {
      display: flex;
    }

  }

  &__indicator-progress-item {
    display: flex;
    flex-direction: column;
    width: calc(70% - (2 * $spacer-3));
    margin: $spacer-5 $spacer-3 $spacer-0;

    &:first-of-type {
      width: calc(30% - (2 * $spacer-3));
    }
  }

  &__head-wrapper {
    display: flex;
    justify-content: space-between;
    margin: $spacer-5 $spacer-3 $spacer-0;
  }

  &__picker {
    width: 100%;
    max-height: 0;
    opacity: 0;
    visibility: hidden;
    overflow: hidden;
    position: absolute;
    left: 0;
    bottom: (-$spacer-2);
  }

  &__picker-badge {
    padding: $spacer-2 $spacer-3;
    background-color: $ifp-color-section-white;
    border-radius: 5px;
    border: 1px solid $ifp-color-grey-7;
    display: inline-block;
  }

  &__picker-wrapper {
    display: flex;
    align-items: flex-end;
    // align-items: center;
    margin: $spacer-0 (
      -$spacer-3
    );
}

&__self-service {
  width: 100%;
  height: 100%;
  margin: $spacer-5 $spacer-3 $spacer-0;
}

&__map,
&__group-list {
  width: 100%;
  margin: $spacer-5 $spacer-0;
  display: flex;
  flex-direction: column;
}

&__main-head {
  text-align: center;
  margin-bottom: $spacer-5;
}

&__tag-group {
  margin-top: $spacer-4;
}

&__tab-wrapper {
  margin-bottom: $spacer-5;
}

&__tab-wrapper-inner {
  display: flex;
  justify-content: center;
}

&__user-access-list {
  display: block;
  background-color: $ifp-color-section-white;
  padding: $spacer-6 $spacer-4 $spacer-4;
  border-radius: 0 0 20px 20px;
  position: relative;

  &::before {
    content: "";
    width: 100%;
    height: 40px;
    background-image: linear-gradient($ifp-color-blue-1, $ifp-color-section-white);
    background-size: 100% 250%;
    background-position-y: 100%;
    background-repeat: no-repeat;
    position: absolute;
    top: 0;
    left: 0;
  }
  &__user-access-list {
    display: block;
    background-color: $ifp-color-section-white;
    padding: $spacer-6 $spacer-4 $spacer-4;
    border-radius: 0 0 20px 20px;
    position: relative;
    &::before {
      content: "";
      width: 100%;
      height: 40px;
      background-image: linear-gradient($ifp-color-blue-1, $ifp-color-section-white);
      background-size: 100% 250%;
      background-position-y: 100%;
      background-repeat: no-repeat;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
}

&__user-overview {
  margin-bottom: $spacer-4;
}

&__active-entities-wrapper,
&__total-card {
  padding: $spacer-4;
  border-radius: 20px;
  background-color: $ifp-color-section-white;
  margin: $spacer-5 $spacer-3 $spacer-0;
}

&__active-entities-wrapper {
  width: calc(33.33% - (2 * $spacer-3));
}

&__total-card {
  display: flex;
}

&__total-item {
  width: 50%;
  margin-inline-end: $spacer-5;

  &::before {
    content: none;
  }

  &:last-child {
    margin-inline-end: $spacer-0;
  }

  &--grey {
    padding: $spacer-4;
    background-color: $ifp-color-pale-grey-50;
    border-radius: 12px;
  }
}


&__domain-insight {
  width: 100%;
}

&__download-inner {
  display: flex;
  margin: $spacer-0 (
    -$spacer-3
  );
}

&__download-card {
  display: block;
  width: calc(50% - (2 * $spacer-3));
  margin: $spacer-0 $spacer-3;
}

&__toggle-wrapper {
  margin: $spacer-0 $spacer-2;
}

&__toggle-label {
  margin-inline-end: $spacer-2;
}

&__sub-head {
  font-size: $ifp-fs-4;
  color: $ifp-color-secondary-grey;
  text-align: center;
  margin-bottom: $spacer-2;
}

&__user-count-txt {
  color: $ifp-color-black;
  font-size: $ifp-fs-6;
  font-weight: $fw-semi-bold;
  margin-bottom: $spacer-3;
}

&__user-detail-card-wrapper {
  margin-inline-start: $spacer-5;
  width: calc(66.66% - (2 * $spacer-3));
}

&__user-total-wrapper {
  display: flex;
  margin: $spacer-5 $spacer-0;
  flex-wrap: wrap;
  margin-inline-start: $spacer-3;
}

&__user-table-wrapper {
  display: block;
  background-color: $ifp-color-white;
  padding: $spacer-5;
  border-radius: 20px;
}

&__user-table {
  display: block;
  max-height: 315px;
  padding-inline-end: $spacer-2 + 2px;
  @include ifp-scroll($ifp-color-dropdown-select, $ifp-color-grey-1, 10px, 10px);

  &--80 {
    width: calc(75% - (2 * $spacer-3));
    max-height: 300px;
    margin: $spacer-0 $spacer-3;
  }
}

&__user-tab {
  display: inline-block;
  border: 1px solid $ifp-color-grey-13;
  border-radius: 8px;
  overflow: hidden;
  &--entity {
    margin-bottom: $spacer-3;
  }
}

&__users-overview-wrapper {
  width: 100%;
  background-color: $ifp-color-white;
  padding: $spacer-5;
  border-radius: 20px;
  margin: $spacer-5 $spacer-0;
}

&__user-sub-table-wrapper {
  border: 1px solid $ifp-color-grey-13;
  padding: $spacer-4;
  border-radius: 20px;
}

&__user-pagination {
  margin-top: $spacer-3;
}

&__data-classification-wrapper {
  width: 100%;
  background-color: $ifp-color-white;
  padding: $spacer-5;
  border-radius: 20px;
}

&__data-classification-action-wrapper {
  display: flex;
  margin: $spacer-0 (-$spacer-2) $spacer-4;
  align-items: center;
  .ifp-byn-db {
    &__entity-dropdown,
    &__acc-level-sec {
      margin: $spacer-0 $spacer-2;
    }
  }
}

&__active-entities {
  display: block;
}

&__active-inactive-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin: $spacer-0 (-$spacer-3);
}

&__user-count-wrapper {
  width: 33.33%;
}

&__user-monthly-split {
  width: calc(66.66% - $spacer-4);
  padding-inline-start: $spacer-4;
  border-inline-start: 1px solid $ifp-color-grey-7;
  margin-inline-start: $spacer-4;
}

&__top-wrapper {
  display: flex;
  margin-top: $spacer-4;
}

&__top-usage-wrapper,
&__peak-time-wrapper {
  width: 100%;
}

&__top-usage-wrapper {
  background-color: $ifp-color-white;
  border-radius: 20px;
  padding: $spacer-4;
  margin-inline-end: $spacer-4;
}

&__peak-time-wrapper {
  background-color: $ifp-color-white;
  border-radius: 20px;
  padding: $spacer-4;
}

&__top-tab {
  display: inline-block;
}

&__indicator-table-wrapper,
&__tools-table-wrapper,
&__download-card {
  background-color: $ifp-color-white;
  border-radius: 20px;
  padding: $spacer-4;
  margin-top: $spacer-5;
}

&__entity-user-type-wrapper {
  background-color: $ifp-color-white;
  border-radius: 20px;
  padding: $spacer-4;
  margin-bottom: $spacer-4;
}
&__entity-dropdown {
  width: 33.33%;
}
&__users-type-wrapper {
  display: flex;
  align-items: flex-end;
  margin: $spacer-0 (-$spacer-2) $spacer-5;
  margin-bottom: $spacer-5;
  .ifp-byn-db {
    &__entity-dropdown {
      width: calc(20% - (2 * $spacer-2));
      margin: $spacer-0 ($spacer-2);
    }
    &__user-tab {
      margin-inline-start: auto;
    }
  }
}

&__date-wrapper {
  margin: $spacer-0 $spacer-2;
  position: relative;
}

&__date-picker {
  width: 100%;
  min-width: 200px;
}

&__date-title {
  margin: $spacer-2 $spacer-0;
  color: $ifp-color-grey-2;
  font-size: $ifp-fs-2;
  font-weight: $fw-semi-bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}


&__tab {
  &--main {
    &::ng-deep {
      .ifp-pills-tab__item {
        min-width: 320px;
      }
    }
  }
}

&__acc-level-sec {
  min-width: 33.33%;
}

&__input-title {
  font-size: $ifp-fs-2;
  color: $ifp-color-grey-2;
  margin: $spacer-2 $spacer-0;
}

&__level-label {
  font-size: $ifp-fs-3;
  font-weight: $fw-medium;
  padding-inline-start: $spacer-5;
  position: relative;
  cursor: pointer;
  &::before,
  &::after {
    content: "";
    position: absolute;
    top: 50%;
    border-radius: 50%;
    transform: translateY(-50%);
    transition: 0.3s;
  }
  &::before {
    width: 24px;
    height: 24px;
    border: 2px solid $ifp-color-grey-1;
    left: 0;
  }
  &::after {
    width: 14px;
    height: 14px;
    background-color: $ifp-color-grey-1;
    left: 5px;
  }
  &:hover {
    &::before {
      border-color: $ifp-color-hover-blue;
    }
    &::after {
      background-color: $ifp-color-hover-blue;
    }
  }
}
&__level-tab {
  display: flex;
  justify-content: space-between;
  border: 1px solid $ifp-color-grey-7;
  border-radius: 10px;
  padding: $spacer-3 $spacer-0;
}
&__level-tab-item {
  margin: $spacer-0 $spacer-4;
}
&__level-radio {
  display: none;
  &:checked + .ifp-byn-db__level-label {
    pointer-events: none;
    &::before {
      border-color: $ifp-color-active-blue;
    }
    &::after {
      background-color: $ifp-color-active-blue;
    }
  }
}

&__switch-platform {
  margin-bottom: 1px;
}
// &__user-overview-action-wrapper {
//   display: flex;
//   justify-content: space-between;
//   align-items: flex-end;
//   margin-bottom: $spacer-3;
// }

&__top-action-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin-bottom: $spacer-3;
}
&__tab-outer-wrapper {
  margin-bottom: $spacer-3;
}
  &__setting {
    min-width: 250px;
  }

}

:host-context(.ifp-dark-theme) {
  .ifp-byn-db {
    &__picker-badge {
      background-color: $ifp-color-grey-7;
    }
  }
}

// Import shared download styles
@import '../../../assets/ifp-styles/components/download-button';
:host ::ng-deep .ifp-btn--black {
  font-size: 1.6rem;
}
::ng-deep .ifp-byn-db__tab-outer-wrapper {
  display: flex;
}
.ifp-byn-db__tab-outer-wrapper{
  display: flex;
  justify-content: space-between;
}
.ifp-byn-db__top-action-wrapper-inner{
  display: flex;
}


:host-context(.ifp-header__fixed) {
  .ifp-byn-db {
    &__tab-wrapper {
      background-color: $ifp-color-grey-bg;
      position: sticky;
      top: $ifp-header-height-sticky;
      left: 0;
      z-index: 200;
      @include slide-down-below-header();
    }
    &__tab {
      &::ng-deep {
        .ifp-pills-tab--lg {
          .ifp-pills-tab__item {
            font-size: $ifp-fs-4;
            padding: $spacer-2;
            min-width: 280px;
          }
        }
      }
    }
  }
}

:host::ng-deep {
  .ifp-byn-db {
    &__setting-icon {
      position: relative;
      top: 1.5px;
    }

    &__overview-card {
      .ifp-user-card-bar {
        padding: $spacer-0;
      }
    }

    &__module-heading {
      font-size: $ifp-fs-9;
      font-weight: $fw-semi-bold;
      margin-bottom: $spacer-3;
    }

    &__card-heading {
      font-size: $ifp-fs-6;
      font-weight: $fw-semi-bold;
    }

    &__pagination {
      display: flex;
      justify-content: center;
      margin-top: auto;
    }

    &__user-detail-card-wrapper,
    &__users-overview-wrapper,
    &__data-classification-wrapper {
      .ifp-pills-tab {
        max-width: 30%;
      }
    }

    .ifp-pills-tab {
      padding: 2px;

      &__item {
        font-size: $ifp-fs-3;
      }
    }

    // &__user-overview-table-wrapper {
    //   .ifp-data-table {
    //     border: 1px solid $ifp-color-grey-13;
    //     padding: $spacer-2;
    //     border-radius: 8px;
    //   }
    // }
    &__top-tab {
      .ifp-pills-tab {
        border: 1px solid $ifp-color-grey-13;
        border-radius: 8px;
        padding: $spacer-1;
      }
    }

    &__indicator-table {
      display: block;
      height: 400px;
      @include ifp-scroll-y(transparent, $ifp-color-grey-7, 8px, 8px);
    }

    &__entity-dropdown {
      .ifp-dropdown {
        min-width: 100%;
        max-width: none;
        height: 48px;
        
        &__title-text {
          font-weight: $fw-regular;
        }
        &__selected {
          padding: $spacer-3;
          height: 48px;
          display: flex;
          align-items: center;
        }
      }

      .ifp-panel-dropdown {
        &__label {
          font-size: $ifp-fs-2;
          margin: $spacer-2 $spacer-0;
        }
        &__select-box {
          padding: ($spacer-2 + 2px) $spacer-3 !important;
          height: 48px;
          display: flex;
          align-items: center;
        }
      }

      
      &.ifp-search-filter__tools-item {
        ::ng-deep .ifp-search-box {
          height: 48px;
          max-width: 48px;
          
          &__btn {
            padding: 4px 15px !important;
          }

          &__input {
            height: 48px;
            display: flex;
            align-items: center;
          }

          &--active,
          &:hover,
          &:focus-within {
            max-width: 100%;
            width: 100%;
          }
        }
      }

      &.ifp-byn-db__user-tab {
        ::ng-deep .ifp-pills-tab {
          height: 48px;
          display: flex;
          align-items: center;
        }
      }
    }
    &__setting {
      .ifp-panel-dropdown {
        &__label {
          font-size: $ifp-fs-2;
          margin: $spacer-2 $spacer-0;
        }
        &__selected {
          font-size: $ifp-fs-3;
        }
      }
    }
    &__switch-platform {
      .ifp-tab__item {
        padding: $spacer-1 $spacer-3 !important;
      }
      .ifp-category-label__classification {
        margin: 0;
      }
    }
    .ifp-data-table {
      &__value {
        max-width: none;
      }
      &__no-data {
        width: 100%;
      }
    }
  }

  .ifp-data-table {
    &__col {
      &--dynamic {
        display: flex;
      }
    }

    &__dynamic-cmp {
      margin: $spacer-0 $spacer-2;
    }
  }
}

.ifp-dark-theme :host::ng-deep {
  .ifp-panel-dropdown__select-icon {
    background-color: $ifp-color-grey-7 !important;
  }
}

:host-context([dir="rtl"]) {
  .ifp-byn-db {
    &__level-label {
      &::before,
      &::after {
        left: auto;
      }
      &::before {
        right: 0;
      }
      &::after {
        right: 5px;
      }
    }
  }
}

@include desktop-xl {
  .ifp-byn-db {
    &__user-overview-card {
      width: calc(33.33% - (2 * $spacer-3));
    }
    &__user-table {
      &--80 {
        width: calc(75% - (2 * $spacer-3));
      }
    }
    &__user-count-wrapper {
      width: 40%;
    }

    &__user-monthly-split {
      width: calc(60% - $spacer-4);
    }
  }
}

@include desktop-sm {
  .ifp-byn-db {
    &__user-overview-card {
      width: calc(33.33% - (2 * $spacer-3));
    }
    &__user-table {
      &--80 {
        width: calc(75% - (2 * $spacer-3));
      }
    }
    &__overview-card {
      &--66 {
        width: calc(100% - (2 * $spacer-3));
      }
    }
    &__active-entities-wrapper {
      width: calc(100% - (2 * $spacer-3));
    }
    &__top-usage-wrapper {
      margin-bottom: $spacer-4;
      margin-inline-end: $spacer-0;
    }
    &__top-wrapper {
      display: block;
    }
  }
}

@include tablet {
  .ifp-byn-db {
    // &__overview-card {
    //   width: calc(50% - (2 * $spacer-3));
    // }

    &__indicator-progress-item {

      width: calc(100% - (2 * $spacer-3));

      &:first-of-type {
        width: calc(100% - (2 * $spacer-3));
      }
    }

    &__peak-items {
      width: calc(100% - (2 * $spacer-3));
    }
  }
}

@include mobile {
  .ifp-byn-db {
    &__head-wrapper {
      flex-wrap: wrap;
    }

    &__overview-card {
      width: calc(100% - (2 * $spacer-3));
    }

    &__indicator-progress-item {
      width: calc(100% - (2 * $spacer-3));

      &:first-of-type {
        width: calc(100% - (2 * $spacer-3));
      }
    }

    &__peak-items {
      width: calc(100% - (2 * $spacer-3));
    }

  }
}

::ng-deep .ifp-modal__visits-custom {
  max-width: 1150px !important;
  width: 100% !important;
}
