import { cloneDeep } from 'lodash';
import { Component, Element<PERSON>ef, inject, linked<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, signal, ViewChild, WritableSignal } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IfpProductLibraryClassificationCardComponent, LibraryClassificationCard } from './ifp-product-library-classification-card/ifp-product-library-classification-card.component';
import { IfpTabComponent } from '../ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';
import { IfpProductLibraryToolsComponent } from './ifp-product-library-tools/ifp-product-library-tools.component';
import { IfpDropdownComponent } from '../ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { IfpProductLibraryIndicatorCardComponent } from './ifp-product-library-indicator-card/ifp-product-library-indicator-card.component';
import { PaginationComponent } from '../ifp-widgets/ifp-molecules/pagination/pagination.component';
import { SubSink } from 'subsink';
import { DomainsService } from '../core/services/domains/domains.service';
import { ToasterService } from '../core/services/tooster/ToastrService.service';
import { classifications, cntTypes } from '../core/constants/domain.constants';
import { Store } from '@ngrx/store';
import { selectCategoryResponse } from '../domains/store/domain.selector';
import { dataClassifications } from '../core/constants/product-library.constants';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IndicatorParams, ScreenerFilter, ScreenerService } from '../core/services/screener/screener.service';
import { productLibraryApi } from './ifp-product-library.constants';
import { IfpCardLoaderComponent } from '../ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { ApiService } from '../core/services/api.service';
import { loaderType } from '../core/constants/loader.constants';
import { DomainsNav, ProductLibraryTreeChartComponent } from './product-library-tree-chart/product-library-tree-chart.component';
import { ActivatedRoute, Router } from '@angular/router';
import { Subdomain, SubthemeTree } from '../core/interface/domains.interface';
import { IfpNoDataComponent } from '../ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { Location } from '@angular/common';
import { IfpButtonComponent } from '../ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from '../core/constants/button.constants';


@Component({
  selector: 'ifp-product-library',
  imports: [TranslateModule, IfpProductLibraryClassificationCardComponent, IfpTabComponent, IfpProductLibraryToolsComponent, IfpDropdownComponent, IfpProductLibraryIndicatorCardComponent, PaginationComponent, FormsModule, ReactiveFormsModule, IfpCardLoaderComponent, ProductLibraryTreeChartComponent, IfpNoDataComponent, IfpButtonComponent],
  templateUrl: './ifp-product-library.component.html',
  styleUrl: './ifp-product-library.component.scss'
})
export class IfpProductLibraryComponent implements OnInit, OnDestroy {


  private readonly _translate: TranslateService = inject(TranslateService);
  private readonly _domainService: DomainsService = inject(DomainsService);
  private readonly _toasterService: ToasterService = inject(ToasterService);
  private readonly _store: Store = inject(Store);
  private readonly _screenerService: ScreenerService = inject(ScreenerService);
  private readonly _apiService: ApiService = inject(ApiService);
  private readonly _formBuilder: FormBuilder = inject(FormBuilder);
  private readonly _activatedParams: ActivatedRoute = inject(ActivatedRoute);
  private readonly _router: Router = inject(Router);
  private readonly _location: Location = inject(Location);

  public offsetPage = 1;
  public offset = 1;
  public limit = 10;
  public total = 0;
  public selectedClassificationId = 0;
  public loaderTypes = loaderType;
  public classificationCards: LibraryClassificationCard[] = [];
  public classification = classifications;
  public subs: SubSink = new SubSink();
  public classificationLoader: boolean = true;
  public layoutViewTab: LayoutTab[] = [
    {
      name: 'Tree View',
      key: 'tree',
      iconClass: 'ifp-icon-tree-view'
    },
    {
      name: 'List View',
      key: 'list',
      iconClass: 'ifp-icon-list-dot'
    }
  ];

  public sort = [
    {
      label: this._translate.instant('By Value Desc'),
      value: 'desc',
      key: 'byValue',
      icon: 'ifp-rotate-90 ifp-icon-rightarrow'
    },
    {
      label: this._translate.instant('By Value Asc'),
      value: 'asc',
      key: 'byValue',
      icon: 'ifp-rotate-270 ifp-icon-rightarrow'
    },
    {
      label: this._translate.instant('Sort by A-Z'),
      value: 'asc',
      key: 'alphabetical',
      icon: 'ifp-rotate-270 ifp-icon-rightarrow'
    },
    {
      label: this._translate.instant('Sort by Z-A'),
      value: 'desc',
      key: 'alphabetical',
      icon: 'ifp-rotate-90 ifp-icon-rightarrow'
    }
  ];

  public sortValue = {
    label: 'A-Z',
    value: 'asc',
    key: 'alphabetical'
  };

  public selectedTab: number = 0;
  public classificationData: Classification[] = [];
  public treeChartData = linkedSignal(() => this.classificationData[0]);
  public treeViewLoader: boolean = true;

  public indicatorProducts: Classification[] = [];
  public dataClassificationList: LibraryDataClassification[] = dataClassifications;
  public domainData: WritableSignal<Classification[]> = signal([]);
  public selectedDomainList: WritableSignal<DomainsNav[]> = signal([]);
  public themeList: WritableSignal<Subdomain[]> = signal([]);
  public subThemeList: WritableSignal<SubthemeTree[]> = signal([]);
  public categoryList: WritableSignal<Subdomain[]> = signal([]);
  // public selectedDomain = new FormControl();
  // public selectedTheme: WritableSignal<Classification | null> = signal(null);
  public selectedSubTheme: WritableSignal<SubthemeTree | null> = signal(null);
  public selectedDataClassification: WritableSignal<LibraryDataClassification | null> = signal(dataClassifications[0]);
  public selectedCategory: WritableSignal<Subdomain | null> = signal(null);
  public screenerFilters: WritableSignal<ScreenerFilter[]> = signal([]);
  // public selectedProduct: WritableSignal<LibraryClassification | null> = signal(null)

  public screenerfilteredValue: WritableSignal<any[]> = signal([]);
  public nodesList: WritableSignal<any[]> = signal([]);
  public selectedClassificationCardIndex: number = 0;
  public isFiltersActive: WritableSignal<boolean> = signal(false);
  public buttonClass = buttonClass;

  @ViewChild('listTop') listTop?: ElementRef<HTMLElement>;

  public form = new FormGroup({});
  public domainForm!: FormGroup;

  public domainsLoader: boolean = true;
  // public defaultDropdownsForm: FormGroup = new FormGroup({});

  constructor() {
    this.subs.add(this._activatedParams.queryParams.subscribe((params) => {
      const tabIndex = this.layoutViewTab.findIndex((tab: LayoutTab) => tab.key === params['tab']);
      this.selectedTab = tabIndex && tabIndex >= 0 ? tabIndex : 0;
    }));
  }

  ngOnInit(): void {
    this.domainForm = this._formBuilder.group({
      selectedProduct: new FormControl(''),
      selectedDomain: new FormControl(''),
      selectedTheme: new FormControl(''),
      selectedCategory: new FormControl('')
    });
    this.getClassification(this.selectedClassificationId);
    // this.getinitaialList();
    this.initTreeView();
    // this.getDomainData();
  }

  // initTreeView() {
  //   this.treeViewLoader = true;
  //   this.subs.add(
  //     this._apiService.getMethodRequest(productLibraryApi.treeView).subscribe({
  //       next: (data) => {
  //         const result = cloneDeep(data);
  //         const dataClone = cloneDeep(data);
  //         result.forEach((item: Classification, index: number) => {
  //           item.domains.forEach((domain: DomainsNav, i: number) => {
  //             if (domain.subdomains?.length === 1 && domain.subdomains[0].name.toLowerCase() === 'unmaped') {
  //               dataClone[index].domains.splice(i, 1);
  //             }
  //           });
  //         });
  //         this.classificationData = dataClone;
  //         this.treeViewLoader = false;
  //       },
  //       error: (error) => {
  //         this.treeViewLoader = false;
  //         this._toasterService.error(error?.error?.message);
  //       }
  //     })
  //   );
  // }

  initTreeView() {
    this.treeViewLoader = true;
    this.subs.add(
      this._apiService.getMethodRequest(productLibraryApi.treeView).subscribe({
        next: (data) => {
          const dataClone = cloneDeep(data);

          dataClone.forEach((classification: Classification) => {
            classification.domains = classification.domains
              .map((domain: DomainsNav) => {
                domain.subdomains = this.filterUnmappedSubdomains(domain.subdomains);
                return domain;
              })
              .filter((domain: DomainsNav) => (domain.subdomains?.length ?? 0) > 0);
          });

          this.classificationData = dataClone;
          this.treeViewLoader = false;
          this.getDomainData();
        },
        error: (error) => {
          this.treeViewLoader = false;
          this._toasterService.error(error?.error?.message);
        }
      })
    );
  }

  filterUnmappedSubdomains(subdomains: any[] | undefined): any[] {
    if (!subdomains) {
      return [];
    }
    return subdomains.filter(this.isMappedSubdomain);
  }

  isMappedSubdomain(sub: any): boolean {
    return sub.name.toLowerCase() !== 'unmaped';
  }

  getDomainData() {
    this.domainData.set(this.classificationData);
    this.indicatorProducts = cloneDeep(this.classificationData);
    this.domainForm.controls['selectedProduct'].setValue(this.indicatorProducts[this.selectedClassificationCardIndex]);
    const productValue = this.domainForm.controls['selectedProduct'].value;
    if (!productValue) {
      return;
    }

    // Reset to default state for the selected product
    this.selectedDataClassification.set(this.dataClassificationList[0]);
    this.screenerFilters.set([]);
    this.screenerfilteredValue.set([]);
    if (this.form && Object.keys(this.form.controls).length > 0) {
      this.form.reset();
      this.form = new FormGroup({});
    }

    // Immediately disable reset button when switching products
    this.isFiltersActive.set(false);

    const selectedProductDomain = this.classificationData.find(x => x.key === productValue?.key)?.domains ?? [];
    this.selectedDomainList.set(selectedProductDomain);
    this.autoSelectOptions();
    this.domainsLoader = false;
  }

  autoSelectOptions() {
    const [firstDomain = null] = this.selectedDomainList();
    this.domainForm.controls['selectedDomain'].setValue(firstDomain);
    const themes = firstDomain?.subdomains ?? [];
    const [firstTheme = null] = themes;
    if (this.domainForm.controls['selectedProduct'].value.key === 'reports') {
      this.categoryList.set(themes);
      this.domainForm.controls['selectedCategory'].setValue(firstTheme);
      this.themeList.set([]);
      this.subThemeList.set([]);
      this.selectedSubTheme.set(null);
    } else {
      this.categoryList.set([]);
      this.selectedCategory.set(null);
      this.domainForm.controls['selectedTheme'].setValue(firstTheme);
      this.themeList.set(themes);
      const subThemes = firstTheme?.subthemes ?? [];
      this.subThemeList.set(subThemes);
      this.selectedSubTheme.set(subThemes[0] ?? null);
    }

    // this.defaultDropdownsForm = this._formBuilder.group({
    //   product: [this.indicatorProducts],
    //   topic: [this.selectedDomainList()],
    //   theme: [this.themeList()],
    //   subTheme: [this.subThemeList()],
    // });

    this.changePagination();
    this.callIndicatorsApi();
  }

  populateFilterOptions() {
    const [firstDomain = null] = this.selectedDomainList();
    const themes = firstDomain?.subdomains ?? [];

    if (this.domainForm.controls['selectedProduct'].value.key === 'reports') {
      this.categoryList.set(themes);
      this.themeList.set([]);
      this.subThemeList.set([]);
    } else {
      this.categoryList.set([]);
      this.themeList.set(themes);
      this.subThemeList.set([]);
    }
  }

  selectProduct(event: any) {
    this.domainForm.controls['selectedProduct'].setValue(event);
    this.selectedClassificationCardIndex = this.classificationCards.findIndex((card: LibraryClassificationCard) => card.key === event.key);
    this.treeChartData.set(this.classificationData[this.selectedClassificationCardIndex]);
    this.clearSelecteion();
    // Reset to default state for the new product
    this.selectedDataClassification.set(this.dataClassificationList[0]);
    this.screenerFilters.set([]);
    this.screenerfilteredValue.set([]);
    if (this.form && Object.keys(this.form.controls).length > 0) {
      this.form.reset();
      this.form = new FormGroup({});
    }

    // Immediately disable reset button when switching products
    this.isFiltersActive.set(false);

    const selectedProduct = this.domainForm.controls['selectedProduct'].value;
    const selectedProductDomain = selectedProduct ? this.domainData().find(x => x.key == selectedProduct.key)?.domains : [];
    if (selectedProductDomain?.length) {
      this.selectedDomainList.set(selectedProductDomain);
      this.autoSelectOptions();
    }
  }

  selectDomain(event: LibraryClassification) {
    this.domainForm.controls['selectedDomain'].setValue(event);
    const themes = event?.subdomains ?? [];
    const firstTheme = themes[0] ?? null;
    if (this.domainForm.controls['selectedProduct'].value.key === 'reports') {
      this.categoryList.set(themes);
      this.domainForm.controls['selectedCategory'].setValue(firstTheme);
    } else {
      this.domainForm.controls['selectedTheme'].setValue(firstTheme);
      this.themeList.set(themes);
      const subThemes = firstTheme?.subthemes ?? [];
      this.subThemeList.set(subThemes);
      this.selectedSubTheme.set(subThemes[0] ?? null);
    }


    this.changePagination();
    this.callIndicatorsApi();
    this.checkFiltersActive();
  }

  selectTheme(event: LibraryClassification, product: string = '') {
    if (product === 'report') {
      this.domainForm.controls['selectedCategory'].setValue(event);
      this.domainForm.controls['selectedTheme'].setValue(null);
      this.themeList.set([]);
    } else {
      this.domainForm.controls['selectedTheme'].setValue(event);
      this.domainForm.controls['selectedCategory'].setValue(null);
      const subThemes = event?.subthemes ?? [];
      this.subThemeList.set(subThemes);
      this.selectedSubTheme.set(subThemes[0] ?? null);
    }
    this.changePagination();
    this.callIndicatorsApi();
    this.checkFiltersActive();
  }

  selectSubTheme(event: SubthemeTree) {
    this.selectedSubTheme.set(event);
    this.changePagination();
    this.callIndicatorsApi();
    this.checkFiltersActive();
  }

  checkScreener() {
    const isScreener = this.domainForm.controls['selectedTheme'].value?.screener;
    if (this.selectedSubTheme()?.screener || isScreener) {
      this.callOfficialIndicatorScreener();
    }
  }

  selectDataClassifiaction(event: LibraryDataClassification) {
    this.selectedDataClassification.set(event);
    this.callIndicatorsApi();
    this.checkFiltersActive();
  }

  clearSelecteion() {
    this.domainForm.controls['selectedDomain'].setValue(null);
    this.selectedDomainList.set([]);
    this.domainForm.controls['selectedTheme'].setValue(null);
    this.domainForm.controls['selectedCategory'].setValue(null);
    this.themeList.set([]);
    this.categoryList.set([]);
    this.selectedSubTheme.set(null);
    this.subThemeList.set([]);
  }

  checkFiltersActive() {
    const formValues = this.domainForm.value;
    const defaults = this.getDefaults();
    const isFirstCard = this.selectedClassificationCardIndex === 0;
    const isFirstProduct = formValues.selectedProduct?.key === this.classificationCards[0]?.key;

    if (isFirstCard && isFirstProduct) {
      const isDomainDefault = !formValues.selectedDomain || formValues.selectedDomain === defaults.domain;
      const isThemeDefault = !formValues.selectedTheme || formValues.selectedTheme === defaults.theme;
      const isCategoryDefault = !formValues.selectedCategory || formValues.selectedCategory === defaults.theme;
      const isSubThemeDefault = !this.selectedSubTheme() || this.selectedSubTheme() === defaults.subTheme;
      const isDataClassificationDefault = this.selectedDataClassification() === defaults.dataClassification;

      if (isDomainDefault && isThemeDefault && isCategoryDefault && isSubThemeDefault && isDataClassificationDefault) {
        const hasScreenerFilters = Object.values(this.form.value).some(value => Array.isArray(value) ? value.length > 0 : !!value);
        this.isFiltersActive.set(hasScreenerFilters);
        return;
      }
    }

    const hasChangedFilters = (formValues.selectedDomain && formValues.selectedDomain !== defaults.domain) || (formValues.selectedTheme && formValues.selectedTheme !== defaults.theme) || (formValues.selectedCategory && formValues.selectedCategory !== defaults.theme) || (this.selectedSubTheme() && this.selectedSubTheme() !== defaults.subTheme) || this.selectedDataClassification() !== defaults.dataClassification;
    const hasScreenerFilters = Object.values(this.form.value).some(value => Array.isArray(value) ? value.length > 0 : !!value);
    this.isFiltersActive.set(hasChangedFilters || hasScreenerFilters);
  }

  private getDefaults() {
    const currentProduct = this.domainForm.value.selectedProduct || this.classificationCards[0];
    const firstDomain = this.selectedDomainList()[0] || null;
    const firstTheme = firstDomain?.subdomains?.[0] || null;

    return {
      product: currentProduct,
      domain: firstDomain,
      theme: firstTheme,
      subTheme: firstTheme?.subthemes?.[0] || null,
      dataClassification: this.dataClassificationList[0]
    };
  }

  resetFilters() {
    // Keep the currently selected card; do not switch to the first card
    const currentProductFromForm = this.domainForm.controls['selectedProduct'].value;
    const currentCard = this.classificationCards[this.selectedClassificationCardIndex];
    const productKey = currentProductFromForm?.key ?? currentCard?.key;
    if (!productKey) {
      return;
    }

    // Clear screener state and form values
    this.screenerFilters.set([]);
    this.screenerfilteredValue.set([]);
    if (this.form) {
      this.form.reset();
      this.form = new FormGroup({});
    }

    // Clear hierarchical selections and pagination
    this.clearSelecteion();
    this.offsetPage = 0;
    this.offset = 1;
    this.nodesList.set([]);
    this.total = 0;

    // Patch form with the current product (Classification object if available)
    const selectedProductObj = this.classificationData.find(x => x.key === productKey) || currentProductFromForm || currentCard;
    this.domainForm.patchValue({
      selectedProduct: selectedProductObj,
      selectedDomain: null,
      selectedTheme: null,
      selectedCategory: null
    });

    this.selectedSubTheme.set(null);
    this.selectedCategory.set(null);
    this.selectedDataClassification.set(this.dataClassificationList[0]);
    [this.themeList, this.subThemeList, this.categoryList].forEach(list => list.set([]));

    // Initialize defaults for the CURRENT card's product
    const productDomains = this.domainData().find(x => x.key === productKey)?.domains ?? [];
    if (productDomains.length) {
      this.selectedDomainList.set(productDomains);
      this.autoSelectOptions();
    } else {
      this.changePagination();
      this.callIndicatorsApi();
    }

    // Disable reset button after operations complete
    setTimeout(() => {
      this.isFiltersActive.set(false);
    }, 0);
  }

  callIndicatorsApi(pageRelated: boolean = false) {
    const isScreener = this.domainForm.controls['selectedTheme'].value?.screener;
    if (isScreener || this.selectedSubTheme()?.screener) {
      if (pageRelated) {
        this.callScreenerNodes();
      } else {
        this.checkScreener();
      }

      return;
    }
    this.screenerFilters.set([]);
    this.callIndicatorNodes();
  }

  callOfficialIndicatorScreener() {
    this.screenerFilters.set([]);
    this.form.reset();
    const isOfficial = this.domainForm.controls['selectedProduct'].value?.key == classifications.officialStatistics;
    const screenerItem = !isOfficial ? this.domainForm.controls['selectedTheme']?.value : this.selectedSubTheme();
    const screenerIndicator = screenerItem?.screenerConfiguration?.screenerIndicator;
    if (!screenerIndicator) {
      return;
    }

    const selectedProductKey = this.domainForm.controls['selectedProduct'].value?.key;
    const cntTypeKey = selectedProductKey !== undefined && selectedProductKey in cntTypes ? cntTypes[selectedProductKey as keyof typeof cntTypes] : '';
    this.subs.add(
      this._domainService.getFilter(cntTypeKey, screenerIndicator).subscribe({
        next: (resp) => {
          this.screenerFilters.set(resp);
          this.setDefefaultselectedFilter();
        },
        error: (err) => this._toasterService.error(err?.error?.message)
      })
    );
  }

  setDefefaultselectedFilter() {
    this.form = new FormGroup({});
    if (this.screenerFilters()?.length) {
      this.screenerfilteredValue.set([]);
      this.screenerFilters().forEach((values) => {
        const valueData = values.default ? [values.default['value']] : [];
        this.form.addControl(values.key, new FormControl(valueData));
        this.screenerfilteredValue().push({ value: valueData, key: values.key });
      });
      this.callScreenerNodes();
    }
  }

  selectScreenerFilter(event: any, key: any) {
    let opts: any = [];
    if (event?.length > 0) {
      opts = event.map((element: any) => element.value);
    }
    if (this.screenerFilters()?.length) {
      const selectedFilterIndex = this.screenerFilters().findIndex((x: { key: any; }) => x.key == key);
      if (selectedFilterIndex >= 0) {
        this.screenerFilters()[selectedFilterIndex].value = opts;
      }
    }
    this.callScreenerNodes();
  }

  onChange(click: string, valueChanged: any, index: number) {
    const formValue: any = this.form.value;
    if (formValue[valueChanged.key].length === valueChanged.items.length) {
      valueChanged.selectAll = true;
      this.screenerFilters()[index].selectAll = true;
    } else {
      valueChanged.selectAll = false;
      this.screenerFilters()[index].selectAll = false;
    }
    if (formValue[valueChanged.key]?.length === 0) {
      this.form.patchValue({ [valueChanged.key]: [valueChanged.default['value']] });
    }
    this.offset = 1;
    this.callScreenerNodes();
    this.checkFiltersActive();
    // this._cdr.detectChanges();
  }

  selectAll(selection: boolean, item: any) {
    item.selectAll = selection;
    if (selection) {
      this.offsetPage = 1;
      this.offset = 1;
      this.callScreenerNodes();
    } else {
      this.form.patchValue({ [item.key]: [item.default['value']] });
      this.callScreenerNodes();
    }
    this.checkFiltersActive();
  }



  async callScreenerNodes() {
    const params = {
      product: this.domainForm.controls['selectedProduct'].value,
      theme: this.domainForm.controls['selectedTheme'].value,
      subTheme: this.selectedSubTheme(),
      screenerFilter: this.screenerFilters(),
      form: this.form,
      limit: this.limit,
      offsetPage: this.offset,
      security: this.selectedDataClassification()?.key === 'all' ? '' : this.selectedDataClassification()?.key
    };
    const nodesList = await this._screenerService.callScreenerNodes(params);
    if (nodesList?.nodes?.length) {
      this.nodesList.set(nodesList?.nodes);
      this.total = nodesList.total;
    } else {
      this.nodesList.set([]);
    }
  }

  async callIndicatorNodes() {
    const params: IndicatorParams = {
      selectedProduct: this.domainForm.controls['selectedProduct'].value,
      selectedDomain: this.domainForm.controls['selectedDomain'].value,
      subTheme: this.selectedSubTheme(),
      page: this.offset,
      limit: this.limit,
      security: this.selectedDataClassification()?.key === 'all' ? '' : this.selectedDataClassification()?.key
    };
    if (params.selectedProduct?.key === this.classification.reports) {
      params.publication_type = this.domainForm.controls['selectedCategory'].value;
    } else {
      params.theme = this.domainForm.controls['selectedTheme'].value;
    }
    const nodesList = await this._screenerService.getIndicatorNodes(params);
    if (nodesList?.nodes?.length) {
      this.nodesList.set(nodesList?.nodes);
      this.total = nodesList.total;
    } else {
      this.nodesList.set([]);
    }
  }



  getClassification(id: number) {
    this.classificationLoader = true;
    const endpoint = `${productLibraryApi.classifications}${id}`;
    this.subs.add(
      this._apiService.getMethodRequest(endpoint).subscribe({
        next: (data) => {
          const classList: LibraryClassificationCard[] = [];
          data.classification.forEach((classification: LibraryClassification) => {
            const cardData = {
              id: classification.id,
              key: classification.key,
              name: classification.name,
              description: classification?.description ?? '',
              count: classification.count,
              image: this.setCardImage(classification.key)
            };
            // if (cardData.key === 'analytical_apps') {
            //   cardData.description = 'Powerful tools that go beyond traditional analysis, designed to uncover key insights, explore emerging trends, and simulate “what-if” scenarios.';
            // }
            classList.push(cardData);
          });
          // classList.push({
          //   id: 0,
          //   key: 'bayaan_tools',
          //   name: 'Self Service Tools',
          //   count: 5,
          //   description: this._translate.instant('Featuring Bookmarks, Data Prep tools, AutoML, and Advanced Analytics for smarter insights.'),
          //   image: this.setCardImage('bayaan_tools')
          // });
          this.classificationCards = classList;
          this.classificationLoader = false;
        },
        error: (error) => {
          this.classificationLoader = false;
          this._toasterService.error(error?.error?.message);
        }
      })
      // this._domainService.getDomainDetailClassification(id).subscribe({

      // })
    );
  }

  getinitaialList() {
    this.subs.add(this._store.select(selectCategoryResponse).subscribe((data: any) => {
      if (data?.length) {

        // this.classificationList = cloneDeep(data);
        // if (this.classificationList?.length > 0) {
        //   const index = this.classificationList.findIndex(x => x.key == 'reports');
        //   this.classificationList.splice(index, 1);
        // }
        // this.selectedClassification = this.classificationList[0];
        // this.domainList = this.selectedClassification.domains;
        // this.selectedTopic = this.domainList[0];
        // if (this.selectedTopic?.subdomains) {
        //   this.themeList = this.selectedTopic.subdomains;
        // }
        // this.selectedTheme = this.themeList[0];
        // if (this.selectedTheme?.subthemes) {
        //   this.subThemeList = this.isHideOfficialScreener ? this.selectedTheme.subthemes.filter((x: { screener: any; }) => !x.screener) : this.selectedTheme.subthemes;
        // }
        // this.selectedSubTheme = this.subThemeList[0];
        // this._cdr.detectChanges();
        // this.getProductandItem();
      }
    }));
  }

  setCardImage(setCardImage: string) {
    const path = '../../../../assets/images/product-library/';
    switch (setCardImage) {
    case this.classification.officialStatistics:
      return `${path}product-lib-official-card.jpg`;
    case this.classification.innovativeStatistics:
      return `${path}product-lib-exp-card.jpg`;
    case this.classification.analyticalApps:
      return `${path}product-lib-analytical-card.jpg`;
    case this.classification.reports:
      return `${path}product-lib-report-card.jpg`;
    case 'bayaan_tools':
      return `${path}product-lib-tools-card.jpg`;
    default: return '';
    }
  }

  onChangeTab(index: number) {
    this.selectedTab = index;
    const params = new URLSearchParams();
    params.set('tab', this.layoutViewTab[index].key);
    this._location.go(this._router.url, params.toString());
  }

  sortClick(event: any) {
    this.sortValue = event;
    // this.currentData.sortBy = {
    //   [this.sortValue.key]: this.sortValue.value
    // };
    // this.offsetPage = 1;
    // this.offset = 1;
    // this.callScreenerData();
    // this._cdr.detectChanges();
  }

  private getStickyHeaderOffset(): number {
    const selectors = ['.ifp-header', 'header.ifp-header', 'header.sticky', '.app-header', 'header'];
    for (const sel of selectors) {
      const el = document.querySelector(sel) as HTMLElement | null;
      if (el && getComputedStyle(el).position === 'fixed') {
        return el.offsetHeight;
      }
    }
    return 0;
  }

  private scrollToTop(): void {
    const target = this.listTop?.nativeElement ?? null;
    if (target) {
      const offset = this.getStickyHeaderOffset() + 8; // small gap
      const top = target.getBoundingClientRect().top + window.scrollY - offset;
      window.scrollTo({ top, behavior: 'smooth' });
      setTimeout(() => target.focus({ preventScroll: true }), 0);
    } else {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }


  onPageChange(event: any) {
    this.offsetPage = event + 1;
    this.offset = (event / this.limit) + 1;
    this.scrollToTop();
    this.callIndicatorsApi(true);
  }

  limitChanged(event: any) {
    this.offsetPage = 1;
    this.offset = 1;
    this.limit = event;
    this.scrollToTop();
    this.callIndicatorsApi(true);
  }

  changePagination() {
    this.offsetPage = 0;
    this.offset = 1;
  }

  onSelectClassificationCard(classificationId: string, index: number) {
    this.selectedClassificationCardIndex = index;
    this.treeChartData.set(this.classificationData[index]);
    this.selectedClassificationCardIndex = index;
    this.domainForm.reset();
    this.getDomainData();
    setTimeout(() => {
      this.domainForm.controls['selectedProduct'].setValue(this.classificationData[index]);
    }, 100);

  }

  onSelectSubdomainTree(event: { domainIndex: number; subDomainIndex: number; }) {
    this.selectedTab = 1;
    this.domainForm.controls['selectedDomain'].setValue(this.selectedDomainList()[event.domainIndex]);
    const subDomain = this.domainForm.controls['selectedDomain'].value?.subdomains ?? [];
    const isReport = this.classificationCards[this.selectedClassificationCardIndex].key === 'reports';
    if (isReport) {
      this.categoryList.set(subDomain);
      this.themeList.set([]);
      this.subThemeList.set([]);
      this.selectedSubTheme.set(null);
    } else {
      this.themeList.set(subDomain);
      this.categoryList.set([]);
      this.selectedCategory.set(null);
    }
    if (subDomain.length) {
      this.selectTheme(subDomain[event.subDomainIndex], isReport ? 'report' : '');
    }
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }


}
interface LayoutTab {
  name: string;
  key: string;
  iconClass: string;
}

interface Classification {
  content_type?: any;
  id: string;
  name: string;
  icon_path: string;
  nodeCount: number;
  isSelected: boolean;
  domains: DomainsNav[]
  nodes?: any[]
  showTree?: boolean;
  key?: string;
}

export interface LibraryClassification {
  id: string;
  key: string;
  name: string;
  light_icon: string;
  dark_icon: string;
  count: number;
  description?: string;
  domains?: any[];
  subdomains?: any[];
  subthemes?: any[];
  screenerConfiguration?: { screenerIndicator: string, screenerView: string };
  screener?: boolean;
  value_key?: string;
  cntType?: string;
}

interface LibraryDataClassification {
  name: string;
  key: string;
}

