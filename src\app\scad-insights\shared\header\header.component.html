<div (click)="outsideClick($event)" class="ifp-header__overlay"
  [ngClass]="{'ifp-header__overlay--active' : isMenuExpanded || isDomain || hamburgerMenu}"
  (click)="toggleMenu($event, false, isMenuExpanded ? 'mobileMenu' : 'other')">
</div>

<app-progress-loader *ngIf="loaderService.isLoading | async"></app-progress-loader>
<div *ngIf="(headerData$ | async) as header">
  <header class="ifp-header" #header [@headerSlideAnimations]="isSticky ? 'true': 'false'"
    (@headerSlideAnimations.done)="animationHeader($event)"
    [ngClass]="{'ifp-header--sticky': isSticky, 'ifp-header--demo': isDemo}">
    <div class="ifp-header__dummy">
      <div class="ifp-container ifp-header__dummy-wrapper">
        <div class="ifp-header__dummy-icon"><svg width="24" height="24" viewBox="0 0 24 24" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path
              d="M13 9H11V7H13V9ZM13 11H11V17H13V11ZM12 4C7.589 4 4 7.589 4 12C4 16.411 7.589 20 12 20C16.411 20 20 16.411 20 12C20 7.589 16.411 4 12 4ZM12 2C17.523 2 22 6.477 22 12C22 17.523 17.523 22 12 22C6.477 22 2 17.523 2 12C2 6.477 6.477 2 12 2Z">
            </path>
          </svg>{{'Demo Mode' | translate}}</div>
        <p class="ifp-header__dummy-text">{{'The numbers shown are not true and for demonstration purposes only' |
          translate}}</p>
      </div>
    </div>
    <div class="ifp-container ifp-header__mobile-menu">
      <div class="ifp-header__menu-bar">
        <div class="ifp-header__hamburger" (click)="toggleMenu($event, true)"><em class="ifp-icon"
            [ngClass]="{'ifp-icon-hamburger' : !isMenuExpanded, 'ifp-icon-cross': isMenuExpanded}"></em></div>
        <ifp-search class="ifp-header__mobile-search" [darkMode]="true" (keyup)="getSearch($event)"
          (searchEvent)="goToPage()" [isKeypress]="true" #searchBox [onSearch]="searchText"
          [isCustomClear]="true"></ifp-search>
        <div class="ifp-header__search-bar">
          <input autocomplete="off" type="text" class="ifp-header__search-input flip-placeholder"
            (keyup)="getSearch($event)" (click)="getSearchData()" [(ngModel)]="searchText"
            placeholder="{{'Search' | translate}} " appSearchSuggestion [suggestions]="suggestionList"
            [suggestionContentTypes]="contentTypes" [suggestionDomains]="domains" [resultLength]="resultLength"
            [searchQuery]="searchQuery" (keyup.enter)="goToSearchPage()" id="header-search">
          <div class="ifp-header__search-btn" id="header-search">
            <ng-container *ngIf="(searchResponse$ | async) as data">
              <span class="ifp-header__search-icon" (click)="goToSearchPage()"
                [ngClass]="{'disabled' : checkClassValue(data) || !replaceSearchText()}"
                *ngIf="header.search_icon ||header.search_light_icon">
                <ifp-img [alt]="'search_icon'" [analyticalClass]="analyticalClass.headerSearchicon"
                  (mouseenter)="removeTooltip()" [darkIcon]="header.search_icon" [lightIcon]="header.search_icon"
                  [height]="16" [width]="16"></ifp-img>
              </span>
            </ng-container>
          </div>
        </div>
      </div>
      <div class="ifp-header__logo-sec">
        <div class="ifp-header__logo-item" *ngIf="header.site_logo"><a [routerLink]="['/home']"
            [title]="'Go to Home' | translate" class="ifp-header__logo-link"><img [src]="header.site_logo"
              alt="Insights and Forsights platform" class="ifp-header__logo"></a></div>
        <div class="ifp-header__logo-item" *ngIf="header.site_slogan"><a href="https://www.abudhabi.gov.ae/"
            [title]="'Go to Government of Abu Dhabi' | translate" target="_blank" class="ifp-header__logo-link"><img
              [src]="header?.site_slogan" alt="Government of Abu Dhabi" class="ifp-header__logo"></a></div>
      </div>
    </div>
    <div class="ifp-header__main">
      <div class="ifp-header__outer" [ngClass]="{'ifp-header__outer--active': isMobile && isMenuExpanded}">
        <div class="ifp-container">
          <div class="ifp-header__settings">
            <ul class="ifp-header__settings-list" *ngIf="false">
              <li class="ifp-header__settings-item ifp-header__settings-item--profile" id="header-profile"
                [appIfpTooltip]="'Profile' | translate" [zIndex]="1001" [extraSpaceTop]="-5"
                [placement]="isSticky ? 'auto' : 'topLeft'" (mouseenter)="removeTooltip()"
                (click)="getSelectedSetting('profile')"
                [ngClass]="{'ifp-header__settings-item--active' : selectedItem === 'profile'}">
                <span class="ifp-header__profile"><img
                    [src]="(_msal.profilePic | async) ? (_msal.profilePic | async)   : '../../../assets/images/profile-img.png'"
                    alt="profile" class="ifp-header__profile-img"></span>
                <div class="ifp-header__dropdown">
                  <ul class="ifp-header__dropdown-list">
                    <li class="ifp-header__dropdown-item" [routerLink]="['/my-apps/draft']"
                      routerLinkActive="ifp-header__dropdown-item--active">{{'Drafts' |translate}}</li>
                    <li class="ifp-header__dropdown-item" [routerLink]="['/analytics/prep-library']"
                      routerLinkActive="ifp-header__dropdown-item--active">{{'Library' |translate}}</li>
                    <li class="ifp-header__dropdown-item" (click)="startJourney()"
                      [ngClass]="{'ifp-header__dropdown-item--disable': themeService.isStartJourny}">
                      {{(!themeService.isStartJourny ? 'Start Journey' : 'Stop Journy') | translate }}</li>
                    <li class="ifp-header__dropdown-item" (click)="logout()">{{'Logout' | translate}}</li>
                  </ul>
                </div>
              </li>


              <a [routerLink]="['/glossary']" routerLinkActive="ifp-header__ham-item--active"
                class="ifp-header__ham-item">
                <em class="ifp-icon ifp-icon-desktop-chart"></em>
                <span class="ifp-header__ham-text">{{'Glossary' | translate}}</span>
              </a>

              <li class="ifp-header__settings-item ifp-header__settings-item--notification"
                [ngClass]="{'active': checkValue(), 'ifp-header__settings-item--active': selectedItem === 'notification'}"
                *ngIf="(header.notification_icon || header.notification_light_icon) && header?.show_notifications"
                (click)="getSelectedSetting('notification')">
                <div class="ifp-header__settings-icon ifp-header__settings-icon--desk"
                  [appIfpTooltip]="'Notification' | translate" [zIndex]="1001"
                  [placement]="isSticky ? 'auto' : 'topLeft'" id="header-notification" (mouseenter)="removeTooltip()">
                  <img [src]="header.notification_icon" alt="icon">
                </div>
                <div class="ifp-header__settings-icon ifp-header__settings-icon--mob">
                  <img
                    [src]="themeService.defaultTheme === 'light' ? header.notification_light_icon : header.notification_icon"
                    alt="icon">
                </div>
                <!-- <ng-container *ngIf="notificationData$ | async as notification">
              <div class="ifp-header__dropdown ifp-header__dropdown--notification" (mouseenter)="removeTooltip('notif')"
                (mouseleave)="changeStyle()" [ngClass]="{'ifp-header__dropdown--empty': !notification.length}"
                #notificationDropdown>
                <div class="ifp-header__notif">
                  <ng-container *ngIf="notification.length > 0; else emptyNotification">
                    <div class="ifp-header__notif-list" *ngFor="let not of notification"
                      (click)="readNotification(not)">
                      <div class="ifp-header__notif-sec-2">
                        <h3 class="ifp-header__notif-title">{{not.CONTENT_NAME | translate}}</h3>
                        <p class="ifp-header__notif-desc" *ngIf="not.CONTENT_DESCRIPTION">{{not.CONTENT_DESCRIPTION |
                          translate }}</p>
                        <p *ngIf="not.INSERT_DATE" class="ifp-header__notif-time"><em
                            class="ifp-icon ifp-icon-calender"></em>{{ not.INSERT_DATE | date: 'mediumDate'}}</p>
                      </div>
                    </div>
                  </ng-container>
                </div>
                <ng-template #emptyNotification>
                  <app-ifp-no-data [bgColor]="colors.colorGreyBg"
                    [message]="'No Unread Notifications' | translate"></app-ifp-no-data>
                </ng-template>
                <div class="ifp-header__notif-actions">
                  <ifp-button class="ifp-header__notif-viewall" [analyticClass]="analyticalClass.notificationViewAll"
                    [label]="'View All Notifications' | translate" [buttonColor]="buttonColor.blue" [buttonClass]="buttonClass.inline"
                    [iconClass]="'ifp-left-arrow'" (ifpClick)="gotoNotifications('view')"></ifp-button>
                  <ifp-button class="ifp-header__notif-viewall" [analyticClass]="analyticalClass.notificationViewAll"
                    [label]="'Notification Settings' | translate" [buttonColor]="buttonColor.blue"
                    [buttonClass]="buttonClass.inline" [iconClass]="'ifp-left-arrow'"
                    (ifpClick)="gotoNotifications('manage')"></ifp-button>
                </div>
              </div>
            </ng-container> -->
              </li>
              <li *ngIf="(header?.language_icon || header?.language_light_icon) && header?.show_language"
                class="ifp-header__settings-item ifp-header__settings-item--select ifp-header__lang" id="header-lang"
                [appIfpTooltip]="'Language' | translate" [zIndex]="1001" [placement]="isSticky ? 'auto' : 'topLeft'"
                (mouseenter)="removeTooltip()" (click)="getSelectedSetting('lang')"
                [ngClass]="{'ifp-header__settings-item--active' : selectedItem === 'lang'}">
                <!-- <ifp-img id="lang" (mouseenter)="removeTooltip()" [darkIcon]="header.language_icon"
              [lightIcon]="header.language_icon" class="ifp-header__settings-icon" [width]="24"
              [height]="24"></ifp-img> -->
                <div class="ifp-header__settings-icon ifp-header__settings-icon--desk" id="lang"
                  (mouseenter)="removeTooltip()"><img [src]="header.language_icon" alt="icon"></div>
                <div class="ifp-header__settings-icon ifp-header__settings-icon--mob">
                  <img [src]="themeService.defaultTheme === 'light' ? header.language_light_icon : header.language_icon"
                    alt="icon">
                </div>
                <div class="ifp-header__dropdown" *ngIf="header?.languages?.length">
                  <ul class="ifp-header__dropdown-list">
                    <li class="ifp-header__dropdown-item {{analyticalClass.languageSwitch}}"
                      *ngFor="let lang of header?.languages" [lang]="lang?.value"
                      [ngClass]="{'active': selectedLanguage === lang?.value}" (click)="languageChange(lang.value)"><em
                        class="ifp-icon ifp-icon-tick"></em>{{lang?.title ? lang?.title : ''}}</li>
                  </ul>
                </div>
              </li>

              <li class="ifp-header__settings-item ifp-header__settings-item--select ifp-header__theme"
                id="header-theme" [appIfpTooltip]="'Theme' | translate" [zIndex]="1001"
                [placement]="isSticky ? 'auto' : 'topLeft'" (mouseenter)="removeTooltip()"
                *ngIf="(header?.dark_theme_dark_icon || header?.dark_theme_light_icon) && header?.show_themes"
                (click)="getSelectedSetting('theme')"
                [ngClass]="{'ifp-header__settings-item--active' : selectedItem === 'theme'}">
                <div class="ifp-header__settings-icon ifp-header__settings-icon--desk" (mouseenter)="removeTooltip()">
                  <img
                    [src]="themeService.defaultTheme === 'light' ? header.dark_theme_light_icon : header.dark_theme_dark_icon"
                    alt="icon">
                </div>
                <div class="ifp-header__settings-icon ifp-header__settings-icon--mob">
                  <img
                    [src]="themeService.defaultTheme === 'light' ? header.light_theme_light_icon : header.dark_theme_dark_icon"
                    alt="icon">
                </div>
                <div class="ifp-header__dropdown">
                  <ul class="ifp-header__dropdown-list">
                    <li class="ifp-header__dropdown-item {{analyticalClass.themeSwitch}}"
                      [ngClass]="{'active' : selectedTheme==='dark'}" (click)="changeTheme('dark')"><em
                        class="ifp-icon ifp-icon-tick"></em>
                      <ifp-img (mouseenter)="removeTooltip()" [darkIcon]="header.light_theme_dark_icon"
                        [lightIcon]="header.dark_theme_dark_icon" class="ifp-header__settings-dropdown-icon"
                        [height]="16" [width]="16"></ifp-img>
                      {{ 'Dark' | translate}}
                    </li>
                    <li class="ifp-header__dropdown-item {{analyticalClass.themeSwitch}}"
                      [ngClass]="{'active' : selectedTheme==='light'}" (click)="changeTheme('light')"><em
                        class="ifp-icon ifp-icon-tick"></em>
                      <ifp-img (mouseenter)="removeTooltip()" [darkIcon]="header.light_theme_light_icon"
                        [lightIcon]="header.dark_theme_light_icon" class="ifp-header__settings-dropdown-icon"
                        [height]="16" [width]="16"></ifp-img>
                      {{ 'Light' | translate}}
                    </li>
                  </ul>
                </div>
              </li>

              <li class="ifp-header__settings-item ifp-header__settings-item--user" id="header-user"
                [appIfpTooltip]="'Accessibility' | translate" [zIndex]="1001"
                [placement]="isSticky ? 'auto' : 'topLeft'" (mouseenter)="removeTooltip()"
                *ngIf="(header?.accessibility_icon || header?.accessibility_light_icon) && header?.show_accessibility">
                <ifp-img (mouseenter)="removeTooltip()" [darkIcon]="header.accessibility_icon"
                  [lightIcon]="header.accessibility_icon" class="ifp-header__settings-icon"></ifp-img>
                <div class="ifp-header__dropdown">
                  <ul class="ifp-header__dropdown-list">
                    <li class="ifp-header__dropdown-item">
                      <p class="ifp-header__dropdown-title">{{'Font Size' | translate}}</p>
                      <em class="ifp-icon ifp-icon-font"></em>
                      <div class="ifp-header__range">
                        <input type="radio" name="font-size"
                          class="ifp-header__radio-btn {{analyticalClass.accessibilityFont}}" id="fontLg"
                          [checked]="selectedFontSize==='lg'" (change)="setFontSize('lg')">
                        <label for="fontLg"></label>
                        <input type="radio" name="font-size"
                          class="ifp-header__radio-btn {{analyticalClass.accessibilityFont}}" id="fontMd"
                          [checked]="selectedFontSize==='md'" (change)="setFontSize('md')">
                        <label for="fontMd"></label>
                        <input type="radio" name="font-size"
                          class="ifp-header__radio-btn {{analyticalClass.accessibilityFont}}" id="fontSm"
                          [checked]="selectedFontSize==='sm'" (change)="setFontSize('sm')">
                        <label for="fontSm"></label>
                      </div>
                    </li>
                    <li class="ifp-header__dropdown-item">
                      <p class="ifp-header__dropdown-title">{{'Cursor' | translate}}</p>
                      <em class="ifp-icon ifp-icon-cursor"></em>
                      <div class="ifp-header__range">
                        <input type="radio" name="cursor"
                          class="ifp-header__radio-btn {{analyticalClass.accessibilityCursor}}" id="cursor3"
                          [checked]="selectedCursor==='type3'" (change)="setCursorStyle('type3')">
                        <label for="cursor3"></label>
                        <input type="radio" name="cursor"
                          class="ifp-header__radio-btn {{analyticalClass.accessibilityCursor}}" id="cursor2"
                          [checked]="selectedCursor==='type2'" (change)="setCursorStyle('type2')">
                        <label for="cursor2"></label>
                        <input type="radio" name="cursor"
                          class="ifp-header__radio-btn {{analyticalClass.accessibilityCursor}}" id="cursor1"
                          [checked]="selectedCursor==='type1'" (change)="setCursorStyle('type1')">
                        <label for="cursor1"></label>
                      </div>
                    </li>
                  </ul>
                </div>
              </li>
            </ul>
            <!-- Mobile dropdown start -->
            <div class="ifp-header__dropdown-mobile" *ngIf="false" #dropdownMobile>
              <ng-container *ngIf="selectedItem === 'profile'">
                <div class="ifp-header__mobile-list">
                  <ul class="ifp-header__mobile-list-dropdown">
                    <li class="ifp-header__mobile-list-item" [routerLink]="['/my-apps/draft']"
                      routerLinkActive="router-link-active">{{'Drafts' | translate}}</li>
                    <li class="ifp-header__mobile-list-item" [routerLink]="['/my-apps/draft']"
                      routerLinkActive="ifp-header__mobile-list-item--active">{{'Library' | translate}}</li>
                    <li class="ifp-header__mobile-list-item" (click)="logout()">{{'Logout' | translate}}</li>
                  </ul>
                </div>
              </ng-container>
              <ng-container *ngIf="selectedItem === 'notification'">
                <div class="ifp-header__notif">
                  <ng-container *ngIf="notificationData$ | async as notification">
                    <ng-container *ngIf="notification.length > 0; else emptyNotification">
                      <div class="ifp-header__notif-list" *ngFor="let not of notification"
                        (click)="readNotification(not)">
                        <div class="ifp-header__notif-sec-2">
                          <h3 class="ifp-header__notif-title">{{not.CONTENT_NAME | translate}}</h3>
                          <p class="ifp-header__notif-desc" *ngIf="not.CONTENT_DESCRIPTION !== ''">
                            {{not.CONTENT_DESCRIPTION | translate }}</p>
                          <p *ngIf="not.INSERT_DATE" class="ifp-header__notif-time"><em
                              class="ifp-icon ifp-icon-calender"></em>{{ not.INSERT_DATE | date: 'mediumDate'}}</p>
                        </div>
                      </div>
                    </ng-container>
                    <ng-template #emptyNotification>
                      <app-ifp-no-data [bgColor]="colors.indigoDark" [message]="'No Notifications' | translate"
                        class="ifp-header__notif-empty"></app-ifp-no-data>
                    </ng-template>
                  </ng-container>
                </div>
                <ifp-button class="ifp-header__notif-viewall" [label]="'View All' | translate"
                  [buttonColor]="buttonColor.blue" [buttonClass]="buttonClass.inline" [iconClass]="'ifp-left-arrow'"
                  (ifpClick)="gotoNotifications('view')"></ifp-button>
                <ifp-button class="ifp-header__notif-mngnotif" [analyticClass]="analyticalClass.notificationViewAll"
                  [label]="'Notification Settings' | translate" [buttonColor]="buttonColor.blue"
                  [buttonClass]="buttonClass.inline" [iconClass]="'ifp-left-arrow'"
                  (ifpClick)="gotoNotifications('manage')"></ifp-button>
              </ng-container>
              <ng-container *ngIf="selectedItem === 'lang'">
                <div class="ifp-header__mobile-list" *ngIf="header?.languages?.length">
                  <ul class="ifp-header__mobile-list-dropdown">
                    <li class="ifp-header__mobile-list-item {{analyticalClass.languageSwitch}}"
                      *ngFor="let lang of header?.languages" [lang]="lang?.value"
                      [ngClass]="{'active': selectedLanguage === lang?.value}" (click)="languageChange(lang.value)"><em
                        class="ifp-icon ifp-icon-tick"></em>{{lang?.title ? lang?.title : ''}}</li>
                  </ul>
                </div>
              </ng-container>
              <ng-container *ngIf="selectedItem === 'theme'">
                <div class="ifp-header__mobile-list">
                  <ul class="ifp-header__mobile-list-dropdown">
                    <li class="ifp-header__mobile-list-item" [ngClass]="{'active' : selectedTheme==='dark'}"
                      (click)="changeTheme('dark')"><em class="ifp-icon ifp-icon-tick"></em>
                      <ifp-img (mouseenter)="removeTooltip()" [darkIcon]="header.light_theme_dark_icon"
                        [lightIcon]="header.dark_theme_dark_icon" class="ifp-header__settings-dropdown-icon"
                        [height]="16" [width]="16"></ifp-img>
                      {{ 'Dark' | translate}}
                    </li>
                    <li class="ifp-header__mobile-list-item" [ngClass]="{'active' : selectedTheme==='light'}"
                      (click)="changeTheme('light')"><em class="ifp-icon ifp-icon-tick"></em>
                      <ifp-img (mouseenter)="removeTooltip()" [darkIcon]="header.light_theme_light_icon"
                        [lightIcon]="header.dark_theme_light_icon" class="ifp-header__settings-dropdown-icon"
                        [height]="16" [width]="16"></ifp-img>
                      {{ 'Light' | translate}}
                    </li>
                  </ul>
                </div>
              </ng-container>
            </div>
            <!-- Mobile dropdown end -->
          </div>
          <div class="ifp-header__wrapper">
            <div class="ifp-header__nav">
              <ul class="ifp-header__nav-wrapper" *ngIf="header?.navigation_menu!.length > 0">
                <li class="ifp-header__nav-item ifp-header__nav-item--hamburger"
                  (click)="toggleMenu($event, true, 'hamburger')"
                  [ngClass]="{'ifp-header__nav-item--active' : hamburgerMenu}">
                  <span #hamburger class="ifp-btn ifp-btn--secondary"
                    [ngClass]="{'ifp-btn--active' : hamburgerMenu}"><span class="ifp-header__desktop-hidden">{{'Profile'
                      | translate }}</span>
                    <em class="ifp-icon ifp-icon-hamburger ifp-header__only-desktop"></em>
                    <em class="ifp-icon ifp-icon-right-arrow ifp-header__desktop-hidden"></em>
                  </span>
                  <div class="ifp-header__hamburger-menu">
                    <div class="ifp-header__ham-wrapper">
                      <ul class="ifp-header__ham-list-outer">

                        <li class="ifp-header__ham-list-head">{{'Dashboards' | translate}}</li>
                        <li class="ifp-header__ham-list">
                          @if(_usageDashboard.usageDashboardStatus()) {
                          <a [routerLink]="['/bayaan-dashboard']" [queryParams]="{tab: 'userOverview'}"
                            routerLinkActive="ifp-header__ham-item--active" class="ifp-header__ham-item">
                            <em class="ifp-icon ifp-icon-user-normal"></em>
                            <span class="ifp-header__ham-text">{{'Overview User and Access' | translate}}</span>
                          </a>
                          <a [routerLink]="['/bayaan-dashboard']" [queryParams]="{tab: 'utilization'}"
                            routerLinkActive="ifp-header__ham-item--active" class="ifp-header__ham-item">
                            <em class="ifp-icon ifp-icon-hand-money"></em>
                            <span class="ifp-header__ham-text">{{'Utilization Analysis' | translate}}</span>
                          </a>
                          }
                          <!-- @if(_usageDashboard.usageDashboardStatus()) { -->
                          <a [routerLink]="['/data-governance']" routerLinkActive="ifp-header__ham-item--active"
                            class="ifp-header__ham-item">
                            <em class="ifp-icon ifp-icon-desktop-chart"></em>
                            <span class="ifp-header__ham-text">{{'Data Governance Dashboard' | translate}}</span>
                          </a>

                          <!-- } -->

                          <!-- @if (isSvAccessible) {
                          <a [routerLink]="['/dashboard/tableau-internal/static']"
                            routerLinkActive="ifp-header__ham-item--active" class="ifp-header__ham-item">
                            <em class="ifp-icon ifp-icon-statistical-indicators"></em>
                            <span class="ifp-header__ham-text">{{'SV Dashboard' | translate}}</span>
                          </a>
                          } -->
                        </li>




                        @if(auditReportAccess() || checkInsightAccess()) {
                        <li class="ifp-header__ham-list-head">{{'Reports' | translate}}</li>

                        <li class="ifp-header__ham-list">
                          @if(auditReportAccess()) {
                          <a (click)="onDownloadAuditReport()" routerLinkActive="ifp-header__ham-item--active"
                            class="ifp-header__ham-item">
                            <em class="ifp-icon ifp-icon-desktop-chart"></em>
                            <span class="ifp-header__ham-text">{{'Download Audit Report' | translate}}</span>
                          </a>
                          }

                          @if(checkInsightAccess()) {
                          <a [routerLink]="['/insight-report-list']" routerLinkActive="ifp-header__ham-item--active"
                            class="ifp-header__ham-item">
                            <em class="ifp-icon ifp-icon-bulb-ins"></em>
                            <span class="ifp-header__ham-text">{{'Bayaan Insight Report GenAI' | translate}}</span>
                          </a>
                          }
                        </li>
                        }



                        <li class="ifp-header__ham-list-head">{{'Glossary' | translate}}</li>
                        <li class="ifp-header__ham-list">
                          <a [routerLink]="['/glossary']" routerLinkActive="ifp-header__ham-item--active"
                            class="ifp-header__ham-item">
                            <em class="ifp-icon ifp-icon-glossary"></em>
                            <span class="ifp-header__ham-text">{{'Glossary' | translate}}</span>
                          </a>
                        </li>


                        <li class="ifp-header__ham-list-head">{{'Bookmarks' | translate}}</li>
                        <li class="ifp-header__ham-list">
                          <a [routerLink]="['/my-apps/draft']" routerLinkActive="ifp-header__ham-item--active"
                            class="ifp-header__ham-item">
                            <em class="ifp-icon ifp-icon-draft"></em>
                            <span class="ifp-header__ham-text">{{'Drafts' | translate}}</span>
                          </a>
                          @if (isAdmin) {
                          <a [routerLink]="['/control-panel']" routerLinkActive="ifp-header__ham-item--active"
                            class="ifp-header__ham-item">
                            <em class="ifp-icon ifp-icon-manage-user"></em>
                            <span class="ifp-header__ham-text">{{'User Management' | translate}}</span>
                          </a>
                          }
                        </li>
                        <!-- <li class="ifp-header__ham-list">
                      <div class="ifp-header__ham-item">
                        <em class="ifp-icon ifp-icon-journey"></em>
                        <span class="ifp-header__ham-text" (click)="startJourney()">{{(!themeService.isStartJourny ? 'Start Journey' : 'Stop Journy') |
                          translate }}</span>
                      </div>
                    </li> -->
                      </ul>
                      <ng-container *ngIf="notificationData$ | async as notification">
                        <ul class="ifp-header__ham-list-outer">
                          <li class="ifp-header__ham-list-head">{{'Notifications' | translate}}</li>
                          <li class="ifp-header__ham-list">
                            <div (click)="gotoNotifications('view', 'UNREAD')" class="ifp-header__ham-item">
                              <em class="ifp-icon ifp-icon-bell-long"></em>
                              <span class="ifp-header__ham-text">{{'Unread Notifications' | translate}}</span>
                              <span class="ifp-header__ham-count">{{headerService.unreadNotificationCount()}}</span>
                            </div>
                          </li>
                          <li class="ifp-header__ham-list">
                            <div (click)="gotoNotifications('view')" class="ifp-header__ham-item">
                              <em class="ifp-icon ifp-icon-eye"></em>
                              <span class="ifp-header__ham-text">{{'View All Notifications' | translate}}</span>
                              <span class="ifp-header__ham-count">{{totalNotificationCount}}</span>
                            </div>
                          </li>
                          <li class="ifp-header__ham-list">
                            <div (click)="gotoNotifications('manage')" class="ifp-header__ham-item">
                              <em class="ifp-icon ifp-icon-settings-hexagon"></em>
                              <span class="ifp-header__ham-text">{{'Notification Settings' | translate}}</span>
                              <!-- <span class="ifp-header__ham-count">{{notification.length}}</span> -->
                            </div>
                          </li>
                        </ul>
                      </ng-container>
                      <ul class="ifp-header__ham-list-outer">
                        <li class="ifp-header__ham-list-head">{{'Language' | translate}}</li>
                        @for (lang of header?.languages; track $index) {
                        <li class="ifp-header__ham-list" [lang]="lang?.value">
                          <div [ngClass]="{'ifp-header__ham-item--active': selectedLanguage === lang?.value}"
                            (click)="languageChange(lang.value)" class="ifp-header__ham-item">
                            <em class="ifp-icon"
                              [class]="lang?.value === 'en' ? 'ifp-icon-english' : 'ifp-icon-arabic'"></em>
                            <span class="ifp-header__ham-text">{{lang?.title ? lang?.title : ''}}</span>
                          </div>
                        </li>
                        }
                      </ul>
                      <ul class="ifp-header__ham-list-outer">
                        <li class="ifp-header__ham-list-head">{{'Accessibility' | translate}}</li>
                        <li class="ifp-header__ham-list">
                          <div class="ifp-header__ham-item">
                            <em class="ifp-icon ifp-icon-font"></em>
                            <span class="ifp-header__ham-text">{{'Font Size' | translate}}</span>
                            <div class="ifp-header__range">
                              <input #fontSizeInput type="range" class="ifp-header__range-input" min="1" max="3"
                                [value]="fontSizeRange" (change)="setFontSize(fontSizeInput.value, 'font')">
                            </div>
                          </div>
                        </li>
                        <li class="ifp-header__ham-list">
                          <div class="ifp-header__ham-item">
                            <em class="ifp-icon ifp-icon-cursor-rounded"></em>
                            <span class="ifp-header__ham-text">{{'Cursor' | translate}}</span>
                            <div class="ifp-header__range">
                              <input #curserSizeInput type="range" class="ifp-header__range-input" min="1" max="3"
                                [value]="cursorRange" (change)="setCursorStyle(curserSizeInput.value, 'cursor')">
                            </div>
                          </div>
                        </li>
                      </ul>
                      <ul class="ifp-header__ham-list-outer">
                        <li class="ifp-header__ham-list-head">{{'Mode' | translate}}</li>
                        <li class="ifp-header__ham-list">
                          <div [ngClass]="{'ifp-header__ham-item--active': selectedTheme==='dark'}"
                            (click)="changeTheme('dark')" class="ifp-header__ham-item">
                            <em class="ifp-icon ifp-icon-moon"></em>
                            <span class="ifp-header__ham-text"> {{'Dark' | translate}}</span>
                          </div>
                        </li>
                        <li class="ifp-header__ham-list">
                          <div [ngClass]="{'ifp-header__ham-item--active': selectedTheme==='light'}"
                            (click)="changeTheme('light')" class="ifp-header__ham-item">
                            <em class="ifp-icon ifp-icon-sun"></em>
                            <span class="ifp-header__ham-text"> {{'Light' | translate}}</span>
                          </div>
                        </li>
                      </ul>
                    </div>
                    <div class="ifp-header__ham-profile">
                      <div class="ifp-header__ham-user-info">
                        <img class="ifp-header__profile-img"
                          [src]="(_msal.profilePic | async) ? (_msal.profilePic | async)   : '../../../assets/images/profile-img.png'"
                          alt="">
                        <div>
                          <p class="ifp-header__profile-name">{{_msal.getLoginData?.account?.name | titlecase}}</p>
                          <p class="ifp-header__profile-email">{{userDesignation | translate}}</p>
                        </div>
                      </div>
                      <em class="ifp-icon ifp-icon-power-off ifp-header__logout-btn" (click)="logout()"
                        [appIfpTooltip]="'Logout' | translate" [zIndex]="1001"></em>
                    </div>
                  </div>
                </li>
                @for (item of header?.navigation_menu; let i = $index; track i) {
                @if (checkCensus(item.menu_label)) {
                <li class="ifp-header__nav-item" [ngClass]="{'ifp-header__nav-item--active' : isNavItemActive === i}"
                  (click)="isNavItemActive = i"
                  [class]="'ifp_'+(item.menu_label !== 'My Apps' ? item.menu_label.toLowerCase() : 'my_apps')"
                  id="header-nav">
                  @if (item.menu_link !== '/domain' && item.menu_link !== '/government') {
                  <a (click)="openLink(item.menu_link, item); assignActive(item.menu_link)" appUserJourney
                    [blinkerZindex]="1202" [fixed]="animationEvent" [key]="getKeys(item?.menu_title ?? item.menu_link)"
                    [route]="getRoute(item.menu_link)" [exploreType]="item.menu_link === '/domain' ? 'click':'route'"
                    [body]="true" [stepNumber]="i+1" class="ifp-btn ifp-btn--secondary"
                    [ngClass]="{'ifp-btn--active' : checkUrl() === item.menu_link && !isDomain}"><em
                      class="ifp-icon ifp-icon-down-arrow" *ngIf="item.show_dropdown !=='No'"></em>{{item.menu_label |
                    translate}}</a>
                  <!-- @if (item.menu_label !== 'Census' && item.menu_label !== 'التعداد') { -->

                  <!-- } -->

                  <!-- @else {
                  <a [href]="item.menu_link" appUserJourney target="_blank" [blinkerZindex]="1202"
                    [fixed]="animationEvent" [key]="getKeys(item.menu_link)"
                    [route]="item.menu_link ==='/my-apps/landing'?'my-apps/landing' : item.menu_link=== '/glossary'? 'glossary': ''"
                    [exploreType]="item.menu_link === '/domain' ? 'click':'route'" [body]="true" [stepNumber]="i+1"
                    (click)="assignActive(item.menu_link)" class="ifp-btn ifp-btn--secondary"
                    [ngClass]="{'ifp-btn--active' : checkUrl() === item.menu_link && !isDomain}"><em
                      class="ifp-icon ifp-icon-down-arrow" *ngIf="item.show_dropdown !=='No'"></em>{{item.menu_label |
                    translate}}</a>
                  } -->
                  } @else if (item.menu_link === '/domain') {
                  <span appOutsideClick (outsideClick)="outsideClick($event)" class="ifp-header__nav-inner">
                    <span class="ifp-btn ifp-btn--secondary" appUserJourney [key]="'domain'" [fixed]="animationEvent"
                      [exploreType]="item.menu_link === '/domain' ? 'click':'route'" [body]="true" [stepNumber]="i+1"
                      [blinkerZindex]="1202" (click)="openDomain($event)"
                      [ngClass]="{'ifp-btn--active': selectedNav === '/domain'}">{{item.menu_label |
                      translate}}
                      <em class="ifp-icon ifp-icon-down-arrow" *ngIf="item.show_dropdown !=='No'"></em>
                      <em class="ifp-icon ifp-icon-right-arrow ifp-header__desktop-hidden"></em></span>
                  </span>
                  <!-- Domain component load -->

                  <div class="ifp-header__megamenu" *ngIf="isDomain" #megamenu @slideDownAnimations>
                    <div class="ifp-header__megamenu-domain">
                      <app-domains [selectedTabIndex]="selectedTabIndex"></app-domains>
                    </div>
                  </div>
                  } @else {
                  <span appOutsideClick (outsideClick)="outsideGovermentClick($event)" class="ifp-header__nav-inner">
                    <span class="ifp-btn ifp-btn--secondary" appUserJourney [key]="'government_affirs'"
                      [fixed]="animationEvent" [body]="true" [stepNumber]="i+1" [blinkerZindex]="1202"
                      (click)="openGovernmentDahboards($event)"
                      [ngClass]="{'ifp-btn--active': selectedNav === '/government'}">{{item.menu_label |
                      translate}}
                      <em class="ifp-icon ifp-icon-down-arrow" *ngIf="item.show_dropdown !=='No'"></em>
                      <em class="ifp-icon ifp-icon-right-arrow ifp-header__desktop-hidden"></em></span>
                  </span>


                  @if (isGovernance || tammDashboard) {
                  <div class="ifp-header__megamenu-government" #government>
                    @if (isPowerBiAccess) {
                    <div class="ifp-header__megamenu-government-item"
                      (click)="openLink('/common-dashboard/6839?contentType=powerbi-dashboard', item)">{{'Statistical Portfolio Performance (Statistical Values)' | translate}}</div>
                    <div class="ifp-header__megamenu-government-item"
                      (click)="openLink('/common-dashboard/6837?contentType=powerbi-dashboard', item)">{{'Statistical Portfolio Performance (Publications)' | translate}}</div>
                    } @if (taamDashboardAccess) {
                    <div class="ifp-header__megamenu-government-item"
                      (click)="openLink('/common-dashboard/6803?contentType=powerbi-dashboard', item)">{{'Diwan Al Ain -
                      Case Details Dashboard (TAMM)' | translate}}</div>
                    }
                  </div>
                  }
                  }
                </li>
                }
                }
                <li class="ifp-header__nav-item">
                  <a [routerLink]="'/product-library'" routerLinkActive="ifp-btn--active" class="ifp-btn ifp-btn--secondary" [ngClass]="{'ifp-btn--active' : checkUrl().includes('/analytics') && !isDomain}">{{'Product Library' | translate}}</a>
                </li>
                <!-- <li class="ifp-header__nav-item">
              <a [routerLink]="'/analytics'" (click)="assignActive('/analytics')" class="ifp-btn ifp-btn--secondary" [ngClass]="{'ifp-btn--active' : checkUrl().includes('/analytics') && !isDomain}">{{'BAYAAN Analytics' | translate}}</a>
            </li> -->
                @if (isSticky || !isHome) {
                <li class="ifp-header__nav-item ifp-header__nav-item--search ifp-header__only-desktop"
                  [ngClass]="{'ifp-header__nav-item--active' : showSearchBar}" (click)="showSearch()"><span
                    class="ifp-btn ifp-btn--secondary" [ngClass]="{'ifp-btn--active': showSearchBar}"><em
                      class="ifp-icon ifp-icon-search"></em></span></li>
                }
              </ul>
            </div>
            <div class="ifp-header__logo-sec">
              <div class="ifp-header__logo-item" *ngIf="header.site_logo"><a [routerLink]="['/home']"
                  [title]="'Go to Home' | translate" class="ifp-header__logo-link"><img [src]="header.site_logo"
                    alt="Insights and Forsights platform" class="ifp-header__logo"></a></div>
              <div class="ifp-header__logo-item" *ngIf="header.site_slogan"><a href="https://scad.gov.ae/"
                  [title]="'Go to SCAD' | translate" target="_blank" class="ifp-header__logo-link"><img
                    [src]="header?.site_slogan" [alt]="'Government of Abu Dhabi' | translate"
                    class="ifp-header__logo"></a></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="ifp-header__search-module" [ngClass]="{'ifp-header__search-module--show': isSticky && showSearchBar}">
    <div class="ifp-container">
    <div class="ifp-header__search-outer">
      <p class="ifp-header__search-header" *ngIf="header?.title">{{header.title | translate}}</p>
      <div class="ifp-header__search-bar">
        <input #searchBar autocomplete="off" type="text" class="ifp-header__search-input" (keyup)="getSearch($event)"
          (click)="getSearchData();isHidePlaceHolder=true;" (focus)="showSearchBar" [(ngModel)]="searchText"
          appSearchSuggestion [suggestions]="suggestionList" [suggestionContentTypes]="contentTypes"
          [searchQuery]="searchQuery" [suggestionDomains]="domains" [resultLength]="resultLength"
          (keyup.enter)="goToSearchPage()" id="header-search" (blur)="onBlurFunction()">
        @if (!isHidePlaceHolder) {
        <div class="ifp-header__search-placeholder" (click)="hidePlaceHolder()">
          <img src="{{placeHolderImage}}" alt="" class="ifp-header__search-logo">
          <p class="ifp-header__search-placetext">{{header?.search_placeholder}}</p>
          <p class="ifp-header__search-type">{{placeHolderText}}</p>
        </div>
        }

        <div class="ifp-header__search-btn" id="header-search">
          <ng-container *ngIf="(searchResponse$ | async) as data">
            <span class="ifp-header__search-icon" (click)="goToSearchPage()"
              [ngClass]="{'disabled' : checkClassValue(data)}" *ngIf="header.search_icon ||header.search_light_icon">
              <ifp-img [alt]="'search_icon'" [analyticalClass]="analyticalClass.headerSearchicon"
                (mouseenter)="removeTooltip()" [darkIcon]="header.search_icon" [lightIcon]="header.search_icon"
                [height]="16" [width]="16"></ifp-img>
            </span>
          </ng-container>
        </div>
      </div>
    </div>
    </div>
  </div> -->
  </header>
  <div class="ifp-header__search-module"
    [ngClass]="{'ifp-header__search-module--show': ((isSticky && showSearchBar) || (showSearchBar && !isHome)), 'ifp-header__search-module--sticky': !isHome}">
    <div class="ifp-container">
      <div class="ifp-header__search-outer">
        <p class="ifp-header__search-header" *ngIf="header?.title">{{header.title | translate}}</p>
        <div class="ifp-header__search-bar">
          <input #searchBar autocomplete="off" type="text" class="ifp-header__search-input" (keyup)="getSearch($event)"
            (click)="getSearchData();isHidePlaceHolder=true;" (focus)="showSearchBar" [(ngModel)]="searchText"
            appSearchSuggestion [suggestions]="suggestionList" [suggestionContentTypes]="contentTypes"
            [searchQuery]="searchQuery" [suggestionDomains]="domains" [resultLength]="resultLength"
            (keyup.enter)="goToSearchPage()" id="header-search" (blur)="onBlurFunction()">
          @if (!isHidePlaceHolder) {
          <div class="ifp-header__search-placeholder" (click)="hidePlaceHolder()">
            <img src="{{placeHolderImage}}" alt="" class="ifp-header__search-logo">
            <p class="ifp-header__search-placetext">{{header?.search_placeholder}}</p>
            <p class="ifp-header__search-type">{{placeHolderText}}</p>
          </div>
          }

          <div class="ifp-header__search-btn" id="header-search">
            <ng-container *ngIf="(searchResponse$ | async) as data">
              <span class="ifp-header__search-icon" (click)="goToSearchPage()"
                [ngClass]="{'disabled' : checkClassValue(data) || !replaceSearchText()}"
                *ngIf="header.search_icon ||header.search_light_icon">
                <ifp-img [alt]="'search_icon'" [analyticalClass]="analyticalClass.headerSearchicon"
                  (mouseenter)="removeTooltip()" [darkIcon]="header.search_icon" [lightIcon]="header.search_icon"
                  [height]="16" [width]="16"></ifp-img>
              </span>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>