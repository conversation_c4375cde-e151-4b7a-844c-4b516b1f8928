<div class="ifp-register">
  <div class="ifp-register__sec-1">
    <div class="ifp-register__sec-inner">
      @if (isRegistered) {
        <div class="ifp-register__submitted">
          <h1 class="ifp-register__main-head">{{ 'Thank you' | translate}}!</h1>
          <p class="ifp-register__sub-title">{{'For requesting access to Bayaan' | translate}},</p>
          <p class="ifp-register__desc">{{'you will receive an email when your access is granted' | translate}}.</p>
        </div>
      } @else {
        @if (!isVerified) {
          <div class="ifp-register__submitted">
            <h1 class="ifp-register__main-head">{{ 'Sorry' | translate}}!</h1>
            <p class="ifp-register__sub-title">{{error.message | translate}}</p>
            @if (error.description) {
              <p class="ifp-register__desc">{{error.description | translate}}</p>
            }
            @if (backToOtp) {
              <ifp-button [label]="'Retry'" (ifpClick)="redirectToOtpPage()" [buttonClass]="buttonClass.primary"></ifp-button>
            }
          </div>
        } @else {
          @if (!isSubmitted && accessToken !== '') {
            <h1 class="ifp-register__main-head">{{ 'Registration For' | translate}} {{decodedToken?.role === role.dg ? decodedToken?.designation : roleList[decodedToken?.role] | translate}}
            </h1>
            <p class="ifp-register__sub-title">{{'Create your account' | translate}}</p>
            @if (decodedToken?.role === role.dg) {
              <p class="ifp-register__desc">{{'To approve/reject data that is classified as sensitive' | translate}}</p>
            }
            <form [formGroup]="registerForm" class="ifp-register__form" (keydown.enter)="$event.preventDefault()">
              <div class="ifp-input-text">
                <p class="ifp-input-text__label">{{'Full name' | translate}}</p>
                <div class="ifp-input-text__wrapper">
                  <div class="ifp-input-text__input ifp-input-text__input--disabled">{{registerForm.value.name}}</div>
                  @if(registerForm.controls['name'].errors) {
                    <em class="ifp-icon ifp-icon-exclamation-round ifp-input-mobile__error-icon"></em>
                  } @else if (registerForm.value.name && (!registerForm.controls['name'].invalid)) {
                    <em class="ifp-icon ifp-icon-tick-round"></em>
                  }
                </div>
              </div>
              <div class="ifp-input-text">
                <p class="ifp-input-text__label">{{'Email' | translate}}</p>
                <div class="ifp-input-text__wrapper">
                  <div class="ifp-input-text__input ifp-input-text__input--disabled">{{registerForm.value.email}}</div>
                  <em class="ifp-icon ifp-icon-tick-round"></em>
                </div>
              </div>
              @if (!isNormalUser && decodedToken?.role !== role.dg) {
                <div class="ifp-input-text">
                  <p class="ifp-input-text__label">{{'Role' | translate}}</p>
                  <div class="ifp-input-text__wrapper">
                    <div class="ifp-input-text__input ifp-input-text__input--disabled">{{registerForm.value.role | removeUnderscore}}</div>
                    <em class="ifp-icon ifp-icon-tick-round"></em>
                  </div>
                </div>
              }

              <div class="ifp-register__form-cols-2">
                <div class="ifp-input-text">
                  <p class="ifp-input-text__label">{{'Entity' | translate}}</p>
                  <div class="ifp-input-text__wrapper">
                    <div class="ifp-input-text__input ifp-input-text__input--disabled">{{registerForm.value.entity}}</div>
                    <em class="ifp-icon ifp-icon-tick-round"></em>
                  </div>
                </div>
                @if (decodedToken?.designation !== '') {
                  <div class="ifp-input-text">
                    <p class="ifp-input-text__label">{{'Job title' | translate}}</p>
                    <div class="ifp-input-text__wrapper">
                      <div class="ifp-input-text__input ifp-input-text__input--disabled">{{registerForm.value.jobTitle}}</div>
                      <em class="ifp-icon ifp-icon-tick-round"></em>
                    </div>
                  </div>
                }
              </div>

              <ifp-radio-group-progress [radioGroup]="accessLevels" [label]="'Data access level'" [isStatic]="true" class="ifp-register__radio-group"></ifp-radio-group-progress>
              @if(superUserRoles.includes(decodedToken?.role)) {
                <div class="ifp-input-mobile">
                  <p class="ifp-input-mobile__label">{{'Phone Number' | translate}}</p>
                  <div class="ifp-input-mobile__wrapper-outer">
                    <div class="ifp-input-mobile__wrapper">
                      <div class="ifp-input-mobile__dropdown" [ngClass]="{'ifp-input-mobile__dropdown--expand': dropdownExpanded}">
                        <div class="ifp-input-mobile__dropdown-selected">
                          <img [src]="selectedCountry.flagIcon" [alt]="selectedCountry.country" class="ifp-input-mobile__icon">
                          <span class="ifp-input-mobile__code">{{ selectedCountry.code }}</span>
                        </div>
                      </div>
                      <div class="ifp-input-text__input ifp-input-text__input--disabled">{{registerForm.value.phoneNumber}}</div>
                      @if(registerForm.controls['phoneNumber'].errors) {
                        <em class="ifp-icon ifp-icon-exclamation-round ifp-input-mobile__error-icon"></em>
                      } @else if (registerForm.value.phoneNumber && (!registerForm.controls['phoneNumber'].invalid)) {
                        <em class="ifp-icon ifp-icon-tick-round"></em>
                      }
                    </div>
                  </div>
                </div>
              } @else {
                <div class="ifp-input-text">
                  <p class="ifp-input-text__label">{{'Emirates ID' | translate}}</p>
                  <div class="ifp-input-text__wrapper">
                    <div class="ifp-input-text__input ifp-input-text__input--disabled">{{registerForm.value.eid}}</div>
                    <em class="ifp-icon ifp-icon-tick-round"></em>
                  </div>
                </div>
              }

              <ifp-button [label]="'NDA Agreement'" (ifpClick)="openNDAModal()" class="ifp-register__button"
              [buttonClass]="buttonClass.primary"></ifp-button>
              <ifp-button [label]="'Register'" (ifpClick)="onRegister()" class="ifp-register__button"
              [buttonClass]="(registerForm.valid && ndaStatus ? buttonClass.primary : buttonClass.disabled)"></ifp-button>
              <app-ifp-modal #ndaModal [modalClass]="'ifp-modal__body'">
                @if (showSignPad) {
                  <ifp-nda [ndaStatus]="ndaStatus" [user]="decodedToken?.role" (getNdaStatus)="signNda($event)" (closeNda)="closeNDAModal()"></ifp-nda>
                }
              </app-ifp-modal>
            </form>
          } @else {
            @if (error) {
              <div class="ifp-register__submitted">
                <h1 class="ifp-register__main-head">{{ 'Sorry' | translate}}!</h1>
                <p class="ifp-register__sub-title">{{error.message | translate}}</p>
                @if (error.description) {
                  <p class="ifp-register__desc">{{error.description | translate}}</p>
                }
              </div>
            } @else {
              <app-ifp-spinner></app-ifp-spinner>
            }
          }
        }
      }
    </div>
  </div>
  <div class="ifp-register__sec-2" style="background-image: url(../../../../assets/images/register-desk-bg.jpg)">
    <div class="ifp-register__sec-inner">
      <div class="ifp-register__logo-sec">
        <img src="../../../../assets/images/bayaan-blue.svg" alt="Bayaan" class="ifp-register__logo">
        <!-- <div class="ifp-register__logo-item"><a href="javascript:void(0)"
          class="ifp-register__logo-link" style="cursor: default"><img src="../../../../assets/images/src/assets/images/bayaan-blue.svg" alt="Bayaan" class="ifp-register__logo"></a></div>
        <div class="ifp-register__logo-item"><a href="https://scad.gov.ae/" [title]="'Go to SCAD' | translate" target="_blank"
          class="ifp-register__logo-link"><img src="../../../../assets/images/scad-white-logo.svg" alt="Statistics Centre" class="ifp-register__logo"></a></div> -->
      </div>
      <img src="../../../../assets/images/register-device-desk.png" [alt]="'Bayaan' | translate" class="ifp-register__img">
    </div>
  </div>
</div>
