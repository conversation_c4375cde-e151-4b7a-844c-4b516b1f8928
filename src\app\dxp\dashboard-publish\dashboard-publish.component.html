<div class="ifp-dashboard-publish">
  <app-ifp-breadcrumbs [pageData]="pageData" class="ifp-dxp-detail__breadcrumb"></app-ifp-breadcrumbs>
  <div class="ifp-dashboard-publish__header-wrapper">
    <h1 class="ifp-dashboard-publish__title">{{'Configure and Publish'| translate}}</h1>
    <!-- <p class="ifp-dashboard-publish__desc">{{'Power users will have the ability to publish dashboards and assign access
      to specific users.' | translate}}</p> -->
  </div>
  <div class="ifp-container">
    <form [formGroup]="dashboardForm">
      <div class="ifp-dashboard-publish__card">
        <div class="ifp-dashboard-publish__card-inner">
          <div class="ifp-dashboard-publish__sec-1">
            <div class="ifp-dashboard-publish__input">
              <label class="ifp-dashboard-publish__input-label">{{'Dashboard Name/Report' | translate}}</label>
              <input class="ifp-input ifp-dashboard-publish__input-field" placeholder="Enter Value"
                formControlName="dashboardName">
              <!-- @if (dashboardForm.get('dashboardName')?.invalid && dashboardForm.get('dashboardName')?.touched) {
              <div class="ifp-error-message">
                @if (dashboardForm.get('dashboardName')?.errors?.['required']) {
                <span>Dashboard name is required</span>
                }
                @if (dashboardForm.get('dashboardName')?.errors?.['minlength']) {
                <span>Dashboard name must be at least 3 characters</span>
                }
              </div>
              } -->
            </div>
            <div class="ifp-dashboard-publish__add-dropdown-section">
              <app-ifp-dropdown class="ifp-dashboard-publish__dropdown" [showTitle]="true"
                [title]="'Select BI Technology'" [dropDownItems]="technologyOptionList" [key]="'display_name'"
                [singleDefaultSelect]="!isEditing()" [selectedValue]="selectedBiTechDisplay"
                (dropDownItemClicked)="onTechnologySelected($event)"></app-ifp-dropdown>
            </div>
            <!-- Debug: Selected Technology: {{selectedTechnology()}} -->
            @if(selectedTechnology() !== 'table_builder' && selectedTechnology() !== 'tableau_cloud' && selectedTechnology() !== 'power_bi_cloud') {
            <div class="ifp-dashboard-publish__add-dropdown-section">
              <label class="ifp-dashboard-publish__input-label">{{'Server' | translate}}</label>
              <ifp-add-dropdown [initialOptions]="serverOptions" placeholder="Select or add a server"
                addButtonText="Add item" (itemAdded)="onServerAdded($event)"
                (selectionChanged)="onServerSelected($event)" class="ifp-dashboard-publish__add-dropdown">
              </ifp-add-dropdown>
            </div>

            <div class="ifp-dashboard-publish__input">
              <label class="ifp-dashboard-publish__input-label">{{'Embedded URL for light theme' | translate}}</label>
              <input class="ifp-input ifp-dashboard-publish__input-field" placeholder="Paste link here"
                formControlName="lightThemeUrl">
              @if (dashboardForm.get('lightThemeUrl')?.invalid && dashboardForm.get('lightThemeUrl')?.touched) {
              <div class="ifp-error-message">
                @if (dashboardForm.get('lightThemeUrl')?.errors?.['required']) {
                <span>Light theme URL is required</span>
                }
                @if (dashboardForm.get('lightThemeUrl')?.errors?.['pattern']) {
                <span>Please enter a valid URL</span>
                }
              </div>
              }
            </div>
            <div class="ifp-dashboard-publish__input">
              <label class="ifp-dashboard-publish__input-label">{{'Embedded URL for dark theme' | translate}}</label>
              <input class="ifp-input ifp-dashboard-publish__input-field" placeholder="Paste link here"
                formControlName="darkThemeUrl">
              @if (dashboardForm.get('darkThemeUrl')?.invalid && dashboardForm.get('darkThemeUrl')?.touched) {
              <div class="ifp-error-message">
                @if (dashboardForm.get('darkThemeUrl')?.errors?.['required']) {
                <span>Dark theme URL is required</span>
                }
                @if (dashboardForm.get('darkThemeUrl')?.errors?.['pattern']) {
                <span>Please enter a valid URL</span>
                }
              </div>
              }
            </div>
            }@else if (selectedTechnology() === 'table_builder') {
              <div class="ifp-dashboard-publish__add-dropdown-section">
                <app-ifp-dropdown
                  class="ifp-dashboard-publish__dropdown"
                  [showTitle]="true"
                  [title]="'Workspace Name'"
                  [singleDefaultSelect]="false"
                  [isMulti]="false"
                  [searchEnable]="true"
                  [disableTranslation]="true"
                  [dropDownItems]="zohoWorkspaceDropdownItems"
                  [selectedValue]="selectedZohoWorkspaceDisplay"
                  (dropDownItemClicked)="onZohoWorkspaceSelected($event)"></app-ifp-dropdown>
              </div>
              <div class="ifp-dashboard-publish__add-dropdown-section">
                <app-ifp-dropdown
                  class="ifp-dashboard-publish__dropdown"
                  [showTitle]="true"
                  [title]="'View/Report Name'"
                  [singleDefaultSelect]="false"
                  [isMulti]="false"
                  [searchEnable]="true"
                  [disableTranslation]="true"
                  [disableSingleValue]="true"
                  [dropDownItems]="zohoViewDropdownItems"
                  [selectedValue]="selectedZohoViewDisplay"
                  (dropDownItemClicked)="onZohoViewSelected($event)"></app-ifp-dropdown>
              </div>
            }@else if (selectedTechnology() === 'tableau_cloud') {
              <div class="ifp-dashboard-publish__add-dropdown-section">
                <label class="ifp-dashboard-publish__input-label">{{'Workspace Name' | translate}}</label>
                <div class="ifp-source-filter__input-wrapper">
                  <input
                    class="ifp-input ifp-source-filter__input-field"
                    [value]="tableauWorkspaceSearchTerm"
                    (click)="onTableauWorkspaceSearch($event)"
                    (keyup)="onTableauWorkspaceSearch($event)"
                    placeholder="{{'Enter Value' | translate}}"
                    autocomplete="off">
                  <div class="ifp-source-filter__suggestion-list">
                    <div
                      [suggestions]="tableauWorkspaceDropdownItems"
                      [isGlobalSearch]="false"
                      [disableFilter]="true"
                      [searchQuery]="tableauWorkspaceSearchTerm || ' '"
                      (setValue)="onTableauWorkspaceSelected($event)"
                      appSearchSuggestion>
                    </div>
                  </div>
                </div>
              </div>
              <div class="ifp-dashboard-publish__add-dropdown-section">
                <app-ifp-dropdown
                  class="ifp-dashboard-publish__dropdown"
                  [showTitle]="true"
                  [title]="'View/Report Name'"
                  [singleDefaultSelect]="false"
                  [isMulti]="false"
                  [searchEnable]="true"
                  [disableTranslation]="true"
                  [disableSingleValue]="true"
                  [isAppendBody]="true"
                  [zIndex]="1000"
                  [dropDownItems]="tableauViewDropdownItems"
                  [selectedValue]="selectedTableauViewDisplay"
                  (dropDownItemClicked)="onTableauViewSelected($event)"></app-ifp-dropdown>
              </div>
            }
            @if (selectedTechnology() === 'power_bi_cloud') {
              <div class="ifp-dashboard-publish__add-dropdown-section">
                <app-ifp-dropdown
                  class="ifp-dashboard-publish__dropdown"
                  [showTitle]="true"
                  [title]="'Workspace Name'"
                  [singleDefaultSelect]="false"
                  [isMulti]="false"
                  [searchEnable]="true"
                  [disableTranslation]="true"
                  [disableSingleValue]="true"
                  [dropDownItems]="powerBiWorkspaceDropdownItems"
                  [selectedValue]="selectedPowerBiWorkspaceDisplay"
                  (dropDownItemClicked)="onPowerBiWorkspaceSelected($event)"></app-ifp-dropdown>
              </div>

              <div class="ifp-dashboard-publish__add-dropdown-section">
                <label class="ifp-dashboard-publish__input-label">{{'Content Type' | translate}}</label>
                <div class="ifp-dashboard-publish__radio-group">
                  <label class="ifp-dashboard-publish__radio">
                    <input type="radio" name="pbiContentType" [checked]="powerBiContentType === 'report'" (change)="onPowerBiContentTypeChange('report')" />
                    <span>Report</span>
                  </label>
                  <label class="ifp-dashboard-publish__radio">
                    <input type="radio" name="pbiContentType" [checked]="powerBiContentType === 'dashboard'" (change)="onPowerBiContentTypeChange('dashboard')" />
                    <span>Dashboard</span>
                  </label>
                </div>
              </div>

              <div class="ifp-dashboard-publish__add-dropdown-section">
                <app-ifp-dropdown
                  class="ifp-dashboard-publish__dropdown"
                  [showTitle]="true"
                  [title]="'Content'"
                  [singleDefaultSelect]="false"
                  [isMulti]="false"
                  [searchEnable]="true"
                  [disableTranslation]="true"
                  [disableSingleValue]="true"
                  [dropDownItems]="powerBiContentDropdownItems"
                  [selectedValue]="selectedPowerBiContentDisplay"
                  (dropDownItemClicked)="onPowerBiContentSelected($event)"></app-ifp-dropdown>
              </div>
            }
            <div class="ifp-dashboard-publish__user">
              <ifp-panel-dropdown [userDropdown]="true" [label]="'Add User'" [enableSearch]="true"
                (multiSelected)="dropDownItemMultiClicked($event)" [isBoxType]="true" [enableSelectAll]="true"
                [multiSelect]="true" [key]="'value'" [multipleSelectedItems]="currentSelection()"
                [options]="sharedUserList()" class="ifp-dashboard-publish__user-list" [title]="'Add users'"
                (searchChanged)="onUserSearch($event)"></ifp-panel-dropdown>
              @if (sharedUserListSelected().length !==0) {
              <ifp-user-tag-group [isEdit]="true" (removeData)="removeData($event)" [tagList]="sharedUserListSelected()"
                class="ifp-dashboard-publish__user-group"></ifp-user-tag-group>
              }
            </div>
          </div>
          <div class="ifp-dashboard-publish__sec-2">
            <div class="ifp-dashboard-publish__wrapper">
              <h6 class="ifp-dashboard-publish__preview-title">{{'Preview'| translate}}</h6>

              <!-- BI Dashboard Preview Content -->
              <div class="ifp-dashboard-publish__preview-content">
                @if (showPowerBiReport() && powerBiReportConfig()) {
                <!-- Power BI Report Preview -->
                <div class="ifp-dashboard-publish__component-container">
                  <powerbi-report [embedConfig]="powerBiReportConfig()!" [cssClassName]="'ifp-dashboard-publish__report'"
                    [eventHandlers]="powerBiEventHandlers">
                  </powerbi-report>
                </div>
                } @else if (showPowerBiDashboard() && powerBiDashboardConfig()) {
                <!-- Power BI Dashboard Preview -->
                <div class="ifp-dashboard-publish__component-container">
                  <powerbi-dashboard [embedConfig]="powerBiDashboardConfig()!" [cssClassName]="'ifp-dashboard-publish__report'"
                    [eventHandlers]="powerBiEventHandlers">
                  </powerbi-dashboard>
                </div>
                } @else if (showTableauPreview()) {
                <!-- Tableau Dashboard -->
                <div class="ifp-dashboard-publish__tableau-container">
                  <app-ifp-dashboard-test [dashboardSrc]="tableauUrl()" [dashboardId]="'tableau-preview'"
                    [dashboardWidth]="'100%'" [dashboardHeight]="400" [toolbar]="'no'" [hideTabs]="true"
                    [hostUrl]="getBiHostUrl()" [token]="tableauToken()" class="ifp-dashboard-publish__tableau">
                  </app-ifp-dashboard-test>
                </div>
                } @else if (showZohoPreview()) {
                <!-- Zoho Dashboard via iframe-loader -->
                <div class="ifp-dashboard-publish__zoho-container">
                  <app-iframe-loader #zohoIframe [hideHeader]="true"></app-iframe-loader>
                </div>
                } @else if (isLoadingPreview()) {
                <!-- Loading state -->
                <div class="ifp-dashboard-publish__loading">
                  <app-ifp-spinner></app-ifp-spinner>
                  <p>Loading dashboard preview...</p>
                </div>
                } @else {
                <!-- Default placeholder -->
                <div class="ifp-dashboard-publish__placeholder">
                  <img src="../../../assets/images/dashboard-publish.svg" class="ifp-dashboard-publish__img" />
                  <!-- <p class="ifp-dashboard-publish__note"><em
                      class="ifp-icon ifp-dashboard-publish__note-icon ifp-icon-info-round"></em><span
                      class="ifp-dashboard-publish__note-text">Note</span>{{'Power users will have the ability to
                    publish
                    dashboards and assign access to specific users.'| translate}}</p> -->
                </div>
                }
              </div>
            </div>
          </div>
        </div>
        <div class="ifp-dashboard-publish__footer">
          <!-- need to update the preview icon  -->
          <ifp-button class="ifp-dashboard-publish__btn" [label]="'Preview'"
            [buttonClass]="(isPreviewEnabled() ? buttonClass.buttonHoverBlueSecondary : buttonClass.disabled) +' '+ buttonClass.normalAplabetic"
            [iconClass]="'ifp-icon-save'" (ifpClick)="onPreview()"></ifp-button>
          <ifp-button class="ifp-dashboard-publish__btn" [label]="'Save as Draft'"
            [buttonClass]="(dashboardForm.value.dashboardName.length ? buttonClass.buttonHoverBlueSecondary : buttonClass.disabled) +' '+ buttonClass.normalAplabetic"
            [iconClass]="'ifp-icon-save'" (ifpClick)="onSubmit(true)"></ifp-button>
          <ifp-button class="ifp-dashboard-publish__btn" [label]="isEditing() ? 'Update' : 'Publish'"
            [buttonClass]="(isFormValid && dashboardForm.value.dashboardName.length ? buttonClass.hoverBlue : buttonClass.disabled) + ' ' + buttonClass.normalAplabetic"
            [iconClass]="'ifp-icon-rightarrow'" (ifpClick)="onSubmit(false)">
          </ifp-button>
        </div>
      </div>
    </form>
  </div>

</div>
