import { NgClass } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';


@Component({
    selector: 'ifp-accordion',
    imports: [TranslateModule, NgClass],
    templateUrl: './ifp-accordion.component.html',
    styleUrl: './ifp-accordion.component.scss'
})
export class IfpAccordionComponent {
  @Input() heading: string = '';
  @Input() isExpanded: boolean | undefined = false;

  @Output() selectAccordian: EventEmitter<boolean> = new EventEmitter<boolean>();

  openExpand(){
    this.selectAccordian.emit(true);
  }
}
