import { DatePipe, Location, NgClass } from '@angular/common';
import { Compo<PERSON>, On<PERSON>estroy, OnInit, ViewChild } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IfpSearchRoundComponent } from '../../molecule/ifp-search-round/ifp-search-round.component';
import { IfpButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpDbListCardComponent } from '../../molecule/ifp-db-list-card/ifp-db-list-card.component';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';
import { DashboadListItem } from '../dashboard.interface';
import { Router } from '@angular/router';
import { PaginationComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/pagination/pagination.component';
import { Subject, debounceTime } from 'rxjs';
import { SubSink } from 'subsink';
import { IfpCardLoaderComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { IfpNoDataComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpRemoveCardComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-remove-card/ifp-remove-card.component';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { IfpShareModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-share-modal/ifp-share-modal.component';
import { ShareAppsData, ShareResponse } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-share-modal/ifp-share-modal.interface';
import { IfpTabComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';
import { LabelData } from 'src/app/scad-insights/core/interface/atom/ifp-category-label.interface';
import { dbdlistTabs } from '../../organism/ifp-chart-toolbar/ifp-chart-toolbar.constants';
import { TabChangeEvent } from '../../organism/ifp-chart-toolbar/ifp-chart-toolbar.interface';
import { UsageDashboardLogService } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard.constants';
import { IfpHorizontalTabComponent } from "src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-horizontal-tab/ifp-horizontal-tab.component";
import { IfpPanelDropdownComponent, PanelDropdownOptions } from "src/app/ifp-analytics/molecule/ifp-panel-dropdown/ifp-panel-dropdown.component";
import { IfpSearchComponent } from "src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-search/ifp-search.component";
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { IfpBreadcrumbsComponent } from "src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component";

@Component({
  selector: 'ifp-dashboard-list',
  templateUrl: './ifp-dashboard-list.component.html',
  styleUrl: './ifp-dashboard-list.component.scss',
  imports: [TranslateModule, IfpSearchRoundComponent, IfpButtonComponent, IfpDbListCardComponent, NgClass, DatePipe, PaginationComponent,
    IfpCardLoaderComponent, IfpNoDataComponent, IfpModalComponent, IfpRemoveCardComponent, IfpShareModalComponent, IfpTabComponent, IfpHorizontalTabComponent, IfpPanelDropdownComponent, IfpSearchComponent, IfpBreadcrumbsComponent]
})
export class IfpDashboardListComponent implements OnInit, OnDestroy {

  @ViewChild('modal') modal!: IfpModalComponent;
  @ViewChild('shareAppsModal') shareAppsModal!: IfpModalComponent;

  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;

  public isFilterApplied: boolean = false;
  public totalCount: number = 0;
  public dashboardList: DashboadListItem[] = [];
  public search = '';
  public offsetPage = 1;
  public offset: number = 0;
  public limit: number = 10;
  public subs = new SubSink();
  private searchInput = new Subject<string>();
  public searchKeyword!: string;
  public isLoading: boolean = false;
  public currentRemoveText!: string;
  private deleteId!: string;
  public recentlyAddedEnabled: string = 'desc';
  public selectedDashboards: string[] = [];
  public dashboardTabs: LabelData[] = dbdlistTabs;
  public selectedTabView: { item: { name: string }; index: number } = {
    item: {
      name: dbdlistTabs[0].name
    },
    index: 0
  };

  private sessionId!: string;

  public sortOptions = [
    {
      order: 'desc',
      sort: 'createdAt',
      icon: 'ifp-icon-downarrow',
      value: 'Newest First',
      key: '-updated_at',
      checked: false
    },
    {
      order: 'asc',
      sort: 'createdAt',
      icon: 'ifp-icon-uparrow',
      value: 'Oldest First',
      key: 'updated_at',
      checked: false
    }
  ];

    pageData: PageData[] = [
      {
        title: 'Home',
        route: '/home'
      },
      {
        title: 'My Bayaan',
        route: '/store'
      },
      {
        title: 'Visualization Builder',
        route: ''
      }
    ];



  public selectedSortOption: PanelDropdownOptions = this.sortOptions[0];


  constructor(public location: Location, private _dashboardService: DashboardService, private router: Router, private _toaster: ToasterService,
    private _translate: TranslateService, public _themeService: ThemeService, private _tooter: ToasterService, private log: UsageDashboardLogService) {
    localStorage.removeItem(this._dashboardService.selectedCards);
    this.subs.add(this.searchInput
      .pipe(debounceTime(800))
      .subscribe((value) => {
        this.searchKeyword = value;
        this.offsetPage = 1;
        this.getDashboards();
      }));
  }


  onSearch(keyword: string) {
    // if (!keyword) {
    //   return;
    // }
    this.searchKeyword = keyword;
    this.searchInput.next(keyword);
  }


  ngOnInit(): void {
    this.getDashboards();
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.dashboard, this.log.currentTime);
  }

  onApplyFilter() {
    this.isFilterApplied = !this.isFilterApplied;
    // filter code
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.dashboard, this.log.currentTime);
  }

  getDashboards() {
    this.isLoading = true;
    this._dashboardService.getDashboards(this.offsetPage, this.limit, this.searchKeyword, this.selectedTabView.item.name, this.recentlyAddedEnabled).subscribe({
      next: resp => {
        this.dashboardList = resp.data;
        if (!this.recentlyAddedEnabled) {
          this.dashboardList = this.dashboardList.reverse();
        }
        this.totalCount = resp.totalCount;
        this.isLoading = false;
      },
      error: _error => {
        this.isLoading = false;
      }
    });
  }

  goToDashboardBuilder() {
    this.router.navigate(['store/dashboard-builder-basic']);
  }

  onPageChange(event: any) {
    this.offset = event + 1;
    this.offsetPage = (event / this.limit) + 1;
    this.getDashboards();

  }

  limitChanged(event: any) {
    this.offsetPage = 1;
    this.offset = 1;
    this.limit = event;
    // console.log("limit", this.limit);
  }

  openDetail(id: string) {
    const list: any = {};
    const dataIndex = this.dashboardList.findIndex(x => x.id == id);
    if (this.selectedTabView.item.name === 'Sent') {
      list.key = this._translate.instant('Recipients');
      list.value = this.dashboardList[dataIndex].recepientEmails?.toString();
    } else if (this.selectedTabView.item.name === 'Received') {
      list.key = this._translate.instant('Received from');
      list.value = this.dashboardList[dataIndex].shareEmail;
    }
    this.router.navigate(['store/dashboard-builder-basic'], { queryParams: { id: id, tab: this.selectedTabView.item.name, sendData: JSON.stringify(list) } });
  }

  editOrDelete(event: any, data: any) {
    if (event == 'edit') {
      this.editDashboard(data.id);
    }
    if (event == 'delete') {
      this.opendeleteModel(data);
    }
  }

  editDashboard(id: string) {
    this.router.navigate(['store/dashboard-builder-basic'], { queryParams: { id: id, mode: 'edit', tab: this.selectedTabView.item.name } });
  }

  deleteDashboard() {
    this._dashboardService.deleteDashboard(this.deleteId, this.selectedTabView.item.name).subscribe(_resp => {
      this._toaster.success('Dashboard deleted Successfully');
      this.getDashboards();
    });
  }

  recentlyAdded() {
    if (this.recentlyAddedEnabled == 'desc') {
      this.recentlyAddedEnabled = 'asc';
    } else {
      this.recentlyAddedEnabled = 'desc';
    }
    this.getDashboards();
  }

  goToStore() {
    this.router.navigate(['store']);
  }

  opendeleteModel(data: any) {
    this.deleteId = data.id;
    this.currentRemoveText = `${this._translate.instant('Do you want to remove the')} ${data.name}? `;
    this.modal.createElement();
  }

  closeModel(event: any) {
    if (event) {
      this.deleteDashboard();
    }
    this.modal.removeModal();
  }


  // ####### for share functions start ####### //

  updateSelectDashboard(event: { id: string, checked: boolean }) {
    if (event.checked) {
      this.selectedDashboards.push(event.id);
    } else {
      this.selectedDashboards = this.selectedDashboards.filter(x => x !== event.id);
    }
  }

  shareSelected() {
    this.shareAppsModal.createElement();
  }

  closeShare() {
    this.shareAppsModal.removeModal();
  }



  onShareApps(data: ShareAppsData) {
    data.dashboards = this.selectedDashboards;
    data.shareNodes = undefined;
    this.subs.add(
      this._dashboardService.shareApps(data).subscribe((resp: ShareResponse) => {
        if (resp) {
          this._tooter.success(resp.message);
          this.selectedDashboards = [];
        }
      })
    );
  }

  changeTabView(event: TabChangeEvent) {
    if (event) {
      this.selectedTabView = event;
      this.resetPages();
    }
  }


  resetPages() {
    this.offsetPage = 1;
    this.offset = 1;
    this.searchKeyword = '';
    this.dashboardList = [];
    this.getDashboards();
  }

  onSortOptionChange(event: any) {
    console.log("event", event)
    if (this.recentlyAddedEnabled == 'desc') {
      this.recentlyAddedEnabled = 'asc';
    } else {
      this.recentlyAddedEnabled = 'desc';
    }
    this.getDashboards();
  }

  ngOnDestroy(): void {
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
  }
}


