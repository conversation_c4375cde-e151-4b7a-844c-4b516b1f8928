import { Component, EventEmitter, inject, Input, OnInit, Output, signal, ViewChild, WritableSignal } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpPrepSaveModalComponent } from '../ifp-prep-save-modal/ifp-prep-save-modal.component';
import { IfpAdvancePrepService } from '../../data-prep/ifp-advance-prep/service/ifp-advance-prep.service';
import { selectNodeStoreAdvance } from '../../data-prep/ifp-advance-prep/store/advance-node-store/advance-node.store';
import { connectionType, prepsApiEndpoints } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { environment } from 'src/environments/environment';
import { DownLoadService } from 'src/app/scad-insights/core/services/download-service/download.service';
import { workFlowState } from '../../data-prep/ifp-data-prep/constants/ifp-state.contants';
import { NgClass } from '@angular/common';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { IfpModalService } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';
import { Router } from '@angular/router';
import { ScUploadModelComponent } from '../../../dashboard-builder/molecule/sc-upload-model/sc-upload-model.component';
import { slaService } from 'src/app/scad-insights/core/services/sla/sla.service';

@Component({
    selector: 'ifp-adv-destination-tool',
    templateUrl: './ifp-adv-destination-tool.component.html',
    styleUrl: './ifp-adv-destination-tool.component.scss',
    imports: [TranslateModule, IfpButtonComponent, IfpModalComponent, IfpPrepSaveModalComponent, NgClass, IfpSpinnerComponent, ScUploadModelComponent]
})
export class IfpAdvDestinationToolComponent implements OnInit {

  constructor(public _prepService: IfpAdvancePrepService, private _toaster: ToasterService, private _downloadService: DownLoadService, private _translate: TranslateService,
    private _modalService: IfpModalService, private _router: Router, private _sla: slaService) {
    this._prepService.cancelWorkFlow.subscribe(resp => {
      if (resp) {
        this.downloadingFile.set(false);
        this._prepService.workflowRunningStatus.set(false);
      }
    });
  }


  @ViewChild('saveDataModal') saveDataModal!: IfpModalComponent;
  @ViewChild('dashboardModel') dashboardModel!: IfpModalComponent;

  @Output() process = new EventEmitter();

  @Input() currentNodeId?: string = '';
  @Input() sourceData: WritableSignal<boolean> = signal(false);
  @Input() workFlowRunning = signal(false);
  @Input() emptyConnection = signal(true);
  @Input() currentConection: WritableSignal<boolean> = signal(false);
  @Input() errorMessageForTools?: string;


  public buttonClass = buttonClass;
  public download = false;
  public readonly advanceStore = inject(selectNodeStoreAdvance);
  public fileName: string = '';
  public downloadingFile: WritableSignal<boolean> = signal(false);
  public analyticsTools: {
    title: string;
    description: string;
    icon: string;
    iconColor: string;
    isDisabled: boolean;
    route: string;
    key?: string;
  }[] = [];

  public analyticsToolsCurrent = [
    {
      title: 'Advance Analytics Tool',
      description: 'Effortlessly generate powerful machine learning models with advanced automation and optimization',
      icon: 'ifp-icon-advanced-analytics',
      iconColor: ifpColors.purple,
      isDisabled: false,
      route: '/analytics/auto-ml/run'
    },
    {
      title: 'Data Exploration Tool',
      description: 'Investigate relationships between variables through correlation analysis, gaining deeper insights into your data',
      icon: 'ifp-icon-desktop-chart',
      iconColor: ifpColors.yellow,
      isDisabled: false,
      route: 'analytics/exploratory/data-analysis'
    },
    {
      title: 'Dashboard Builder',
      description: 'Effortlessly create personalized dashboards by integrating saved indicators from my bookmarks or the platform',
      icon: 'ifp-icon-builder',
      iconColor: ifpColors.brickRed,
      isDisabled: false,
      key: 'dashboard',
      route: 'store/dashboard-builder-basic'
    }
  ];

  public tabData = [
    {
      name: 'Instructions'
    }
  ];

  public toolForward = false;
  public currentTool!:  {
    title: string;
    description: string;
    icon: string;
    iconColor: string;
    isDisabled: boolean;
    route: string;
    key?: string;
  };

  public name = '';

  ngOnInit(): void {
    this.analyticsTools = [];
    if (this._sla.permission().autoML) {
      this.analyticsTools.push(this.analyticsToolsCurrent[0]);
    }
    if (this._sla.permission().dataExploration) {
      this.analyticsTools.push(this.analyticsToolsCurrent[1]);
    }
    if (this._sla.permission().dashBoardBuilder) {
      this.analyticsTools.push(this.analyticsToolsCurrent[2]);
    }
    this._prepService.destinationSaveModelOpen.subscribe(resp => {
      if (resp && this.saveDataModal) {
        this.download = true;
        this._modalService.removeAllModal();
        this.saveDataModal?.createElement();
      }
    });
  }

  onDownloadClick(isDownload: boolean) {
    this.download = isDownload;
    this.saveDataModal.createElement();
  }

  closeModal(event: string) {
    if (this.toolForward && event === 'cancel') {
      this.download = true;
      this.updateConfig(`save_${this.currentNodeId}`, false);
      this.openTool(this.currentTool, false);
    }
    this.saveDataModal.removeModal();
  }

  goTOTool(tool: {
    title: string;
    description: string;
    icon: string;
    iconColor: string;
    isDisabled: boolean;
    route: string;
    key?: string;
  }) {
    const columnKeyength = Object.keys(this._prepService.previewTableData?.data?.[0] ?? {});
    if (tool.key == 'dashboard' && (this._prepService.previewTableData.data.length > 200 || columnKeyength.length > 25)) {
      this.dashboardModel.createElement();
      return;
    }
    this.currentTool = tool;
    this.download = true;
    this.toolForward = true;
    this.saveDataModal?.createElement();
  }


  updateConfig(filename: string, workFlow:boolean = true) {
    const config = {
      type: 'csv',
      name: filename,
      is_download: this.download
    };
    this.fileName = filename;
    this.advanceStore.updateNode(this.currentNodeId ?? '', 'Destination Tool', connectionType.outputTool, config, 'destination tool updated');
    this._prepService.nodeChangeDetect.next('node updated');
    if (!workFlow) {
      return;
    }
    this.process.emit(false);
    const subsValue = this._prepService.processStatus.subscribe({
      next: data => {
        if (data?.workflow_status === workFlowState.completed) {
          subsValue?.unsubscribe();
          if (this.download) {
            this.downloadFile();
          } else {
            this._toaster.success('Saved to the library!');
          }
        }
      },
      error: _error => {
        this.downloadingFile.set(false);
        this._prepService.workflowRunningStatus.set(false);
      }
    });

  }

  exportData(event: { name: string, type: string }) {
    this.name = event.name;
    if (this.toolForward) {
      this.download = false;
      this.updateConfig(event.name, false);
      this.openTool(this.currentTool, true);
    } else if (this.download) {
      this.downloadingFile.set(true);
      this._prepService.workflowRunningStatus.set(true);
      this.updateConfig(event.name);
    } else {
      this.updateConfig(event.name);
    }
    this.saveDataModal.removeModal();
  }

  downloadFile() {
    // this._toaster.success('Download started!');
    const subscribe = this._prepService.getDownloadRequest(`${environment.prepbaseUrl}${prepsApiEndpoints.datasetDownlaod}${this._prepService.currentNodeId()}/download/csv/`).subscribe({
      next: data => {
        const matches = (data.headers.get('Content-Disposition'))?.match(/"(.*?)"/);
        // const nameValue = matches ? matches[1] : '.xlsx';
        this._downloadService.downloadFiles(data.body, this.fileName);
        this._toaster.success(`${this.fileName} ${this._translate.instant('Download completed successfully!')}`);
        // this._prepService.workflowRunningStatus.set(true);
        // this._toaster.success('Data Saved Successfully');
        this.downloadingFile.set(false);
        this._prepService.workflowRunningStatus.set(false);
        subscribe.unsubscribe();
      }
      // error: err => {
      //   const error = err?.error;
      //   this.errorHandler(error);
      //   this._prepService.processActive.set(true);
      //   this._store.dispatch(loadNode[loadRemoveConnectionUploadUpdateName]({ objectId: this._prepService.currentNodeId ?? ''}));
      //   this._toaster.error(`${this._translate.instant('Download failed!')}` );
      // }
    });
  }

  checkValidation() {
    return false;
  }

  openTool(tool: {
    title: string;
    description: string;
    icon: string;
    iconColor: string;
    isDisabled: boolean;
    route: string;
    key?: string;
  }, save: boolean = false) {

    const key = tool.key == 'dashboard' ? 'prepId' : 'id';
    const nodeData: {source:string, anchor: number} = this.getSourceNode();
    const queryParams = { [key]: nodeData.source, anchor: nodeData.anchor };
    if (tool.route && tool.key == 'dashboard' && !save) {
      this._router.navigate([tool.route], { queryParams: queryParams });
      this.advanceStore.updateStore({
        name: 'Workflow',
        description: '',
        nodes: [],
        connections: []
      });
      this._prepService.isWorkFlowStarted.set(false);
      this.clearPrveiew();
    } else {
      this.openTools(tool, save, queryParams);
    }
  }

  clearPrveiew() {
    this._prepService.previewTableData = {
      heading: [],
      data: []
    };
    this._prepService.showPreview.set(false);
  }

  getSourceNode() {
    const sourceNodes: any = this.advanceStore.selectNodeDestinationConnection().destinationConnections(this.currentNodeId ?? '');
    return {source: sourceNodes[0].source, anchor:  sourceNodes[0].source_anchor};
  }

  async openTools(tool: {
    title: string;
    description: string;
    icon: string;
    iconColor: string;
    isDisabled: boolean;
    route: string;
    key?: string;
  }, save: boolean = false, currentNodeId: {
    [x: string]: string | number;
    anchor: number;
}) {

    if (!save) {
      const dataSetId = await this.getDataSet();
      if (dataSetId && dataSetId != null && !save) {
        this._router.navigate([tool.route], { queryParams: { id: dataSetId, adv: 'y' } });
        this.clearPrveiew();
        this.advanceStore.updateStore({
          name: 'Workflow',
          description: '',
          nodes: [],
          connections: []
        });
        this._prepService.isWorkFlowStarted.set(false);
        return;
      }
    }

    this.process.emit(false);
    const subsValue = this._prepService.processStatus.subscribe({
      next: async data => {
        if (data?.workflow_status === workFlowState.completed) {
          subsValue?.unsubscribe();
          const dataId = await this.getDataSet();
          if (tool.route && tool.key == 'dashboard') {
            this._router.navigate([tool.route], { queryParams: currentNodeId });
          } else {
            this._router.navigate([tool.route], { queryParams: { id: dataId, basic: 'y' } });
          }
          this.advanceStore.updateStore({
            name: 'Workflow',
            description: '',
            nodes: [],
            connections: []
          });
          this._prepService.isWorkFlowStarted.set(false);
        } else   if (data?.workflow_status === workFlowState.error) {
          this.download = false;
        }
        this.clearPrveiew();
      },
      error: _error => {
        this.downloadingFile.set(false);
        this._prepService.workflowRunningStatus.set(false);
        this.workFlowRunning.set(false);
      }
    });
  }

  getDataSet() {
    return new Promise(resolve => {
      this._prepService.getDataSet(`${prepsApiEndpoints.getDataSet}${this._prepService.currentNodeId()}/getdataset/`).subscribe({
        next: next => {
          resolve(next.dataset_id);
        },
        error: _error => {
          resolve(null);
        }
      });
    });
  }

  // errorHandler(error: any) {
  //   for (const key in error) {
  //     if (Object.prototype.hasOwnProperty.call(error, key)) {
  //       const element = error[key];
  //       let data = '';
  //       if (Array.isArray(element) ) {
  //         element.forEach((elementValue:string) => {
  //           data =  `${data} ${elementValue}`;
  //         });
  //       } else {
  //         data = element;
  //       }
  //       if (data!== '') {
  //         this._toaster.error(data);
  //       }
  //     }
  //   }
  // }

  closeDashBordModel(_event: boolean) {
    this.dashboardModel.removeModal();
  }

}
