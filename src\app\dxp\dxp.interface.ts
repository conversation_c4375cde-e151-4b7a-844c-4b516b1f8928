export interface ProductDetail {
  id: string;
  name: string;
  organization: Assets;
  image: string;
  assets: Assets[];
  onboardingState: OnboardingState
}

export interface OnboardingState {
  status: string;
  displayName: string;
  message: string;
}

export interface Assets {
  title: any;
  id: string;
  name: string;
  createdAt: string;
}

export interface DxpPlotData {
  chartType: string;
  title: string;
  subtitle: string;
  xAxis: string;
  yAxis: string;
  series: {
    xAxis: {
      categories: string[];
    };
    series: {
      name: string;
      data: number[];
    }[];
  };
  tooltip: {
    enabled: boolean;
    shared: boolean;
  };
  plotOptions: Record<string, any>; // need to change - currently exact type is unknow.
}

export interface ConvertedChartData {
  category?: string[];
  series: {
    name: string;
    data: number[];
    is_total_series?: boolean;
  }[]
  xAxisLabel?:string;
  yAxisLabel?:string;
  unit:string;
}

export interface DxpUser {
  id: string;
  name: string;
  email: string;
}

export interface DxpUserListingResponse {
  total: number;
  totalPages: number;
  data: DxpUser[];
}

export interface ListingPageData {
  totalCount: number;
  page: number;
  limit: number;
  data: ListingPageItem[];
}




export interface DxpDetail {
  icon?:string;
  id: number;
  objectId: string;
  title: string;
  subTitle: string;
  sourceAssetId: string;
  sourceProductId: string;
  visualizationConfig: VisualizationConfigDxpDetail;
  updatedAt: string;
  createdAt: string;
  createdBy: CreatedByDxpDetail;
  approvalRequest: ApprovalRequest;
  userAccess: CreatedByDxpDetail[];
  is_draft: boolean;
  user: User;
  product: Product;
  asset: Product;
  filterPanel: DxpFilterPanel2[];
  series: Series2;
  legendPanel: LegendPanelDxpDetail[];
}

export interface LegendPanelDxpDetail {
  column: string;
  data_type: string;
  selected_values: string[];
  default: boolean;
  options: string[];
  unit:string;
}
interface Series2 {
  xAxis: XAxis;
  series: Series[];
}

interface Series {
  name: string;
  data: number[];
}

interface XAxis {
  categories: string[];
}

export interface DxpFilterPanel2 {
  column: string;
  inferred_dtb_dt_format?: string;
  data_type: string;
  label: string;
  default?: string | string[];
  defaultArray?: string[];
  options: string[];
  selected?: string[];
}

interface Product {
  id: string;
  displayName: string;
}

interface User {
  id: string;
  name: string;
  generalizedRole: string;
}

interface ApprovalRequest {
  id: string;
  status: string;
  assigneeId: string;
  is_reverted: boolean;
  approvalActions: ApprovalAction;
}

interface ApprovalAction {
  claim?: Claim;
  approve?: Claim;
  reject?: Claim;
  revert?: Claim;
}

interface Claim {
  action: string;
  legend: string;
  active?: boolean;
  enabled: boolean;
  visible: boolean;
}

interface CreatedByDxpDetail {
  name: string;
  id: string;
}

interface VisualizationConfigDxpDetail {
  source_filter: SourceFilterDxp;
  chart_configuration: Chartconfiguration;
}

interface Chartconfiguration {
  default_chart_type: string;
  unit?: string;
  x_axis: Xaxis;
  y_axis: Xaxis;
  filterPanel: FilterPanel[];
  legendPanel: LegendPanel[];
}

interface LegendPanel {
  column: string;
  data_type: string;
  selected_values: string[];
  default: boolean;
}

interface FilterPanel {
  column: string;
  data_type: string;
  label: string;
  default: string;
}

interface Xaxis {
  label: string;
  axis: Axis;
}

interface Axis {
  column: string;
  data_type: string;
  aggregator: string;
}

export interface SourceFilterDxp {
  groups: GroupDxp[];
  global_operator: string;
}

export interface GroupDxp {
  conditions: ConditionDxp[];
  operator: string;
}

export interface ConditionDxp {
  column: string;
  data_type: string;
  comparator: string;
  inferred_dtb_dt_format?:string;
  value: string | string[];
}


export interface DxpFilterConfig {
  chartType: string;
  title: string;
  subtitle: string;
  xAxis: string;
  yAxis: string;
  series: Series2;
  tooltip: Tooltip;
  plotOptions: Record<string, string>; // need to change - currently exact type is unknow.
}



interface Tooltip {
  enabled: boolean;
  shared: boolean;
}

interface Series2 {
  xAxis: XAxis;
  series: Series[];
}

interface Series {
  name: string;
  data: number[];
}

interface XAxis {
  categories: string[];
}

export interface DxpComments {
  id: string;
  requestId: string;
  content: string;
  commentType: string;
  createdAt: string;
  updatedAt: string;
  userDetails?: UserDetails;
}

interface UserDetails {
  name: string;
  email: string;
  role: string;
}

export interface UserConfigVisualization {
  column: string;
  filterOptions: FilterOption[];
  filterLabel: string;
  defaultValue: string;
}

interface FilterOption {
  id: string;
  value: string;
  index?: number;
}

export interface SideBarDxp {
  user: UserDxp;
  filters: FiltersDxp;
  tabs: Record<string, DxpKpi>;
}


export interface DxpKpi {
  showCount?: boolean;
  label: string;
  param: string;
  order: number;
  description?: string;
  count?: number;
  subMenus?: SubMenu[];
  permission: string[];
}

interface SubMenu {
  label: string;
  description: string;
  param: string;
  permission: string[];
}


export interface FiltersDxp {
  creators: CreatorDxp[];
  entities: EntityDxp[];
}

export interface EntityDxp {
  id: string;
  name: string;
}

export interface CreatorDxp {
  name: string;
  id: string;
  entityId: string;
}

export interface UserDxp {
  id: string;
  name: string;
  entityId: string;
  generalizedRole: string;
}


export interface TabSelectionData {
  param: string;
  subMenus?: any[];
  selectedMenuItem?: any;
  tabKey?: string;        // Added: Store the main tab key (e.g., 'my-kpis')
  subMenuKey?: string;    // Added: Store the submenu key (e.g., 'my-kpis-complete')
  userRole?: string;      // Added: Store the user role (e.g., 'BUILDER')
  key?:string;
}


export interface  sideBarSelectDxp {
  name: string;
  param: string;
  key: string;
  showCount: boolean;
  subMenus: SubMenu[];
  description?: string;
  count?: number;
}

export interface DxpColumnList {
  name: string;
  data_type: string;
  available_comparators?: AvailableComparator[];
  possible_aggregations?: PossibleAggregations;
  icon?: string;
  checked?: boolean;
  inferred_dtb_dt_format?: string;
  options?: { id:string, value: string, checked?: boolean }[];
  selectedValues?: { id:string, value: string, checked?: boolean }[];
}

export interface DxpCurrentLegend  {
  name: string;
  data_type: string;
  available_comparators?: AvailableComparator[];
  possible_aggregations?: PossibleAggregations;
  index?: number;
  selectedValues?: { id:string, value: string, checked?: boolean }[];
  options?: { id:string, value: string, checked?: boolean }[];
  checked?: boolean;
  default?: boolean;
}

export interface DxpSelectedLegendOption  { id:string, value: string }

interface PossibleAggregations {
  x_aggregations: string[];
  y_aggregations: string[];
}

interface AvailableComparator {
  value: string;
  display_name: string;
}

export interface SourceFiltersDxp {
  column: DxpColumnList;
  comparator: AvailableComparator;
  value: string;
  data_type: string;
  inferred_dtb_dt_format?: string ;
}


export interface DxpLegend {
  id: string;
  value: string;
  checked?: boolean;
}

export  interface DxpAxisVariable {
  name: string;
  data_type: string;
  available_comparators?: AvailableComparator[];
  possible_aggregations?: PossibleAggregations;
  icon?: string;
  aggregations: string[];
  selectedAggregation: string;
  checked?: boolean;
}
export interface ResponseForSaveKpi {
  objectId: string;
  id: number;
  title: string;
  subTitle: string;
  sourceAssetId: string;
  sourceProductId: string;
  iconClassName: string;
  visualizationConfig: VisualizationConfig;
  createdById: string;
  status: string;
  updatedAt: string;
  createdAt: string;
}

export interface SourceFilterPayLoadDxp {
  groups: Group[];
  global_operator: string;
}

interface Group {
  conditions: Condition[];
  operator: string;
}

interface Condition {
  column: string;
  comparator: string;
  data_type: string;
  value: string;
  inferred_dtb_dt_format?: string;
}


export interface ListingPageItem {
  id: number;
  objectId: string;
  title: string;
  subTitle: string;
  sourceAssetId: string;
  visualizationConfig: VisualizationConfig;
  createdAt: string;
  updatedAt: string;
  sourceProductId: string;
  status: string;
  createdBy: CreatedBy;
  icon: string;
  sourceProductSubscription: SourceProductSubscription;
  product: Product;
  asset: Asset;
  overview: Overview;
  filterPanel: FilterPanel[];
  series?:series;
}

interface series {
  xAxis?: XAxis;
  series?: Series[];
}

interface Series {
  name: string;
  data: number[];
}
interface Overview {
  value: number;
  unit: string;
}

interface Asset {
  id: string;
  displayName: string;
}

interface Product {
  id: string;
  displayName: string;
  organization: Entity;
}

interface SourceProductSubscription {
  hasSubscription: boolean;
  productUrl?: string;
}

interface CreatedBy {
  designation: string;
  id: string;
  name: string;
  entity: Entity;
}

interface Entity {
  id: string;
  name: string;
}

interface VisualizationConfig {
  source_filter: Sourcefilter;
  chart_configuration: Chartconfiguration;
}



interface Axis {
  column: string;
  data_type: string;
  aggregator: string;
}

interface FilterPanel {
  column: string;
  label: string;
  comparator: string;
  default: string;
  data_type: string;
  options: any[];
}

interface Sourcefilter {
  groups: Group[];
  global_operator: string;
}

interface Group {
  conditions: Condition[];
  operator: string;
}

interface Condition {
  column: string;
  comparator: string;
  data_type: string;
  value: string;
}
