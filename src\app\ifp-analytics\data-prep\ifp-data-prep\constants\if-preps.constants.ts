export const prepsApiEndpoints = {
  sourceUpload: '/v1/dataset/create/',
  indicatorUpload: '/v1/dataset/create/indicator/',
  sheetList: '/v1/dataset/temp/create/',
  // library apis
  library: '/v1/dataset/list/',
  libraryDelete: '/v1/dataset/',
  libraryDataset: '/v1/dataset/',
  libraryPreview: '/preview/',
  libraryDatasetDownload: '/update/',
  libraryChartType: '/charts',
  // select apis
  selectNode: '/v1/node/',
  coloumUpstrem: '/upstream/columns',
  coloumDownsterm: '/downstream/columns',
  formulaList: 'formula/list',
  baisc: '?is_basic=true',
  // workflowapis
  workflow: '/v1/workflow/',
  workflowrun: '/v1/workflows/run?force=true',
  workflowStop: '/v1/workflows/stop/',
  workflowStatus: '/v1/workflows/stream/progress/',
  workFlowStop: '/v1/workflows/stop',
  workflowPreview: '/preview',
  workflowDraft: '/v1/workflows/draft-list',
  workflowDraftDelete: '/draft-delete',
  workflows: '/v1/workflows/',
  workflowsDraft: '/v1/workflows/draft',
  // impute api
  getImpute: '/impute-fields/',
  // node api
  getNode: '/v1/node/',
  nodePreviewDownload: '/preview/download/excel/',
  // summery
  summeryDownload: '/summary/download/excel',
  datasetDownlaod: '/v1/dataset/output/',
  summeryStream: '/v1/summary/stream/progress/',
  getDatasetNode: '/v1/workflows/',
  getDataSet: '/v1/node/',

  // exploratory
  expChartDownload: '/charts/download/',
  correlation: '/correlogram/',
  exploratoryDataset: '/v1/dataset/',
  exploratoryGetChart: '/getcharts/',
  exploratoryTabular: '/tabulardata/?type=table'
};


export const connectionType = {
  dataset: 'dataset',
  inputTool: 'InputTool',
  selectTool: 'SelectTool',
  imputeTool: 'ImputeTool',
  unionTool: 'UnionTool',
  ifp: 'ifp',
  outputTool: 'OutputTool',
  formulaTool: 'FormulaTool',
  sortTool: 'SortTool',
  joinTool: 'JoinTool',
  aggregateTool: 'AggregateTool',
  cleansingTool: 'CleansingTool',
  filterTool: 'FilterTool'
};

export const connectionTypeConfigs = {
  [connectionType.inputTool]: {
    toolName: 'source',
    type: connectionType.inputTool,
    config: {
      connection: {
        connection_type: 'dataset',
        path: ''
      }
    }
  },
  [connectionType.outputTool]: {
    toolName: 'destination',
    type: connectionType.outputTool,
    config: {}
  },
  [connectionType.selectTool]: {
    toolName: 'select',
    type: connectionType.selectTool,
    config: { update: {}, remove: []}
  },
  [connectionType.filterTool]: {
    toolName: 'filter',
    type: connectionType.filterTool,
    config: {}
  },
  [connectionType.cleansingTool]: {
    toolName: 'clean',
    type: connectionType.cleansingTool,
    config: { columns: []}
  },
  AggregateTool: {
    toolName: 'aggregate',
    type: 'AggregateTool',
    config: {columns: []}
  },
  [connectionType.sortTool]: {
    toolName: 'sort',
    type: connectionType.sortTool,
    config: {columns: {}}
  },
  [connectionType.imputeTool]: {
    toolName: 'impute',
    type: connectionType.imputeTool,
    config: {columns: []}
  },
  [connectionType.formulaTool]: {
    toolName: 'calculations',
    type: connectionType.formulaTool,
    config: {columns: []}
  },
  JoinTool: {
    toolName: 'join',
    type: 'JoinTool',
    config: { join_by: {} }
  },
  [connectionType.unionTool]: {
    toolName: 'append',
    type: connectionType.unionTool,
    config: {}
  }
};

export const storageType = {
  s3: 's3',
  azure: 'azure'
}