<form [formGroup]="shareForm" class="ifp-share-modal">
  <div class="ifp-share-modal__close" (click)="closeModal()">
    <em class="ifp-icon ifp-icon-cross"></em>
  </div>
  <div class="ifp-share-modal__header">
    <h3 class="ifp-share-modal__title">{{modelHead | translate}}</h3>
    <div (click)="onSubmit()" class="if-myapp-landing__button ifp-btn ifp-btn--primary">{{ 'Share' | translate }}</div>
  </div>
  <p class="ifp-share-modal__count">{{ 'Sharing' | translate }} {{ sharedAppsCount }} {{shareItem | translate}}</p>
  @if (!isHideTitleColumn) {
  <div class="ifp-share-modal__field-wrapper">
    <em class="ifp-icon  ifp-icon-edit"></em>
      <div class="ifp-share-modal__field-item">
        <input type="text" formControlName="name" [placeholder]="'App title (Optional)' | translate" class="ifp-share-modal__text">
        @if(shareForm.controls['name'].errors?.['maxlength']) {
          <p class="ifp-share-modal__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Maximum character limit exceeded' | translate}}</p>
        }
      </div>
  </div>
}
  <div class="ifp-share-modal__field-wrapper">
    <em class="ifp-icon ifp-icon-add-user"></em>
    <div class="ifp-share-modal__field-item">
      <div class="ifp-share-modal__share-sec">
        @for (receipient of receipientList; track $index) {
          <div class="ifp-share-modal__share-tag">
            <img src="../../../../../assets/images/profile-img.png" alt="image" class="ifp-share-modal__profile-img">
            <p class="ifp-share-modal__username">{{ receipient }}</p>
            <div class="ifp-share-modal__share-cancel" (click)="removeUser($index)"><em class="ifp-icon ifp-icon-cross"></em></div>
          </div>
        }
        <input autocomplete="off" type="email" formControlName="email" (keyup.enter)="onEmailEntered()" [placeholder]="receipientList.length ? '' : ('Type email and press enter' | translate)" [size]="shareForm.controls['email'].value.length < 5 ? (receipientList.length ? 5 : 30) : shareForm.controls['email'].value.length" class="ifp-share-modal__email" id="user">
      </div>
      @if(!isValid) {
        @if(shareForm.controls['email'].errors?.['required']) {
          <p class="ifp-share-modal__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Email is required' | translate}}</p>
        } @else if (shareForm.controls['email'].errors?.['email']) {
          <p class="ifp-share-modal__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid email address' | translate}}</p>
        } @else if (shareForm.get('email')?.hasError('notCurrentUserEmail')) {
          <p class="ifp-share-modal__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Cannot add yourself as a recipient' | translate}}</p>
        }
        @if(receipientList.length === 0 && this.shareForm.controls['email'].value === '') {
          <p class="ifp-share-modal__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please add atleast one recipient' | translate}}</p>
        }
      }
    </div>
  </div>

  <div class="ifp-share-modal__field-wrapper">
    <em class="ifp-icon ifp-icon-comment"></em>
    <div class="ifp-share-modal__field-item">
      <textarea rows="5" [placeholder]="'Enter message' | translate" class="ifp-share-modal__comment" formControlName="comment"></textarea>
      @if(shareForm.controls['comment'].errors?.['maxlength']) {
        <p class="ifp-share-modal__error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Maximum character limit exceeded' | translate}}</p>
      }
    </div>
  </div>

</form>
