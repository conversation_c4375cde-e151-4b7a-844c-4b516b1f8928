import { commentType, statusParams } from './../../core/constants/dashboard-builder.constants';
import { contentType, contentTypeDashboard } from 'src/app/scad-insights/core/constants/contentType.constants';
import { DatePipe, DOCUMENT, Location, NgClass, NgIf, Ng<PERSON>tyle } from '@angular/common';
import { ChangeDetectorRef, Component, ElementRef, HostListener, Inject, Input, OnChanges, OnDestroy, OnInit, QueryList, Renderer2, SimpleChanges, ViewChild, ViewChildren, WritableSignal, computed, inject, signal, viewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { buttonClass, buttonColor, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { IfpModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpImportIndicatorsComponent } from '../../organism/ifp-import-indicators/ifp-import-indicators.component';
import { FileData, IfpDbFileUploaderComponent } from '../../molecule/ifp-db-file-uploader/ifp-db-file-uploader.component';
import { IfpDbCardComponent } from '../../molecule/ifp-db-card/ifp-db-card.component';
import { IfpChartToolbarComponent } from '../../organism/ifp-chart-toolbar/ifp-chart-toolbar.component';
import { SubSink } from 'subsink/dist/subsink';
import { Store } from '@ngrx/store';
import { selectIndicatorGetById } from 'src/app/scad-insights/store/incidicator-list/incidicator-list.selector';
import { cloneDeep } from 'lodash';
import { chartConstants } from 'src/app/scad-insights/core/constants/chart.constants';
import {
  CompactType,
  DisplayGrid,
  GridsterComponent,
  GridsterConfig,
  GridsterItem,
  GridsterItemComponent,
  GridsterModule,
  GridType
} from 'angular-gridster2';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';
import html2canvas from 'html2canvas';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { getIndicator } from 'src/app/scad-insights/store/incidicator-list/incidicator-list.action';
import { PagesService } from 'src/app/scad-insights/core/services/pages/pages.service';
import { SourceList } from '../../molecule/ifp-import-dropdown/ifp-import-dropdown.component';
import { getStatisticsInsights } from 'src/app/scad-insights/store/statistics-insights/statistics-insights-list.action';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { Title } from '@angular/platform-browser';
import { cardTypes, chartToolTypes, chartTypes, dashboardActions, dashboardConstants, dashboardTypes, dataTypes, desfulatCardState, deviceTypes, kpiCardTemplates, numericDataTypes, sourceTypes } from 'src/app/scad-insights/core/constants/dashboard.constants';
import { axisDropDowns, chartSettingOpts, multiDimentionData, singleDimentionData } from '../../organism/ifp-chart-toolbar/ifp-chart-toolbar.constants';
import { AxisDropDown } from '../../organism/ifp-chart-toolbar/ifp-chart-toolbar.interface';
import { ScUploadModelComponent } from '../../molecule/sc-upload-model/sc-upload-model.component';
import { CustomCardService } from 'src/app/scad-insights/core/services/create-dashboard/custom-card.service';
import { loadAnalyticalSuccess } from 'src/app/scad-insights/home/<USER>/Analytical apps/analyticalApps.action';
import { title } from 'src/app/scad-insights/core/constants/header.constants';
import { prepsApiEndpoints } from 'src/app/ifp-analytics/data-prep/ifp-data-prep/constants/if-preps.constants';
import { IfpPrepService } from 'src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-prep-service';
import { UsageDashboardLogService } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard.constants';
import { IfpRemoveCardComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-remove-card/ifp-remove-card.component';
import { IfpPrepUploadService } from 'src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-data-prep-upload/services/ifp-prep-upload.service';
import { IfpDashboardChartCardComponent } from '../../molecule/ifp-dashboard-chart-card/ifp-dashboard-chart-card.component';
import { IfpVisulizationBuilderService } from 'src/app/scad-insights/core/services/ifp-visulization-builder.service';
import { dashboardEndpoints } from 'src/app/scad-insights/core/apiConstants/dashboard.api.constants';
import { cardObject, sourceCards, tabs } from '../dashboard.interface';
import { FormsModule } from '@angular/forms';
import { IfpPrepLibraryComponent } from '../../../ifp-analytics/data-prep/ifp-data-prep/ifp-prep-library/ifp-prep-library.component';
import { FileResponePrep } from 'src/app/ifp-analytics/data-prep/ifp-data-prep/interface/ifp-data-prep.interface';
import { KpiTemplateCardComponent, SelectActionEvent } from '../../molecule/kpi-template-card/kpi-template-card.component';
import { EditKpiModalComponent } from './edit-kpi-modal/edit-kpi-modal.component';
import { DxpPopupComponent } from '../../../dxp/dxp-popup/dxp-popup.component';
import { approveConst, approveStatus, dxpApi, generalizedRoles } from 'src/app/dxp/dxp.constants';
import { DxpComments, ProductDetail } from 'src/app/dxp/dxp.interface';
import { SelectedProduct } from 'src/app/dxp/widgets/dxp-accordian/dxp-accordian.component';
import { DashboardConfigStore } from 'src/app/ifp-analytics/data-prep/ifp-advance-prep/store/visulization-builder/dashboard-builder.store';
import { ImportDataModalComponent } from './import-data-modal/import-data-modal.component';
import { IfpDxpCardComponent } from '../../../dxp/widgets/ifp-dxp-card/ifp-dxp-card.component';
import { ChartCardConfig, DashboardConfigState } from 'src/app/ifp-analytics/data-prep/ifp-advance-prep/store/visulization-builder/dashboard-builder.state';
import { ResizeObserverDirective } from 'src/app/scad-insights/core/directives/resize-observer.directive';
import { SourceDataModalComponent } from './source-data-modal/source-data-modal.component';
import { kpiTemplateMenuEvents } from '../../molecule/kpi-template-card/kpi-template-card.constant';
import { KpiCardTemplate } from '../../organism/ifp-db-value-card-templates/ifp-db-value-card-templates.component';
import { IfpDashboardLibraryPreviewComponent } from './ifp-dashboard-library-preview/ifp-dashboard-library-preview.component';
import { Filter, SelectedSourceFilters } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-source-filter/ifp-source-filter.component';
import { IfpRichTextEditorComponent } from '../../molecule/ifp-rich-text-editor/ifp-rich-text-editor.component';
import { ApiStatus } from 'src/app/scad-insights/core/constants/api-status.constants';
import { DxpValidationPopUpComponent } from 'src/app/dxp/dxp-validation-pop-up/dxp-validation-pop-up.component';
import { AdminService } from 'src/app/scad-insights/core/services/sla/admin.service';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';
import { IfpAbbreviationTagComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-abbreviation-tag/ifp-abbreviation-tag.component';
import { IfpMarkDownComponent } from 'src/app/scad-insights/ifp-chat-bot/ifp-mark-down/ifp-mark-down.component';
import { dateFormat } from 'src/app/scad-insights/core/constants/date.constants';
import { IfpToggleButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-toggle-button/ifp-toggle-button.component';
import { mainTabParams } from '../../core/constants/dashboard-builder.constants';
import { catchError, debounceTime, EMPTY, finalize, forkJoin, fromEvent, of, Subject, switchMap, takeUntil, tap } from 'rxjs';
import { IfpDashboardBuilderHelperService } from '../../core/services/ifp-dashboard-builder-helper.service';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { IfpPanelDisplayDropdownComponent } from 'src/app/ifp-analytics/molecule/ifp-panel-display-dropdown/ifp-panel-display-dropdown.component';
import { IfpNoDataComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { IfpGlobalService } from 'src/app/scad-insights/core/services/ifp-global.service';
import { IfpBreadcrumbsComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { IfpModalService } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';
import { IfpIconTextComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-icon-text/ifp-icon-text.component';

import { IfpUserTagComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-user-tag/ifp-user-tag.component';


@Component({
  selector: 'app-ifp-dashboard-builder',
  templateUrl: './ifp-dashboard-builder.component.html',
  styleUrls: ['./ifp-dashboard-builder.component.scss'],
  imports: [TranslateModule, IfpButtonComponent, IfpModalComponent, IfpImportIndicatorsComponent, NgClass, IfpDbFileUploaderComponent, IfpDbCardComponent, NgIf, IfpChartToolbarComponent,
    GridsterModule, NgStyle, IfpTooltipDirective, IfpSpinnerComponent, ScUploadModelComponent, IfpRemoveCardComponent, IfpDashboardChartCardComponent, IfpPrepLibraryComponent, KpiTemplateCardComponent, EditKpiModalComponent, DxpPopupComponent, ImportDataModalComponent, IfpDxpCardComponent, ResizeObserverDirective, IfpDashboardLibraryPreviewComponent, IfpRichTextEditorComponent, IfpIconTextComponent, DxpValidationPopUpComponent, IfpAbbreviationTagComponent, IfpMarkDownComponent, DatePipe, FormsModule, SourceDataModalComponent, IfpToggleButtonComponent,
    IfpPanelDisplayDropdownComponent, IfpNoDataComponent, IfpUserTagComponent, IfpBreadcrumbsComponent],
  providers: [IfpPrepService, IfpPrepUploadService]
})
export class IfpDashboardBuilderComponent implements OnInit, OnDestroy, OnChanges {

  @HostListener('window:beforeunload', ['$event'])
  unloadNotification($event: any) {
    // Show browser's default confirmation message
    if (this.mode === dashboardActions.edit && (this.getUnconfiguredCards().length && this.checkAnyCardConfigured())) {
      $event.returnValue = this._translate.instant(this.unconfiguredMessage);
    }
  }

  @HostListener('window:pointerup', ['$event.target'])
  onOutsideClick(target: HTMLElement) {
    if (target.classList.contains('fixed')) {
      this.selectedId = '';
      this.selectedCard.set(desfulatCardState);
      this.selectedCardId.set('not');
      this.cardEditMode = false;
      this.expandOrCollapseToolbar(false);
    }
    if (!target.classList.contains('ifp-db__header-title')) {
      this.isTitleUpdate = false;
      // if (this._dashboardService.dashboardProperties.title) {
      //   this.isWhiteSpaces = this._dashboardService.dashboardProperties.title.trim() === '';
      // } else {
      //   this.isWhiteSpaces = false;
      // }
    }
  }

  @Input() dashboardId!: string;
  @Input() selectTab!: string;
  @Input() mode: string = 'edit';
  @Input() isAiDashboard: boolean = false;
  @Input() mainTab!: string;
  @Input() subTab!: string;

  @ViewChild('importInidcators') importInidcators!: IfpModalComponent;
  @ViewChild('alertModal') alertModal!: IfpModalComponent;
  @ViewChild('alertCardModal') alertCardModal!: IfpModalComponent;
  @ViewChild('loaderModal') loaderModal!: IfpModalComponent;
  @ViewChild('uploadData') uploadData!: IfpModalComponent;
  @ViewChild('libraryListModal') libraryListModal!: IfpModalComponent;
  // @ViewChild('dbHeader', { static: true }) dbHeader!: ElementRef;
  @ViewChild('playGround', { static: false }) playGround!: GridsterComponent;
  @ViewChild('sideBar') sideBar!: ElementRef;
  @ViewChild('downloadPrint') downloadPrint!: ElementRef;
  @ViewChild('toolbar') toolbar!: ElementRef;
  @ViewChild('dbPlayGround') dbPlayGround!: ElementRef;
  @ViewChild('toolbarCmp') toolbarCmp!: IfpChartToolbarComponent;
  @ViewChildren(GridsterItemComponent) gridsterItems!: QueryList<GridsterItemComponent>;

  editKpiModal = viewChild<IfpModalComponent>('editKpiModal');
  addIndicatorsModal = viewChild<IfpModalComponent>('addIndicatorsModal');
  dbHeader = viewChild<ElementRef>('dbHeader');
  viewSourceDataModal = viewChild<IfpModalComponent>('viewSourceDataModal');
  public approverActionModal = viewChild<IfpModalComponent>('approverActionModal');
  public unConfiguredCardWarningModal = viewChild<IfpModalComponent>('unConfiguredCardWarningModal');
  private readonly _adminService = inject(AdminService);
  private readonly _commonApiService = inject(ApiService);
  private readonly _msalService = inject(IFPMsalService);

  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public importType: string = 'browse';
  // public showImportTypes: WritableSignal<boolean> = signal(false);
  public logoFormat: string[] = ['.png', '.svg'];
  public selectedCards: any = [];
  public selectedId!: string;
  public subs = new SubSink();
  public selectedAllCardData: any = [];
  public selectedCardData: any = [];
  public isPieDisabled: boolean = false;
  public chartConstants = chartConstants;
  // public selectedChartType!: string; // Removed duplicate declaration
  public contentType!: string;
  public isToolbarExpanded: boolean = false;
  options!: GridsterConfig;
  dashboard!: GridsterItem[];
  public isAddIndicator: boolean = false;
  public isToolbar: boolean = true;
  public isFilterPanel: boolean = false;
  public deletedCards: any = [];
  public height: number = 260;
  public isSticky: boolean = false;
  public itemComponent: any;
  public pageData: PageData[] = [];

  public allDropDownData: { name: string }[] = [];
  public addIndicatorPos: any = { id: 1, cols: 3, rows: 6, y: 0, x: 0, resizeEnabled: false, key: 'add_indicator' };
  public message: string = this._translate.instant('Would you like to proceed and save the current dashboard?');
  public editMessage: string = this._translate.instant('Would you like to continue editing?');
  public detailMessage: string = this._translate.instant('Are you sure you want to go back?');
  public buttonColor = buttonColor;
  public gridsterHeight: number = 1000;
  public isEdit: boolean = false;
  public isEnableButton: boolean = false;
  public dataType!: string;
  public isImportDropdown: boolean = false;
  public isPreviousMode: string = 'edit';
  // public isTitleLengthOver: boolean = false;
  public isDragging = false;
  private offsetX = 0;
  private offsetY = 0;
  public isLeftCut: boolean = false;
  // public isWhiteSpaces: boolean = false;
  public isTextareaExpanded: boolean = false;
  public isDragged: boolean = false;
  public sendData!: { key: string, value: string[] | string };
  private selectedToolbarPosition: string = 'right';
  public isTitleUpdate: boolean = false;
  public isSelectedTool!: string;
  private dragging = false;
  private startX = 0;
  private startWidth = 0;
  public toolbarWidth: number = 0;
  public uploadedFile!: any;
  public csvData: any = [];
  public prepAnchor: any;
  public cutomCardDataSourceType!: string;
  public prepId: string = '';
  private sessionId!: string;
  public controller!: AbortController;
  public isImportIndicatorsOpen = false;
  public comments = signal<DxpComments[] | []>([]);
  public currentUserEmail = this._msalService.getLoginData.account.username;
  public dateFormat = dateFormat;
  public approveStatus = approveStatus;
  public newComment: string = '';
  public hideSideBar = signal(true);
  public enableChat = signal(false);
  public ifpColors = ifpColors;
  public enableExport: boolean = false;

  // new variables //
  public statusParams = statusParams;
  public mainTabParams = mainTabParams;
  public generalizedRoles = generalizedRoles;
  public role = this._adminService.generalizedRole();
  public chartTypes = chartTypes;
  public chartToolTypes = chartToolTypes;
  public cardTypes = cardTypes;
  public kpiTemplateMenuEvents = kpiTemplateMenuEvents;
  public dashboardActions = dashboardActions;
  public widgetCardBackgroundColor: WritableSignal<string> = signal(ifpColors.white);
  public widgetCardTextColor: WritableSignal<string> = signal(ifpColors.black);
  public sampleSourceDate: WritableSignal<any[]> = signal([]);
  public sampleSourceDataHead: WritableSignal<string[]> = signal([]);
  public selectedSourceFilter: WritableSignal<{ filter: Filter[]; operator: string }> = signal({ filter: [], operator: 'AND' });
  public sourceModelType: WritableSignal<string> = signal('');
  public uploadedDataResponse: WritableSignal<{ id: string, file: string, name: string, owner: string, storage_backend: string, streamComplete: boolean }> = signal({
    id: '',
    file: '',
    name: '',
    owner: '',
    storage_backend: '',
    streamComplete: false
  });

  public kpiIsEdit: boolean = false;
  public currentTabIndex: number = 0;
  public currentTheme = signal(this._themeService.defaultTheme);

  public dashboardConfig: DashboardConfigState = {
    layouts: [],
    currentLayoutId: null,
    selected_page_id: '',
    dashbaord_id: '',
    name: '',
    subtitle: '',
    thumbnail_light: '',
    thumbnail_dark: '',
    tabs: [],
    isLoading: false,
    error: null,
    isEditMode: false,
    isDragging: false,
    isResizing: false,
    dashboard_type: '',
    logo: '',
    approval_status: '',
    status: ''
  };

  // inject store  //
  public dashboardBuilderStore = inject(DashboardConfigStore);
  private _prepService = inject(IfpPrepUploadService);
  private readonly _dbHelperService = inject(IfpDashboardBuilderHelperService);
  private readonly _modalService = inject(IfpModalService);

  // modals variables  //

  private selectedTabIndex: number = 0;
  public availableTabs: tabs[] = [];
  public createdCards: WritableSignal<any> = signal([]);
  public selectedTab: WritableSignal<tabs> = signal({ selected_page_id: '', page_object_id: '', page_title: '' });
  public cardsArrayEmpty: WritableSignal<boolean> = signal(false);
  public selectedCardId: WritableSignal<string> = signal('');
  public selectedCard: WritableSignal<ChartCardConfig> = signal(desfulatCardState);
  public saveType: string = '';
  public unConfiguredCards: any[] = [];
  public tabChanging: boolean = false;
  public sourceName = computed(() => {
    return this._dbHelperService.getSourceName(this.selectedCard().source, this.selectedCard().source_type);
  });

  public selectedChartType: WritableSignal<'line' | 'bar' | 'pie' | 'doughnut' | 'area' | 'scatter' | 'column'> = signal('column');
  public isLaibraryModelOpen: WritableSignal<boolean> = signal(false);
  public sourceData = [];
  public savedDashboardId = '';
  public sourceTableLoader = true;
  public dashboardConstants = dashboardConstants;
  public kpiCardTemplates = kpiCardTemplates;
  public selectedKpiData: WritableSignal<Record<string, any>> = signal({});
  public dataSourceList: SourceList[] = [
    {
      key: 'library',
      name: 'My Bayaan Library',
      iconClass: 'ifp-icon-library'
    },
    {
      key: 'upload',
      name: 'Upload Data',
      iconClass: 'ifp-icon-upload-thick'
    },
    {
      key: 'browse',
      name: 'Browse Indicators',
      iconClass: 'ifp-icon-browse'
    },
    {
      key: 'myApps',
      name: 'Add from My BookMark',
      iconClass: 'ifp-icon-apps-plus'
    },
    {
      key: 'dxp',
      name: 'From DXP',
      iconClass: 'ifp-icon-catelogue'
    }
  ];

  public selectedSource = this.dataSourceList[0].key;
  public isKpiModalOpen = false;
  public sourceDataFilters: SelectedSourceFilters = { filter: [], operator: 'AND' };
  public isGovAffairs: boolean = false;
  public commentType = commentType;
  public dashboardCardLoader = false;
  public dashboardSavingState: WritableSignal<boolean> = signal(false);
  public unconfiguredMessage: string = 'By proceeding, you will lose any unconfigured cards.';

  public widgetAggregationLoader = false;
  public chartAggregationLoader = false;
  public isUploadFail = false;
  // service variables //
  public _apiService = inject(IfpVisulizationBuilderService);
  public _toasterService = inject(ToasterService);

  private tabDetailSubject = new Subject<{ id: string; index: number }>();
  private destroy$ = new Subject<void>();

  // dashboard detail variables //
  public currentStatus = signal('');
  public approveConst = approveConst;

  // dxp variables //
  public productListLoader: boolean = true;
  public productdetailList: ProductDetail[] = [];
  public selectedProduct: SelectedProduct = {
    sourceAssetId: '',
    sourceProductId: '',
    title: ''
  };

  // import indicator variables  //
  public selectedSourcesCards = signal<sourceCards[]>([]);

  public dataTypes = dataTypes;
  public dashboardTypes = dashboardTypes;
  public uploadDataProgess: WritableSignal<number> = signal(0);
  approvalDesc = signal('');
  approvalTitle = signal('');
  approvalIcon = signal('');
  enableCommentBox = signal(false);
  // Signal to enable/disable review section
  public enableReview = signal(false);
  public approvalRequestId!: string;
  public dashboardType!: string;
  public dashboardStatus!: string;
  public objectId!: string;

  public dashboardName: string = 'Untitled';
  public dashboardNameLimit: number = 60;
  public dashboardPageLoader: boolean = true;
  public resized = new Subject();
  public isMobileView = signal(false);
  public dataHistory: DataHistory = {
    created_by: { email: '' },
    shared_with_users: []
  };



  public isCreateTab: boolean = false;
  public cardEditMode: boolean = false;

  @HostListener('window:scroll', ['$event'])
  onScroll() {
    if (this.sideBar) {
      if (window.matchMedia('(min-width: 1023.98px)').matches) {
        const body = document.getElementsByTagName('body')[0];
        const currentScroll = window.scrollY;
        this.isSticky = false;
        if ((currentScroll > this.height + 50) && (body.offsetHeight > 500)) {
          this.isSticky = true;
        }
      }
    }
  }


  constructor(public location: Location, private store: Store, public _dashboardService: DashboardService, private _renderer: Renderer2,
    private router: Router, private _toaster: ToasterService, private route: ActivatedRoute, private _pageService: PagesService,
    private _translate: TranslateService, @Inject(DOCUMENT) private document: Document, private _themeService: ThemeService, private _cdr: ChangeDetectorRef,
    private _titleService: Title, private _elementRef: ElementRef, private _preService: IfpPrepService,
    private _customService: CustomCardService, public themeService: ThemeService, private log: UsageDashboardLogService,
    public _globalService: IfpGlobalService) {
    this.isEdit = false;
    this.dashboardId = '';
    this.route.queryParams.subscribe(val => {
      if (val?.['id']) {
        this.dashboardId = val?.['id'];
        this.mode = this.dashboardActions.detail;
        this.getDashboradDetail();
      }
      if (val?.['mode']) {
        this.mode = val?.['mode'];
      }
      if (val?.['tab']) {
        this.selectTab = val?.['tab'];
      }
      if (val?.['mainTab']) {
        this.mainTab = val?.['mainTab'];
      }
      if (val?.['subTab']) {
        this.subTab = val?.['subTab'];
      }
      if (val?.['sendData']) {
        this.sendData = JSON.parse(val?.['sendData']);
      }
      if (val?.['device_type']) {
        this.isMobileView.set(val?.['device_type'] == deviceTypes.mobile);
      }
      if (val['prepId']) {
        this.prepId = val['prepId'];
        this.prepAnchor = val['anchor'];
        this.uploadFileData();
      }
      if (val['government-affairs']) {
        this.isGovAffairs = val['government-affairs'];
        this.dataSourceList = this.dataSourceList.filter(x => x.key == this.dataTypes.dxp);
        this.selectedSource = this.dataTypes.dxp;
      }

    });


    // resizing subject //
    this.subs.add(this.resized
      .pipe(debounceTime(300))
      .subscribe((value) => {
        this.getCardHeight();
        this.getDynamicBackgroundHeight();
      }),
    this._themeService.defaultTheme$.subscribe((theme) => {
      this.currentTheme.set(theme);

      // const isDark = theme === 'dark';

      // // Theme-based color configuration
      // const themeConfig = {
      //   bgColor: isDark ? ifpColors.sectionWhiteDark : ifpColors.white,
      //   textColor: isDark ? ifpColors.white : ifpColors.black,
      //   revertBg: isDark ? ifpColors.white : ifpColors.sectionWhiteDark,
      //   revertText: isDark ? ifpColors.black : ifpColors.white,
      // };

      // // Apply global widget colors
      // this.widgetCardBackgroundColor.set(themeConfig.bgColor);
      // this.widgetCardTextColor.set(themeConfig.textColor);

      // // Filter only widget cards once
      // const widgetCards = this.createdCards().filter(
      //   (el: any) => el.type === cardTypes.widget
      // );

      // // Update widget card-specific colors
      // widgetCards.forEach((card: any) => {
      //   const customConfig = card?.metadata?.cardConfig?.customConfig;
      //   if (!customConfig) {
      //     return;
      //   }

      //   if (customConfig.bgColor === themeConfig.revertBg) {
      //     customConfig.bgColor = themeConfig.bgColor;
      //   }
      //   if (customConfig.textColor === themeConfig.revertText) {
      //     customConfig.textColor = themeConfig.textColor;
      //   }
      // });

      this.widgetCardBackgroundColor.set(theme === 'dark' ? ifpColors.sectionWhiteDark : ifpColors.white);
      this.widgetCardTextColor.set(theme === 'dark' ? ifpColors.white : ifpColors.black);
      // Filter only widget cards once
      const widgetCards = this.createdCards().filter(
        (el: any) => el.type === cardTypes.widget
      );

      // // Update widget card-specific colors
      widgetCards.forEach((card: any) => {
        const customConfig = card?.metadata?.cardConfig?.customConfig;
        if (!customConfig) {
          return;
        }

        if (theme === 'dark') {
          if (customConfig.textColor === ifpColors.black) {
            customConfig.textColor = ifpColors.white;
          }
          if (customConfig.bgColor === ifpColors.white) {
            customConfig.bgColor = ifpColors.sectionWhiteDark;
          }
        } else if (theme === 'light' && (customConfig.bgColor === ifpColors.sectionWhiteDark || customConfig.bgColor === ifpColors.black)) {
          if (customConfig.textColor === ifpColors.white) {
            customConfig.textColor = ifpColors.black;
          }
          customConfig.bgColor = ifpColors.white;
        }

        // if (customConfig.bgColor === themeConfig.revertBg) {
        //   customConfig.bgColor = themeConfig.bgColor;
        // }
        // if (customConfig.textColor === themeConfig.revertText) {
        //   customConfig.textColor = themeConfig.textColor;
        // }
      });

      // this.style.update((style) => {
      //   if (!style['color'] || (style['color'] === '')) {
      //     style['color'] = this.defaultFontColor();
      //   }
      //   if (!style['background-color'] || style['background-color'] === '') {
      //     style['background-color'] = this.defaultBgColor();
      //   }

      //   if (theme === 'dark') {
      //     if (style['color'] === ifpColors.black) {
      //       style['color'] = ifpColors.white;
      //     }
      //     if (style['background-color'] === ifpColors.white) {
      //       style['background-color'] = ifpColors.sectionWhiteDark;
      //     }
      //   } else if (theme === 'light' && (style['background-color'] === ifpColors.sectionWhiteDark || style['background-color'] === ifpColors.black)) {
      //     if (style['color'] === ifpColors.white) {
      //       style['color'] = ifpColors.black;
      //     }
      //     style['background-color'] = ifpColors.white;
      //   }

      //   return style;
      // });
    })
    // this._themeService.defaultTheme$.subscribe((theme) => {
    //   this.currentTheme.set(theme);
    //   let widgetCards = [];
    //   if (this.createdCards().length) {
    //     widgetCards = this.createdCards().filter((element: any) => element.type === cardTypes.widget);
    //   }
    //   if (theme === 'dark') {
    //     this.widgetCardBackgroundColor.set(ifpColors.sectionWhiteDark);
    //     this.widgetCardTextColor.set(ifpColors.white);
    //     if (widgetCards.length) {
    //       widgetCards.forEach((card: any) => {
    //         if (card?.metadata?.cardConfig?.customConfig?.bgColor === ifpColors.white) {
    //           card.metadata.cardConfig.customConfig.bgColor = ifpColors.sectionWhiteDark;
    //         }
    //         if (card?.metadata?.cardConfig?.customConfig?.textColor === ifpColors.black) {
    //           card.metadata.cardConfig.customConfig.textColor = ifpColors.white;
    //         }
    //       });
    //     }
    //   } else {
    //     this.widgetCardBackgroundColor.set(ifpColors.white);
    //     this.widgetCardTextColor.set(ifpColors.black);
    //     if (widgetCards.length) {
    //       widgetCards.forEach((card: any) => {
    //         if (card?.metadata?.cardConfig?.customConfig?.bgColor === ifpColors.sectionWhiteDark) {
    //           card.metadata.cardConfig.customConfig.bgColor = ifpColors.white;
    //         }
    //         if (card?.metadata?.cardConfig?.customConfig?.textColor === ifpColors.white) {
    //           card.metadata.cardConfig.customConfig.textColor = ifpColors.black;
    //         }
    //       });
    //     }
    //   }
    // })
    );


    this.tabDetailSubject.pipe(
      switchMap(({ id, index }) => this.getTabDetailObservable(id, index)),
      takeUntil(this.destroy$)
    ).subscribe();
  }

  ngOnInit(): void {
    this.setGridsterOption();
    if (!this.isAiDashboard) {
      this._titleService.setTitle(`${title.bayaan} | Dashboards`);
    }
    this._dashboardService.chartSettings = [];
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.dashboard, this.log.currentTime);
    // if (this.mode == this.dashboardActions.new) {
    this.createTabs();
    // }
    this.getAssetList();
    // this.setBreadcrumbs();
  }

  setBreadcrumbs(): void {
    const breadcrumbs: PageData[] = [
      {
        title: 'Home',
        route: '/home'
      },
      {
        title: this.isGovAffairs ? 'Government Affairs' : 'My Bayaan',
        route: this.isGovAffairs ? '/dxp/dashboards' : '/store/dashboards',
        queryParams: this.isGovAffairs ? { 'government-affairs': 'true' } : undefined
      },
      {
        title: this.dashboardConfig.name,
        route: ''
      }
    ];
    this.pageData = breadcrumbs;
  }

  /**
   * Check if user is approver or creator on approval-status main tab with reverted sub tab
   * This determines the conditional display of buttons and comment box
   */
  ********************************(): boolean {
    return (this.role === generalizedRoles.approver || this.role === generalizedRoles.builder) && this.mainTab === mainTabParams.approvalStatus && this.subTab === statusParams.reverted;
  }

  /**
   * Check if user is approver or creator on approval-status main tab with unpublished or rejected sub tab
   * This determines when to show only comment panel without buttons
   */
  isApproverOrCreatorOnReadOnlyTab(): boolean {
    return (this.role === generalizedRoles.approver || this.role === generalizedRoles.builder) && this.mainTab === mainTabParams.approvalStatus && (this.subTab === statusParams.unpublished || this.subTab === approveStatus.rejected);
  }

  /**
   * Check if user is BUILDER on approval-status main tab with pending sub tab
   * This determines when BUILDER should see only comment panel without buttons (read-only mode)
   */
  isBuilderOnPendingTab(): boolean {
    return this.role === generalizedRoles.builder && this.mainTab === mainTabParams.approvalStatus && this.subTab === statusParams.pending;
  }

  /**
   * Check if user is APPROVER on approval-status main tab with approved sub tab
   * This determines when APPROVER should see only comment panel without buttons (read-only mode)
   */
  isApproverOnApprovedTab(): boolean {
    return this.role === generalizedRoles.approver && this.mainTab === mainTabParams.approvalStatus && this.subTab === statusParams.selfApproved;
  }

  isApproverOnMyRequestsTab(): boolean {
    return this.role === generalizedRoles.approver && this.mainTab === 'new-request' && this.subTab === statusParams.assigned;
  }

  isApproverOnUnassignedRequestsTab(): boolean {
    return this.role === generalizedRoles.approver && this.mainTab === 'new-request' && this.subTab === statusParams.unAssigned;
  }

  isApproverOnNewRequestTab(): boolean {
    return this.isApproverOnMyRequestsTab() || this.isApproverOnUnassignedRequestsTab();
  }

  isApproverOrBuilderOnDashboardsTab(): boolean {
    return (this.role === generalizedRoles.approver || this.role === generalizedRoles.builder) && this.mainTab === statusParams.govAffairsDashboards;
  }

  isBuilderOnMyDashboardsTab(): boolean {
    return this.role === generalizedRoles.builder && this.mainTab === statusParams.nativeMyDashboards;
  }

  shouldHideEverything(): boolean {
    return this.isApproverOrBuilderOnDashboardsTab() || this.isBuilderOnMyDashboardsTab();
  }

  /**
   * Check if user should hide footer (no footer buttons, no toggle)
   * Combines conditions: unpublished/rejected for both roles + pending for BUILDER + approved for APPROVER
   */
  shouldHideFooter(): boolean {
    return this.isApproverOrCreatorOnReadOnlyTab() || this.isBuilderOnPendingTab() || this.isApproverOnApprovedTab();
  }

  /**
   * Check if user should be in comment read-only mode (no comment input)
   * For unpublished/rejected tabs (both roles) + approved tab (APPROVER only)
   * BUILDER on pending should have active comments
   */
  isCommentReadOnlyMode(): boolean {
    return this.isApproverOrCreatorOnReadOnlyTab() || this.isApproverOnApprovedTab();
  }

  shouldShowAnyFooterButtons(): boolean {
    if (this.isApproverOnNewRequestTab()) {
      return true;
    }
    return !this.shouldHideFooter();
  }

  shouldShowRevertButton(): boolean {
    return this.********************************() || this.isApproverOnNewRequestTab();
  }

  shouldShowRejectApproveButtons(): boolean {
    if (this.isApproverOnNewRequestTab()) {
      return true;
    }
    return !this.********************************();
  }

  shouldShowCommentBox(): boolean {
    if (this.shouldHideEverything()) {
      return false;
    }
    if (this.********************************() || this.shouldHideFooter() || this.isApproverOnNewRequestTab()) {
      return true;
    }
    return this.mode === 'detail' && this.role !== generalizedRoles.explorer && this.dashboardConfig.dashboard_type === dashboardTypes.govAffairs && this.dashboardStatus === 'PENDING-FOR-APPROVAL';
  }

  shouldShowToggleButton(): boolean {
    if (this.isApproverOnNewRequestTab()) {
      return true;
    }
    return !this.********************************() && !this.shouldHideFooter();
  }

  /**
   * Check if comment panel input section should be shown
   * Hide entire comment input section (textarea + buttons) only for comment read-only tabs
   * (unpublished/rejected for both roles - BUILDER on pending should have active comments)
   */
  shouldShowCommentPanelButtons(): boolean {
    return !this.isCommentReadOnlyMode();
  }

  shouldShowFooter(): boolean {
    if (this.shouldHideEverything()) {
      return false;
    }
    // Hide footer specifically for approver on approval-status -> reverted tab
    if (this.role === generalizedRoles.approver && this.mainTab === mainTabParams.approvalStatus && this.subTab === statusParams.reverted) {
      return false;
    }
    if (this.isApproverOnNewRequestTab()) {
      return true;
    }
    return !this.shouldHideFooter();
  }

  shouldShowCommentIcon(): boolean {
    return this.********************************() || this.shouldHideFooter() || this.isApproverOnNewRequestTab();
  }

  shouldApplySingleButtonStyling(): boolean {
    return this.********************************() && !this.isApproverOnNewRequestTab();
  }

  getDashboardConfig() {
    const config = this.dashboardBuilderStore.dashboardConfigState();
    this.dashboardConfig = {
      ...config,
      dashbaord_id: (config as any).dashbaord_id ?? (config as any).dashboard_id ?? '',
      name: (config as any).name ?? '', dashboard_type: this.isGovAffairs ? this.dashboardTypes.govAffairs : this.dashboardTypes.native
    };
    this.dashboardName = this.dashboardConfig.name.trim() ?? 'Untitled';
  }

  ngOnChanges(_changes: SimpleChanges): void {
    if (_changes['dashboardId']) {
      this.getDashboradDetail();
    }
  }

  onItemChange(item: GridsterItem, itemComponent: any): void {
    this.resized.next(true);
  }


  preview(flag: string) {
    // Preview Dashboard
    this.mode = flag;
    this._dashboardService.dashboardProperties.title = this._dashboardService?.dashboardProperties?.title ? this._dashboardService?.dashboardProperties?.title : 'Untitled';
    if (this.isPreviousMode != this.mode) {
      this.isPreviousMode = this.mode;
    }
    this.selectedId = '';
    const unconfiguredDxp = this.getUnconfiguredCards().filter((unConfigCard: any) => unConfigCard.source_type === sourceTypes.dxp);
    this.enableExport = !this.createdCards().some((card: any) => (card.source_type === sourceTypes.dxp && !(unconfiguredDxp.find((item: any) => item === card))));
    // this.calculateHeight(200);
    setTimeout(() => {
      this.setGridsterOption();
    }, 300);
  }



  // toggleDropDown() {
  //   this.showImportTypes.set(this.showImportTypes() ? false : true);
  // }

  openAddIndicatorsModal(type: string) {
    this.isImportIndicatorsOpen = true;
    this.sourceModelType.set(type);
    if (this.selectedCard().type != this.cardTypes.widget) {
      this.resetCardSelection();
    }
    this.getAssetList();
    this.selectedSourcesCards.set([]);
    this.addIndicatorsModal()?.createElement();
  }



  generateRandomNumber(): number {
    return Math.floor((1000 + Math.random()) * 9000);
  }

  closeImport() {
    this.isAddIndicator = false;
    this.isImportDropdown = false;
    this.importInidcators.removeModal();
  }

  closeImportDataModal() {
    this.selectedSource = this.dataSourceList[0].key;
    this.isImportIndicatorsOpen = false;
    this.addIndicatorsModal()?.removeModal();
  }


  getObjectKeys(obj: any): string[] {
    return Object.keys(obj);
  }






  checkObjectLength() {
    return Object.keys(this.selectedCards).length;
  }

  updateTitle(title: string, key: string) {
    if (!this.contentType) {
      this.contentType = this.selectedCardData.key;
    }
    this._dashboardService.updateIndicatorTitle(title, this.contentType, this.selectedId, key);
    this._dashboardService.titleUpdated.next(key);
  }

  checkCardsLength() {
    let toolbar = false;
    Object.keys(this.selectedCards).some(key => {
      const cards = this.selectedCards[key];
      if (cards?.length > 0) {
        if (key !== dashboardConstants.customCard) {
          toolbar = true;
        }
        if (!toolbar && key === dashboardConstants.customCard) {
          toolbar = !!cards[0].chartType;
        }
      }
    });
    return toolbar;
  }

  deletaCard(event: any) {
    setTimeout(() => {
      const index = this.selectedCards[event.cntType].findIndex((x: { id: any; }) => x.id == event.id);
      this.selectedCards[event.cntType].splice(index, 1);
      this._dashboardService.chartSettings[event.cntType].splice(index, 1);
      this._dashboardService.deleteSelectedCard.next({ id: event.id, cntType: event.cntType });
      this.checkCardLength();
      if (this.selectedId == event.id) {
        this.selectedId = '';
      }
    }, 300);
  }

  checkCardLength() {
    this.isEnableButton = Object.keys(this.selectedCards).some(key => {
      const cards = this.selectedCards[key];
      if (cards?.length > 0) {
        if (key !== dashboardConstants.customCard) {
          return true;
        }
        return !!cards[0].chartType;
      }
      return false;
    });

    if (!this.isEnableButton) {
      this.addIndicatorPos = { id: 1, cols: 3, rows: 6, y: 0, x: 0, resizeEnabled: false, key: 'add_indicator' };
    }
  }

  openFilterPanel(_event: boolean) {
    this.expandOrCollapseToolbar(true);
    this.isFilterPanel = true;
    this.toolbarWidth = 480;
  }

  // closeFilterPanel(_event: boolean) {
  //   this.isFilterPanel = false;
  //   this.isToolbar = true;
  // }



  setOptions(options: string[]) {
    const filterOptions: { name: string }[] = [];
    if (options?.length > 0) {
      options.forEach(element => {
        const data = {
          name: element
        };
        filterOptions.push(data);
      });
    }
    return filterOptions;
  }

  updateDashboardTitle() {
    // this.isTitleLengthOver = event.target.value?.length > 60 ? true : false;
    // this.isWhiteSpaces = event.target.value.trim() === '';
    this.dashboardConfig.name = this.dashboardName.trim() ?? 'Untitled';
    this.dashboardBuilderStore.updateDashboardTitle(this.dashboardConfig.name);
  }

  updateTabTitle(event: any, item: any) {
    item.page_title = event.target.value;
    this.subs.add(
      fromEvent(event.target, 'input')
        .pipe(debounceTime(800))
        .subscribe(() => {
          this.dashboardBuilderStore.updateTabTitle(item.page_object_id, item.page_title);
        })
    );
  }


  async uploadLogo(event: any) {
    if (event.url.changingThisBreaksApplicationSecurity) {
      const base64Data = event.url.changingThisBreaksApplicationSecurity;

      // Check if it's SVG by looking for SVG data URI pattern
      if (base64Data.includes('data:image/svg+xml')) {
        // Convert SVG to PNG
        try {
          const pngBase64 = await this.convertSvgBase64ToPngBase64(base64Data);
          this.dashboardConfig.logo = pngBase64;
        } catch (error) {
          console.error('Error converting SVG to PNG:', error);
          this.dashboardConfig.logo = base64Data; // Fallback to original
        }
      } else if (base64Data.includes('data:image/png')) {
        // It's already PNG, use as is
        this.dashboardConfig.logo = base64Data;
      } else {
        // For other formats or if format detection fails
        this.dashboardConfig.logo = base64Data;
      }
    }
    this.dashboardBuilderStore.updateDashboardLogo(this.dashboardConfig.logo);
  }

  deleteLogo(_event: any) {
    this.dashboardConfig.logo = '';
  }

  convertSvgBase64ToPngBase64(svgBase64: string, outputWidth?: number, outputHeight?: number): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        // Create Image element
        const img = new Image();
        img.onload = () => {
          // Use provided size or fallback to SVG intrinsic size
          const width = outputWidth || img.width;
          const height = outputHeight || img.height;

          // Create canvas
          const canvas = document.createElement("canvas");
          canvas.width = width;
          canvas.height = height;
          const ctx = canvas.getContext("2d");

          if (!ctx) {
            reject("Unable to get canvas context");
            return;
          }

          // Draw SVG into canvas
          ctx.drawImage(img, 0, 0, width, height);

          // Convert canvas back to base64 (PNG)
          const pngBase64 = canvas.toDataURL("image/png");
          resolve(pngBase64);
        };

        img.onerror = (err) => reject(err);

        // Set source from base64 svg
        img.src = svgBase64;
      } catch (err) {
        reject(err);
      }
    });
  }







  canvasRender(element: HTMLElement) {
    return new Promise((resolve: (value: string) => void) => {
      html2canvas(element, { allowTaint: true, useCORS: true, scale: 1 }).then(canvas => {
        resolve(canvas.toDataURL('image/png', 3));
      });
    });
  }


  getClassificationNodes() {
    const dynamicObject: any = {};
    const chartSettings = this._dashboardService.chartSettings;
    const keys = Object.keys(chartSettings);
    keys.forEach(key => {
      let keyData: any = [];
      if (chartSettings[key]?.length >= 0) {
        keyData = [];
        chartSettings[key].forEach((element: {
          id: any; cols: number, rows: number, x: number, y: number, type: string, title?: string, key: string,
          Yaxis: AxisDropDown[], Xaxis: AxisDropDown, data: any, screener: boolean, chartType: string;
        }) => {
          const index = this.selectedCards[key]?.findIndex((x: { id: any; }) => x.id == element.id);
          if (index >= 0) {
            element.cols = this.selectedCards[key][index].cols;
            element.rows = this.selectedCards[key][index].rows;
            element.x = this.selectedCards[key][index].x;
            element.y = this.selectedCards[key][index].y;
            element.type = this.selectedCards[key][index].type;
            element.screener = this.selectedCards[key][index].screener;
            element.key = this.selectedCards[key][index].key;
            element.Xaxis = this.selectedCards[key][index].Xaxis;
            element.Yaxis = this.selectedCards[key][index].Yaxis;
            element.id = element.id.toString();
          }
          if (element.key == dashboardConstants.customCard) {
            delete element.data;
          }
          const data = {
            id: element.id,
            properties: element
          };
          if (element.rows && element.cols) {
            keyData.push(data);
          }
        });
      }
      if (keyData?.length > 0) {
        dynamicObject[key] = keyData;
      }
    });
    return dynamicObject;
  }

  // calculateHeight(addValue: number) {
  //   const cards: any[] = [];
  //   Object.keys(this.selectedCards).forEach((key) => cards.push(...this.selectedCards[key]));
  //   setTimeout(() => {
  //     const maxY = cards.reduce((max: number, item: { y: number; }) => Math.max(max, item.y), Number.NEGATIVE_INFINITY);
  //     const maxRow = cards.reduce((max: number, item: { rows: number; }) => Math.max(max, item.rows), Number.NEGATIVE_INFINITY);
  //     this.gridsterHeight = ((40 * (maxY + maxRow)) + (maxRow * addValue));
  //   }, 300);
  // }

  convertBase64ToFile(base64Data: string, fileName: string): File | null {
    try {
      // Ensure the base64 has correct data URI format
      const matches = base64Data.match(/^data:(image\/(png|svg\+xml));base64,(.+)$/);
      if (!matches) {
        console.warn('Invalid base64 format or unsupported file type');
        return null;
      }

      const mimeType = matches[1]; // "image/png" or "image/svg+xml"
      const extension = mimeType === 'image/png' ? 'png' : 'svg';

      // Decode base64 string
      const byteCharacters = atob(matches[3]);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);

      // Create File object
      return new File([byteArray], `${fileName}.${extension}`, { type: mimeType });
    } catch (error) {
      console.error('Error converting base64 to file:', error);
      return null;
    }
  }





  setSelectedCardsData(nodes: any) {
    this.selectedCards = {
      experimental_statistics: [],
      analytical_apps: [],
      official_statistics: [],
      compare_statistics: [],
      add_indicator: [],
      custom_card: []
    };
    this._dashboardService.chartSettings = {
      experimental_statistics: [],
      analytical_apps: [],
      official_statistics: [],
      compare_statistics: [],
      custom_card: []
    };
    const ids: any = [];
    Object.keys(nodes).forEach((key) => {
      if (nodes[key]?.length > 0) {
        nodes[key].forEach((element: {
          nodeRequest: any; id: string; properties: any
        }) => {
          if ((key === 'analytical_apps' || key === 'experimental_statistics') && element.properties?.type != 'Livability Dashboard') {
            const dispatchPayload: any = {
              id: element.id,
              contentType: key === 'analytical_apps' ? (element.properties.type == 'coi' || element.properties.type == 'scad' ? contentType['scad_official_indicator'] : key) : 'innovative-insights'
            };
            if (key === 'experimental_statistics') {
              dispatchPayload.visa = true;
            }
            if (key === 'analytical_apps' && element.properties?.selectedFilter) {
              this.callInsightDiscoveryFilter(element.properties);
            }
            this.store.dispatch(getIndicator(dispatchPayload));
          }
          if (key === 'official_statistics') {
            const dispatchPayload: any = {
              id: element.id,
              contentType: element.properties.screener ? contentType['official-insights'] : contentType['scad_official_indicator']
            };
            ids.push(element.id);
            this.store.dispatch(getIndicator(dispatchPayload));
          }
          if (element.properties?.type == 'Livability Dashboard') {
            this._dashboardService.getLiveabilityData(element.id);
          }

          if (key === 'compare_statistics') {
            this._dashboardService.getCompareIndicatorData(element.id, element.properties.title);
          }


          const data = {
            id: element.id,
            x: element.properties.x,
            y: element.properties.y,
            cols: element.properties.cols,
            rows: element.properties.rows,
            type: element.properties.type,
            requestNode: element.nodeRequest,
            screener: element.properties.screener,
            key: element.properties.key,
            Xaxis: element.properties.Xaxis,
            Yaxis: element.properties.Yaxis,
            chartType: element.properties.chartType
          };
          this.selectedCards[key].push(data);
          this._dashboardService.chartSettings[key].push(element.properties);
        });
      }
    });
    this.store.dispatch(getStatisticsInsights({ id: ids, name: contentTypeDashboard['statistics-insights'] }));
    Object.keys(this.selectedCards).forEach(key => {
      if (this.selectedCards[key]?.length <= 0) {
        delete this.selectedCards[key];
      }
    });
    if (!this.isAiDashboard) {
      // this.calculateHeight(40);
    }
  }


  callInsightDiscoveryFilter(filters: any) {
    this._pageService.getFilterCall(filters.filterPayload).subscribe(dataValue => {
      let updatedResponse: any = [];
      this.store.select(selectIndicatorGetById(filters.id.toString())).subscribe(resp => {
        updatedResponse = [];
        updatedResponse = cloneDeep(resp);
        if (updatedResponse.body && updatedResponse.body.visualizations?.length > 0) {
          const dataIndex = updatedResponse.body.visualizations.findIndex((x: { id: number; }) => x.id == filters.selectedVisualId);
          const metaIndex = updatedResponse.body.visualizations[dataIndex].indicatorVisualizations.visualizationsMeta.findIndex((y: { id: any; }) => y.id == updatedResponse.body.visualizations[dataIndex].indicatorVisualizations.visualizationDefault);
          updatedResponse.body.visualizations[dataIndex].indicatorVisualizations.visualizationsMeta[metaIndex].seriesMeta[0].data = dataValue[0].data;
          const id = filters.id;
          const initialization = dataValue.type !== 'insights-discovery' ? updatedResponse.body : updatedResponse.body.visualizations?.[0];
          const postData = { data: { ...updatedResponse.body, id }, status: { status: true, errorMessage: '', loader: false }, initialData: initialization, isRender: true };
          this.store.dispatch(loadAnalyticalSuccess({ data: postData }));
          // setTimeout(() => {
          this._dashboardService.settingsChanged.next({ type: contentType['analytical-apps'], id: filters.id, tools: this._dashboardService.cardFilter });
          // }, 3000);
        }
      });

    });
  }


  // dashboard detail end //


  exportPDF() {
    this.selectedId = '';
    if (this.mode != 'detail' && this.mode != 'preview') {
      this.preview('preview');
    }
    this.loaderModal.createElement();
    setTimeout(() => {
      this._dashboardService.downloadPdf(this.downloadPrint.nativeElement, this.dashboardConfig.name ?? 'Untitled');
      this.loaderModal.removeModal();
    }, 500);
  }


  onMouseDown(event: MouseEvent) {
    if (this.isDragged) {
      this.isDragging = true;
      this.offsetX = event.clientX;
      this.offsetY = event.clientY;
    }
  }

  onMouseUp() {
    this.isDragging = false;
  }

  onMouseMove(event: MouseEvent) {
    if (this.isDragging) {
      this.expandOrCollapseToolbar(true);
      const dx = event.clientX - this.offsetX;
      const dy = event.clientY - this.offsetY;
      const toolbar = this.toolbar.nativeElement as HTMLElement;
      const left = toolbar.offsetLeft > (this.document.documentElement.clientWidth - 480) ? (this.document.documentElement.clientWidth - 480) : toolbar.offsetLeft;
      toolbar.style.left = `${(left > 0 ? left : 0) + dx}px`;
      toolbar.style.top = `${toolbar.offsetTop + dy}px`;
      this.offsetX = event.clientX;
      this.offsetY = event.clientY;
      if (toolbar.offsetLeft > (this.document.documentElement.clientWidth / 2)) {
        this.changeToolbarRight();
      } else {
        this.changeToolbarToLeft();
      }
    }
  }





  closeExpandModel(_event: boolean) {
    const isRight = this.toolbar.nativeElement.classList.contains('ifp-db__toolbar--right');
    const defaultLang = this._themeService.defaultLang;
    this.toolbar.nativeElement.style.left = isRight ? (defaultLang == 'en' ? 'auto' : 0) : (defaultLang == 'en' ? 0 : 'auto');
    this.toolbar.nativeElement.style.right = isRight ? (defaultLang == 'en' ? 0 : 'auto') : (defaultLang == 'en' ? 'auto' : 0);
    // this._renderer.setStyle(this._elementRef.nativeElement.querySelector('.resizable'), 'width', `${0}px`);
    // this._renderer.setStyle(this._elementRef.nativeElement.querySelector('.resizable'), 'max-width', `${0}px`);
    this.expandOrCollapseToolbar(false);
  }

  openTextArea(event: boolean) {
    this.expandOrCollapseToolbar(event);
    this.isTextareaExpanded = event;
  }


  // toolbar position change  //
  changeToolbarPosition(event: string) {
    this.selectedToolbarPosition = event;
    if (this.selectedToolbarPosition == 'drag') {
      this.isDragged = false;
    }
    this.isDragged = false;
    if (event == 'left') {
      this.changeToolbarToLeft();
      this.toolbar.nativeElement.style.left = 0;
      this.toolbar.nativeElement.style.right = 'auto';
    } else if (event == 'right') {
      this.changeToolbarRight();
      this.toolbar.nativeElement.style.left = 'auto';
      this.toolbar.nativeElement.style.right = 0;
    } else {
      this.isDragged = true;
    }
  }

  pinDashboard(_event: boolean) {
    if (this.selectedToolbarPosition == 'drag') {
      this.isDragged = !this.isDragged;
    }
  }

  changeToolbarToLeft() {
    this.toolbar.nativeElement.classList.add('ifp-db__toolbar--left');
    this.toolbar.nativeElement.classList.remove('ifp-db__toolbar--right');
    this.isLeftCut = true;
  }

  changeToolbarRight() {
    this.toolbar.nativeElement.classList.remove('ifp-db__toolbar--left');
    this.toolbar.nativeElement.classList.add('ifp-db__toolbar--right');
    this.isLeftCut = false;
  }

  goBack() {
    if (this.location?.back) {
      this.location.back();
    } else {
      this.router.navigate(['/home']);
    }
  }

  // ** for customcard //
  updateCustomChart(event: string, cardIndex: number) {
    if (!this.dashboardId) {
      const singleDimentionChartType: string[] = ['line', 'column', 'bar', 'table', 'circular'];
      this.selectedCards[dashboardConstants.customCard][cardIndex].chartType = event;
      const chartData = this.cutomCardDataSourceType == 'Upload Data' ? this.csvData : (singleDimentionChartType.includes(event) ? singleDimentionData : multiDimentionData);
      this.selectedCards[dashboardConstants.customCard][cardIndex].data = chartData;
      const cardId = this.selectedCards[dashboardConstants.customCard][cardIndex].id;
      const customCardIndex = this._dashboardService.chartSettings['custom_card'].findIndex((x: { id: any; }) => x.id == cardId);
      if (customCardIndex >= 0) {
        this._dashboardService.chartSettings['custom_card'][customCardIndex].chartType = event;
        this._dashboardService.chartSettings['custom_card'][customCardIndex].data = chartData;
      }
    }
    this.selectedCardData = this.selectedCards[dashboardConstants.customCard][cardIndex];
    this.selectedCardData.isOpen = true;
    if (this.mode != 'detail' && this.mode != 'preview') {
      this.selectedId = this.selectedCards[dashboardConstants.customCard][cardIndex].id;
    }
    this.expandOrCollapseToolbar(true);
    this.isSelectedTool = 'data';
    this.setToolBarWidth();
    this.checkCardLength();
  }

  onMouseResizeDown(event: MouseEvent) {
    if ((event.target as HTMLElement).classList.contains('handle') && !this.isDragged && this.isSelectedTool == 'data') {
      this.dragging = true;
      this.startX = event.clientX;
      this.startWidth = this._elementRef.nativeElement.querySelector('.resizable').offsetWidth;
      this._renderer.setStyle(document.body, 'cursor', 'ew-resize');
      this._renderer.setStyle(document.body, 'user-select', 'none');
    }
  }

  onResizeMouseMove(event: MouseEvent) {
    if (!this.dragging) {
      return;
    }
    const newWidth = this.startWidth + (this.selectedToolbarPosition == 'right' ? (this.startX - event.clientX) : (event.clientX - this.startX));
    this._renderer.setStyle(this._elementRef.nativeElement.querySelector('.resizable'), 'width', `${newWidth}px`);
    this._renderer.setStyle(this._elementRef.nativeElement.querySelector('.resizable'), 'max-width', `${newWidth}px`);
  }

  onResizeMouseUp(event: MouseEvent) {
    if (this.dragging) {
      this.dragging = false;
      this._renderer.setStyle(document.body, 'cursor', 'default');
      this._renderer.setStyle(document.body, 'user-select', 'auto');
    }
  }

  selectTool(event: string) {
    this.isSelectedTool = event;
    this.setToolBarWidth();
  }

  setToolBarWidth() {
    this.toolbarWidth = 480;
  }

  customCardConfiguration() {
    const multiDimentionTypes: string[] = ['pie', 'doughnut'];
    const customCardIndex = this._dashboardService.chartSettings[dashboardConstants.customCard].findIndex((x: { id: any; }) => x.id == this.selectedId);
    if (multiDimentionTypes.includes(this._dashboardService.chartSettings[dashboardConstants.customCard][customCardIndex].chartType)) {
      this.isPieDisabled = false;
    }
  }

  // **for upload/add data functions //;
  closeUploadModel() {
    this.uploadData.removeModal();
  }

  uploadOrAddData(event: { data?: any, type: string, result: any, objectId: string, fileName: string }) {
    this.cutomCardDataSourceType = event.type;
    if (!this.selectedCards.custom_card) {
      this.selectedCards['custom_card'] = [];
      this._dashboardService.chartSettings['custom_card'] = [];
    }
    const customeCard = {
      id: this.generateRandomNumber(), cols: 3, rows: 11, y: 0, x: 0, key: dashboardConstants.customCard, type: '',
      Xaxis: axisDropDowns[0].options[0], Yaxis: [axisDropDowns[0].options[1]], dataObjectId: event.objectId,
      fileName: event.fileName, chartType: 'column'
    };
    this.selectedCards['custom_card'].push(customeCard);
    this._dashboardService.chartSettings['custom_card'].push(customeCard);
    this.checkCardLength();
    // setTimeout(() => {
    //   this.showImportTypes.set(false);
    // }, 100);
    this.uploadedFile = undefined;
    if (this.cutomCardDataSourceType == 'Upload Data') {
      this.uploadedFile = event.data;
      this.csvData = event.result;
      this.selectedId = customeCard.id.toString();
      this._dashboardService.setUploadCardTitle(event?.data?.file?.[0]?.name ?? 'Custom card', this.selectedId);
    }
    this.uploadData.removeModal();
    const cardIndex = this.selectedCards[dashboardConstants.customCard].findIndex((x: { id: number; }) => x.id == customeCard.id);
    this.updateCustomChart('column', cardIndex);
  }


  getData(index: number) {
    return this._dashboardService.chartSettings[dashboardConstants.customCard][index].data;
  }


  ngOnDestroy(): void {
    localStorage.setItem(this._dashboardService.selectedCards, JSON.stringify(this.selectedCards));
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
    this._modalService.removeAllModal();
    this.subs.unsubscribe();
    this.dashboardBuilderStore.resetStore();
    this.destroy$.next();
    this.destroy$.complete();
    this.tabDetailSubject.complete();
    // Clear breadcrumbs when component is destroyed
    this.pageData = [];
  }

  uploadFileData() {
    this._preService.getMethodRequest(`${prepsApiEndpoints.getNode + this.prepId + prepsApiEndpoints.workflowPreview}`, { limit: 200, offset: 0, page: 1, anchor: this.prepAnchor }).subscribe({
      next: next => {
        this._customService.uploadCustomData(next.records).subscribe({
          next: data => {
            const event = {
              data: {},
              type: 'Upload Data',
              fileName: '',
              result: data.records,
              objectId: data.dataId
            };
            // this.uploadOrAddData(event);
          },
          error: error => {
            this._toaster.error(error.error.error);
            return;
          }
        });
      },
      error: error => {
        this._toaster.error(error.error.error);
      }
    });
  }




  // new functions  //

  async createTabs() {
    this.tabChanging = true;
    if (this.dashboardBuilderStore.getTabs().length >= 1 && (this.dashboardBuilderStore.getSelectedPageId() == '' || !this.dashboardBuilderStore.getSelectedPageId())) {
      await this.checkConfigurations(statusParams.draft, true);
    }

    const tabStoreData = {
      selected_page_id: '',
      page_object_id: '',
      page_title: this._translate.instant('Untitled')
    };
    this.dashboardBuilderStore.createTab(tabStoreData);
    this.currentTabIndex = this.dashboardBuilderStore.getTabs().length - 1;
    this.isCreateTab = false;
    this.changeTab(tabStoreData.page_object_id, this.currentTabIndex);
  }


  tabAction(index: number, event: Event) {
    event.stopPropagation();
    if (index >= 0) {
      this.selectedTabIndex = index;
      this.alertModal.createElement();
    } else {
      if (this.createdCards()?.length <= 0) {
        this._toaster.error("Please Configure atleast one card");
        return;
      }
      this.createTabs();
    }
  }


  confirmRemoveTab(event: boolean) {
    const pageId = this.dashboardBuilderStore.getTabs()[this.selectedTabIndex].page_object_id;
    if (pageId && pageId !== '') {
      const endpoint = dashboardEndpoints.deleteTab(pageId);
      this.subs.add(
        this._apiService.getDeleteRequest(endpoint).subscribe({
          next: () => {
            this.dashboardBuilderStore.deleteTabById(pageId);
            this.currentTabIndex = Math.max(0, this.selectedTabIndex > 0 ? (this.currentTabIndex - 1) : 0);
            this.changeTab(this.dashboardBuilderStore.getTabs()[this.currentTabIndex].page_object_id, this.currentTabIndex, false);
          },
          error: (error) => {
            this._toaster.error(error?.error?.detail ?? '');
            return;
          }
        })
      );
    } else {
      this.dashboardBuilderStore.deleteTabById(pageId);
      this.currentTabIndex = Math.max(0, this.selectedTabIndex > 0 ? (this.currentTabIndex - 1) : 0);

      this.changeTab(this.dashboardBuilderStore.getTabs()[this.currentTabIndex]?.page_object_id, this.currentTabIndex, false);
    }
    this.alertModal.removeModal();
  }

  addAllIndicators() {
    if (this.selectedCard().type != this.cardTypes.widget) {
      this.resetCardSelection();
    }
    if (this.sourceModelType() == this.cardTypes.widget) {
      this.setWidgetCardAggregations();
      return;
    }

    // const selectedSourceCards = this.dashboardBuilderStore.currentTab()?.selectedSourceCards;
    // if (!Array.isArray(selectedSourceCards) || selectedSourceCards.length === 0) {
    //   return;
    // }

    // const pageId = this.selectedTab().page_object_id;
    // const cards = this.dashboardBuilderStore.currentTabCards() ?? [];
    // const selectedCardIds = new Set((this.dashboardBuilderStore.currentTab()?.selectedSourceCards ?? []).map(x => x.cardId));

    // Remove cards not in selectedSourcesCards and add new indicators in one loop
    // const selectedCardIdsArr = (this.dashboardBuilderStore.currentTab()?.selectedSourceCards ?? []).map(x => x.cardId);
    // const selectedCardIdsSet = new Set(selectedCardIdsArr);

    // Filter out cards not in selectedSourcesCards
    // const filteredCards = cards.filter(card => selectedCardIdsSet.has(card.position.positionId) || card.type != this.cardTypes.chart);
    // Track existing cardIds after filtering
    // const existingCardIds = new Set(filteredCards.map(card => card.position.positionId));
    // Add new indicators that are not already present
    // Add new indicators and datasets that are not already present
    // const newCards = (this.dashboardBuilderStore.currentTab()?.selectedSourceCards ?? []).filter(
    //   element => !existingCardIds.has(element.cardId)
    // );


    // Update cards if any were removed
    // if (filteredCards.length !== cards.length) {
    //   this.dashboardBuilderStore.updateAllCards(pageId, filteredCards);
    //   this.setCreateCards();
    // }

    const indicatorPromises: Promise<void>[] = [];
    for (const element of this.selectedSourcesCards()) {
      if (element.sourceType === sourceTypes.indicator) {
        indicatorPromises.push(this.callIndicatorCardData(element));
      } else if (element.sourceType === sourceTypes.dataset || element.sourceType == sourceTypes.dxp) {
        this.getFromLibrary(element);
      }
    }
  }

  async setWidgetCardAggregations() {
    const source = this.selectedKpiData()['customConfig']?.source || {};
    Object.assign(this.selectedKpiData(), {
      id: '',
      title: source.title || 'Untitled',
      type: this.cardTypes.widget,
      x: 0,
      y: 0,
      cols: 3,
      rows: 4,
      source_type: this.selectedKpiData()['sourceType'] || this.dataTypes.dataset,
      source: source,
      config: this.selectedKpiData()['config'] ?? {},
      metadata: this.selectedKpiData()['metadata'] ?? { cardId: this.log.createUUid }
    });


    if (this.selectedKpiData()['source_type'] == this.dataTypes.indicator) {
      await this.callIndicatorCardData({
        cols: this.selectedKpiData()['cols'],
        id: source.indicator_id,
        rows: this.selectedKpiData()['rows'],
        sourceType: this.selectedKpiData()['source_type'],
        title: source.indicator_name ?? 'Untitled',
        description: source.description ?? '',
        subtitle: this.selectedKpiData()['subtitle'] ?? '',
        type: this.selectedKpiData()['type'],
        x: this.selectedKpiData()['x'],
        y: this.selectedKpiData()['y'],
        cardId: this.selectedKpiData()['cardId'] ?? '',
        cardType: this.selectedKpiData()['cardType'] ?? '',
        source: source
      });
    }

    // console.log("this.selectedKpiData()", this.selectedKpiData())
    this.getWidgetCardAggregation(this.selectedKpiData());
  }

  callIndicatorCardData(element: sourceCards): Promise<void> {
    return new Promise<void>((resolve) => {
      if (this.sourceModelType() != this.cardTypes.widget) {
        this.createCards({
          source_type: this.dataTypes.indicator,
          positionId: element.cardId,
          cols: element.cols,
          rows: element.rows,
          source: {
            indicator_id: element.id,
            indicator_name: element.title
          },
          file: '',
          id: '',
          name: element.title,
          owner: '',
          storage_backend: '',
          index: 0
        });
      } else {
        this.selectedKpiData.update((value) => {
          value['source'] = {
            indicator_id: element.id,
            indicator_name: element.title
          };
          return value;
        });
        // this.selectedKpiData()['source'] = {
        //   dataset_id: resp.id,
        //   dataset_name: resp.name
        // }
      }
      resolve();
    });
  }


  createCards(resp: IndicatorResponse) {
    const cardConfig = {
      dashboard_object_id: this.dashboardBuilderStore.getDashboardId() ?? '',
      page_object_id: this.dashboardBuilderStore.getSelectedPageId() ?? '',
      page_title: '',
      id: '',
      title: resp.name,
      subtitle: '',
      description: '',
      type: resp.type ?? this.cardTypes.chart,
      position: {
        x: resp.x ?? 0,
        y: resp.y ?? 0,
        w: resp.cols ?? 3,
        h: resp.rows ?? 4,
        positionId: resp.positionId // <-- Add positionId here
      },
      x: resp.x ?? 0,
      y: resp.y ?? 0,
      cols: resp.cols ?? 3,
      rows: resp.rows ?? 11,
      source_type: resp.source_type,
      source: resp.source ?? {
        dataset_id: resp.id,
        dataset_name: resp.name
      },
      dashboard_type: this.isGovAffairs ? this.dashboardTypes.govAffairs : this.dashboardTypes.native,
      config: {
        x_label: '',
        x_axis: '',
        y_label: '',
        y_axis: [],
        chartType: this.selectedChartType(),
        subtitle: '',
      },
      metadata: {
        x_axis_aggregations: {},
        y_axis_aggregations: {},
        x_axis_category: [],
        y_axis_series: [],
        possibleAggregations: [],
        cardConfig: resp.cardConfig ?? {},
        cardId: this.log.createUUid,
        source_type: resp.source_type,
        source: resp.source ?? {
          dataset_id: resp.id,
          dataset_name: resp.name
        },
        x: resp.x ?? 0,
        y: resp.y ?? 0,
        cardType: resp.type ?? this.cardTypes.chart,
        cols: resp.cols ?? 3,
        rows: resp.rows ?? 11,
        card_title: {
          text_size: {
            index: 0,
            name: '20',
            value: 20
          },
          text_color: '#000000',
          text_style: ''
        },
        card_description: {
          text_size: {
            index: 0,
            name: '14',
            value: 14
          },
          text_color: '#000000',
          text_style: ''
        }
      }
    };
    this.dashboardBuilderStore.createChartCard(this.selectedTab().page_object_id, cardConfig.type, cardConfig);
    this.setCreateCards();
    this.checkLength();
    if (cardConfig.type === this.cardTypes.text) {
      this.selectTextCard(cardConfig);
    }
  }


  async selectCard(id: string, card: any, event?: Event): Promise<void> {
    event?.stopPropagation();
    if (this.selectedCard()?.metadata['cardId'] === id) {
      return;
    }
    return new Promise((resolve) => {
      this.selectedCardId.set(cloneDeep(id));
      this.selectedCard.set(cloneDeep(card));
      const cardIndex = this.dashboardBuilderStore.currentTabCards().findIndex(x => x.metadata['cardId'] == card.metadata.cardId);
      if (cardIndex !== -1) {
        this.selectedCard().config['x_axis'] = this.dashboardBuilderStore.currentTabCards()[cardIndex].config['x_axis'];
        this.selectedCard().config['y_axis'] = this.dashboardBuilderStore.currentTabCards()[cardIndex].config['y_axis'];
      }
      if (this.selectedCard().type == this.cardTypes.widget) {
        this.selectedKpiData.set(this.selectedCard());
      }

      if (this.selectedCard().type == this.cardTypes.widget || this.selectedCard().type == this.cardTypes.text) {
        this.expandOrCollapseToolbar(true);
        this.toolbarCmp.toolbarTabSelect(2);
      }
      const filterGroup = this.selectedCard().config['filters']?.filters ? this.selectedCard().config['filters']?.filters?.groups?.[0] : this.selectedCard().config['filters']?.groups?.[0];
      const sourceFilter = filterGroup?.conditions;
      const sourceFilterOperator = filterGroup?.operator;
      this.selectedSourceFilter.set(sourceFilter?.length ? this.getFilterConfig(sourceFilter, sourceFilterOperator) : { filter: [], operator: 'AND' });
      if (card.type != this.cardTypes.chart) {
        resolve();
        return;
      }
      if (this.selectedCard() && this.selectedCard().metadata?.['possibleAggregations']?.length > 0) {
        this.expandOrCollapseToolbar(true);
        resolve();
        return;
      }
      this.getPossibleAggregationColumns(card);
      this.expandOrCollapseToolbar(true);
      this.toolbarCmp.toolbarTabSelect(0);
      resolve();
    });
  }

  private getComparatorKey(cmp: unknown): string {
    if (cmp && typeof cmp === 'object') {
      // Support different shapes: {key, display_name} or {value, display_name}
      const obj = cmp as { key?: string; value?: string };
      return obj.key ?? obj.value ?? '';
    }
    return (cmp as string) ?? '';
  }

  getFilterConfig(filters: any, operator: string) {
    const filterConf = {
      filter: [],
      operator: operator && operator !== '' ? operator : 'AND'
    };

    if (!filters?.length) {
      return filterConf;
    }

    const possibleAggregations = this.selectedCard()?.metadata?.['possibleAggregations'] || [];

    filterConf.filter = filters.map((element: { comparator: any; value: any; column: any; }) => {
      const columnObj = possibleAggregations.find((x: { name: any }) => x.name === element.column);

      // Ensure comparator is always an object with display_name when available
      let comparator = element.comparator;
      if (columnObj?.comparators && typeof element.comparator === 'string') {
        const match = (columnObj.comparators as any[]).find((c: any) => c?.value === element.comparator || c?.key === element.comparator);
        comparator = match ?? element.comparator;
      }

      return {
        comparator,
        value: element.value,
        column: columnObj
      };
    });

    return filterConf;
  }

  checkLength() {
    this.cardsArrayEmpty.set(this.createdCards().length <= 0);
  }


  expandOrCollapseToolbar(toggle: boolean) {
    if (toggle == this.isToolbarExpanded) {
      return;
    }
    this.isToolbarExpanded = toggle;
    if (this.isToolbarExpanded) {
      this.toolbarWidth = 480;
    }
    requestAnimationFrame(() => this.setGridsterOption());
  }

  setGridsterOption() {
    const width = this.dbHeader()?.nativeElement.offsetWidth || 0;
    this.options = {
      gridType: GridType.Fixed,
      compactType: CompactType.None,
      margin: 10,
      outerMargin: false,
      outerMarginTop: null,
      outerMarginRight: null,
      outerMarginBottom: null,
      outerMarginLeft: null,
      useTransformPositioning: false,
      mobileBreakpoint: 200,
      minCols: 12,
      maxCols: 12,
      minRows: 11,
      maxRows: 100,
      maxItemCols: 12,
      minItemCols: 1,
      maxItemRows: 100,
      minItemRows: 1,
      maxItemArea: 1000,
      minItemArea: 1,
      defaultItemCols: 3,
      defaultItemRows: 4,
      fixedColWidth: ((width / 12) - 9.5),
      fixedRowHeight: 40,
      keepFixedHeightInMobile: false,
      keepFixedWidthInMobile: false,
      scrollSensitivity: 0,
      scrollSpeed: 20,
      enableEmptyCellClick: true,
      enableEmptyCellContextMenu: false,
      enableEmptyCellDrag: false,
      emptyCellDragMaxCols: 12,
      emptyCellDragMaxRows: 12,
      ignoreMarginInRow: false,
      draggable: {
        enabled: this.mode === 'edit' ? true : false
      },
      resizable: {
        enabled: this.mode === 'edit' ? true : false
      },
      swap: true,
      pushItems: true,
      disablePushOnDrag: true,
      disableScrollVertical: true,
      disableScrollHorizontal: true,
      disablePushOnResize: false,
      pushDirections: { north: true, east: true, south: true, west: true },
      pushResizeItems: false,
      displayGrid: DisplayGrid.None,
      disableWindowResize: true,
      disableWarnings: false,
      scrollToNewItems: false,
      externalItems: true, // ✅ ENABLE external drop
      enableEmptyCellDrop: true, // ✅ Enables drop into empty cells
      emptyCellDropCallback: this.onExternalDrop.bind(this), // ✅ Callback
      itemChangeCallback: this.onItemChange.bind(this),
      itemResizeCallback: this.onItemResize.bind(this)
    };
  }

  openConfigurePanel(_event: boolean, card: cardObject) {
    this.expandOrCollapseToolbar(true);
    this.toolbarCmp.toolbarTabSelect(0);
  }


  getPossibleAggregationColumns(card: cardObject) {
    const idKey = card.source_type == this.dataTypes.dxp ? 'asset_id' : (card.source_type == this.dataTypes.dataset ? 'dataset_id' : 'indicator_id');
    const sourceId = card?.source && idKey in card.source ? (card.source as any)[idKey] : undefined;
    let payload = {};
    payload = {
      source: card?.source,
      source_type: card.source_type
    };
    if (!sourceId) {
      return;
    }
    this.chartAggregationLoader = true;
    this._apiService.postFormDataRequest(`${dashboardEndpoints.possibleAggregations}/possible-aggregations/`, payload).subscribe({
      next: columns => {
        this.chartAggregationLoader = false;
        if (!this.selectedCard()) {
          return;
        }
        const updatedConfig = { ...this.selectedCard().metadata, possibleAggregations: columns };
        // const updatedConfig = { ...this.selectedCard().metadata, possibleAggregations: aggregationOptions };
        const updatedCard = { ...this.selectedCard(), metadata: updatedConfig };
        this.selectedCard.set(updatedCard);
        this.updateCurrentCard();
        this.setCreateCards();
      },
      error: err => {
        this.chartAggregationLoader = false;
        this._toaster.error(err.error.error || 'Failed to fetch aggregation columns');
      }
    });
  }

  setCreateCards() {
    const currentCards = this.dashboardBuilderStore.currentTabCards();
    const existingCards = this.createdCards();
    const existingCardIds = new Set(existingCards.map((card: any) => card.metadata?.cardId));

    // Only add new cards that don't already exist
    const newCards = currentCards.filter((card: any) => !existingCardIds.has(card.metadata?.cardId)
    );

    this.createdCards().push(...newCards);
  }

  getFromLibrary(event: FileResponePrep) {
    if (!event) {
      return;
    }
    const indResp: IndicatorResponse = {
      file: event.file ?? '',
      id: event.id ?? '',
      name: event.title ?? '',
      owner: event.owner ?? '',
      storage_backend: event.storage_backend ?? '',
      index: Number(event.index) || 0,
      source_type: event.sourceType ?? this.dataTypes.dataset,
      positionId: event.id ?? '',
      source: event.source ?? {}
    };
    this.createCards(indResp);
    this.closeImportDataModal();
  }

  closeLibrary() {
    this.isLaibraryModelOpen.set(false);
    this.libraryListModal.removeModal();
  }


  createCardAction(form: { value: any, type: string } = { value: {}, type: '' }) {
    this.applaySelectCard();
    if (!this.selectedCard()) {
      return;
    }
    const formValue = Object.keys(form?.value).length ? form?.value : this.selectedCard().metadata;
    // ['aggregation'];
    if (formValue) {
      const { xAxisLabel, xAxisValue, yAxisLabel, yAxisValue } = formValue;
      const legends = formValue.legends;
      const keep_initial = formValue.keep_initial ?? true;
      const filters = this.selectedSourceFilter();

      // Extract common logic
      const getAxisConfig = (axis: any[]) => ({
        column: (axis ?? []).map(x => x.name).join(','),
        aggregator: (axis ?? []).map(x => x.dropdownValue).join(',')
      });

      const buildFilterConditions = () => (filters.filter ?? []).map(e => ({
        column: e.column.name,
        data_type: e.column.type,
        comparator: this.getComparatorKey(e.comparator),
        value: numericDataTypes.includes(e.column.type) ? +e.value : e.value
      }));
      // Build payload once

      const payload: ChartCardConfig = {
        dashboard_object_id: this.dashboardBuilderStore.getDashboardId() ?? '',
        page_object_id: this.dashboardBuilderStore.getSelectedPageId() ?? '',
        title: this.selectedCard().title || 'Untitled',
        subtitle: this.selectedCard().subtitle || '',
        description: this.selectedCard().description || '',
        type: this.cardTypes.chart,
        position: this.selectedCard().position ?? { x: 0, y: 0, w: 1, h: 1, positionId: this.selectedCard().id ?? '' },
        source_type: this.selectedCard().source_type ?? this.dataTypes.dataset,
        source: this.selectedCard().source ?? { dataset_id: '', dataset_name: '' },
        dashboard_type: this.isGovAffairs ? this.dashboardTypes.govAffairs : this.dashboardTypes.native,
        config: {
          x_axis: { label: xAxisLabel, axis: getAxisConfig(xAxisValue) },
          y_axis: { label: yAxisLabel, axis: getAxisConfig(yAxisValue) },
          legends,
          chart_type: (this.selectedCard().metadata['chart_value'] == this.chartTypes.dounght ? this.chartTypes.pie : this.selectedCard().metadata['chart_value']) ?? this.chartTypes.column,
          subtitle: '',
          filters: {
            groups: [{ conditions: buildFilterConditions(), operator: filters.operator || 'and' }],
            global_operator: 'and',
          },
          keep_initial
        },
        metadata: { ...this.selectedCard().metadata, chart_value: this.selectedCard()?.metadata?.['chart_value'] ?? this.chartTypes.column },
        page_title: '',
        id: this.selectedCard().id ?? '',
        x: this.selectedCard().x ?? 0,
        y: this.selectedCard().y ?? 0,
        cols: this.selectedCard().cols ?? 0,
        rows: this.selectedCard().rows ?? 0
      };

      // Determine API action
      const isPreview = form.type === this.dashboardConstants.cardPreview;
      const isUpdate = form.type === dashboardConstants.cardUpdate;
      const isCreate = form.type === dashboardConstants.cardCreate;
      const endpoint = isPreview ? dashboardEndpoints.cardPreview : (form.type === this.dashboardConstants.cardCreate ? dashboardEndpoints.cardCreate : `${dashboardEndpoints.kpiCardUpdate + this.selectedCard().id}/update`);


      const apiAction = isUpdate ? this._apiService.putFormDataRequest(endpoint, payload) : this._apiService.postFormDataRequest(endpoint, payload);
      this.dashboardCardLoader = true;
      apiAction.subscribe({
        next: next => {
          this.dashboardCardLoader = false;
          if (isPreview) {
            this.handlePreviewResponse(next, payload, this.selectedCard());
          } else if (isCreate) {
            this.handleCreateUpdateResponse(next, payload);
          } else {
            this.updateSelectCard(next);
          }
          this.selectedCard.set(payload);
          this.updateCardData();
          if (this.selectedCard().config['chart_type'] == this.chartTypes.pie || this.selectedCard().config['chart_type'] == this.chartTypes.dounght) {
            this.setPieValues(this.selectedCard());
            this.updateCardData();
          }
        },
        error: err => {
          this.dashboardCardLoader = false;
          this._toaster.error(err.error || 'Operation failed');
        }
      });
    }
  }

  updateSelectCard(next: any) {
    if (!this.selectedCard()) {
      return;
    }

    this.selectedCard().config = next.config;
    this.selectedCard().metadata = next.metadata;
    this.selectedCard().position = next.position;
    this.selectedCard().source = next.source;
    this.selectedCard().source_type = next.source_type;
    this.selectedCard().title = next.title;
  }

  private handlePreviewResponse(response: any, payload: ChartCardConfig, selectedCard: any) {
    this.selectedChartType.set(response.metadata?.chart_value ?? this.chartTypes.column);
    const meta = payload.metadata;
    const chartType = (this.selectedCard().config['chart_value'] == this.chartTypes.dounght ? this.chartTypes.pie : this.selectedCard().metadata['chart_value']) ?? this.chartTypes.column;

    // Set series data
    meta['y_axis_series'] = response.series ?? meta['y_axis_series'];
    meta['y_axis_series']?.forEach((series: any, index: number) => {
      Object.assign(series, {
        type: chartType,
        color: series.color ?? selectedCard.metadata?.['y_axis_series']?.[index]?.color ?? this._dashboardService.getColor(index)
      });
    });

    // Set default chart properties
    const defaultChartProps = {
      chartSpacing: [10, 10, 10, 10],
      isDatalabel: true,
      isPrecise: false,
      legendEnable: true,
      legendPosition: 'center'
    };

    Object.entries(defaultChartProps).forEach(([key, defaultValue]) => {
      meta[key] = meta[key] ?? defaultValue;
    });

    // Apply properties to first series
    if (meta['y_axis_series']?.[0]) {
      Object.assign(meta['y_axis_series'][0], {
        spacing: meta['chartSpacing'],
        isDatalabel: meta['isDatalabel'],
        isPrecise: meta['isPrecise'],
        legendEnable: meta['legendEnable'],
        legendPosition: meta['legendPosition']
      });
    }

    meta['x_axis_category'] = response.xAxis?.categories ?? meta['x_axis_category'];
    meta['possibleAggregations'] = selectedCard.metadata?.['possibleAggregations'] ?? [];
  }

  private handleCreateUpdateResponse(response: any, payload: ChartCardConfig) {
    Object.assign(payload, {
      id: response?.card?.object_id ?? payload.id,
      title: response?.card?.name ?? payload.title,
      dashboard_object_id: response?.dashboard?.object_id ?? payload.dashboard_object_id,
      page_object_id: response?.page?.object_id ?? payload.page_object_id
    });
    this.setDashboardAndPageId(response);
  }

  setDashboardAndPageId(resp: any) {
    if (!this.dashboardBuilderStore.getDashboardId()) {
      this.dashboardBuilderStore.updateDashboardId(resp?.dashboard_object_id);
    }
    if (!this.dashboardBuilderStore.getSelectedPageId()) {
      this.dashboardBuilderStore.setSelectedPage(resp?.page_object_id);
      this.dashboardBuilderStore.updateTabPageObjectId('', resp?.page_object_id);
      const currentTab = this.dashboardBuilderStore.currentTab();
      if (currentTab) {
        this.selectedTab.set(currentTab);
      }
    }
  }


  updateCardData() {
    this.updateCurrentCard();
  }

  openImportIndicators(type: string) {
    this.importType = type;
    this.getAssetList();
    switch (type) {
      case 'library':
        this.isLaibraryModelOpen.set(true);
        this.libraryListModal.createElement();
        break;
      case 'upload':
        this.uploadData.createElement();
        break;
      default:
        this.isImportDropdown = true;
        this.importInidcators.createElement();
        break;
    }
  }

  async onSelectKpiEvent(event: SelectActionEvent, card: any) {
    this.selectCard(card.metadata.cardId, card);
    // this.selectedCardId.set(card.metadata.cardId);
    // this.selectedCard.set(card);
    if (event.event === this.kpiTemplateMenuEvents.edit) {
      this.kpiIsEdit = true;
      this.selectedKpiData.update((value) => {
        let newConfig = card?.metadata?.cardConfig?.config;
        newConfig = { ...newConfig, customConfig: card?.metadata?.cardConfig?.customConfig };
        return { ...value, config: newConfig };
      });
      this.isKpiModalOpen = true;
      this.editKpiModal()?.createElement();
    }
    if (event.event == this.kpiTemplateMenuEvents.delete) {
      this.alertCardModal.createElement();
    }

    if (event.event == this.kpiTemplateMenuEvents.duplicate) {
      const duplicateCard = cloneDeep(this.createdCards().find((x: { metadata: { cardId: any; }; }) => x.metadata.cardId == card.metadata['cardId']));
      duplicateCard.id = '';
      duplicateCard.metadata['cardId'] = this.log.createUUid;
      this.dashboardBuilderStore.createChartCard(this.selectedTab().page_object_id, duplicateCard.type, duplicateCard);
      await this.selectCard(duplicateCard.metadata['cardId'], duplicateCard, null as any);
      this.createdCards().push(duplicateCard);
    }
  }

  closeKpiEditModal() {
    this.isKpiModalOpen = false;
    this.selectedKpiData.set({});
    this.editKpiModal()?.removeModal();
  }

  getAssetList(searchKey: string = '') {
    return new Promise(resolve => {
      this.productListLoader = true;
      this.subs.add(
        this._apiService.getMethodRequest(dxpApi.product, { search: searchKey }, false).subscribe({
          next: (resp) => {
            this.productdetailList = resp;
            this.productListLoader = false;            //     key: 'library',
            //     name: 'My Bayaan Library',
            //     iconClass: 'ifp-icon-library'
            //   },
            //   {
            //     key: 'upload',
            //     name: 'Upload Data',
            //     iconClass: 'ifp-icon-upload-thick'
            //   },
            //   {
            //     key: 'browse',
            //     name: 'Browse Indicators',
            //     iconClass: 'ifp-icon-browse'
            //   },
            //   {
            //     key: 'myApps',
            //     name: 'Add from My BookMark',
            //     iconClass: 'ifp-icon-apps-plus'
            //   },
            //   {
            //     key: 'dxp',
            //     name: 'From DXP',
            //     iconClass: 'ifp-icon-catelogue'
            //   }
            // ];
            resolve(true);
          },
          error: (error) => {
            if (error.status == '403') {
              this.dataSourceList = this.dataSourceList.filter(item => item.key !== 'dxp');
            }
            this._toasterService.error(error.error);
            this.productListLoader = false;
            resolve(false);
          }
        })
      );
    });
  }

  onSelectedProduct(selectedProduct: SelectedProduct) {
    this.selectedProduct = selectedProduct;
    const indResp: IndicatorResponse = {
      file: '',
      id: this.selectedProduct.sourceAssetId ?? '',
      name: this.selectedProduct.title ?? '',
      owner: '',
      storage_backend: '',
      index: 0,
      source_type: this.dataTypes.dxp,
      positionId: this.selectedProduct.sourceAssetId
    };

    // const selectedSource = this.dashboardBuilderStore.currentTab()?.selectedSourceCards;
    // selectedSource?.push({
    //   cols: 0,
    //   id: selectedProduct.sourceAssetId,
    //   rows: 1,
    //   sourceType: selectedProduct.sourceProductId,
    //   title: selectedProduct.title,
    //   subtitle: selectedProduct.subTitle,
    //   type: 'dxp',
    //   x: 0,
    //   y: 0,
    //   cardId: selectedProduct.sourceProductId,
    //   cardType: selectedProduct.cardType,s
    // });
    // this.dashboardBuilderStore.updateSelectedSourceCards(this.dashboardBuilderStore.currentTab()?.page_object_id ?? '', [...(selectedSource ?? [])]);
    this.createCards(indResp);
  }



  removeSelect(index: number) {
    this.selectedSourcesCards()?.splice(index, 1);
    this.selectedSourcesCards.set(cloneDeep(this.selectedSourcesCards()));
  }

  onDragOver(event: DragEvent) {
    event.preventDefault(); // Required for drop to work
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
  }

  onExternalDrop(event: MouseEvent, gridPosition: GridsterItem) {
    const raw = (event as DragEvent).dataTransfer?.getData('text/plain');
    if (!raw) {
      return;
    }
    const droppedItem = JSON.parse(raw);
    const userInputConfig = { element_text: '' };
    const newItem: IndicatorResponse = {
      x: gridPosition.x,
      y: gridPosition.y,
      cols: droppedItem.type == this.cardTypes.widget ? gridPosition.cols : 4,
      rows: droppedItem.type == this.cardTypes.widget ? gridPosition.rows : (droppedItem.key == this.cardTypes.text ? 5 : 3),
      label: droppedItem.label,
      file: '',
      id: this.log.createUUid,
      name: '',
      owner: '',
      storage_backend: '',
      index: 0,
      source_type: droppedItem.key == this.cardTypes.text ? sourceTypes.userInput : '',
      type: droppedItem.type,
      positionId: this.log.createUUid,
      cardConfig: this.cardTypes.text ? userInputConfig : droppedItem
    };

    // const selectedSourcesCards = this.dashboardBuilderStore.currentTab()?.selectedSourceCards;
    // selectedSourcesCards?.push({
    //   sourceType: newItem.source_type ?? '',
    //   title: newItem.label ?? '',
    //   cardId: newItem.positionId ?? '',
    //   id: newItem.id ?? '',
    //   cols: newItem.cols ?? 0,
    //   rows: newItem.rows ?? 0,
    //   type: '',
    //   x: gridPosition.x,
    //   y: gridPosition.y
    // });
    // this.dashboardBuilderStore.updateSelectedSourceCards(this.dashboardBuilderStore.currentTab()?.page_object_id ?? '', [...(selectedSourcesCards ?? [])]);

    this.createCards({
      ...newItem,
      source_type: droppedItem.key == this.cardTypes.text ? sourceTypes.userInput : this.dataTypes.dataset,
    });

    if (droppedItem.type === cardTypes.widget) {
      this.selectedKpiData.set(newItem.cardConfig ?? {});
    }

    if (droppedItem.key == this.cardTypes.text) {
      this.selectRichTextCard(this.createdCards().length - 1);
      this.cardEditMode = true;
    }
    // this.selectedCards = { id: event.id, cols: 3, rows: 11, y: 0, x: 0, key: event.cnt_type, type: event.type, indicatorId: event.indicatorId, title: event.title, screener: isScreener, sourceType: sourceTypes.indicator, cardId: event.id };
  }

  getSelectedIndicators(event: sourceCards[]) {
    if (this.sourceModelType() === this.cardTypes.chart) {
      this.selectedSourcesCards.set(event);
      return;
    }
    this.selectedKpiData.update((value) => {
      const sourceValue = Array.isArray(event) ? event[0] : event;
      if (sourceValue.sourceType == this.dataTypes.indicator) {
        sourceValue.source = {
          dataset_id: '',
          dataset_name: '',
          indicator_id: sourceValue.id,
          indicator_name: sourceValue.title,
        };
      }
      value['customConfig'] = {
        ...(value['customConfig'] || {}),
        source: sourceValue.source,
      };
      value['sourceType'] = sourceValue.sourceType;
      return value;
    });
    if (this.sourceModelType() == cardTypes.widget) {
      this.addAllIndicators();
      this.closeImportDataModal();
    }
  }


  updateChartSettings(event: any) {
    this.applaySelectCard();
    if (!this.selectedCard() || !this.selectedCard().metadata?.['y_axis_series']?.length) {
      return;
    }
    switch (event.type) {
      case this.chartToolTypes.chartType:
        this.selectedChartType.set(event.value.key);
        this.selectedCard().metadata?.['y_axis_series'].forEach((x: any) => x.type = event.value.key);
        this.selectedCard().config['chart_type'] = event.value.key;
        this.selectedCard().metadata['chart_value'] = event.value.value;
        if (this.selectedChartType() == this.chartTypes.pie || this.selectedChartType() == this.chartTypes.dounght) {
          this.selectedCard().metadata['pie_axis_series'] = this.selectedCard().metadata['pie_axis_series'] ?? [{
            type: '',
            data: [],
            isDatalabel: this.selectedCard().metadata?.['y_axis_series'][0].isDatalabel,
            isPrecise: this.selectedCard().metadata?.['y_axis_series'][0].isPrecise
          }];
          this.selectedCard().metadata['pie_axis_series'][0].type = event.value.key;
          this.setPieValues(this.selectedCard());
          this.selectedCard().metadata['selectedPieSeries'] = this.selectedCard()?.metadata?.['x_axis_category'][this.selectedCard()?.metadata?.['x_axis_category'].length - 1];
        }
        break;
      case this.chartToolTypes.chartStyle:
        const dynamicKey = this.selectedChartType() == this.chartTypes.pie || this.selectedChartType() == this.chartTypes.dounght ? 'pie_axis_series' : 'y_axis_series';
        this.selectedCard().metadata[dynamicKey] = event.value.value;
        break;
      case this.chartToolTypes.chartSpacing:
        this.selectedCard().metadata['chartSpacing'][event.value.index] = event.value.value;
        this.selectedCard().metadata['y_axis_series'][0].spacing = this.selectedCard().metadata['chartSpacing'];
        break;
      case this.chartToolTypes.chartSettings:
        if (event.value?.key === chartSettingOpts.dataLabel || event.value?.key === chartSettingOpts.preciseValue) {
          const keyMap: Record<string, string> = {
            [chartSettingOpts.dataLabel]: 'isDatalabel',
            [chartSettingOpts.preciseValue]: 'isPrecise'
          };
          const metaKey = keyMap[event.value.key];
          this.selectedCard().metadata[metaKey] = event.value.value;
          if (this.selectedCard().metadata['y_axis_series']?.[0]) {
            this.selectedCard().metadata['y_axis_series'][0][metaKey] = event.value.value;
          }
          if (this.selectedCard().metadata['pie_axis_series']?.[0]) {
            this.selectedCard().metadata['pie_axis_series'][0][metaKey] = event.value.value;
          }
        }
        break;
      case this.chartToolTypes.legendEnable:
        this.selectedCard().metadata['legendEnable'] = event.value.value;
        this.selectedCard().metadata['y_axis_series'][0].legendEnable = this.selectedCard().metadata['legendEnable'];
        break;
      case this.chartToolTypes.legendPosition:
        this.selectedCard().metadata['legendPosition'] = event.value.value;
        this.selectedCard().metadata['y_axis_series'][0].legendPosition = this.selectedCard().metadata['legendPosition'];
        break;
      case this.chartToolTypes.icon:
        this.selectedCard().metadata['icon'] = event.value.icon;
        break;
      case this.chartToolTypes.cardTitle:
        // this.selectedCard().title = event.value;
        this.selectedCard.update(value => {
          value.title = event.value;
          return value;
        });
        break;
      case this.chartToolTypes.titleSettings:
        this.selectedCard().metadata['card_title'].text_size = event.value.text_size;
        this.selectedCard().metadata['card_title'].text_color = event.value.text_color;
        this.selectedCard().metadata['card_title'].text_style = event.value.text_style;
        break;
      case this.chartToolTypes.cardDescription:
        // this.selectedCard().subtitle = event.value;
        // this.selectedCard().description = event.value;
        this.selectedCard.update(value => {
          value.description = event.value;
          return value;
        });
        break;
      case this.chartToolTypes.descriptionSettings:
        this.selectedCard().metadata['card_description'].text_size = event.value.text_size;
        this.selectedCard().metadata['card_description'].text_color = event.value.text_color;
        this.selectedCard().metadata['card_description'].text_style = event.value.text_style;
        break;
      default:
        break;
    }

    this.updateCardData();
    this._cdr.detectChanges();
  }

  setPieValues(selectedCard: ChartCardConfig) {
    const pieAxis = selectedCard.metadata?.['pie_axis_series']?.[0];
    const yAxisSeries = selectedCard.metadata?.['y_axis_series'];

    if (!pieAxis || !yAxisSeries?.length) return;

    const series = yAxisSeries[0];
    const categories = selectedCard.metadata['x_axis_category'] || [];

    pieAxis.data = yAxisSeries.length > 1
      ? yAxisSeries.map(({ name, color, data }: any, index: number) => ({
        name,
        color: this._dashboardService.getColor(index),
        y: data?.[data.length - 1] || 0
      }))
      : series.data?.map((y: number, i: number) => ({
        name: categories[i] || `Category ${i + 1}`,
        color: pieAxis.data?.[i]?.color || this._dashboardService.getColor(i),
        y
      })) || [];
  }


  updatePieSeries(event: string) {
    if (!this.selectedCard()?.metadata?.['y_axis_series']?.length) {
      return;
    }

    const categories = this.selectedCard().metadata['x_axis_category'] ?? [];
    const idx = categories.indexOf(event);
    if (idx === -1) {
      return;
    }

    const yAxisSeries = this.selectedCard().metadata['y_axis_series'];
    // this.selectedCard().metadata['pie_axis_series'] = [];
    this.selectedCard().metadata['pie_axis_series'][0] = {
      ...this.selectedCard().metadata['pie_axis_series'][0],
      type: this.selectedCard().config['chart_type'] ?? '',
      data: yAxisSeries.map((element: { name: string; color: string; data: any[] }) => ({
        name: element.name,
        color: element.color,
        y: element.data?.[idx]
      }))
    };
    this.selectedCard().metadata['selectedPieSeries'] = event;
    this.updateCardData();
  }

  onItemResize(item: any): void {
    this.resized.next(true);
  }



  updateCurrentCard() {
    if (this.selectedCard()) {
      this.dashboardBuilderStore.updateChartCard(
        this.selectedTab().page_object_id,
        this.selectedCardId(),
        this.selectedCard()
      );
      this.updateCardInCreatedCards(this.selectedCardId(), this.selectedCard());
      this._cdr.detectChanges();
    }
  }

  updateCardInCreatedCards(cardId: string, updatedCard: ChartCardConfig) {
    const cardIndex = this.createdCards().findIndex((card: { metadata: { cardId: string; }; }) => card.metadata?.cardId === cardId);
    if (cardIndex !== -1) {
      this.createdCards()[cardIndex] = { ...updatedCard };
    }
  }


  async cardActionClicked(event: { event: string, value: boolean }, card: ChartCardConfig, index: number) {
    if (event.event !== 'duplicate') {
      if (card.type === cardTypes.chart) {
        this.selectCard(card.metadata['cardId'], card);
      } else if (card.type === cardTypes.text) {
        this.selectRichTextCard(index);
      }
    }
    switch (event.event) {
      case 'edit':
        this.cardEditMode = true;
        break;
      case 'delete':
        this.alertCardModal.createElement();
        break;
      case 'duplicate':
        // const duplicateCard = cloneDeep(this.dashboardBuilderStore.getCardByMetadataCardId(card.page_object_id, card.metadata['cardId']));
        const duplicateCard = cloneDeep(this.createdCards().find((x: { metadata: { cardId: any; }; }) => x.metadata.cardId == card.metadata['cardId']));

        if (duplicateCard) {
          duplicateCard.id = '';
          duplicateCard.metadata['cardId'] = this.log.createUUid;
          this.dashboardBuilderStore.createChartCard(this.selectedTab().page_object_id, duplicateCard.type, duplicateCard);
          await this.selectCard(duplicateCard.metadata['cardId'], duplicateCard, null as any);
          if (duplicateCard.type === cardTypes.text) {
            this.saveTextCardConfig(duplicateCard.metadata['cardConfig'], card);
          }
          this.createdCards().push(duplicateCard);
        }
        break;
      default:
        break;
    }
  }


  confirmRemoveCard(_event: boolean) {
    if (!this.selectedCard()) {
      this.alertCardModal.removeModal();
      return;
    }

    // Remove from UI immediately
    const cardIndex = this.createdCards().findIndex((x: { metadata: any; }) => x.metadata.cardId === this.selectedCard().metadata['cardId']);
    if (cardIndex !== -1) {
      this.createdCards().splice(cardIndex, 1);
    }

    // Update store
    this.dashboardBuilderStore.deleteChartCard(this.selectedTab().page_object_id, this.selectedCard().metadata['cardId']);

    // Handle chart-specific cleanup
    if (this.selectedCard().type === this.cardTypes.chart) {
      this.removeCardFromSourseList(this.selectedCard().position?.positionId ?? '');

      // Only make API call if card has been saved
      if (this.selectedCard().id) {
        this._apiService.getDeleteRequest(`${dashboardEndpoints.cardDetail}${this.selectedCard().id}/delete`).subscribe({
          next: () => this._toasterService.success('Card deleted successfully'),
          error: (error) => this._toasterService.error(error.error)
        });
      }
    }

    // Reset selection and close modal
    this.selectedCard.set(desfulatCardState);
    this.selectedCardId.set('');
    this.setCreateCards();
    this.cardsArrayEmpty.set(!(this.createdCards()?.length > 0));
    this.alertCardModal.removeModal();
  }


  removeCardFromSourseList(id: string) {
    const currentTab = this.dashboardBuilderStore.currentTab();
    if (currentTab && Array.isArray(currentTab.selectedSourceCards)) {
      currentTab.selectedSourceCards = currentTab.selectedSourceCards.filter(card => card.id !== id);
    }
  }


  // dxp function //
  getDxpDataset() {
    // this.subs.add(
    //   this._apiService.getMethodRequest(`${dxpApi.dataSet}${this.selectedProduct.sourceAssetId}/columns`).subscribe({
    //     next: (data) => {
    //       const dxpAssetColumnData = data.map(({ possible_aggregations, ...rest }: { possible_aggregations: any;[key: string]: any }) => ({ ...rest, ...possible_aggregations }));
    //     },
    //     error: error => {
    //       this._toasterService.error(error?.error?.message);
    //     }
    //   })
    // )
  }

  openViewSourceDataModal() {
    // Prefill the Source Data modal's filter input from the selected card's saved filters
    this.sourceDataFilters = this.selectedSourceFilter();
    this.viewSourceDataModal()?.createElement();
    this.getSampleSourceData();
  }

  onCloseSourceDataModal() {
    this.viewSourceDataModal()?.removeModal();
  }

  getSampleSourceData() {
    if (!this.selectedCard()) {
      return;
    }
    this.sourceTableLoader = true;
    this.sampleSourceDate.set([]);
    const filters = this.selectedSourceFilter();
    const conditions = (filters.filter ?? []).map(element => ({
      column: element.column.name,
      data_type: element.column.type,
      comparator: this.getComparatorKey(element.comparator),
      value: numericDataTypes.includes(element.column.type) ? +element.value : element.value
    }));

    const payload = {
      filters: {
        groups: [{ conditions, operator: filters.operator || 'and' }],
        global_operator: 'and',
      },
      source: this.selectedCard().source,
      source_type: this.selectedCard().source_type
    };

    this.setFilterInSelectedAccount(payload);
    this._apiService.postFormDataRequest(dashboardEndpoints.sourcePreview, payload).subscribe({
      next: data => {
        if (Array.isArray(data) && data.length) {
          this.sampleSourceDate.set(
            data.map(element => Object.keys(element).map(key => ({
              key,
              title: key,
              value: element[key],
              type: 'default'
            }))
            )
          );
          this.sampleSourceDataHead.set(Object.keys(data[0]));
        }
        this.sourceTableLoader = false;
      },
      error: err => {
        this.sourceTableLoader = false;
        this._toaster.error(err.error?.error || 'Preview failed');
      }
    });
  }


  setFilterInSelectedAccount(filter: any) {
    const cardIndex = this.createdCards().findIndex((x: { metadata: { cardId: any; }; }) => x.metadata.cardId == this.selectedCard().metadata['cardId'])
    this.createdCards()[cardIndex].config.filters = filter;
  }


  applySourceFilter(event: any) {
    this.selectedSourceFilter.set(event);
    this.getSampleSourceData();
    this.toolbarCmp.preview()
    // this.createCardAction({value: {}, type: dashboardConstants.cardPreview});
  }


  selectWidgetTemplate(event: KpiCardTemplate) {
    this.isKpiModalOpen = true;
    this.editKpiModal()?.createElement();
    // this.selectedKpiData = event;
    this.kpiIsEdit = false;
    this.selectedKpiData.set(event);
    if (this.selectedCard().source) {
      const idKey = this.selectedCard()?.['source_type'] == this.dataTypes.dxp ? 'asset_id' : (this.selectedCard()?.['source_type'] == this.dataTypes.dataset ? 'dataset_id' : 'indicator_id');
      const sourceObj = this.selectedCard().source as { [key: string]: string };
      const kpiPayload = [{
        cardId: sourceObj[idKey] || '',
        cardType: this.cardTypes.widget,
        cols: 3,
        rows: 11,
        id: sourceObj[idKey] || '',
        source: {
          dataset_id: this.selectedCard().source?.dataset_id || '',
          dataset_name: this.selectedCard().source?.dataset_name || '',
          product_id: this.selectedCard().source?.product_id || '',
          asset_id: this.selectedCard().source?.asset_id || '',
          product_name: this.selectedCard().source?.asset_name || ''
        },
        source_type: this.selectedCard().metadata['source_type'],
        sourceType: this.selectedCard().metadata['source_type'],
        subtitle: '',
        title: this.selectedCard()?.title || '',
        type: '',
        x: 0,
        y: 0,
        description: ''
      }]
      this.sourceModelType.set(cardTypes.widget);
      this.getSelectedIndicators(kpiPayload)
    }
  }

  getWidgetCardAggregation(card: Record<string, any> = {}) {
    const idKey = card?.['source_type'] == this.dataTypes.dxp ? 'asset_id' : (card?.['source_type'] == this.dataTypes.dataset ? 'dataset_id' : 'indicator_id');
    const sourceId = card?.['source'] && idKey in card?.['source'] ? (card?.['source'] as any)[idKey] : undefined;
    let payload = {};
    payload = {
      source: card?.['source'],
      source_type: card?.['source_type']
    };
    if (!sourceId) {
      return;
    }
    this.widgetAggregationLoader = true;
    this._apiService.postFormDataRequest(`${dashboardEndpoints.possibleAggregations}/possible-aggregations/`, payload).subscribe({
      next: columns => {
        // this.selectedKpiData['metadata'].possibleAggregations = columns;
        this.selectedKpiData.update((value) => {
          if (!value['metadata']) {
            value['metadata'] = {}
          }
          value['metadata'].possibleAggregations = columns;
          return value;
        });
        this.widgetAggregationLoader = false;
      },
      error: err => {
        this.widgetAggregationLoader = false;
        this._toaster.error(err.error.error || 'Failed to fetch aggregation columns');
      }
    });
  }

  setWidgetCardConfig(event: { payload: KpiCardTemplate | any; isExisting: boolean; }) {
    const cardDetail = event.payload;
    const actionEndpoint = dashboardEndpoints.cardPreview;

    // Build config and inject filters only if user selected/appplied in this modal
    let config = cardDetail.config['cardConfig'] ?? {};
    const srcFilters = cardDetail.currentSourceFilters ?? cardDetail.sourceFilters;
    const hasUsable = !!(srcFilters?.filter?.some((f: any) => f?.column?.name && (typeof f?.comparator === 'object' || !!f?.comparator) && f?.value !== '' && f?.value !== undefined));
    if (hasUsable) {
      const conditions = (srcFilters.filter ?? []).map((e: any) => ({
        column: e.column.name,
        data_type: e.column.type,
        comparator: this.getComparatorKey(e.comparator),
        value: numericDataTypes.includes(e.column.type) ? +e.value : e.value
      }));
      config = {
        ...config,
        filters: {
          groups: [{ conditions, operator: srcFilters.operator || 'and' }],
          global_operator: 'and'
        }
      };
    }

    const payload = {
      title: 'Untitled',
      type: this.cardTypes.widget,
      position: { x: cardDetail.x, y: cardDetail.y, w: cardDetail.cols, h: cardDetail.rows },
      source_type: cardDetail.source_type,
      source: cardDetail.source,
      config,
      metadata: {
        ...cardDetail.config, cardType: this.cardTypes.widget, source: cardDetail.source, source_type: cardDetail.source_type,
        possibleAggregations: cardDetail.metadata.possibleAggregations, cardId: cardDetail.metadata.cardId
      },
      dashboard_object_id: this.dashboardBuilderStore.getDashboardId() ?? '',
      page_object_id: this.dashboardBuilderStore.getSelectedPageId() ?? '',
      dashboard_type: this.isGovAffairs ? this.dashboardTypes.govAffairs : this.dashboardTypes.native
    };
    this._apiService.postFormDataRequest(actionEndpoint, payload).subscribe({
      next: resp => {
        resp.card_id = '';
        resp.source = cardDetail.source;
        // Persist filters into stored config
        resp.config = { ...cardDetail.config, cardConfig: payload.config };
        resp.position = payload.position;
        resp.source_type = payload.source_type;
        resp.possibleAggregations = payload.metadata.possibleAggregations;
        if (event.isExisting) {
          this.updateKpiWidgetDetails(resp);
        } else {
          this.createWidgetCard(resp);
        }
      },
      error: error => {
        this._toaster.error(error.error);
      }
    });
  }

  updateWidgetCard(event: KpiCardTemplate | any) {
    return new Promise((resolve) => {
      // Build config and inject filters only if user selected/applied in this modal
      let config = event.config?.['cardConfig'] ?? {};
      const srcFilters = event.currentSourceFilters ?? event.sourceFilters;
      const hasUsable = !!(srcFilters?.filter?.some((f: any) => f?.column?.name && (typeof f?.comparator === 'object' || !!f?.comparator) && f?.value !== '' && f?.value !== undefined));
      if (hasUsable) {
        const conditions = (srcFilters.filter ?? []).map((e: any) => ({
          column: e.column.name,
          data_type: e.column.type,
          comparator: this.getComparatorKey(e.comparator),
          value: numericDataTypes.includes(e.column.type) ? +e.value : e.value
        }));
        config = {
          ...config,
          filters: {
            groups: [{ conditions, operator: srcFilters.operator || 'and' }],
            global_operator: 'and'
          }
        };
      }

      const payload = {
        title: event.title,
        type: this.cardTypes.widget,
        position: { x: event.x, y: event.y, w: event.cols, h: event.rows },
        source_type: event.source_type,
        source: event.source,
        config,
        metadata: { ...event.config, cardType: this.cardTypes.widget, cardId: event.metadata.cardId, possibleAggregations: event.metadata.possibleAggregations },
        dashboard_object_id: this.dashboardBuilderStore.getDashboardId() ?? '',
        page_object_id: this.dashboardBuilderStore.getSelectedPageId() ?? '',
        dashboard_type: this.isGovAffairs ? this.dashboardTypes.govAffairs : this.dashboardTypes.native
      };
      this.subs.add(
        this._apiService.postFormDataRequest(`${dashboardEndpoints.cardPreview}`, payload).subscribe({
          next: next => {
            next.source_type = event.source_type;
            next.source = event.source;
            // Persist filters into stored config for future prefill in edit
            next.config = { ...event.config, cardConfig: config };
            resolve(next);
          }
        })
      );
    });
  }

  getUpdatedWidgetDetails(event: KpiCardTemplate | any) {
    this.updateWidgetCard(event).then((resp) => {
      this.updateKpiWidgetDetails(resp);
    });
  }

  updateKpiWidgetDetails(resp: any) {
    const filters = resp.config?.cardConfig?.filters;
    let widgetData = this.getWidgetData(resp);
    widgetData['config'] = {
      ...resp.config,
      ...(filters && { filters })
    },
      this.selectedCard.update(value => {
        if (value) {
          value.source_type = resp.source_type;
          value.source = resp.source;
          value.config = {
            ...resp.config,
            ...(filters && { filters })
          }
          value.metadata = {
            cardConfig: {
              customConfig: resp.metadata.customConfig,
              config: widgetData
            },
            source: resp.source,
            source_type: resp.source_type,
            possibleAggregations: resp.metadata.possibleAggregations,
            cardId: resp.metadata.cardId
          };
        }
        return value;
      });
    if (this.kpiIsEdit) {
      this._toasterService.success('KPI updated successfully');
    }
    this.updateCardData();
  }


  createWidgetCard(resp: any) {
    const filters = resp.config?.cardConfig?.filters;
    const widgetCardData = this.dashboardBuilderStore.selectCard(this.selectedTab().page_object_id, resp.card_id) as ChartCardConfig | undefined;
    const widgetData = this.getWidgetData(resp);
    const cardConfig = {
      dashboard_object_id: this.dashboardBuilderStore.getDashboardId() ?? '',
      page_object_id: this.dashboardBuilderStore.getSelectedPageId() ?? '',
      id: resp.card_id,
      title: 'Untitled',
      subtitle: '',
      description: '',
      type: this.cardTypes.widget,
      dashboard_type: this.isGovAffairs ? this.dashboardTypes.govAffairs : this.dashboardTypes.native,
      position: {
        x: widgetCardData?.position?.x ?? 0,
        y: widgetCardData?.position?.y ?? 0,
        w: widgetCardData?.position?.w ?? 3,
        h: widgetCardData?.position?.h ?? 4,
        positionId: resp.card_id // <-- Add positionId here
      },
      x: widgetCardData?.x ?? 0,
      y: widgetCardData?.y ?? 0,
      cols: widgetCardData?.cols ?? 3,
      rows: widgetCardData?.cols ?? 4,
      source_type: resp.source_type,
      source: resp.source,
      config: {
        ...resp.config,
        ...(filters && { filters })
      },
      metadata: {
        cardType: this.cardTypes.widget,
        cardId: this.log.createUUid,
        source_type: resp.source_type,
        source: resp.source,
        possibleAggregations: resp.possibleAggregations,
        cardConfig: {
          customConfig: resp.metadata.customConfig,
          config: widgetData
        }
      },
      page_title: '' // <-- Add the required property here
    };

    this.selectedCardId.set(cardConfig.metadata.cardId);
    this.selectedCard.set(cardConfig);
    this.dashboardBuilderStore.createChartCard(this.selectedTab().page_object_id, cardConfig.type, cardConfig);
    this.setCreateCards();
    this.checkLength();
  }


  uploadFile(file: FileData) {
    if (!file?.file?.[0]) {
      this._toasterService.error('No file selected');
      return;
    }

    const uploadData = this.createUploadFormData(file);
    this.uploadDataProgess.set(0);
    this.subs.add(
      this._prepService.getUploadData(uploadData).subscribe({
        next: data => {
          this.isUploadFail = false;
          this.handleUploadResponse(data);
        },
        error: err => {
          this._toasterService.error(err?.error?.non_field_errors[0] || 'Upload failed');
          this.uploadDataProgess.set(100);
          this.isUploadFail = true;
        }
      })
    );
  }

  private createUploadFormData(file: FileData): FormData {
    const uploadData = new FormData();
    const newFilename = this.generateUniqueFilename(file.file[0].name);

    uploadData.append('name', newFilename);
    uploadData.append('storage_backend', 's3');
    uploadData.append('file', file.file[0]);

    return uploadData;
  }

  private generateUniqueFilename(originalName: string): string {
    const lastDotIndex = originalName.lastIndexOf('.');
    const name = lastDotIndex > 0 ? originalName.substring(0, lastDotIndex) : originalName;
    const extension = lastDotIndex > 0 ? originalName.substring(lastDotIndex + 1) : '';
    const timestamp = new Date().toISOString().replace(/-/g, '');

    return extension ? `${name}.${extension}` : `${name}`;
  }

  private handleUploadResponse(data: any): void {
    if (data?.type === 1) {
      const percentage = +(((data?.loaded / data?.total) * 100).toFixed(0));
      this.uploadDataProgess.set(percentage <= 75 ? percentage : 75);
    }
    if (data?.type === 4 && data?.status === ApiStatus.created) {
      this.uploadedDataResponse.set(data.body);
      this.uploadDataProgess.set(100);
      this.addToSelectedSourceCards();
      this.uploadedDataResponse.update(resp => ({ ...resp, streamComplete: true }));
    }
    // Handle progress updates if needed (data?.type === 1)
  }

  private addToSelectedSourceCards(): void {
    const uploadedData = this.uploadedDataResponse();

    if (this.sourceModelType() === this.cardTypes.widget) {
      this.selectedKpiData.update(value => ({
        ...value,
        customConfig: {
          ...value['customConfig'],
          source: {
            dataset_id: uploadedData.id,
            dataset_name: uploadedData.name
          }
        },
        source_type: this.dataTypes.dataset
      }));
      this.addAllIndicators();
      this.closeImportDataModal();
      return;
    }


    // if (this.sourceModelType() == cardTypes.widget) {
    //   this.addAllIndicators();
    //   this.closeImportDataModal();
    //   return;
    // }


    const currentTab = this.dashboardBuilderStore.currentTab();
    if (!currentTab) {
      return;
    }

    const newCard: sourceCards = {
      cols: 3,
      id: uploadedData.id,
      rows: 11,
      sourceType: sourceTypes.dataset,
      title: uploadedData.name,
      type: uploadedData.storage_backend,
      x: 0,
      y: 0,
      subtitle: '',
      description: '',
      cardId: uploadedData.id,
      cardType: 'library',
      source: {
        dataset_id: uploadedData.id,
        dataset_name: uploadedData.name
      }
    };

    this.selectedSourcesCards.set([...this.selectedSourcesCards(), newCard]);
  }

  buttonActionClicked(status: string = this.statusParams.completed, isProceed: boolean = false) {
    this.tabChanging = false;
    this.checkConfigurations(status, false, isProceed);
  }


  checkConfigurations(status: string = this.statusParams.completed, createTab: boolean = false, isProceed: boolean = false): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.unConfiguredCards = this.getUnconfiguredCards();
      if (this.unConfiguredCards.length) {
        this.unConfiguredCardWarningModal()?.createElement();
        this.saveType = status;
        this.isCreateTab = createTab;
        return;
      }

      this.saveType = '';
      this.saveDashboard(status, isProceed)
        .then(() => resolve())
        .catch((error) => reject(error));
    });
  }

  getUnconfiguredCards() {
    return this.createdCards().filter((card: Record<string, any>) => {
      switch (card['type']) {
        case cardTypes.chart:
          return card['metadata'].y_axis_series.length === 0;
        case cardTypes.element:
          return card['metadata'].cardConfig.element_text === '' && !card['metadata'].cardConfig?.['element_style'];
        default:
          return false;
      }
    });
  }

  saveDashboard(status: string = this.statusParams.completed, isProceed: boolean = false): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if (this.unConfiguredCardWarningModal()) {
        this.createdCards.set(this.createdCards().filter((card: any) => {
          return !this.unConfiguredCards.some(unconfigured => {
            switch (card['type']) {
              case cardTypes.chart:
                return unconfigured['metadata'].y_axis_series === card['metadata'].y_axis_series;
              case cardTypes.element:
                return unconfigured['metadata'].cardConfig.element_text === card['metadata'].cardConfig.element_text && unconfigured['metadata'].cardConfig?.['element_style'] === card['metadata'].cardConfig?.['element_style'];
              default:
                return false;
            }
          });
        }
        ));
        this.unConfiguredCardWarningModal()?.removeModal();
      }
      this.unConfiguredCards = [];
      if (!this.tabChanging) {
        this.resetCardSelection();
        this.loaderModal.createElement();
        this.dashboardSavingState.set(true);
      }
      try {
        const dashboardConfig = this.dashboardBuilderStore.dashboardConfigState();
        const currentTabDetails = this.dashboardBuilderStore.currentTab();
        const dashboardPayload: any = {
          object_id: dashboardConfig?.dashboard_id ?? '',
          name: dashboardConfig?.name.trim() && dashboardConfig?.name.trim() != '' ? dashboardConfig?.name : 'Untitled',
          dashboard_type: this.isGovAffairs ? this.dashboardTypes.govAffairs : this.dashboardTypes.native,
          description: '',
          page: {
            object_id: currentTabDetails?.page_object_id ?? '',
            title: currentTabDetails?.page_title ?? 'Untiltled',
            config: {
              selectedSourceCards: currentTabDetails?.selectedSourceCards
            },
            cards: this.getCardDetails()
          }
        };

        const paylod: any = {
          data: dashboardPayload,
        };
        this.subs.add(
          this._apiService.postFormDataRequest(
            dashboardEndpoints.bulkSave,
            paylod
          ).subscribe({
            next: async next => {
              try {
                this.setDashboardAndPageId(next);
                const tabIndex = this.dashboardBuilderStore.dashboardConfigState().tabs.findIndex(x => x.page_object_id == next.page_object_id);
                if (tabIndex >= 0) {
                  const storeConfig = this.dashboardBuilderStore.dashboardConfigState();
                  this.dashboardConfig = {
                    ...storeConfig,
                    dashbaord_id: (storeConfig as any).dashboard_id ?? this.dashboardBuilderStore.getDashboardId() ?? '',
                    dashboard_type: this.isGovAffairs ? this.dashboardTypes.govAffairs : this.dashboardTypes.native
                  };
                  this.dashboardName = this.dashboardConfig.name ?? 'Untitled';
                  try {
                    await this.getTabDetail(next.page_object_id, tabIndex);
                    if (!this.tabChanging) {
                      setTimeout(async () => {
                        await this.updateDashboardStatus(status, isProceed);
                        this.loaderModal.removeModal();
                      }, 300);
                    } else {
                      this.loaderModal.removeModal();
                      this.tabChanging = false;
                      this.dashboardSavingState.set(false);
                    }

                    if (this.isCreateTab) {
                      this.createTabs();
                    }
                    // this._toaster.success('Dashboard Tab saved successfully');
                    resolve();
                  } catch (error) {
                    reject(error);
                  }
                } else {
                  reject(new Error('Tab not found after save'));
                }
              } catch (error) {
                reject(error);
              }
            },
            error: (error) => {
              this._toaster.error(error?.error?.error ?? 'Failed to save dashboard');
              this.dashboardSavingState.set(false);
              this.loaderModal.removeModal();
              reject(error);
            },
            complete: () => {
            }
          })
        );
      } catch (e) {
        this._toaster.error('Failed to generate dashboard thumbnails');
        this.loaderModal.removeModal();
        reject(e);
      }
    });
  }





  async updateDashboardStatus(status: string = this.statusParams.completed, isProceed: boolean = false) {
    if (!this.dashboardBuilderStore.getDashboardId()) {
      this._toaster.warning('At least configure one card and save the card, then try again');
      return;
    }

    // this.loaderModal.createElement();

    // Toggle theme for screenshot
    const themeClass = this.currentTheme() == 'light' ? 'ifp-dark-theme' : 'ifp-light-theme';
    const revertClass = this.currentTheme() == 'light' ? 'ifp-light-theme' : 'ifp-dark-theme';

    let newTheme = themeClass === 'ifp-dark-theme' ? 'dark' : 'light';

    this._themeService.defaultTheme = newTheme;
    this._themeService.defaultTheme$.next(newTheme);
    this._renderer.addClass(this.downloadPrint.nativeElement, 'ifp-db__wrapper--bg');
    this._renderer.removeClass(document.body, revertClass);
    this._renderer.addClass(document.body, themeClass);

    let isSuccess = false;
    try {
      let darkImageFile, lightImageFile;
      await new Promise(resolve => {
        setTimeout(resolve, 500);
      });
      const oppThemeImage = await this.canvasRender(this.downloadPrint.nativeElement);
      if (newTheme == 'light') {
        lightImageFile = oppThemeImage ? this.convertBase64ToFile(oppThemeImage, 'dashboard-light-thumbnail.png') : '';
      } else {
        darkImageFile = oppThemeImage ? this.convertBase64ToFile(oppThemeImage, 'dashboard-dark-thumbnail.png') : '';
      }

      // Revert theme for light screenshot

      newTheme = revertClass === 'ifp-dark-theme' ? 'dark' : 'light';
      this._themeService.defaultTheme = newTheme;
      this._themeService.defaultTheme$.next(newTheme);

      this._renderer.removeClass(this.downloadPrint.nativeElement, themeClass);
      this._renderer.removeClass(document.body, themeClass);
      this._renderer.addClass(document.body, revertClass);

      await new Promise(resolve => {
        setTimeout(resolve, 500);
      });
      const previewImage = await this.canvasRender(this.downloadPrint.nativeElement);
      if (newTheme == 'dark') {
        darkImageFile = previewImage ? this.convertBase64ToFile(previewImage, 'dashboard-dark-thumbnail.png') : '';
      } else {
        lightImageFile = previewImage ? this.convertBase64ToFile(previewImage, 'dashboard-light-thumbnail.png') : '';
      }

      this._renderer.removeClass(this.downloadPrint.nativeElement, 'ifp-db__wrapper--bg');

      const payload: any = {
        status: status,
        dashboard_type: this.isGovAffairs ? this.dashboardTypes.govAffairs : this.dashboardTypes.native,
        name: this.dashboardConfig.name || 'Untitled',
        light_thumbnail: lightImageFile,
        dark_thumbnail: darkImageFile,
        page_orders: this.getTabKeys()
      };

      if (this.dashboardConfig.logo != '' && this.dashboardConfig.logo != null && this.dashboardConfig.logo && !this.dashboardConfig.logo.includes('https')) {
        payload.logo = this.convertBase64ToFile(this.dashboardConfig.logo, 'latest-logo');
      }
      this.subs.add(
        this._apiService.putFormDataRequest(
          `${dashboardEndpoints.dashboard + this.dashboardBuilderStore.getDashboardId()}/update`,
          payload
        ).subscribe({
          next: next => {
            isSuccess = true;
            this.savedDashboardId = next.object_id;
            if (this.isGovAffairs) {
              if ((status !== this.statusParams.draft) || isProceed) {
                if (this.role === this.generalizedRoles.explorer) {
                  const queryParams = {
                    'government-affairs': true,
                    tab: mainTabParams.govMyDashboards,
                    status: statusParams.completed
                  };
                  this.router.navigate(['/dxp/dashboards'], { queryParams: queryParams });
                } else {
                  this.router.navigate([`/dxp/manage-access/dashboard/${this.savedDashboardId}`]);
                }
              }
            } else {
              if (status == this.statusParams.completed) {
                this._toaster.success(`Dashboard ${this.isEdit ? 'Updated' : 'Created'} Successfully`);
              }
              if (status !== this.statusParams.draft) {
                this.router.navigate(['/store/dashboards']);
              }
            }
          },
          error: (error) => {
            isSuccess = false;
            this._toaster.error(error?.error?.error ?? 'Failed to save dashboard');
            this.dashboardSavingState.set(false);
          },
          complete: () => {
            // this.loaderModal.removeModal();
            this.dashboardSavingState.set(false);
            if (status === statusParams.draft && isSuccess) {
              this._toaster.success('Dashboard Saved as Draft!');
            }
          }
        })
      );
    } catch (e) {
      this._toaster.error('Failed to generate dashboard thumbnails');
      this.loaderModal.removeModal();
    } finally {
      // Ensure theme is reverted back
      this._renderer.removeClass(document.body, themeClass);
      this._renderer.addClass(document.body, revertClass);
    }
  }

  getTabKeys() {
    const tabOrder: Record<string, number> = {};
    this.dashboardBuilderStore.getTabs().forEach((element: any, index: number) => {
      tabOrder[element.page_object_id] = index;
    });
    return tabOrder;
  }



  getCardDetails() {
    // const currenTabDetails = this.dashboardBuilderStore.currentTab();
    const cardPayload: { object_id: string; title: string; type: string; position: { x: number; y: number; w: number; h: number; positionId: string; }; config: Record<string, any>; metadata: Record<string, any>; source_type: string; source: { dataset_id?: string; dataset_name?: string; product_id?: string; asset_id?: string; description: string }; description: string; }[] = [];
    if (this.createdCards()?.length) {
      this.createdCards().forEach((element: {
        description: string; id: any; title: any; type: any; position: any; config: any; metadata: Record<string, any>; x: any; y: any; cols: any; rows: any; source_type: any; source: any;
      }) => {
        cardPayload.push({
          object_id: element.id ?? '',
          title: element.title && element.title !== '' ? element.title : 'untitled',
          description: element.description,
          type: element.type,
          position: element.position,
          config: element.config,
          metadata: { ...element.metadata, x: element.x, y: element.y, cols: element.cols, rows: element.rows, cardType: element.type },
          source_type: element.source_type,
          source: element.source
        });
      });
    }
    return cardPayload;
  }

  // onProceedToPublication() {
  //   if (!this.dashboardBuilderStore.getDashboardId()) {
  //     this._toaster.warning('At least configure one card and save the card, then try again');
  //     return;
  //   }
  //   this.updateDashboardStatus();
  //   // this.router.navigate(['/dxp/manage-access/dashboard/' + this.savedDashboardId]);
  // }

  changeTab(tabId: string, index: number, userClick: boolean = false, event?: any) {
    event?.stopPropagation();
    const tab = cloneDeep(this.dashboardBuilderStore.getTabById(this.dashboardBuilderStore.getSelectedPageId()));
    if (tab) {
      if (tabId != this.dashboardBuilderStore.getSelectedPageId() && tab && tab.cards && tab.cards.length > 0) {
        setTimeout(() => {
          // Return early to save the dashboard first, then continue with tab change after saving
          if (this.mode === dashboardActions.edit) {
            this.tabChanging = true;
            this.saveDashboard(this.statusParams.draft).then(() => {
              this.proceedWithTabChange(tabId, index, userClick);
            }).catch(() => {
              // If saving fails, still proceed with tab change
              this.proceedWithTabChange(tabId, index, userClick);
            });
          } else { // proceed with tab change without saving if dashboard is not in edit mode
            this.proceedWithTabChange(tabId, index, userClick);
          }
        }, 50);
        return; // Important: stop execution here
      } else if (tab.cards?.length <= 0 && this.dashboardBuilderStore.getTabs()?.length > 1 && userClick && this.dashboardBuilderStore.getSelectedPageId() && this.dashboardBuilderStore.getSelectedPageId() != '') {
        this._toaster.error('Please configure at least one card');
        return; // Important: stop execution here
      }
    }

    // Proceed with tab change if conditions above aren't met
    this.proceedWithTabChange(tabId, index, userClick);
  }

  /**
   * Handle the actual tab change logic after any necessary save operations
   */
  private proceedWithTabChange(tabId: string, index: number, userClick: boolean = false) {
    if (tabId == this.dashboardBuilderStore.getSelectedPageId() && userClick) {
      return;
    }
    this.resetCardSelection();
    this.expandOrCollapseToolbar(false)
    this.currentTabIndex = index;
    this.dashboardBuilderStore.setSelectedPage(tabId);
    const currentTab = this.dashboardBuilderStore.currentTab();
    if (currentTab) {
      this.selectedTab.set(currentTab);
    }
    this.createdCards.set(this.dashboardBuilderStore.currentTabCards());
    this.cardsArrayEmpty.set(!(this.createdCards()?.length > 0));
    if ((tabId || tabId != '') && this.dashboardBuilderStore.currentTabCards()?.length <= 0) {
      this.getTabDetail(tabId, index);
    } else {
      this.dashboardPageLoader = false;
    }
  }



  // dashboard detail start //

  getDashboradDetail() {
    this._apiService.getMethodRequest(`${dashboardEndpoints.dashboard + (this.dashboardId ?? '')}/`, this.selectTab).subscribe(resp => {
      this.dashboardConfig = { ...resp, logo: resp.icon_url };
      if (resp?.name) {
        this.setBreadcrumbs();
      }
      this.approvalRequestId = resp.approval_request_id;
      this.objectId = resp.object_id;
      this.dashboardType = resp.dashboard_type;
      this.dashboardStatus = resp.status;
      this.dataHistory = {
        shared_with_users: resp.shared_with_users,
        created_by: resp.created_by
      };
      // Set enableReview based on is_assigned field from API response
      this.enableReview.set(resp.is_assigned || false);
      this.enableExport = this.dashboardStatus === statusParams.completed && !resp.has_dxp_cards;
      // If dashboard is assigned, enable chat and fetch comments to show comment icon
      if (resp.is_assigned && this.approvalRequestId) {
        this.getComments();
      }

      this.dashboardConfig = {
        ...this.dashboardConfig, // keep defaults for missing props
        ...resp,
        dashbaord_id: this.dashboardId, // spread the API response
        tabs: (resp.pages || []).map((page: any) => ({
          ...page,
          page_object_id: page.object_id,
          page_title: page.title // rename
        })) // rename pages → tabs
      };
      this.dashboardName = this.dashboardConfig.name ?? 'Untitled';
      if (this.dashboardConfig?.tabs.length) {
        this.getTabDetail(this.dashboardConfig?.tabs[0].page_object_id, 0);
      }
    });
  }

  getTabDetail(id: string, index: number): Promise<void> {
    return new Promise((resolve, reject) => {
      // Cancel previous request and start new one
      this.tabDetailSubject.next({ id, index });

      // Store resolve/reject for the current request
      this.currentResolve = resolve;
      this.currentReject = reject;
    });
  }

  private currentResolve?: () => void;
  private currentReject?: (error: any) => void;

  private getTabDetailObservable(id: string, index: number) {
    this.dashboardPageLoader = true;

    return this._apiService.getMethodRequest(dashboardEndpoints.tab + id).pipe(
      switchMap(next => {
        // Initialize tab configuration
        const initialTab = cloneDeep(next);
        initialTab.cards = [];
        this.dashboardConfig.tabs[index] = {
          ...initialTab,
          page_object_id: initialTab.object_id,
          selectedSourceCards: initialTab.config.selectedSourceCards,
          page_title: initialTab.title
        };
        this.dashboardBuilderStore.updateDashboardConfigState(this.dashboardConfig);
        this.dashboardBuilderStore.setSelectedPage(this.dashboardConfig.tabs[index].page_object_id);
        this.selectedTab.set(this.dashboardConfig.tabs[index]);

        if (!next.cards?.length) {
          this.setCreateCards();
          this.checkLength();
          this.setGridsterOption();
          this.dashboardPageLoader = false;
          this.currentResolve?.();
          return EMPTY; // Complete the observable
        }

        // Create observables for card details
        const cardDetailObservables = next.cards.map((card: { object_id: string }) => this._apiService.getMethodRequest(dashboardEndpoints.cardDetail + card.object_id).pipe(
          catchError(error => {
            this._toaster.error(error.error);
            return of(null); // Return null for failed requests
          })
        )
        );

        return forkJoin<any[]>(cardDetailObservables).pipe(
          tap((cardDetails: any[]) => {
            // Process successfully fetched cards
            cardDetails
              .filter(cardDetail => cardDetail !== null)
              .forEach((cardDetail, cardIndex) => {
                cardDetail.dashboard_object_id = this.dashboardConfig.dashbaord_id;
                cardDetail.page_object_id = id;
                const updatedCard = this.transformCardDetail(cardDetail, next.cards[cardIndex].object_id);
                this.dashboardBuilderStore.createChartCard(
                  this.selectedTab().page_object_id,
                  updatedCard.type,
                  updatedCard
                );
              });

            // Update UI after all cards are processed
            this.reAssignAllCards();
            this.checkLength();
            this.setGridsterOption();

            // Post message after all operations are completed
            window.parent.postMessage({ type: 'dashboard', loaded: true }, '*');

            this.currentResolve?.();
          })
        );
      }),
      catchError(error => {
        this._toaster.error(error.error);
        this.currentReject?.(error);
        return EMPTY;
      }),
      finalize(() => {
        this.dashboardPageLoader = false;
      })
    );
  }

  reAssignAllCards() {
    this.createdCards.set(this.dashboardBuilderStore.currentTabCards());
  }

  private transformCardDetail(cardDetail: any, objectId: string): any {
    const { metadata = {}, position = {}, primary_kpi, secondary_kpi } = cardDetail;
    const cardType = metadata.cardType;

    const updatedCard: any = {
      ...cardDetail,
      x: this.isMobileView() ? 0 : metadata.x,
      y: this.isMobileView() ? 0 : metadata.y,
      cols: this.isMobileView() ? (cardType == this.cardTypes.widget ? 6 : 12) : metadata.cols,
      rows: metadata.rows,
      id: objectId,
      type: cardType,
      source_type: metadata.source_type,
      source: metadata.source
    };

    if (cardType === this.cardTypes.widget) {
      const widgetData = this.getWidgetData(cardDetail);
      updatedCard.metadata.cardConfig = {
        ...updatedCard.metadata.cardConfig,
        config: widgetData
      };
      updatedCard.metadata.possibleAggregations = metadata.possibleAggregations;
    }
    return updatedCard;
  }

  getWidgetData(data: any) {
    const widgetData: Record<string, any> = {
      label: data.primary_kpi.label,
      value: data.primary_kpi.value,
    };
    if (data.secondary_kpi) {
      widgetData['labelSecondary'] = data.secondary_kpi.label;
      widgetData['valueSecondary'] = data.secondary_kpi.value;
    }
    if (data.time_comparison) {
      const val = data.time_comparison?.percent_change ? +(data.time_comparison?.percent_change.toFixed(2)) : 'Nil';
      widgetData['compareValue'] = { ...widgetData['compareValue'], value: val };
    }
    return widgetData;
  }


  closeModal() {
    this.approverActionModal()?.removeModal();

    // if (this.currentStatus() === approveConst.approve) {
    //   this.redirectRoutes();
    // } else {
    //   this.approverActionModal()?.removeModal();
    // }
    // this._modelService.removeAllModal();
  }

  onSelectToolbarAction(action: string) {
    this.currentStatus.set(action);
    switch (action) {
      case approveConst.approve:
        this.currentStatusUpdate(approveConst.approve);
        break;
      case approveConst.reject:
        this.approvalDesc.set('Are You Sure You Want to Reject?');
        this.approvalTitle.set('Please confirm if you want to reject this dashboard? This action cannot be undone.');
        this.approvalIcon.set('ifp-icon ifp-icon-round-cross');
        this.enableCommentBox.set(true);
        this.approverActionModal()?.createElement();
        break;
      case approveConst.revert:
        this.approvalDesc.set('Are you sure you want to return this dashboard? This action cannot be undone.');
        this.approvalTitle.set('Are You Sure You Want to Return?');
        this.approvalIcon.set('ifp-icon ifp-icon-refresh');
        this.enableCommentBox.set(true);
        this.approverActionModal()?.createElement();
        break;
      default:
        break;
    }
  }

  approverPrimaryAction(status: string, comment: string = '') {
    if (status === approveConst.approve) {
      this.redirectRoutes();
    } else {
      this.currentStatusUpdate(status, comment);
    }
  }

  currentStatusUpdate(status: string, comment: string = '') {
    const payload = {
      action: status,
      ...(status === approveConst.approve && {
        object_id: this.objectId,
        approval_request_id: this.approvalRequestId
      }),
      comment: comment.trim(),
      title: this.dashboardConfig.name
    };
    this.subs.add(
      this._commonApiService.postMethodRequest(`${status === approveConst.approve ? dashboardEndpoints.manageApprovalRequest : dashboardEndpoints.manageActions}${status === approveConst.approve ? '' : `${this.approvalRequestId}/actions`}`, payload, undefined, status === approveConst.approve).subscribe({
        next: resp => {
          if (status == approveConst.approve) {
            this.getDashboradDetail();
            this.approvalDesc.set('The dashboard has been successfully approved.');
            this.approvalTitle.set('Approved');
            this.approvalIcon.set('ifp-icon ifp-icon-tick ');
            this.enableCommentBox.set(false);
            this.approverActionModal()?.createElement();
          }
          if (
            status !== approveConst.claim && status !== approveConst.unclaim && status !== approveConst.approve && status !== approveConst.reject && status !== approveConst.revert
          ) {
            this.addCommentApi(comment.trim());
          } else {
            this.getComments();
            // Redirect to DXP dashboard list with government affairs after approval actions
            if (status === approveConst.reject || status === approveConst.revert) {
              this._toasterService.success(status === approveConst.revert ? 'Reverted successfully' : 'Rejected successfully');
              this.redirectRoutes();
            }
          }
        },
        error: (error) => {
          this._toaster.error(error.error.message);
        }
      })
    );
  }

  redirectRoutes() {
    this.closeModal();
    // Navigate to respective sub-tabs under approval-status based on action
    let subTabStatus = '';
    let mainTab = 'approval-status';
    if (this.currentStatus() === approveConst.approve) {
      mainTab = 'dashboards';
      subTabStatus = 'dashboards';
    } else if (this.currentStatus() === approveConst.reject) {
      subTabStatus = 'rejected';
    } else if (this.currentStatus() === approveConst.revert) {
      subTabStatus = 'reverted';
    }
    this.router.navigate(['dxp/dashboards'], {
      queryParams: {
        'government-affairs': true,
        tab: mainTab,
        status: subTabStatus
      }
    });
  }

  addComment() {
    this.addCommentApi(this.newComment.trim());
    this.newComment = '';
  }

  // Call API to add a comment
  addCommentApi(data: string) {
    this.subs.add(
      this._commonApiService
        .postMethodRequest(
          dxpApi.getCommentList(this.approvalRequestId ?? ''),
          {
            content: data,
          }
        )
        .subscribe((data) => {
          this.getComments();
        })
    );
  }

  // Fetch comments from API
  getComments() {
    if (!this.approvalRequestId) {
      return;
    }
    this.subs.add(
      this._apiService.getMethodRequest(
        dxpApi.getCommentList(this.approvalRequestId ?? ''), '', false
      )
        .subscribe((data: DxpComments[]) => {
          this.comments.set(data);
          this.enableChat.set(true);
          this.hideSideBar.set(this.comments().length === 0);
          // need to redirect to the listing
        })
    );
  }

  selectRichTextCard(index: number) {
    if (this.mode === dashboardActions.edit) {
      this.selectTextCard(this.createdCards()[index]);
    }
  }

  selectTextCard(card: ChartCardConfig) {
    this.selectedCard.set(card);
    this.selectedCardId.set(card.metadata['cardId']);
    this._cdr.detectChanges();
  }

  resetCardSelection() {
    this.selectedCard.set(desfulatCardState);
    this.selectedCardId.set('not');
    this.cardEditMode = false;
  }

  checkDisableProceed(): boolean {
    const currentTab = this.dashboardBuilderStore.currentTab();
    return !currentTab?.page_object_id || currentTab.cards?.some(card => !card.id);
  }

  applaySelectCard() {
    const actionCard = this.createdCards().find((x: { metadata: { cardId: any; }; }) => x.metadata.cardId == this.selectedCard().metadata['cardId']);
    if (actionCard) {
      this.selectCard(actionCard.metadata.cardId, actionCard, null as any);
    }
  }

  saveTextCardConfig(event: { element_text: string, element_style: Record<string, string> }, card: any) {
    card.metadata.cardConfig['element_style'] = event.element_style;
    card.metadata.cardConfig['element_text'] = event.element_text;
  }

  checkAnyCardConfigured(): boolean {
    return !this.createdCards().some((card: any) => {
      if (card.type === this.cardTypes.text) {
        return card.metadata.cardConfig.element_text?.trim();
      }
      if (card.type === this.cardTypes.widget) {
        return true;
      }
      return card.metadata.y_axis_series?.length > 0;
    });
  }

  getCardHeight() {

    const dropDownEnabled = (this.selectedCard().config && (this.selectedCard().config['chart_type'] == this.chartTypes.pie) || (this.selectedCard().config['chart_type'] == this.chartTypes.dounght) && this.selectedCard().metadata) && this.selectedCard().metadata['y_axis_series'] && this.selectedCard().metadata['y_axis_series'].length > 1
    if (this.selectedCard()) {
      const cardIndex = this.createdCards().findIndex((x: { metadata: { cardId: any; }; }) => x.metadata.cardId == this.selectedCard().metadata['cardId']);
      if (cardIndex >= 0) {
        const chartHeight = this.gridsterItems.toArray()[cardIndex].height;
        this.createdCards()[cardIndex].metadata.cardHeight = chartHeight > 0 ? (chartHeight - (dropDownEnabled ? 250 : 150)) : 440;
      }
    }
  }

  updateCardAxisConfig(event: { value: any }) {
    const { xAxisLabel, xAxisValue, yAxisLabel, yAxisValue } = event.value;
    const getAxisConfig = (axis: any[]) => ({
      column: (axis ?? []).map(x => x.name).join(','),
      aggregator: (axis ?? []).map(x => x.dropdownValue).join(',')
    });
    this.selectedCard().config['x_axis'] = { label: xAxisLabel, axis: getAxisConfig(xAxisValue) },
      this.selectedCard().config['y_axis'] = { label: yAxisLabel, axis: getAxisConfig(yAxisValue) },
      this.dashboardBuilderStore.updateChartCard(
        this.selectedTab().page_object_id,
        this.selectedCardId(),
        this.selectedCard()
      );
  }

  setTabValue(event: any) {
    if (event.target.value === '') {
      event.target.value = this._translate.instant('Untitled');
    }
  }

  clearDefaultTabValue(event: any) {
    if (event.target.value === this._translate.instant('Untitled') && this.mode === dashboardActions.edit) {
      event.target.value = '';
      return;
    }
  }

  canDeactivate(): boolean {
    if (this.mode === dashboardActions.edit && (this.getUnconfiguredCards().length && this.checkAnyCardConfigured())) {
      return confirm(this._translate.instant(this.unconfiguredMessage));
    }
    return true;
  }

  getDynamicBackgroundHeight() {
    const gridsterItemsArray = this.gridsterItems.toArray();
    const maxY = gridsterItemsArray.reduce((max, card) => {
      return Math.max(max, card.item.y);
    }, 0);

    const maxYCards = gridsterItemsArray.filter(card => card.item.y === maxY);
    const maxRows = maxYCards.reduce((max, card) => {
      return Math.max(max, card.item.rows);
    }, 0);

    const maxRowsCard = maxYCards.find(card => card.item.rows === maxRows);
    if (maxRowsCard) {
      this.gridsterHeight = (maxRowsCard.height + maxRowsCard.top) + 400;
    }
  }


  updateText(event: { type: string, value: string }) {
    const field = event.type === 'title' ? 'title' : 'description';
    this.selectedCard.update((card) => {
      card[field] = event.value;
      return card;
    });
  }

  removeUploadedFile(file: File) {
    if (this.selectedSourcesCards()?.length) {
      const removeIndex = this.selectedSourcesCards().findIndex((card: sourceCards) => card.source.dataset_name === file.name && card.sourceType === dataTypes.dataset);
      if (removeIndex >= 0) {
        this.removeSelect(removeIndex);
      }
    }
  }


}

interface DataHistory {
  created_by?: { email: string };
  shared_with_users?: {
    email: string;
    id: number;
    name: string;
  }[];
}

interface IndicatorResponse {
  file: string,
  id: string,
  name: string,
  owner: string,
  storage_backend: string
  index: number;
  source_type: string;
  positionId: string;
  x?: number,
  y?: number,
  cols?: number,
  rows?: number
  label?: string,
  type?: string,
  cardConfig?: Record<string, any>
  source?: {},
  title?: string
}
