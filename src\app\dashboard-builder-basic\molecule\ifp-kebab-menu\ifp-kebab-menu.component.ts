import { Component, EventEmitter, HostListener, Input, Output } from '@angular/core';
import { kebabMenuOptions } from './ifp-kebab-menu.constants';
import { NgClass } from '@angular/common';
import { KebabMenuOption } from './ifp-kebab-menu.interface';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'ifp-kebab-menu',
    imports: [NgClass, TranslateModule],
    templateUrl: './ifp-kebab-menu.component.html',
    styleUrl: './ifp-kebab-menu.component.scss'
})
export class IfpKebabMenuComponent {

  @HostListener('window:pointerup', ['$event.target'])
  onOutsideClick(_target: HTMLElement) {
    this.showDropdown = false;
  }

  @Input() options: KebabMenuOption[] = kebabMenuOptions;
  @Input() position: 'left' | 'right' = 'left';
  @Input() verticalPosition: 'top' | 'bottom' = 'bottom';
  @Output() optionSelected: EventEmitter<string> = new EventEmitter<string>();

  public showDropdown: boolean = false;

  onOptionSelect(event: string) {
    this.optionSelected.emit(event);
    this.showDropdown = false;
  }
}
