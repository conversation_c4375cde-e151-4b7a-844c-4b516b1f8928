import { cloneDeep } from 'lodash';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IfpDropdownComponent } from '../../ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { IfpButtonComponent } from '../../ifp-atoms/ifp-button/ifp-button.component';
import { IfpWhatsNewCardComponent } from '../../ifp-molecules/ifp-whats-new-card/ifp-whats-new-card.component';
import { Store } from '@ngrx/store';
import { loadscreenterFilter } from 'src/app/scad-insights/store/filter-store/screener-filter.action';
import { selectScreenerFilterGetById } from 'src/app/scad-insights/store/filter-store/screener-filter.selector';
import { SubSink } from 'subsink';
import { RemoveUnderscorePipe } from 'src/app/scad-insights/core/pipes/underscoreRemove.pipe';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { PaginationComponent } from '../../ifp-molecules/pagination/pagination.component';
import { ScreenerService } from 'src/app/scad-insights/core/services/screener/screener.service';
import { IfpCardLoaderComponent } from '../../ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { IfpNoDataComponent } from '../../ifp-molecules/ifp-no-data/ifp-no-data.component';
import { getIndicator } from 'src/app/scad-insights/store/incidicator-list/incidicator-list.action';
import { selectIndicatorGetById } from 'src/app/scad-insights/store/incidicator-list/incidicator-list.selector';
import { distinctUntilChanged } from 'rxjs';
import { Router } from '@angular/router';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { analyticsClasses } from 'src/app/scad-insights/core/constants/analytics-class.constants';
import { indicatorType } from 'src/app/scad-insights/core/constants/contentType.constants';
import { chartConstants } from 'src/app/scad-insights/core/constants/chart.constants';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';

@Component({
    selector: 'app-ifp-screener',
    imports: [CommonModule, IfpDropdownComponent, IfpButtonComponent, IfpWhatsNewCardComponent,
    ReactiveFormsModule, PaginationComponent, IfpCardLoaderComponent, IfpNoDataComponent, IfpTooltipDirective,
    TranslateModule],
    templateUrl: './ifp-screener.component.html',
    styleUrls: ['./ifp-screener.component.scss'],
    providers: [RemoveUnderscorePipe],
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class IfpScreenerComponent implements OnInit, OnDestroy, OnChanges {
  @Input() indicator: any;
  @Input() addMyApps!: boolean;
  @Input() innovative = true;
  @Input() domainId: any = '';
  @Input() screenerSettings: any;
  @Input() selectedTheme: any = {};
  @Output() myApps = new EventEmitter();
  public enableCompare: any = [];
  public location = ['abu dhabi'];
  public subs = new SubSink();
  public filters: { name: string, items: { name: string, value: string, disabled?: boolean }[], key: string, default: { name: string, value: string }, selectAll: boolean }[] = [];
  public form = new FormGroup({});
  public filteredValue: any[] = [];
  public offsetPage = 1;
  public offset = 1;
  public limit = 10;
  public total = 0;
  public loader = true;
  public extraFilters: any = {};
  public indicatorType!: string;
  public viewName = '';
  public filterSubscription: any;
  public filterLoader = true;
  public clearDisable = true;
  public analyticsClasses = analyticsClasses;
  public contentType = indicatorType;
  public indicatorDetails = {
    topic: '',
    theme: '',
    subtheme: '',
    product: '',
    viewName: ''
  };

  @Input() domain!: string;
  public currentData: any = {
    viewName: '',
    filters: {
    },
    sortBy: {
      'byValue': 'desc'
    }
  };

  public sortValue = {
    label: 'A-Z',
    value: 'asc',
    key: 'alphabetical'
  };

  public sort = [
    {
      label: this._translate.instant('By Value Desc'),
      value: 'desc',
      key: 'byValue',
      icon: 'ifp-rotate-90 ifp-icon-rightarrow'
    },
    {
      label: this._translate.instant('By Value Asc'),
      value: 'asc',
      key: 'byValue',
      icon: 'ifp-rotate-270 ifp-icon-rightarrow'
    },
    {
      label: this._translate.instant('A-Z'),
      value: 'asc',
      key: 'alphabetical',
      icon: 'ifp-rotate-270 ifp-icon-rightarrow'
    },
    {
      label: this._translate.instant('Z-A'),
      value: 'desc',
      key: 'alphabetical',
      icon: 'ifp-rotate-90 ifp-icon-rightarrow'
    }
    // {
    //   label: 'By Percentage Asc',
    //   value: 'asc',
    //   key: 'byChange',
    //   icon: 'ifp-rotate-270 ifp-icon-rightarrow'
    // },
    // {
    //   label: 'By Percentage Desc',
    //   value: 'desc',
    //   key: 'byChange',
    //   icon: 'ifp-rotate-90 ifp-icon-rightarrow'
    // }
  ];

  public cardData: any[] = [];
  public buttonClass = buttonClass;


  constructor(private store: Store, private _screenerService: ScreenerService, private _cdr: ChangeDetectorRef, private _route: Router, private _translate: TranslateService, private _themeServise:ThemeService) {

  }

  sortClick(event: any) {
    this.sortValue = event;
    this.currentData.sortBy = {
      [this.sortValue.key]: this.sortValue.value
    };
    this.offsetPage = 1;
    this.offset = 1;
    this.callScreenerData();
    this._cdr.detectChanges();
  }

  myappsEvent(event: any) {
    this.myApps.emit(event);
  }


  callApiCalls() {
    this.extraFilters = {};
    this.loader = true;
    this.filterLoader = true;
    if (this.screenerSettings) {
      this.filters = [];
      this.indicatorDetails = {
        topic: this.screenerSettings?.domain,
        theme: this.screenerSettings?.subdomain,
        subtheme: this.screenerSettings?.subtheme,
        product: this.screenerSettings?.product,
        viewName: this.screenerSettings?.screenerView
      };
      this.viewName = this.screenerSettings.screenerView;
      this.currentData.viewName = this.screenerSettings.screenerView;
      this.extraFilters = this.screenerSettings.screenerFilterBy ? this.screenerSettings.screenerFilterBy : {};
      this.indicatorType = this.screenerSettings.screenerIndicator;
      this.store.dispatch(loadscreenterFilter({ contentType: this.innovative ? 'innovative-insights' : 'official-insights', id: this.indicatorType }));
      this.filterCall();
    }
    if (this.indicator && this.indicator?.length !== 0) {

      this.store.dispatch(getIndicator({
        id: this.indicator[0]?.id,
        contentType: this.indicator[0]?.content_type,
        overview: false,
        visa: false,
        screener: true
      }));
      this.subs.add(
        this.store.select(selectIndicatorGetById(this.indicator[0].id))
          .pipe(distinctUntilChanged((prev, curr) => prev.loader === curr.loader))
          .subscribe((data) => {
            if (data.status) {
              this.filters = [];
              this.indicatorDetails = {
                topic: data.body.domain,
                theme: data.body.theme,
                subtheme: data.body.subtheme,
                product: data.body.product,
                viewName: data.body.screenerView
              };
              this.viewName = data?.body?.screenerView;
              this.currentData.viewName = data?.body?.screenerView;
              this.extraFilters = data?.body?.screenerFilterBy ? data?.body?.screenerFilterBy : {};
              this.indicatorType = data?.body?.screenerIndicator;
              this.store.dispatch(loadscreenterFilter({ contentType: this.innovative ? 'innovative-insights' : 'official-insights', id: data.body.screenerIndicator }));
              this.filterCall();
            }
          })
      );

    }


  }

  filterCall() {
    this.subs.add(
      this.store.select(selectScreenerFilterGetById(this.indicatorType)).subscribe((data: any) => {
        if (data.status) {
          this.filters = cloneDeep(data.body);
          this.setFilter();
        }
      }));
  }

  ngOnInit(): void {
    this.callApiCalls();
  }

  setFilter() {
    this.form = new FormGroup({});
    this.filteredValue = [];
    this.filters.forEach((values) => {
      values['selectAll'] = ((this.domain == chartConstants.CENSUS_EN || this.domain == chartConstants.CENSUS_AR) && (values?.key == 'REGION_EN' || values?.key == 'REGION_AR')) ? true : false;
      const valueData = values.default ? [values.default['value']] : [];
      if ((this.domain == chartConstants.CENSUS_EN || this.domain == chartConstants.CENSUS_AR) && (values?.key == 'REGION_EN' || values?.key == 'REGION_AR')) {
        values.items.forEach((data) => {
          valueData.push(data.value);
        });
      }
      this.form.addControl(values.key, new FormControl(valueData));
      this.filteredValue.push({ value: valueData, key: values.key });
    });
    console.log("this.extraFilters", this.extraFilters)
    for (const key in this.extraFilters) {
      if (Object.prototype.hasOwnProperty.call(this.extraFilters, key)) {
        this.form.addControl(key, new FormControl(this.extraFilters[key]));
      }
    }
    this.currentData.filters = this.form.value;
    this.filterLoader = false;
    this._cdr.detectChanges();
    this.callScreenerData();
  }

  ngOnChanges(): void {
    this.callApiCalls();
  }

  onChange(click: string, valueChanged: { name: string, items: { name: string, value: string }[], key: string, default: { name: string, value: string }, selectAll: boolean }) {
    // const value: any = this.form.value;
    // const control: any =  this.form.controls;
    // if (click ==='All') {
    //   this.form.patchValue({[valueChanged]: [click]});
    // } else if (value[valueChanged].includes('All') ) {
    //   const array = value[valueChanged];p
    //   const indexArray =  array.indexOf('All');
    //   if (indexArray > -1) {
    //     array.splice(indexArray, 1);
    //   }
    //   control[valueChanged].setValue(array);
    // } else if (value[valueChanged].length === 0) {
    //   this.form.patchValue({[valueChanged]: [defaultValue]});
    // }
    // this.filteredValue = [];
    // for (const key in this.form.value) {
    //   if (Object.prototype.hasOwnProperty.call(this.form.value, key)) {
    //     const element:any = value[key];
    //     element.forEach((values: string) => {
    //       this.filteredValue.push({value: values, key: key});
    //     });
    //   }
    // }
    const formValue: any = this.form.value;
    if (formValue[valueChanged.key].length === valueChanged.items.length) {
      valueChanged.selectAll = true;
    } else {
      valueChanged.selectAll = false;
    }
    this.clearButton();
    if (formValue[valueChanged.key]?.length === 0) {
      this.form.patchValue({ [valueChanged.key]: [valueChanged.default['value']] });
    }
    const domainKey = this._themeServise.defaultLang == 'en' ? 'Census' : 'التعداد';
    if (this.domain == domainKey && this.selectedTheme?.name == 'Population') {
      this.disableSomeoptions();
    }

    this.currentData.filters = this.form.value;
    this.offset = 1;
    this.callScreenerData();
    this._cdr.detectChanges();
  }

  disableSomeoptions() {
    const filters: any = this.form.value;
    const ageKey = this._themeServise.defaultLang == 'en' ? 'AGE_GROUP_EN' : 'AGE_GROUP_AR';
    const maritialKey = this._themeServise.defaultLang == 'en' ? 'MARITAL_STATUS_EN' : 'MARITAL_STATUS_AR';
    this.filters.forEach(element => {
      if (element.key == ageKey) {
        if (element.items?.length > 0) {
          element.items.forEach(opts => {
            opts.disabled = filters[maritialKey].some((x: string) => x != 'ALL') ? this.checkMinorflag(opts.value) : false;
          });
        }
      }

      if (element.key == maritialKey) {
        if (element.items?.length > 0) {
          element.items.forEach(opts => {
            opts.disabled = (filters[ageKey].some((x: string) => this.checkMinorflag(x)) == true) ? (opts.value != 'ALL' ? true : false) : false;
          });
        }
      }

    });
  }

  clearButton() {
    const dataValues: any = this.form.value;
    const clearIndex = this.filters.findIndex((values) => {
      if (!dataValues[values.key].includes(values.default.value)) {
        if (dataValues[values.key].length >= 0) {
          return true;
        }
      }
      if (dataValues[values.key].length > 1) {
        return true;
      }

      return false;
    });
    if (clearIndex !== -1) {
      this.clearDisable = false;
    } else {
      this.clearDisable = true;
    }
  }

  callScreenerData() {
    this.filterSubscription?.unsubscribe();
    this.loader = true;
    this._cdr.detectChanges();
    this.filterSubscription = this._screenerService.screener(this.currentData, this.limit, this.offset, this.innovative).subscribe((screener: any) => {
      this.cardData = screener?.data;
      this.loader = false;

      this.filterLoader = false;
      this.total = screener?.totalCount;
      this._cdr.detectChanges();
    });

  }

  onPageChange(event: any) {
    this.offsetPage = event + 1;
    this.offset = (event / this.limit) + 1;
    this.callScreenerData();

  }

  limitChanged(event: any) {
    this.offsetPage = 1;
    this.offset = 1;
    this.limit = event;
  }

  reset() {
    this.offsetPage = 1;
    this.offset = 1;
    this.setFilter();
    this.clearButton();
    const domainKey = this._themeServise.defaultLang == 'en' ? 'Census' : 'التعداد';
    if (this.domain == domainKey && this.selectedTheme?.name == 'Population') {
      this.disableSomeoptions();
    }
  }

  selectAll(selection: boolean, item: { name: string, items: { name: string, value: string }[], key: string, default: { name: string, value: string }, selectAll: boolean }) {
    item.selectAll = selection;
    const domainKey = this._themeServise.defaultLang == 'en' ? 'Census' : 'التعداد';
    if (this.domain == domainKey && this.selectedTheme?.name == 'Population') {
      this.disableSomeoptions();
    }
    if (selection) {
      this.currentData.filters = this.form.value;
      this.offsetPage = 1;
      this.offset = 1;
      this.callScreenerData();
    } else {
      this.form.patchValue({ [item.key]: [item.default['value']] });
      this.currentData.filters = this.form.value;
      this.callScreenerData();
    }
    this.clearButton();
  }

  remove(data: { value: string, key: any }, index: number) {
    const formValue: any = this.form.controls;
    const array = formValue[data.key].value;
    const indexArray = array.indexOf(data.value[0]);
    if (indexArray > -1) {
      array.splice(indexArray, 1);
    }

    this.filteredValue.splice(index, 1);
    formValue[data.key].setValue(array);
    this._cdr.detectChanges();
    this.currentData.filters = this.form.value;
    this.offset = 1;
    this.callScreenerData();

  }

  emitCompare(event: any, item: any) {
    // const data = {
    //   event: event.flag,
    //   contentType: event.contentType,
    //   id: item.indicatorId
    // };
    // this.compareChecked.emit(data);
    if (event.flag) {
      const data = {
        id: item.indicatorId,
        contentType: event.contentType,
        viewName: this.viewName
      };
      this.enableCompare.push(data);
    }
    if (!event.flag) {
      const index = this.enableCompare.findIndex((x: any) => x.id == event.id);
      this.enableCompare.splice(index, 1);
    }

    this.enableCompare = cloneDeep(this.enableCompare);
  }

  compare() {
    this._route.navigate(['compare-chart'], { queryParams: { compareIds: JSON.stringify(this.enableCompare), prevUrl: this._route.url, domain: this.domain }, skipLocationChange: true });
  }

  checkLength() {
    return this.enableCompare.length > 1;
  }

  checkMinorflag(option: string) {
    const ageRegex = /(\d{1,2})\D+(\d{1,2})/;
    const matches = option.match(ageRegex);
    if (matches) {
      const age1 = parseInt(matches[matches.length - 1]);
      const age2 = parseInt(matches[matches.length - 2]);
      return age1 < 15 || age2 < 15;
    }
    return false;
  }

  ngOnDestroy(): void {
    this.filterSubscription?.unsubscribe();
    this.subs.unsubscribe();
  }

}
