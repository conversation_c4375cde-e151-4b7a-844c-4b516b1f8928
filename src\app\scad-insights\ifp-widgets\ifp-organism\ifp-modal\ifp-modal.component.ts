import { Component,  EventEmitter,  Inject, Input, OnDestroy, Output, Renderer2, TemplateRef, ViewChild, ViewContainerRef} from '@angular/core';
import { CommonModule, DOCUMENT } from '@angular/common';
import { IfpModalTemplateComponent } from './ifp-modal-template/ifp-modal-template.component';
import { IfpModalService } from './ifp-modal.service';

@Component({
    selector: 'app-ifp-modal',
    imports: [CommonModule],
    templateUrl: './ifp-modal.component.html',
    styleUrls: ['./ifp-modal.component.scss']
})
export class IfpModalComponent implements OnDestroy{
  @ViewChild('template',  { read: TemplateRef }) template!: TemplateRef<any>;

  constructor(private _render: Renderer2,
    @Inject(DOCUMENT) private document: Document, private _vRef:ViewContainerRef,
    private _modalService: IfpModalService
  ) {
  }

  @Input() enableOverlay: boolean = true;
  @Input() modalClass = 'ifp-modal__template';
  @Input() modalModifier = '';
  @Input() overlayClass!:string;
  @Input() overlayType: 'blur' | 'transparent' = 'blur';
  @Input() overlayIndex!: number;
  @Input() zIndex!: number;
  @Input() isClose: boolean = false;
  @Output() closeModal = new EventEmitter();

  public currentElement!: Element;
  public overlayElement!: Element;
  createElement() {
    const container = document.fullscreenElement ? document.fullscreenElement : this.document.body;
    if (this.enableOverlay) {
      this.overlayElement =   this._render.createElement('div');
      if (this.overlayIndex) {
        this._render.setStyle(this.overlayElement, 'z-index', this.overlayIndex);
      }
      this._render.addClass(this.overlayElement, 'ifp-modal__overlay');
      if (this.overlayClass) {
        this._render.addClass(this.overlayElement, this.overlayClass);
      }
      if (this.overlayType === 'transparent') {
        this._render.addClass(this.overlayElement, 'ifp-modal__overlay--transparent');
      }
      this._render.addClass(container, 'ifp-modal-show');
      this._render.appendChild(container, this.overlayElement);

    }
    this.currentElement =   this._render.createElement('div');
    this._vRef.clear();
    const componentRef = this._vRef.createComponent(IfpModalTemplateComponent);
    componentRef.instance.template = this.template;
    this._render.addClass(componentRef.location.nativeElement, this.modalClass);
    this._render.appendChild(this.currentElement, componentRef.location.nativeElement);
    this._render.addClass(  this.currentElement, 'ifp-modal');
    if (this.zIndex) {
      this._render.setStyle(this.currentElement, 'z-index', this.zIndex);
    }
    if (this.modalModifier.length) {
      this._render.addClass(  this.currentElement, this.modalModifier);
    }
    this._render.appendChild(container, this.currentElement);
    const modalElement = this._modalService.modalElement();
    modalElement.push(this.currentElement);
    modalElement.push(this.overlayElement);
    this._modalService.modalElement.set(modalElement);
  }

  removeModal(preventOverlay = false) {
    const container = document.fullscreenElement ? document.fullscreenElement : this.document.body;
    if (this.currentElement) {
      this._render.removeChild(container, this.currentElement);

      if (this.enableOverlay && !preventOverlay) {
        this._render.removeChild(container, this.overlayElement);
        this._render.removeClass(container, 'ifp-modal-show');
      }
    }
  }

  closeModalEvent() {
    this.closeModal.emit();
  }

  ngOnDestroy(): void {
    const container = document.fullscreenElement ? document.fullscreenElement : this.document.body;
    if (this.overlayElement) {
      this._render.removeChild(container, this.overlayElement);
      this._render.removeClass(container, 'ifp-modal-show');
    }
  }

}
