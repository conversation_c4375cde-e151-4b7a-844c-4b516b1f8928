@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-access-list {
  position: relative;
  &__selected {
    font-size: $ifp-fs-3;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 5px;
    padding: $spacer-3;
    cursor: pointer;
  }
  &__box {
    width: 100%;
    min-width: 230px;
    border: 1px solid $ifp-color-grey-7;
    box-shadow: 0 0 6px $ifp-color-black-32;
    position: absolute;
    top: 100%;
    margin-top: $spacer-2;
    visibility: hidden;
    transform: scaleY(0);
    transform-origin: top;
    transition: 0.3s;
    background-color: $ifp-color-white;
    border-radius: 6px;
    overflow: hidden;
    z-index: 1;
    &--body {
      position: fixed;
    }
    &--expanded {
      visibility: visible;
      transform: scaleY(1);
      z-index: 1;
    }
  }
  &__item {
    padding: $spacer-3;
    border-bottom: 1px solid $ifp-color-grey-7;
    cursor: pointer;
    &:last-child {
      border: none;
    }
    &--selected {
      background-color: $ifp-color-grey-4;
    }
    &--disabled {
      pointer-events: none;
      &::ng-deep {
        .ifp-checkbox:checked + .ifp-checkbox__label::before {
          background-color: transparent;
        }
      }
    }
  }
  &__inner {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
  }
  &__dot {
    display: inline-block;
    width: 10px;
    min-width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-inline-end: $spacer-2;
  }
  &__note {
    margin-top: $spacer-1;
    color: $ifp-color-tertiary-text;
    font-size: $ifp-fs-2;
    margin-inline-start: $spacer-3 + 2px;
  }
  &__item-name {
    display: flex;
    align-items: center;
    font-weight: $fw-medium;
  }
  &__item-name-grey {
     color: $ifp-color-grey-6;
     font-weight: $fw-regular;
  }
  &--inline {
    .ifp-access-list {
      &__selected {
        padding: $spacer-0;
        border: none;
      }
    }
  }
  &--view {
    .ifp-access-list {
      &__selected {
        pointer-events: none;
        .ifp-icon {
          display: none;
        }
      }
    }
  }
}

:host {
  display: block;
  &::ng-deep {
    .ifp-checkbox__label {
      padding-inline-start: 20px !important;
    }
    .ifp-access-list__item--disabled {
      .ifp-checkbox__label::before {
        background-color: transparent !important;
      }
    }
  }
}
