@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-access-list {
  position: relative;
  &__selected {
    font-size: $ifp-fs-3;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 5px;
    padding: $spacer-3;
    cursor: pointer;
  }
  &__box {
    width: 100%;
    min-width: 230px;
    border: 1px solid $ifp-color-grey-7;
    box-shadow: 0 32px 96.14px -13px $ifp-color-black-08;
    position: absolute;
    top: 100%;
    margin-top: $spacer-2;
    visibility: hidden;
    transform: scaleY(0);
    transform-origin: top;
    transition: 0.3s;
    background-color: $ifp-color-white;
    border-radius: 6px;
    overflow: hidden;
    z-index: 1;
    &--body {
      position: fixed;
    }
    &--expanded {
      visibility: visible;
      transform: scaleY(1);
      z-index: 1;
    }
  }
  &__item {
    padding: $spacer-3;
    border-bottom: 1px solid $ifp-color-grey-7;
    cursor: pointer;
    &:last-child {
      border: none;
    }
    &--clear {
      text-align: center;
      cursor: pointer;
      .ifp-access-list__item-outer {
        color: $ifp-gen-pills-blue;
      }
    }
    &--clear-disabled {
      opacity: 0.5;
      pointer-events: none;
    }
    &--selected {
      background-color: $ifp-color-grey-4;
    }
    &--disabled {
      pointer-events: none;
      &::ng-deep {
        .ifp-checkbox:checked + .ifp-checkbox__label::before {
          background-color: transparent;
        }
      }
    }
  }
  &__inner {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
  }
  &__dot {
    display: inline-block;
    width: 10px;
    min-width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-inline-end: $spacer-2;
  }
  &__bgSelected {
    background-color: #e0f2fe; /* light blue */
    border-left: 3px solid #0284c7; /* blue indicator */
  }
  &__note {
    margin-top: $spacer-1;
    color: $ifp-color-tertiary-text;
    font-size: $ifp-fs-2;
    margin-inline-start: $spacer-3 + 2px;
  }
  &__item-name {
    display: flex;
    align-items: center;
    font-weight: $fw-medium;
  }
  &__item-name-grey {
     color: $ifp-color-grey-6;
     font-weight: $fw-regular;
  }
  &__select-tick {
    color: $ifp-gen-pills-blue;
    opacity: 0;
    visibility: hidden;
    transition: 0.3s;
  }
  &__item-label {
    color: $ifp-color-secondary-grey;
  }
  &__desc {
    color: $ifp-color-grey-14;
    margin-top: $spacer-1;
  }
  &__selected-right {
    display: flex;
    align-items: center;
  }
  &__clear {
    font-size: 0.8em;
    margin-inline: $spacer-2 $spacer-3;
    opacity: 0.7;
    cursor: pointer;
    transition: 0.3s;
    &:hover {
      opacity: 1;
    }
  }
  &--inline {
    .ifp-access-list {
      &__selected {
        padding: $spacer-0;
        border: none;
      }
    }
  }
  &--single {
    .ifp-access-list {
      &__box {
        border-radius: 15px;
        padding: $spacer-2 + 2px;
        border: 1px solid $ifp-color-pale-grey;
      }
      &__inner {
        display: block;
      }
      &__item-name {
        display: flex;
        justify-content: space-between;
      }
      &__item-outer {
        padding: ($spacer-2 + 2px) $spacer-0;
        border-bottom: 1px solid $ifp-color-pale-grey;
      }
      &__item {
        padding: $spacer-0 ($spacer-2 + 2px);
        border: none;
        border-radius: 9px;
        overflow: hidden;
        &--clear {
          padding-top: $spacer-2;
        }
        &:last-child {
          .ifp-access-list__item-outer {
            border: none;
          }
        }
        &--active {
          background-color: $ifp-color-blue-1;
          .ifp-access-list__select-tick {
            opacity: 1;
            visibility: visible;
          }
        }
      }
    }
  }
  &--view {
    .ifp-access-list {
      &__selected {
        pointer-events: none;
        .ifp-icon {
          display: none;
        }
      }
    }
  }
}

:host {
  display: block;
  &::ng-deep {
    .ifp-checkbox__label {
      padding-inline-start: 20px !important;
    }
    .ifp-access-list__item--disabled {
      .ifp-checkbox__label::before {
        background-color: transparent !important;
      }
    }
  }
}
