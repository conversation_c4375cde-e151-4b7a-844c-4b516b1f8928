<div class="ifp-tnc-modal">
  <em class="ifp-icon ifp-icon-round-cross ifp-tnc-modal__close" (click)="onButtonClick(false)"></em>
  <!-- <ng-container *ngIf="termData">
    <h3 class="ifp-tnc-modal__head">{{(termData?.title ? termData?.title : '') | translate}}</h3>
    <ul ngbNav #tabs #nav="ngbNav" [(activeId)]="active" [destroyOnHide]="false" class="nav-tabs">
      <li #navItem [ngbNavItem]="i" [destroyOnHide]="true" *ngFor="let section of termData?.sections; index as i">
        <button ngbNavLink [disabled]="!isAccepted" class="ifp-tnc-modal__tab-item" [ngClass]="{'ifp-tnc-modal__tab-item--disabled' : !isTabComplete ?  i > active+1 : false}" (click)="getIndex(i)">{{section.sectionTitle | translate}}</button>
        <ng-template ngbNavContent>
          <div class="ifp-tnc-modal__content" *ngFor="let subsection of section.subsections">
            <h3 class="ifp-tnc-modal__sub-title">{{(subsection.subsectionTitle ? subsection.subsectionTitle : '') | translate}}</h3>
            <p class="ifp-tnc-modal__desc" [innerHTML]="(subsection.subsectionContent ? subsection.subsectionContent : '') | lineBreak"></p>
          </div>
        </ng-template>
      </li>
    </ul>
    <div [ngbNavOutlet]="nav" class="mt-2"></div>
    <div class="ifp-tnc-modal__btn-sec" *ngIf="!isAccepted">
      <ifp-button *ngIf="isLanguage"  [label]="selectedLanguage === 'en' ? 'عربي': 'English'" class="ifp-insight__btn-lang" (ifpClick)="languageChange(selectedLanguage === 'en' ? 'ar': 'en')" [buttonClass]="buttonClass.primary"></ifp-button>
      <ifp-button *ngIf="!isTabComplete" [label]="'Prev' | translate" class="ifp-insight__btn" (ifpClick)="nav.select(active-1); prevClicked(active)" [buttonClass]="active === 0 ? buttonClass.disabled : buttonClass.primary"></ifp-button>
      <ifp-button *ngIf="active !== (termData?.sections?.length - 1) && !isTabComplete" [label]="'Next' | translate" class="ifp-insight__btn" (ifpClick)="nav.select(active+1); nextClicked(active)"></ifp-button>
      <ifp-button [label]="'Accept' | translate" class="ifp-insight__btn" (ifpClick)="onButtonClick(true, termData.tcVersion)" *ngIf="active === (termData?.sections?.length - 1) || isTabComplete"></ifp-button>
    </div>
  </ng-container> -->

  <app-terms-n-conditions [isModal]="true" (termsResponse)="termsResponseTrigger($event)" (tncVersion)="getTermsVersion($event)" [isLanguage]="isLanguage" [isAccepted]="isAccepted"></app-terms-n-conditions>
</div>
