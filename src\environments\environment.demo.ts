
export const environment = {
  production: true,
  domain: '',
  isEnableRealEstate: false,
  cookieDomain: '',
  dummyVar:'',
  baseUrl:  window.location.origin,
  baseVersion: '/v1',
  apiVersion: '/api/',
  protocol: 'https://',
  env: 'demo',
  prepbaseUrl: `${window.location.origin}/api/data-prep/api`,
  genAiBaseUrl: 'https://bayaan-staging.scad.gov.ae/genai/api',  
  genAiChatVersion: 'v2',
  msalConfig: {
    authority:  `https://login.microsoftonline.com/${(window as any)['__env']?.tenantId}/`,
    clientId: (window as any)['__env']?.clientId,
    redirectUri: `${window.location.origin}/auth`,
    postLogoutRedirectUri: window.location.origin+'/login',
    scopes: [
      'User.read'
    ],
    domain: (window as any)['__env']?.domain,
    // userList: (window as any)['__env']?.userlist
  }
};

