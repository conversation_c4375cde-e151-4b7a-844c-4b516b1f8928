import { roleList, superUserRoles } from './../control-panel/ifp-access-control/ifp-access-control.constants';
import { ChangeDetectorRef, Component, ElementRef, OnDestroy, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { slaService } from '../../core/services/sla/sla.service';
import {  NgClass } from '@angular/common';
import { IfpButtonComponent } from '../../ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass, buttonIconPosition } from '../../core/constants/button.constants';
import { IfpRadioGroupProgressComponent } from '../../ifp-widgets/ifp-molecules/ifp-radio-group-progress/ifp-radio-group-progress.component';
import { SubSink } from 'subsink';
import { Title } from '@angular/platform-browser';
import { IfpModalComponent } from '../../ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { ToasterService } from '../../core/services/tooster/ToastrService.service';
import { ApiService } from '../../core/services/api.service';
import { dg, superUser, uaePassInfo, user, verifyInviteToken } from '../user-onboarding.constants';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { DecodeJwtTokenService } from '../../core/services/decode-jwt-token.service';
import { dataClassification, role } from '../control-panel/ifp-access-control/ifp-access-control.constants';
import { IfpSpinnerComponent } from '../../ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { UaePassService } from '../../core/services/uae-pass-service/uae-pass-service.service';
import { Router } from '@angular/router';
import { RemoveUnderscorePipe } from '../../core/pipes/underscoreRemove.pipe';
import { IfpNdaComponent } from '../../ifp-widgets/ifp-organism/ifp-nda/ifp-nda.component';
import { ApiStatus } from '../../core/constants/api-status.constants';
import { title } from '../../core/constants/header.constants';
import { AccessRequests, Level } from '../user-onboarding.interface';

@Component({
    selector: 'ifp-user-onboarding-register',
    imports: [TranslateModule, ReactiveFormsModule, NgClass, IfpButtonComponent, IfpRadioGroupProgressComponent, IfpModalComponent, FormsModule, IfpSpinnerComponent, RemoveUnderscorePipe, IfpNdaComponent],
    templateUrl: './user-onboarding-register.component.html',
    styleUrl: './user-onboarding-register.component.scss',
    providers: [DecodeJwtTokenService, UaePassService]
})
export class UserOnboardingRegisterComponent implements OnInit, OnDestroy {

  @ViewChild('ndaModal') ndaModal!: IfpModalComponent;
  @ViewChild('ndaModalRef') ndaModalRef!: ElementRef;
  @ViewChildren('otpInput') otpInput!: QueryList<ElementRef>;
  public showNDAModal = false;
  public registerForm!: FormGroup;
  // public accessLevels: RadioGroupItems = {
  //   label: 'accessLevel',
  //   key: 'accessLevel',
  //   options: [
  //     {
  //       label: 'Open',
  //       value: 'open'
  //     },
  //     {
  //       label: 'Confidential',
  //       value: 'confidential'
  //     },
  //     {
  //       label: 'Sensitive',
  //       value: 'sensitive'
  //     }
  //   ]
  // };

  public accessLevels: AccessRequests = {
    name: 'classification',
    levels: [
      {
        classification: 'Open',
        accessId: null,
        selected: true
      },
      {
        classification: 'Confidential',
        accessId: null,
        selected: false
      },
      {
        classification: 'Sensitive',
        accessId: null,
        selected: false
      }
    ]
  };

  public dropdownExpanded: boolean = false;
  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public isReadOnly: boolean = false;
  public countryList: CountryList[] = [
    {
      country: 'UAE',
      flagIcon: '../../../../assets/images/uae-flag-icon.png',
      code: 971,
      isSelected: true
    }
  ];

  public selectedCountry = this.countryList[0];
  public isSubmitted: boolean = false;
  public isRegistered: boolean = false;
  // public accessLevel: string = 'open';
  public subs: SubSink = new SubSink();
  public showSignPad: boolean = false;
  public ndaStatus: boolean = false;
  public token!: string;
  public decodedToken!: any;
  public userEmail!: string;
  public isNormalUser!: boolean;
  public isVerified: boolean = true;
  public uaePassToken!: string;
  public accessToken: string = '';
  public apiBaseUrl = environment.baseUrl + environment.apiVersion;
  public error!: {message: string; description?: string};
  public role = role;
  public roleList = roleList;
  public backToOtp: boolean = false;
  public superUserRoles = superUserRoles;
  // public isAtBottom: boolean = false;

  constructor(private _slaService: slaService, private _formBuilder: FormBuilder, private _apiService: ApiService, private _titleService: Title, private _cdr: ChangeDetectorRef, private _toaster: ToasterService, private _http: HttpClient, private _jwtTokenService: DecodeJwtTokenService, private _router: Router, private _uaePassService: UaePassService) {
    this._titleService.setTitle(`${title.bayaan} | Register`);
    this.registerForm = this._formBuilder.group({
      name: new FormControl('', [Validators.required, Validators.minLength(3)]),
      email: new FormControl('', [Validators.required, Validators.email]),
      role: new FormControl('', [Validators.required]),
      jobTitle: new FormControl(''),
      entity: new FormControl('', [Validators.required]),
      phoneNumber: [''],
      eid: ['']
    });
  }

  ngOnInit(): void {
    this._slaService.slaLoader$.next(false);
    this.isRegistered = localStorage.getItem('isRegistered') === 'true';
    if (!this.isRegistered) {
      this.token = localStorage.getItem('userEmailToken') ?? '';
      this.uaePassToken = localStorage.getItem('accessToken') ?? '';
      if (this.token && this.token !== '') {
        this.verifyToken();
        // this.decodedToken = this._jwtTokenService.getTokenDetails(this.token);
        // this.userEmail = this.decodedToken?.userId;
        // this.isNormalUser = this.decodedToken?.role === role.normalUser;
        // if (this.decodedToken.accessPolicy) {
        //   const access = this.decodedToken.accessPolicy.map((item: any) => item.classification.toLowerCase());
        //   const uniqueAccess = [...new Set(access)];
        //   if (uniqueAccess.length) {
        //     this.accessLevel = this.getAccess(uniqueAccess);
        //   }
        // }
        // this.getUserInfo();
      }
    }
  }

  getAccess(uniqueAccess: any[]) {
    if (uniqueAccess.includes(dataClassification.sensitive.toLowerCase() || dataClassification.secret.toLowerCase())) {
      this.accessLevels.levels.forEach((x: Level) => x.selected = x.classification.toLowerCase() === dataClassification.sensitive.toLowerCase());
      return;
    } else if (uniqueAccess.includes(dataClassification.confidential.toLowerCase())) {
      this.accessLevels.levels.forEach((x: Level) => x.selected = x.classification.toLowerCase() === dataClassification.confidential.toLowerCase());
      return;
    }
    this.accessLevels.levels.forEach((x: Level) => x.selected = x.classification.toLowerCase() === dataClassification.open.toLowerCase());
  }

  openNDAModal() {
    this.ndaModal.createElement();
    this.showSignPad = true;
    this._cdr.detectChanges();
  }

  closeNDAModal() {
    this.ndaModal.removeModal();
    this.showSignPad = false;
  }

  // onCheckTnc(status: boolean) {
  //   this.ndaStatus = status;
  // }

  signNda(status: boolean) {
    this.ndaStatus = status;
    this.closeNDAModal();
  }

  onRegister() {
    if (this.registerForm.valid) {
      const headers = new HttpHeaders({
        'X-TOKEN': localStorage.getItem('userEmailToken') ?? this.token,
        // 'X-UAEPASS-NAME': 'Dev',
        // 'X-UAEPASS-PHONE': this.registerForm.value.phoneNumber ? `971${this.registerForm.value.phoneNumber}` : this.registerForm.value.eid,
        // 'X-UAEPASS-IDN': this.registerForm.value.eid ?? '784199762883108',
        // 'X-UAEPASS-UUID': '1a46938d-a046-4da6-8849-2d393b79cf15',
        'X-UAEPASS-TOKEN': this.accessToken
      });
      const params = {
        signed_nda: this.ndaStatus
      };
      const isDgEndpoint = this.decodedToken?.role === role.dg ? dg.register : superUser.register;
      const endpoint = this.isNormalUser ? user.register : isDgEndpoint;
      this.subs.add(
        this._http.post(this.apiBaseUrl + endpoint, params, { headers }).subscribe({
          next: (resp: any) => {
            if (resp) {
              // this._toaster.success(resp.message);
              this.isSubmitted = true;
              this.isRegistered = true;
              localStorage.setItem('isRegistered', 'true');
              this._uaePassService.uaePassLogout('/user-register');
            }
          },
          error: (error) => {
            this._toaster.error(error.error.message);
            setTimeout(() => {
              this.redirectToOtpPage();
            }, 2000);
          }
        })
      );
    }
  }

  redirectToOtpPage () {
    localStorage.removeItem('isRegistered');
    localStorage.removeItem('userEmailToken');
    localStorage.removeItem('accessToken');
    const url = `/pre-register-auth?token=${this.token}`;
    this._uaePassService.uaePassLogout(url);
    // this._router.navigate(['/pre-register-auth'], {queryParams: {token: this.token}});
  }

  // convertBase64ToFile(base64Data: any, filename: string) {
  //   const base64String = base64Data.split(',')[1]; // Assuming the base64Data format is "data:image/png;base64,<actual_base64_data>"
  //   const byteCharacters = atob(base64String);
  //   const byteNumbers = new Array(byteCharacters.length);
  //   for (let i = 0; i < byteCharacters.length; i++) {
  //     byteNumbers[i] = byteCharacters.charCodeAt(i);
  //   }
  //   const byteArray = new Uint8Array(byteNumbers);
  //   const blob = new Blob([byteArray], { type: 'image/png' });
  //   return new File([blob], filename, { type: 'image/png' });
  // }

  setActive(country: CountryList) {
    this.countryList.forEach(x => x.isSelected = false);
    this.selectedCountry = country;
    country.isSelected = true;
    this.dropdownExpanded = false;
  }

  checkDigitLength(event: KeyboardEvent, control: string) {
    if (this.registerForm.controls[control].value != null && event.key == 'Backspace' && this.isReadOnly) {
      const value = this.registerForm.controls[control].value.toString();
      const lastValue = value.substring(0, value.length - 1);
      this.registerForm.patchValue({
        [control]: parseInt(lastValue)
      });
    }
    const mobileNumberLength = this.registerForm.controls[control].value != null ? this.registerForm.controls[control].value.toString().length : 0;
    this.isReadOnly = event.key !== 'Backspace' && mobileNumberLength >= 10;
  }

  verifyToken() {
    const data = {
      accessToken: this.uaePassToken,
      invitationToken: this.token
    };
    if (this.token) {
      this.subs.add(
        this._apiService.postMethodRequest(verifyInviteToken, data).subscribe({
          next: (res) => {
            this.isVerified = res.isValidUser;
            if (this.isVerified) {
              this.getUserInfo();
            }
          },
          error: (error) => {
            this.isVerified = false;
            if (error.status === ApiStatus.forbidden) {
              this.error = {message: 'Unauthorized Access', description: 'The information associated with your invitation does not align with your UAE Pass details.'};
              this.backToOtp = true;
            } else if (error.status === ApiStatus.badRequest) {
              this.error = {message: 'Unauthorized Access', description: 'The invitation you received is either no longer valid or has expired.'};
            } else {
              this.error = {message: error.error.message};
            }
          }
        })
      );
    }
  }

  getUserInfo() {
    if (this.uaePassToken && this.uaePassToken !== '') {
      this.getTokenDetails();
      const headers = new HttpHeaders({
        'X-UPASS-ACCESS-TOKEN': this.uaePassToken
      });
      const api = this.apiBaseUrl + uaePassInfo;
      this.subs.add(
        this._http.get(api, { headers }).subscribe({
          next: ((resp: any) => {
            if (resp) {
              const visibleDigits = 4;
              const userRole = this.decodedToken?.role ? this.roleList[this.decodedToken.role] : this.role.normalUser;
              const eid = resp.userInfo.idn;
              const eidEncrypted = eid.slice(0, visibleDigits) + eid.slice(visibleDigits, -visibleDigits).replace(/./g, '*') + eid.slice(-visibleDigits);
              this.accessToken = resp.accessToken;
              this.registerForm.setValue({
                name: resp?.userInfo?.fullnameEN ?? this.decodedToken.userId.split('@')[0],
                email: this.userEmail,
                role: userRole,
                jobTitle: this.decodedToken?.designation,
                entity: this.decodedToken?.entity,
                phoneNumber: resp?.userInfo?.mobile?.substring(3),
                eid: eidEncrypted
              });
            }
          }),
          error: ((error) => {
            this.error = {
              message: error.error.message
            };
          })
        })
      );
    }
  }

  getTokenDetails() {
    this.decodedToken = this._jwtTokenService.getTokenDetails(this.token);
    this.userEmail = this.decodedToken?.userId;
    this.isNormalUser = this.decodedToken?.role === role.normalUser;
    if (this.decodedToken.accessPolicy) {
      const access = this.decodedToken.accessPolicy.map((item: any) => item.classification.toLowerCase());
      const uniqueAccess = [...new Set(access)];
      if (uniqueAccess.length) {
        this.getAccess(uniqueAccess);
      }
    }
  }

  // onScroll(event: Event): void {
  //   const target = event.target as HTMLElement;
  //   const scrollHeight = target.scrollHeight;
  //   const scrollTop = target.scrollTop;
  //   const clientHeight = target.clientHeight;
  //   if (+((scrollTop + clientHeight).toFixed(0)) >= scrollHeight) {
  //     this.isAtBottom = true;
  //   }
  // }

  ngOnDestroy(): void {
    localStorage.removeItem('uaePass');
    this.subs.unsubscribe();
  }
}

interface CountryList {
  country: string;
  flagIcon: string;
  code: number;
  isSelected: boolean
}
