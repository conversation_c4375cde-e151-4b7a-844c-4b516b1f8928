export interface searchState {
  query: string;
  result: any[];
}


export interface searchResponseState {
  numberOfResults: number;
  query: string;
  result: {
    contentTypes: [{
      contentType: string;
      id: string;
      imgSrc?: string;
      light_icon?: string;
      dark_icon?: string;
      isSelected: boolean;
      items: []
      machineName: string;
      title: string;
      categories: any;
    }]
  }
}

export const initialSearchState: searchResponseState = {
  query: '',
  result: {
    contentTypes: [{
      contentType: 'no-data',
      id: '',
      imgSrc: '',
      isSelected: false,
      items: [],
      machineName: '',
      title: '',
      categories: {}
    }]
  },
  numberOfResults: 0
};

export interface HeaderState {
  accessibility_icon: string;
  accessibility_light_icon: string;
  dark_theme_light_icon: string;
  dark_theme_dark_icon: string;
  language: [];
  language_icon: string;
  language_light_icon: string;
  languages: Languages[];
  light_theme_dark_icon: string;
  light_theme_light_icon: string;
  navigation_menu: {
    primary: {
      app_type: string,
      children: [],
      dark_icon: string,
      id: string,
      is_parent: boolean,
      label: string,
      light_icon: string,
      parent_id: string,
      type: string,
      url: string
    }[]
  }
  notification_icon: string;
  notification_light_icon: string;
  search_button_label: string;
  search_icon: string;
  search_light_icon: string;
  search_placeholder: string;
  show_accessibility: boolean;
  show_language: boolean;
  show_notifications: boolean;
  show_themes: boolean;
  site_logo: string;
  site_logo_light: string;
  site_slogan: string;
  site_slogan_light: string;
  sub_title: string;
  title: string;
}

export const initialHaeder: HeaderState = {
  accessibility_icon: '',
  accessibility_light_icon: '',
  dark_theme_light_icon: '',
  dark_theme_dark_icon: '',
  language: [],
  language_icon: '',
  language_light_icon: '',
  languages: [],
  light_theme_dark_icon: '',
  light_theme_light_icon: '',
  navigation_menu: {
    primary: []
  },
  notification_icon: '',
  notification_light_icon: '',
  search_button_label: '',
  search_icon: '',
  search_light_icon: '',
  search_placeholder: '',
  show_accessibility: false,
  show_language: false,
  show_notifications: false,
  show_themes: false,
  site_logo: '',
  site_logo_light: '',
  site_slogan: '',
  site_slogan_light: '',
  sub_title: '',
  title: ''
};



export interface HeaderResponse {
  accessibility_icon: string;
  accessibility_light_icon: string;
  dark_theme_light_icon: string;
  dark_theme_dark_icon: string;
  language: [];
  language_icon: string;
  language_light_icon: string;
  languages: Languages[];
  light_theme_dark_icon: string;
  light_theme_light_icon: string;
  navigation_menu: {
    primary: {
      app_type: string,
      children: [],
      dark_icon: string,
      id: string,
      is_parent: boolean,
      label: string,
      light_icon: string,
      parent_id: string,
      type: string,
      url: string
    }[]
  }
  notification_icon: string;
  notification_light_icon: string;
  search_button_label: string;
  search_icon: string;
  search_light_icon: string;
  search_placeholder: string;
  show_accessibility: boolean;
  show_language: boolean;
  show_notifications: boolean;
  show_themes: boolean;
  site_logo: string;
  site_logo_light: string;
  site_slogan: string;
  site_slogan_light: string;
  sub_title: string;
  title: string;
}

export interface ThemeSettings {
  status: string;
  result: any[];
  error: any;
}

interface Languages {
  title: string;
  value: string;
}

export interface PermissionList {
  feature_name: string;
  config: {
    title: string;
    light_link: string;
    dark_link: string;
    isClip: boolean;
  };
}
