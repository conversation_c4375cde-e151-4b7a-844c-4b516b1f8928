import { AsyncPipe, Location, NgClass } from '@angular/common';
import { AfterViewInit, ChangeDetectorRef, Component, computed, inject, OnDestroy, OnInit, Signal, signal, viewChild, ViewChild, WritableSignal } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IfpButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpDbListCardComponent } from '../../molecule/ifp-db-list-card/ifp-db-list-card.component';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';
import { DashboardList } from '../dashboard.interface';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { PaginationComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/pagination/pagination.component';
import { Subject, debounceTime, forkJoin, of, Observable, switchMap, merge } from 'rxjs';
import { SubSink } from 'subsink';
import { catchError, map, tap, finalize } from 'rxjs/operators';

import { IfpCardLoaderComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { IfpNoDataComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpRemoveCardComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-remove-card/ifp-remove-card.component';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { IfpShareModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-share-modal/ifp-share-modal.component';
import { ShareAppsData, ShareResponse } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-share-modal/ifp-share-modal.interface';
import { UsageDashboardLogService } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard.constants';
import { IfpHorizontalTabComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-horizontal-tab/ifp-horizontal-tab.component';
import { IfpSearchComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-search/ifp-search.component';
import { IfpDbTemplateListComponent } from '../ifp-db-template-list/ifp-db-template-list.component';
import { IfpBreadcrumbsComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { IfpVisulizationBuilderService } from 'src/app/scad-insights/core/services/ifp-visulization-builder.service';
import { dashboardEndpoints } from 'src/app/scad-insights/core/apiConstants/dashboard.api.constants';
import { IfpTabComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';
import { govAffairsSubTabs, DASHBOARD_TYPE_OPTIONS } from './ifp-dashboard-list.constants';
import { cloneDeep } from 'lodash';
import { dateFormat } from 'src/app/scad-insights/core/constants/date.constants';
import { approveConst, generalizedRoles, dxpApi } from 'src/app/dxp/dxp.constants';
import { IfpPanelDropdownComponent, PanelDropdownOptions } from 'src/app/ifp-analytics/molecule/ifp-panel-dropdown/ifp-panel-dropdown.component';
import { AdminService } from 'src/app/scad-insights/core/services/sla/admin.service';
import { actionButtonList, mainTabParams, viewAction, detailTypes, dashboardTypeKeys, subTabParams, statusParams } from '../../core/constants/dashboard-builder.constants';
import { dashboardTypes } from 'src/app/scad-insights/core/constants/dashboard.constants';
import { FILTER_VISIBILITY_CONFIG, FilterVisibilityConfig, RoleFilterConfig } from '../../core/constants/filter-visibility.constants';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { DxpUserListingResponse } from 'src/app/dxp/dxp.interface';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { IfpExploratorySelectPageComponent } from 'src/app/ifp-analytics/ifp-exploratory/ifp-exploratory-select-page/ifp-exploratory-select-page.component';

import { DASHBOARD_EMBED_TYPES } from 'src/app/dxp/dxp-constants-text';
import { DxpTabDataService } from 'src/app/dxp/services/dxp-tab-data.service';
import { DxpUserSyncService } from 'src/app/scad-insights/core/services/dxp/dxp-user-sync.service';

@Component({
  selector: 'ifp-dashboard-list',
  templateUrl: './ifp-dashboard-list.component.html',
  styleUrl: './ifp-dashboard-list.component.scss',
  imports: [TranslateModule, IfpButtonComponent, IfpDbListCardComponent, PaginationComponent,
    IfpCardLoaderComponent, IfpNoDataComponent, IfpModalComponent, IfpShareModalComponent, IfpHorizontalTabComponent, IfpSearchComponent, IfpDbTemplateListComponent,
    IfpBreadcrumbsComponent, NgClass, IfpTabComponent, IfpPanelDropdownComponent, IfpRemoveCardComponent, IfpExploratorySelectPageComponent, AsyncPipe]
})
export class IfpDashboardListComponent implements OnInit, AfterViewInit, OnDestroy {

  @ViewChild('modal') modal!: IfpModalComponent;
  @ViewChild('shareAppsModal') shareAppsModal!: IfpModalComponent;
  @ViewChild('templateListModal') templateListModal!: IfpModalComponent;
  private readonly _dashboardModal: Signal<IfpModalComponent> = viewChild.required<IfpModalComponent>('dashboardModal');

  private readonly _dashboardApiService = inject(IfpVisulizationBuilderService);
  private readonly _apiService = inject(ApiService);
  private readonly _adminService = inject(AdminService);
  private readonly _cdr = inject(ChangeDetectorRef);
  private readonly _dxpTabService = inject(DxpTabDataService);
  public _dxpUserSyncService = inject(DxpUserSyncService);

  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;

  public isFilterApplied: boolean = false;
  public totalCount: number = 0;
  public dashboardList: DashboardList[] = [];
  public search = '';
  public offsetPage = 1;
  public offset: number = 0;
  public limit: number = 10;
  public subs = new SubSink();
  private readonly searchInput = new Subject<string>();
  public searchKeyword!: string;
  public isLoading: boolean = true;
  public currentRemoveText!: string;
  public recentlyAddedEnabled: string = 'desc';
  private readonly dependentFiltersTrigger$ = new Subject<{ resetPages?: boolean; skipDashboardCall?: boolean }>();
  private readonly dashboardTypeChange$ = new Subject<string>();
  private readonly dashboardQueryChange$ = new Subject<void>();

  public selectedDashboards: any[] = [];
  public availableUsers: any[] = [];
  public dashboardTabs: WritableSignal<MainTabsMenu[]> = signal([]);
  public openTemplateListModal: boolean = false;
  public isGovAffairs: boolean = false;
  public selectedTabView: MainTabsMenu = {
    showCount: false,
    param: '',
    order: 0
  };

  public govAffairsSubTab: any = govAffairsSubTabs;
  public subTabList: WritableSignal<SubMenu[] | undefined> = signal([]);
  public status: WritableSignal<string> = signal('');
  public dateFormat = dateFormat;
  private tabCountData: any = null;
  public role = this._adminService.generalizedRole;
  public currentUserRole: string = this._adminService.generalizedRole();
  // public secondaryRole = this._adminService.secondaryRole();
  public actionButtonList = actionButtonList;
  public embeddedTypeKey: string = dashboardTypeKeys.embedded;
  public subTabParams = subTabParams;

  public showEmbeddedDraftActionsMap: Record<string, boolean> = {};

  public actionButtons = computed(() => {
    const role = this.role();
    const status = (this.status() || '').toString();
    const selectedType = (this.selectedDashboardType instanceof Function) ? this.selectedDashboardType() : (this.selectedDashboardType as unknown as string);
    const isBI = ((selectedType ?? '') === this.embeddedTypeKey);

    if (isBI) {
      const isDraft = this.equalsIgnoreCase(status, statusParams.draft);
      const isCompleted = this.equalsIgnoreCase(status, statusParams.completed);
      const onMyDashboards = this.selectedTabView?.param === this.mainTabParams.govMyDashboards;
      const isAdvancedCreator = this.currentUserRole === this.generalizedRoles.advanced_visulization_creator;

      if (isDraft) {
        return [
          { label: 'Edit', value: approveConst.edit, icon: 'ifp-icon-edit-pencil-new' },
          viewAction
        ];
      }

      if (onMyDashboards && isCompleted && isAdvancedCreator) {
        return [
          { label: 'Unpublish', value: approveConst.unpublish, icon: 'ifp-icon-eye-closed' },
          viewAction
        ];
      }

      return [viewAction];
    }

    return this.actionButtonList[role]?.[status] ?? [viewAction];
  });

  public showEmbeddedDraftActions = computed(() => {
    const selectedType = (this.selectedDashboardType instanceof Function) ? this.selectedDashboardType() : (this.selectedDashboardType as unknown as string);
    const isBI = ((selectedType ?? '') === this.embeddedTypeKey);
    const statusStr = (this.status() || '').toString();
    const isDraft = (/draft/i).test(statusStr);
    const isCompleted = (/completed/i).test(statusStr);
    const onMyDashboards = this.selectedTabView?.param === this.mainTabParams.govMyDashboards;
    return isBI && (isDraft || (onMyDashboards && isCompleted));
  });


  public selectedMainTabIndex: number = 0;
  public subTabSelected: number = 0;
  public generalizedRoles = generalizedRoles;

  public shouldShowEmbeddedDraftActions(d: DashboardList): boolean {
    const detailTypeVal = ((d as unknown as { detail_type?: string }).detail_type ?? '').toString().toLowerCase();
    const isEmbedded = detailTypeVal === detailTypes.embedded;
    const statusStr = (this.status() || '').toString();
    const isDraft = (/draft/i).test(statusStr);
    const isCompleted = (/completed/i).test(statusStr);
    const selectedType = (this.selectedDashboardType instanceof Function) ? this.selectedDashboardType() : (this.selectedDashboardType as unknown as string);
    const isBI = ((selectedType ?? '') === this.embeddedTypeKey);
    const onMyDashboards = this.selectedTabView?.param === this.mainTabParams.govMyDashboards;
    return isEmbedded && (isDraft || (isBI && onMyDashboards && isCompleted));
  }

  public selectedIndex: number = -1;
  public sort: string = '-updated_at';
  public tabFromParam: string = '';
  public filterList!: Record<string, any>;
  public entityList: PanelDropdownOptions[] = [];
  public usersList: PanelDropdownOptions[] = [];
  public selectedEntityOptions: PanelDropdownOptions[] = [];
  public selectedCreatorOptions: PanelDropdownOptions[] = [];
  private creatorsInitialized: boolean = false;
  private entityInitialized: boolean = false;
  private isInitialFilterLoad: boolean = true;


  public dashboardTypesKeys: PanelDropdownOptions[] = [];
  public selectedDashboardTypeOptions: PanelDropdownOptions[] = [];
  selectedDashboardType = signal<string>('');
  public selectedDashboardTypeLabel: any;
  private isEmbeddedNav: boolean = false;

  public sharedUsersByDashboard: Record<string, PanelDropdownOptions[]> = {};
  public statusParams = statusParams;

  pageData: PageData[] = [
    {
      title: 'Home',
      route: '/home'
    },
    {
      title: 'My Bayaan',
      route: '/store'
    },
    {
      title: 'Advanced Visualization Builder',
      route: ''
    }
  ];

  public sortOptions = [
    {
      order: 'desc',
      sort: 'createdAt',
      icon: 'ifp-icon-downarrow',
      value: 'Newest First',
      key: '-updated_at',
      checked: false
    },
    {
      order: 'asc',
      sort: 'createdAt',
      icon: 'ifp-icon-uparrow',
      value: 'Oldest First',
      key: 'updated_at',
      checked: false
    },
    {
      label: 'A-Z',
      order: 'desc',
      sort: 'title',
      icon: 'ifp-icon-downarrow',
      value: 'A-Z',
      key: 'name',
      checked: false
    },
    {
      order: 'asc',
      sort: 'title',
      icon: 'ifp-icon-uparrow',
      value: 'Z-A',
      key: '-name',
      checked: false
    }
  ];

  public dashboardBuilderCard = [
    {
      id: 'biDasboard',
      title: 'Publish Dashboards & Tables',
      description: 'Embed BI dashboards, tables and publish them directly for seamless access to interactive, real-time insights.',
      url: '/dxp/publish',
      color: ifpColors.purple,
      icon: 'ifp-icon-dashboard-circle',
      external: false,
      hide: false
    },
    {
      id: 'createDashboard',
      title: 'Create Native Dashboard',
      description: 'Build fully integrated, high-performance dashboards with Create Native Dashboard. Tailor insights to your platform for a seamless user experience.',
      url: '/store/dashboard-builder',
      color: ifpColors.cyan,
      icon: 'ifp-icon-native-dashboard',
      external: false,
      hide: false
    }
  ];

  public selectedSortOption: PanelDropdownOptions = this.sortOptions[0];
  public dashboardTypes = dashboardTypes;
  public mainTabParams = mainTabParams;

  private filterVisibilityConfig = FILTER_VISIBILITY_CONFIG;

  public showSelectTypeFilter: boolean = true;
  public showSelectEntityFilter: boolean = true;
  public showCreatedByFilter: boolean = true;
  public showSortFilter: boolean = true;
  public createButtonTabs = [mainTabParams.dxpPublishedDashboards, mainTabParams.nativeMyDashboards];
  private sessionId!: string;
  private readonly _activatedRoute: ActivatedRoute = inject(ActivatedRoute);
  private searchSubject = new Subject<string>();

  constructor(public location: Location, private _dashboardService: DashboardService, private router: Router, private _toaster: ToasterService,
    private _translate: TranslateService, public _themeService: ThemeService, private _tooter: ToasterService, private log: UsageDashboardLogService) {
    localStorage.removeItem(this._dashboardService.selectedCards);


    this.subs.add(
      this._activatedRoute.queryParams.subscribe((value: Params) => {
        if (value['government-affairs']) {
          this.isGovAffairs = value['government-affairs'];
        }
        if (value['status']) {
          this.status.set(value['status']);
        }
        if (value['tab']) {
          this.tabFromParam = value['tab'];
        }
        if (value['isembedded']) {
          this.isEmbeddedNav = true;
        }
      })
    );

    const subscribingKey = this.isGovAffairs ? this._dxpTabService.Search$ : this.searchInput;
    this.subs.add(
      subscribingKey
        .pipe(debounceTime(800))
        .subscribe((value) => {
          this.searchKeyword = value;
          this.offsetPage = 1;
          this.getDashboards();
        })
    );
  }


  onSearch(keyword: string) {
    this.searchKeyword = keyword;
    this.searchInput.next(keyword);
  }



  equalsIgnoreCase(a?: string | null, b?: string | null): boolean {
    if (a == null || b == null) {
      return false;
    }
    return a.toString().toLowerCase() === b.toString().toLowerCase();
  }

  computeEmbeddedDraftActionVisibility(): void {
    const statusStr = (this.status() || '').toString();
    const isDraft = this.equalsIgnoreCase(statusStr, statusParams.draft);
    const isCompleted = this.equalsIgnoreCase(statusStr, statusParams.completed);

    const selectedType = (this.selectedDashboardType instanceof Function) ? this.selectedDashboardType() : (this.selectedDashboardType as unknown as string);
    const isBI = ((selectedType ?? '') === this.embeddedTypeKey);
    const onMyDashboards = this.selectedTabView?.param === this.mainTabParams.govMyDashboards;
    const allowCompletedForEmbedded = isBI && onMyDashboards && isCompleted;

    type LocDash = { object_id?: string; detail_type?: string };
    const map: Record<string, boolean> = {};
    (this.dashboardList as Array<LocDash>).forEach((item) => {
      const id = (item.object_id ?? '').toString();
      const detailTypeVal = (item.detail_type ?? '').toString().toLowerCase();
      const isEmbedded = detailTypeVal === detailTypes.embedded;
      map[id] = isEmbedded && (isDraft || allowCompletedForEmbedded);
    });
    this.showEmbeddedDraftActionsMap = map;
  }

  ngOnInit(): void {
    // this._adminService.getUserRole().subscribe((res) => {
    //   this.currentUserRole = res?.role;
    //   this.updateFilterVisibility();
    // });
    this.updateFilterVisibility();
    this.setupDependentFiltersStream();
    this.setupDashboardTypeChangeStream();
    // this.status.set('dashboards');
    this.isLoading = true;

    this.getDashboardSideMenu(true, true).then(() => {
      const selectedIndex = this.dashboardTabs().findIndex((item: MainTabsMenu) => item.param === this.tabFromParam);
      this.selectedTabView = selectedIndex >= 0 ? this.dashboardTabs()[selectedIndex] : this.dashboardTabs()[this.selectedMainTabIndex];
      this.subTabList.update(() => this.selectedTabView?.subMenus?.length ? this.setSubTabName(this.selectedTabView?.subMenus) : []);

      const statusParam = this._activatedRoute.snapshot.queryParams['status'];
      if (statusParam && (this.subTabList()?.length ?? 0) > 0) {
        // Find the sub-tab that matches the status parameter
        const matchingSubTab = this.subTabList()?.find(subTab => subTab.param === statusParam);
        this.status.set(matchingSubTab ? statusParam : this.subTabList()?.[0].param);
      } else {
        this.status.set(this.subTabList()?.length ? this.subTabList()?.[0]?.param ?? this.selectedTabView?.param : this.selectedTabView?.param);
      }

      this.updateFilterVisibility();

      this.getDropDownTypes();

      // After initiating listing, load tab counts and merge into sidebar
      this.getEntityTabCount().then(() => {
        this.mergeTabCountsIntoSidebar();
      });
    });
    if (!this.isGovAffairs) {
      this.getUserListingData('', 1);
    }

    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.dashboard, this.log.currentTime);
    if (!this.isGovAffairs) {
      this.searchSubject.pipe(debounceTime(300)).subscribe(term => {
        this.getUserListingData(term, 1);
      });
    }
  }

  ngAfterViewInit(): void {
    if (this.openTemplateListModal) {
      this.templateListModal.createElement();
    }
  }

  setupDashboardTypeChangeStream(): void {
    this.subs.add(
      merge(this.dashboardTypeChange$, this.dashboardQueryChange$)
        .pipe(
          switchMap(() => {
            this.isLoading = true;
            return this.performDashboardListRequest()
              .pipe(
                finalize(() => this.isLoading = false),
                catchError(() => {
                  this.isLoading = false;
                  this.dashboardList = [];
                  this._cdr.detectChanges();
                  return of(null);
                })
              );
          })
        )
        .subscribe({
          next: (resp) => {
            if (!resp) {
              return;
            }

            this.dashboardList = cloneDeep(resp.results ?? []);
            this.totalCount = resp.count ?? 0;

            this.computeEmbeddedDraftActionVisibility();

            this.sharedUsersByDashboard = {};
            this.dashboardList.forEach((d: any) => {
              const users = Array.isArray(d?.shared_with_users) ? d.shared_with_users : [];
              this.sharedUsersByDashboard[d.object_id] = users.map((u: any) => ({
                key: u?.id,
                value: u?.name,
                checked: false,
              }));
            });

            this.isLoading = false;
            this._cdr.detectChanges();
          }
        })
    );
  }

  private performDashboardListRequest(): Observable<any> {
    const limit = this.limit;
    const offset = this.offset;
    const body: Record<string, any> = {
      status: this.status(),
      search: this.searchKeyword ?? '',
      ordering: this.sort,
    };
    body['dashboard_type'] = this.isGovAffairs ? this.getSelectedDashboardTypeParam() : this.dashboardTypes.native;
    if (this.showCreatedByFilter && this.selectedCreatorOptions && this.selectedCreatorOptions.length > 0) {
      const creatorCsv = this.getSelectedCreatorIdsParam();
      const createdByArr = creatorCsv ? creatorCsv.split(',').filter(Boolean) : [];
      if (createdByArr.length > 0) {
        body['created_by'] = createdByArr;
      }
    }
    if (this.showSelectEntityFilter && this.selectedEntityOptions && this.selectedEntityOptions.length > 0) {
      const entityCsv = this.getSelectedEntityIdsParam();
      const entityArr = (entityCsv ? entityCsv.split(',') : []).filter(Boolean);
      if (entityArr.length > 0) {
        body['entity_id'] = entityArr;
      }
    }

    this.changeQueryParams({ tab: this.selectedTabView?.param, status: this.status() });
    return this._apiService.postMethodRequest(`${dashboardEndpoints.dashboardListMulti}?limit=${limit}&offset=${offset}`, body, undefined, true);
  }

  private setupDependentFiltersStream(): void {
    this.subs.add(
      this.dependentFiltersTrigger$
        .pipe(
          switchMap(({ resetPages, skipDashboardCall }) => {
            if (resetPages) {
              this.resetPagesWithoutDashboardCall();
            }

            const tasks: Observable<void>[] = [];
            if (this.showSelectEntityFilter) {
              tasks.push(this.fetchEntityFilterOptions$());
            }
            if (this.showCreatedByFilter) {
              tasks.push(this.fetchCreatorFilterOptions$());
            }

            return tasks.length > 0 ? forkJoin(tasks).pipe(map(() => skipDashboardCall)) : of(skipDashboardCall);
          })
        )
        .subscribe({
          next: (skipDashboardCall) => {
            if (this.isInitialFilterLoad) {
              setTimeout(() => {
                this.isInitialFilterLoad = false;
              }, 100);
            }

            if (!skipDashboardCall) {
              this.getDashboards();
            }
          },
          error: () => {
            this.isInitialFilterLoad = false;
            this.getDashboards();
          }
        })
    );
  }

  onApplyFilter() {
    this.isFilterApplied = !this.isFilterApplied;
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.dashboard, this.log.currentTime);
  }

  getDashboards() {
    this.dashboardQueryChange$.next();
  }

  getDashboardSideMenu(loadFilters: boolean = true, isInitialLoad: boolean = false) {
    return new Promise((resolve, reject) => {
      let params = {};
      if (!this.isGovAffairs) {
        params = { is_native: true };
      }
      this.subs.add(
        this._dashboardApiService.getMethodRequest(dashboardEndpoints.dashboardMainTabs, params).subscribe({
          next: (result: any) => {

            if (Object.keys(result?.tabs).length) {
              this.setMainTabs(result.tabs, isInitialLoad);
            }
            if (result?.filters) {
              if (Object.keys(result.filters).length && loadFilters) {
                this.filterList = result?.filters;
                this.usersList = [];
                const userFilter = result?.filters?.['created_by'];
                if (userFilter.length) {
                  this.usersList = userFilter.map((user: UserFilter) => ({
                    ...user,
                    key: user.id,
                    id: user.id,
                    value: user.name,
                    checked: false,
                  }));
                }
              }
            }
            resolve(this.dashboardTabs());
          },
          error: error => {
            this._toaster.error(error?.error?.message);
            reject(new Error(error));
          }
        })
      );
    });
  }

  setMainTabs(tabs: any, isInitialLoad: boolean = false) {
    const tab = this._activatedRoute.snapshot.queryParams['tab'];
    const tabList = Object.keys(tabs).map((key: string, index: number) => {
      const { order, param, showCount, subMenus, count, description } = tabs[key];
      const displayName = this._themeService.defaultLang === 'en' ? tabs[key]?.label_en : tabs[key]?.label_ar;
      const name = tabs[key].label ?? displayName;
      if (tab && tab === param) {
        this.selectedMainTabIndex = index;
      }
      return { name, order, param, showCount, subMenus, count, description };
    });
    this.dashboardTabs.set(tabList);
    const selectedSubtabIndex = this.dashboardTabs()[this.selectedMainTabIndex].subMenus?.findIndex((subMenu: SubMenu) => subMenu.param === this.status());
    this.subTabSelected = selectedSubtabIndex && selectedSubtabIndex >= 0 ? selectedSubtabIndex : 0;

    if (!isInitialLoad) {
      this.changeTabView({ item: this.dashboardTabs()[this.selectedMainTabIndex], index: this.selectedMainTabIndex }, this.subTabSelected, false);
    }
  }


  goToDashboardBuilder() {
    if (!this.isGovAffairs) {
      this.openRoutes({ route: 'store/dashboard-builder', id: 'createDashboard' });
      return;
    }
    if (this.currentUserRole === this.generalizedRoles.advanced_visulization_creator) {

      // const sharedDashboardTabs = this._dxpTabService.getCurrentDashboardTabs();
      // const currentTab = this._dxpTabService.getCurrentTabKey();
      // const subTabs = sharedDashboardTabs.find(tab => tab.name === currentTab)?.subTabs as Array<any>;
      // if (this.isGovAffairs) {
      //   if (subTabs.length > 1) {
      //     this._dashboardModal().createElement();
      //     return;
      //   }
      // }

      // if (subTabs.length === 1) {
      //   const singleSubTab = subTabs[0];
      //   if (singleSubTab.key === 'native') {
      //     this.router.navigate(['/store/dashboard-builder']);
      //   } else if (singleSubTab.key === 'embedded') {
      //     this.router.navigate(['/dxp/publish']);
      //   }
      //   return;
      // }

      if (this._dxpUserSyncService.isUserSyncedInDxp()) {
        this._dashboardModal().createElement();
      } else {
        this.router.navigate(['/dxp/publish']);
      }

      return;
    }

    this.openRoutes({ route: 'store/dashboard-builder', id: 'createDashboard' });
  }

  openRoutes(event: { route: string, id: string }) {
    let params: any = {};
    if (event.id == 'createDashboard') {
      if (this.isGovAffairs) {
        params = { 'government-affairs': true };
      }

      const mainTab = this.selectedTabView?.param; // Main tab parameter
      const subTab = this.status(); // Sub tab parameter

      params.mainTab = mainTab;
      params.subTab = subTab;
    }
    this.router.navigate([event.route], { queryParams: params });
  }

  onPageChange(event: any) {
    this.offset = event ? (event + 1) : 0;
    this.offsetPage = (event / this.limit) + 1;
    this.getDashboards();

  }

  limitChanged(event: any) {
    this.offsetPage = 1;
    this.offset = 0;
    this.limit = event;
  }

  openDetail(id: string, mode: string = 'detail') {
    const list: any = {};
    const dataIndex = this.dashboardList.findIndex(x => x.object_id == id);
    if (this.selectedTabView.param === 'sent') {
      list.key = this._translate.instant('Recipients');
      list.value = this.dashboardList[dataIndex].recepientEmails?.toString();
    } else if (this.selectedTabView.param === 'Received') {
      list.key = this._translate.instant('Received from');
      list.value = this.dashboardList[dataIndex].shareEmail;
    }

    const dashboardItem = this.dashboardList[dataIndex] as DashboardList;
    const typeStr = (dashboardItem?.dashboard_type ?? '').toString();
    const detailTypeVal = ((dashboardItem as unknown as { detail_type?: string }).detail_type ?? '').toString().toLowerCase();
    const isEmbedded = detailTypeVal === 'embedded';
    const isTableBuilder = typeStr === DASHBOARD_EMBED_TYPES.TABLE_BUILDER;
    const isPowerBi = typeStr === DASHBOARD_EMBED_TYPES.powerBi;
    const isTableau = new RegExp('tableau', 'i').test(typeStr);
    const isPowerBiReportServer = typeStr == DASHBOARD_EMBED_TYPES.powerBIRepoertServer;
    // Base breadcrumbs reused for embedded providers
    const basePageData: PageData[] = [
      { title: 'Home', route: '/home' },
      { title: 'Government Affairs', route: '/dxp/dashboards', queryParams: { 'government-affairs': 'true' } }
    ];

    if (isEmbedded && isTableBuilder) {
      sessionStorage.setItem(`tablebuilder:pageData:${id}`, JSON.stringify(basePageData));
      this.router.navigate([`/common-dashboard/${id}`], {
        queryParams: { type: 'table-builder', dashboardId: id },
        state: { pageData: basePageData }
      });
      return;
    }

    if (isEmbedded && isPowerBi) {
      sessionStorage.setItem(`powerbi:pageData:${id}`, JSON.stringify(basePageData));
      this.router.navigate([`/power-bi/${id}`], {
        queryParams: { dashboardId: id },
        state: { pageData: basePageData }
      });
      return;
    }

    if (isEmbedded && isTableau) {
      sessionStorage.setItem(`tableau:pageData:${id}`, JSON.stringify(basePageData));
      this.router.navigate([`/tableau/${id}`], { state: { pageData: basePageData } });
      return;
    }

    if (isEmbedded && isPowerBiReportServer) {
      const queryParams: Record<string, any> = {
        contentType: 'power-bi',
        dashboardId: id
      };
      this.router.navigate([`/common-dashboard/${id}`], {
        queryParams: queryParams
      });
      return;
    }

    const mainTab = this.selectedTabView.param; // Main tab parameter
    const subTab = this.status(); // Sub tab parameter
    const queryParams: Record<string, any> = {
      id: id,
      mode: mode,
      tab: this.selectedTabView.param,
      mainTab: mainTab,
      subTab: subTab
    };

    if (this.dashboardList[dataIndex].dashboard_type === dashboardTypes.govAffairs) {
      queryParams['government-affairs'] = true;
    }
    this.router.navigate(['store/dashboard-builder'], {
      queryParams: queryParams
    });
  }

  onSelectCardOption(event: any, data: DashboardList, index: number) {
    if (event == 'edit') {
      this.editDashboard(data.object_id, data.dashboard_type);
    }
    if (event == 'delete') {
      this.selectedIndex = index;
      this.opendeleteModel(data);
    }
  }

  editDashboard(id: string, dashboardType?: string) {
    const mainTab = this.selectedTabView.param; // Main tab parameter
    const subTab = this.status(); // Sub tab parameter
    const queryParams: Record<string, any> = {
      id: id,
      mode: 'edit',
      tab: this.selectedTabView.param,
      mainTab: mainTab,
      subTab: subTab
    };

    if (dashboardType === dashboardTypes.govAffairs) {
      queryParams['government-affairs'] = true;
    }
    this.router.navigate(['store/dashboard-builder'], {
      queryParams: queryParams
    });
  }

  deleteDashboard() {
    const objectId = this.dashboardList[this.selectedIndex].object_id;
    if (this.subTabList()?.length) {
      const selectedSubtabIndex = this.subTabList()?.findIndex((subTab: SubMenu) => subTab.param === this.status());
      if (selectedSubtabIndex && selectedSubtabIndex >= 0) {
        const selSubIndex = selectedSubtabIndex ?? 0;
        (this.subTabList() as SubMenu[])[selSubIndex].count -= 1;
      }
    }
    this.subs.add(
      this._apiService.getDeleteRequest(`${dashboardEndpoints.deleteDashboard(objectId)}`).subscribe({
        next: () => {
          this.dashboardList.splice(this.selectedIndex, 1);
          this._toaster.success('Dashboard deleted Successfully');
        },
        error: error => {
          this._toaster.error(error?.error?.message);
        }
      })
    );
  }

  recentlyAdded() {
    if (this.recentlyAddedEnabled == 'desc') {
      this.recentlyAddedEnabled = 'asc';
    } else {
      this.recentlyAddedEnabled = 'desc';
    }
    this.getDashboards();
  }

  goToStore() {
    this.router.navigate(['store']);
  }

  opendeleteModel(data: DashboardList) {
    this.currentRemoveText = `${this._translate.instant('Do you want to remove ')} ${data.name}? `;
    this.modal.createElement();
  }

  closeModel(event: any) {
    if (event) {
      this.deleteDashboard();
    }
    this.selectedIndex = -1;
    this.modal.removeModal();
  }




  updateSelectDashboard(event: { id: string, checked: boolean, cardData?: any, event?: any }) {
    if (event.checked) {
      this.selectedDashboards.push({ 'id': event.id, 'shareDashboardName': event?.cardData?.name });
    } else {
      this.selectedDashboards = this.selectedDashboards.filter(x => x !== event.id);
    }
  }

  shareSelected() {
    this.shareAppsModal.createElement();

  }

  closeShare() {
    this.shareAppsModal.removeModal();
    this.selectedDashboards = []; // Clear selected dashboards to reset checkbox states
    this.getDashboards(); // Refresh the dashboard listing data
  }



  onShareApps(data: ShareAppsData) {
    data.dashboards = this.selectedDashboards;
    data.shareNodes = undefined;
    const payload = {
      'dashboards': data.dashboards,
      'shareList': data.shareList,
      'comment': data.comment
    };
    this.subs.add(
      this._dashboardService.shareDashboards(payload).subscribe((resp: ShareResponse) => {
        if (resp) {
          this._tooter.success(resp.message);
          this.selectedDashboards = []; // Clear selected dashboards
          this.shareAppsModal.removeModal(); // Close the modal
          this.getDashboards(); // Refresh the dashboard listing data
        }
      })
    );
  }

  changeTabView(event: any, subTabIndex: number = 0, resetPages: boolean = true) {
    if (event) {
      this.selectedMainTabIndex = event.index;
      this.selectedTabView = event.item;
      if (this.selectedTabView?.subMenus?.length) {
        this.subTabList.set(this.setSubTabName(this.selectedTabView.subMenus));
        this.status.set(this.subTabList()?.[subTabIndex].param ?? '');
      } else {
        this.subTabList.set([]);
        this.status.set(this.selectedTabView.param);
      }
      this.updateFilterVisibility();
      if (resetPages) {
        this.resetPagesWithoutDashboardCall();
      }
      this.resetFiltersToInitialStateForTab();
      this.isInitialFilterLoad = true;

      this.isLoading = true;
      this.getDropDownTypes();
    }
  }

  setSubTabName(subMenu: SubMenu[]) {
    return subMenu.map((tab: SubMenu) => {
      const displayName = this._themeService.defaultLang === 'en' ? tab?.label_en : tab?.label_ar; // to be changed when label key is added in API
      const name = tab.label ?? displayName;
      return { ...tab, name: name };
    });
  }


  resetPages() {
    this.isLoading = true;
    this.offsetPage = 1;
    this.offset = 0;
    this.searchKeyword = '';
    this.dashboardList = [];
    this.sort = '';

    // Reset filter selections and initialization flags
    this.selectedEntityOptions = [];
    this.selectedCreatorOptions = [];
    this.creatorsInitialized = false;
    this.entityInitialized = false;

    this.getDashboards();
  }

  resetPagesWithoutDashboardCall() {
    this.isLoading = true;
    this.offsetPage = 1;
    this.offset = 0;
    this.searchKeyword = '';
    this.dashboardList = [];
    this.sort = '';

    // Reset filter selections and initialization flags
    this.selectedEntityOptions = [];
    this.selectedCreatorOptions = [];
    this.creatorsInitialized = false;
    this.entityInitialized = false;
  }

  private resetFiltersToInitialStateForTab(): void {
    // Clear dependent filter selections and initialization flags
    this.selectedEntityOptions = [];
    this.selectedCreatorOptions = [];
    this.creatorsInitialized = false;
    this.entityInitialized = false;

    // Reset Select Type (single + multi models)
    this.selectedDashboardTypeOptions = [];
    this.selectedDashboardType.set('');

    // Reset Sort to initial default option
    this.selectedSortOption = this.sortOptions[0];
    this.sort = this.selectedSortOption.key;
  }

  closeTemplateListModal() {
    this.templateListModal.removeModal();
    this.router.navigateByUrl('/store/dashboards');
  }

  onSubMenuTabClick(event: any) {
    this.status.set(event.event.param);
    this.subTabSelected = event.index;
    this.updateFilterVisibility();
    this.resetPagesWithoutDashboardCall();
    this.resetFiltersToInitialStateForTab();
    this.isInitialFilterLoad = true;

    this.isLoading = true;
    this.getDropDownTypes();
  }

  changeQueryParams(params: { tab?: string; status?: string }) {
    const queryParams: any = { ...this._activatedRoute.snapshot.queryParams };

    if (params.tab !== undefined) {
      if (params.tab === '') {
        delete queryParams['tab'];
      } else {
        queryParams['tab'] = params.tab;
      }
    }


    if (params.status !== undefined) {
      if (params.status === '') {
        delete queryParams['status'];
      } else {
        queryParams['status'] = params.status;
      }
    }

    this.router.navigate([], {
      relativeTo: this._activatedRoute,
      queryParams,
      queryParamsHandling: 'merge',
      replaceUrl: true,
    });
  }

  onSelectAprroveAction(action: string, objectId: string, index: number) {

    const selectedTypeVal = (this.selectedDashboardType instanceof Function) ? this.selectedDashboardType() : (this.selectedDashboardType as unknown as string);
    const isEmbeddedBi = ((selectedTypeVal ?? '') === this.embeddedTypeKey);
    const isDraftStatus = (/draft/i).test((this.status() || '').toString());
    if (action === approveConst.edit && isEmbeddedBi && isDraftStatus) {
      this.router.navigate(['/dxp/publish'], { queryParams: { fromEdit: true, dashboardId: objectId } });
      return;
    }

    switch (action) {
    case 'builder_send':
      this.router.navigate([`/dxp/manage-access/dashboard/${objectId}`], {queryParams: {edit: 'false'}});
      return;
    case approveConst.edit:
      this.openDetail(objectId, 'edit');
      return;
    case approveConst.view:
      this.openDetail(objectId);
      return;
    case approveConst.delete:
      this.onSelectCardOption(approveConst.delete, this.dashboardList[index], index);
      return;
    case approveConst.unpublish: {
      const dashboardItem = this.dashboardList[index];
      const detailTypeVal = ((dashboardItem as unknown as { detail_type?: string }).detail_type ?? '').toString().toLowerCase();
      const isEmbedded = detailTypeVal === detailTypes.embedded;

      if (isEmbedded) {
        this.unpublishEmbeddedDashboard(objectId);
      } else {
        this.callActionApi(action, index);
      }
      return;
    }
    default:
      this.callActionApi(action, index);
    }
  }

  getActionMessage(action: string) {
    switch (action) {
    case approveConst.claim:
      return 'Assigned successfully!';
    case approveConst.unclaim:
      return 'Unassigned successfully!';
    case approveConst.unpublish:
      return 'Unpublished successfully!';
    default:
      return 'Completed successfully';
    }
  }

  private unpublishEmbeddedDashboard(objectId: string): void {
    this.subs.add(
      this._apiService
        .patchMethodRequest(dxpApi.unpublishEmbeddedDashboard(objectId), {})
        .subscribe({
          next: () => {
            const message = this.getActionMessage(approveConst.unpublish);
            this._toaster.success(message);
            this.getDashboards();
          },
          error: (error) => {
            this._toaster.error(error?.error?.message);
          }
        })
    );
  }

  callActionApi(action: string, index: number) {
    const defaultPayload = {
      action: action
    };
    this.subs.add(
      this._apiService.postMethodRequest(`${dashboardEndpoints.manageRequestActions(this.dashboardList[index]?.approval_request_id ?? '')}`, defaultPayload).subscribe({
        next: () => {
          this.dashboardList.splice(index, 1);
          this.totalCount = Math.max(0, this.totalCount - 1);

          Promise.all([
            this.getDashboardSideMenu(false),
            this.getEntityTabCount()
          ]).then(() => {
            this.mergeTabCountsIntoSidebar();

            setTimeout(() => {
              this._cdr.detectChanges();
              const message = this.getActionMessage(action);
              this._toaster.success(message);
            }, 0);
          }).catch(() => {
            const message = this.getActionMessage(action);
            this._toaster.success(message);
          });
        },
        error: error => {
          this._toaster.error(error?.error?.message);
        }
      })
    );
  }

  onSortOptionChange(selectedOptions: PanelDropdownOptions) {
    // For sort, we only use the first selected option
    this.selectedSortOption = selectedOptions;
    this.sort = this.selectedSortOption.key;
    this.offsetPage = 1;
    this.offset = 0;
    this.getDashboards();
  }



  onEntityOptionsChange(selected: PanelDropdownOptions[]) {
    this.selectedEntityOptions = selected || [];

    if (this.isInitialFilterLoad) {
      return;
    }

    this.offsetPage = 1;
    this.offset = 0;
    this.getDashboards();
  }

  getSelectedEntityIdsParam(): string {
    return (this.selectedEntityOptions || []).map(o => o.id || o.key).join(',');
  }

  onCreatorOptionsChange(selected: PanelDropdownOptions[]) {
    this.selectedCreatorOptions = selected || [];

    if (this.isInitialFilterLoad) {
      return;
    }

    this.offsetPage = 1;
    this.offset = 0;
    this.getDashboards();
  }

  getSelectedCreatorIdsParam(): string {
    return (this.selectedCreatorOptions || []).map(o => o.id || o.key).join(',');
  }

  onSelectDashboardType(type: DashboardFilters) {
    this.selectedDashboardType.set(type.key);
    this.selectedDashboardTypeLabel = type;
    this.isLoading = true;
    this.dashboardList = [];
    this.offsetPage = 1;
    this.offset = 0;
    this.selectedEntityOptions = [];
    this.selectedCreatorOptions = [];
    this.creatorsInitialized = false;
    this.entityInitialized = false;
    this.isInitialFilterLoad = false;

    this.dashboardTypeChange$.next(type.key);
    this.loadDependentFiltersIfNeeded();
  }



  getUserListingData(search: string, page: number) {
    const params = { search, page };
    this.subs.add(
      this._apiService.getMethodRequest(dxpApi.userOnboardingUsers, params).subscribe({
        next: (res: DxpUserListingResponse) => {
          // Map the response to the format expected by the panel dropdown
          this.availableUsers = (res.data || []).map(user => ({
            key: user.email,
            value: user.email, // Display the email in the dropdown
            id: user.id.toString(),
            checked: false
          }));
        },
        error: () => {
          this.availableUsers = [];
        }
      })
    );
  }

  onUserSearch(searchTerm: string) {
    this.searchSubject.next(searchTerm);
  }

  getDropDownTypes() {
    // Using static dashboard type options instead of API
    const options = DASHBOARD_TYPE_OPTIONS.map((x: { id: string; name: string }) => ({ key: x.id, value: x.name, checked: false }));
    this.dashboardTypesKeys = options;

    if (this.isEmbeddedNav && options.length > 1) {
      const second = { ...options[1], checked: true };
      this.selectedDashboardType.set(second.key);
    } else if (options.length > 0) {
      this.selectedDashboardType.set(options[0].key);
    }

    this.dashboardTypeChange$.next(this.selectedDashboardType());
    this.loadDependentFiltersIfNeeded();
  }

  getEntityTabCount(): Promise<void> {
    return new Promise((resolve, reject) => {
      this._apiService.getMethodRequest(dashboardEndpoints.entityTabCount, '', true).subscribe({
        next: (response) => {
          this.tabCountData = response;
          resolve();
        },
        error: (error) => {
          console.error('Error fetching entity tab count:', error);
          reject(error);
        }
      });
    });
  }

  mergeTabCountsIntoSidebar(): void {
    if (!this.tabCountData) {
      return;
    }

    const currentTabs = this.dashboardTabs();
    const updatedTabs = currentTabs.map(tab => {
      const tabKey = Object.keys(this.tabCountData).find(key => {
        return this.tabCountData[key].param === tab.param;
      });

      if (tabKey && this.tabCountData[tabKey]) {
        const countData = this.tabCountData[tabKey];
        const updatedTab = {
          ...tab,
          showCount: countData.showCount ?? tab.showCount,
          count: countData.count ?? tab.count
        };

        if (tab.subMenus && countData.subMenus) {
          updatedTab.subMenus = tab.subMenus.map(subMenu => {
            const subMenuCountData = countData.subMenus.find((sm: any) => sm.param === subMenu.param);
            if (subMenuCountData) {
              return {
                ...subMenu,
                showCount: subMenuCountData.showCount ?? subMenu.showCount,
                count: subMenuCountData.count ?? subMenu.count
              };
            }
            return subMenu;
          });
        }

        return updatedTab;
      }

      return tab;
    });

    this.dashboardTabs.set(updatedTabs);

    const currentSelectedTab = updatedTabs[this.selectedMainTabIndex];
    if (currentSelectedTab?.subMenus) {
      this.subTabList.set(this.setSubTabName(currentSelectedTab.subMenus));
    }

    this._cdr.detectChanges();
  }

  loadDependentFiltersIfNeeded(): void {
    if (this.showSelectEntityFilter || this.showCreatedByFilter) {
      this.dependentFiltersTrigger$.next({ resetPages: false, skipDashboardCall: true });
    }
  }



  getSelectedDashboardTypeParam(): string {
    const csv = (this.selectedDashboardTypeOptions || [])
      .map(o => o.key)
      .filter(Boolean)
      .join(',');
    if (csv && csv.length > 0) {
      return csv;
    }
    // Fallback to single-select value when multi-select has no selections
    const single = (this.selectedDashboardType instanceof Function) ? this.selectedDashboardType() : (this.selectedDashboardType as unknown as string);
    return single;
  }



  fetchEntityFilterOptions$(): Observable<void> {

    if (!this.isGovAffairs) {
      return of(void 0);
    }
    const statusVal = this.status();
    const typeCsv = this.getSelectedDashboardTypeParam();

    if (!statusVal || !typeCsv) {
      this.entityList = [];
      this.selectedEntityOptions = [];
      return of(void 0);
    }
    const params = { status: statusVal, dashboard_type: typeCsv };
    return this._apiService.getMethodRequest(dashboardEndpoints.entityDropdown, params, true).pipe(
      tap((res: { id: string; name: string }[]) => {
        const items = Array.isArray(res) ? res : [];
        this.entityList = items.map((it) => ({ key: it.id, value: it.name, checked: false }));
        const ue = this._adminService.userEntity;
        if (!this.entityInitialized && this.entityList.length) {
          if (ue?.id && (!this.selectedEntityOptions || this.selectedEntityOptions.length === 0)) {
            const matches = this.entityList.filter(e => e.key === ue.id);
            if (matches.length) {
              this.selectedEntityOptions = matches.map(m => ({ ...m }));
            }
          }
          this.entityInitialized = true;
        }
        this._cdr.detectChanges();
      }),
      map(() => void 0),
      catchError(() => {
        this.entityList = [];
        this.selectedEntityOptions = [];
        return of(void 0);
      })
    );
  }

  fetchCreatorFilterOptions$(): Observable<void> {
    if (!this.isGovAffairs) {
      return of(void 0);
    }
    const statusVal = this.status();
    const typeCsv = this.getSelectedDashboardTypeParam();
    if (!statusVal || !typeCsv) {
      this.usersList = [];
      this.selectedCreatorOptions = [];
      return of(void 0);
    }
    const params = { status: statusVal, dashboard_type: typeCsv };
    return this._apiService.getMethodRequest(dashboardEndpoints.creatorDropdown, params, true).pipe(
      tap((res: { id: string; name: string }[]) => {
        const items = Array.isArray(res) ? res : [];
        const mapped = items.map((it) => ({ key: it.id, id: it.id, value: it.name, checked: false }));
        this.usersList = mapped;
        if (!this.creatorsInitialized || !this.selectedCreatorOptions || this.selectedCreatorOptions.length === 0) {
          this.selectedCreatorOptions = mapped.map(m => ({ ...m }));
          this.creatorsInitialized = true;
        } else {
          this.selectedCreatorOptions = (this.selectedCreatorOptions || []).filter((sel) => mapped.some((m) => (m.id ?? m.key) === (sel.id ?? sel.key)));
        }
        this._cdr.detectChanges();
      }),
      map(() => void 0),
      catchError(() => {
        this.usersList = [];
        this.selectedCreatorOptions = [];
        return of(void 0);
      })
    );
  }

  fetchEntityDropdownOptions(): void {
    this.fetchEntityDropdownOptionsWithCallback();
  }

  fetchEntityDropdownOptionsWithCallback(callback?: () => void): void {
    if (!this.isGovAffairs) {
      callback?.();
      return;
    }
    const statusVal = this.status();
    const typeCsv = this.getSelectedDashboardTypeParam();

    if (!statusVal || !typeCsv) {
      this.entityList = [];
      this.selectedEntityOptions = [];
      callback?.();
      return;
    }

    const params = { status: statusVal, dashboard_type: typeCsv };
    this.subs.add(
      this._apiService.getMethodRequest(dashboardEndpoints.entityDropdown, params, true).subscribe({
        next: (res: { id: string; name: string }[]) => {
          const items = Array.isArray(res) ? res : [];
          this.entityList = items.map((it) => ({ key: it.id, value: it.name, checked: false }));


          const ue = this._adminService.userEntity;
          if (!this.entityInitialized && this.entityList.length) {
            if (ue?.id && (!this.selectedEntityOptions || this.selectedEntityOptions.length === 0)) {
              const matches = this.entityList.filter(e => e.key === ue.id);
              if (matches.length) {
                this.selectedEntityOptions = matches.map(m => ({ ...m }));
              }
            }
            this.entityInitialized = true;
          }

          this._cdr.detectChanges();
          if (callback) {
            callback();
          }
        },
        error: () => {
          this.entityList = [];
          this.selectedEntityOptions = [];
          if (callback) {
            callback();
          }
        }
      })
    );
  }

  fetchCreatorDropdownOptions(): void {
    this.fetchCreatorDropdownOptionsWithCallback();
  }

  fetchCreatorDropdownOptionsWithCallback(callback?: () => void): void {
    if (!this.isGovAffairs) {
      callback?.();
      return;
    }

    const statusVal = this.status();
    const typeCsv = this.getSelectedDashboardTypeParam();

    if (!statusVal || !typeCsv) {
      this.usersList = [];
      this.selectedCreatorOptions = [];
      callback?.();
      return;
    }

    const params = { status: statusVal, dashboard_type: typeCsv };

    this.subs.add(
      this._apiService
        .getMethodRequest(dashboardEndpoints.creatorDropdown, params, true)
        .pipe(
          map((res: { id: string; name: string }[] | null | undefined) => (Array.isArray(res) ? res : [])),
          tap((items) => {
            const mapped = items.map((it) => ({ key: it.id, id: it.id, value: it.name, checked: false }));
            this.usersList = mapped;

            const hasExistingSelection = !!(this.selectedCreatorOptions && this.selectedCreatorOptions.length);
            if (!this.creatorsInitialized || !hasExistingSelection) {
              this.selectedCreatorOptions = mapped.map((m) => ({ ...m }));
              this.creatorsInitialized = true;
            } else {
              const validIds = new Set(mapped.map((m) => m.id ?? m.key));
              this.selectedCreatorOptions = (this.selectedCreatorOptions || []).filter((sel) => validIds.has(sel.id ?? sel.key));
            }

            this._cdr.detectChanges();
          }),
          catchError(() => {
            this.usersList = [];
            this.selectedCreatorOptions = [];
            return of([]);
          }),
          finalize(() => callback?.())
        )
        .subscribe()
    );
  }



  closeModal() {
    if (this._dashboardModal()) {
      this._dashboardModal().removeModal();
    }
  }

  updateFilterVisibility() {
    const currentTab = this.selectedTabView?.param || '';
    const userRole = this.currentUserRole || '';
    const currentStatus = this.status() || '';

    const roleConfig = this.filterVisibilityConfig[userRole as keyof typeof this.filterVisibilityConfig] || this.filterVisibilityConfig.DEFAULT;
    const tabConfig: FilterVisibilityConfig = (roleConfig as RoleFilterConfig)[currentTab] || this.filterVisibilityConfig.DEFAULT;

    this.showSelectTypeFilter = tabConfig.showSelectType ?? true;
    this.showSelectEntityFilter = tabConfig.showSelectEntity ?? true;
    this.showCreatedByFilter = tabConfig.showCreatedBy ?? true;
    this.showSortFilter = tabConfig.showSort ?? true;

    if (userRole === this.generalizedRoles.advanced_visulization_creator && currentTab === this.mainTabParams.approvalStatus) {
      this.showSelectTypeFilter = this.equalsIgnoreCase(currentStatus, statusParams.unpublished);
    }
  }

  initializeFiltersAndLoadDashboards(): void {
    this.getDropDownTypes();
  }

  loadDependentFiltersAndRefresh(resetPages: boolean = false): void {
    this.dependentFiltersTrigger$.next({ resetPages, skipDashboardCall: false });
  }

  ngOnDestroy(): void {
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
    this.subs.unsubscribe();
  }
}

interface MainTabsMenu {
  showCount: boolean;
  label_en?: string;
  label_ar?: string;
  name?: string;
  param: string;
  order: number;
  count?: number;
  subMenus?: SubMenu[];
  description?: string;
  subTabs?: Array<{
    key: string;
    label: string;
    rank: number;
  }>;
}

interface SubMenu {
  label: string;
  label_en?: string;
  label_ar?: string;
  param: string;
  name?: string;
  count: number;
  showCount: boolean;
  description?: string;
}


interface UserFilter {
  id: string;
  name: string;
}

interface DashboardFilters {
  key: string;
  value: string;
}



