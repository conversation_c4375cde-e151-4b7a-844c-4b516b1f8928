import { AfterViewInit, Component, EventEmitter, Output, ElementRef, OnDestroy, OnInit, ViewChild, Input, inject } from '@angular/core';
import { CommonService } from '../services/common.service';
import { Subscription } from 'rxjs';
import { CommonModule, DatePipe, NgClass } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { geoMapKeys } from '../constants/geospatial.contants';
import { CdkDrag, CdkDragDrop, CdkDragHandle, CdkDragPlaceholder, CdkDropList, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { InsightChartComponent } from '../components/analytics/insight-chart/insight-chart.component';
import { EmptyChartComponent } from '../components/empty-chart/empty-chart.component';
import { GridsterModule } from 'angular-gridster2';
import { IfpDropdownComponent } from '../../ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { IfpButtonComponent } from '../../ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpRangeSliderComponent } from '../../ifp-widgets/ifp-molecules/ifp-range-slider/ifp-range-slider.component';
import { MapDataService } from 'src/app/scad-insights/geospatial/services/map-data-management.service';
import { ChartDataManagementService } from '../services/charts-data-management.service';
import { FiltersDataManagementService } from '../services/filters-data-management.service';
import { cloneDeep } from 'lodash';
import { ActivatedRoute, Router } from '@angular/router';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';


@Component({
  selector: 'ifp-geo-indicators',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    InsightChartComponent,
    GridsterModule,
    TranslateModule,
    NgClass, CdkDrag, CdkDragHandle,
    CdkDropList,
    CdkDragPlaceholder,
    IfpDropdownComponent,
    IfpButtonComponent,
    IfpRangeSliderComponent,
    EmptyChartComponent,
    DatePipe
  ],
  templateUrl: './indicators-component.component.html',
  styleUrl: './indicators-component.component.scss'
})
export class IndicatorsComponent implements OnInit, AfterViewInit, OnDestroy {

  private readonly _themeService: ThemeService = inject(ThemeService);

  @Output() regionChangedFromChart = new EventEmitter();

  @ViewChild('leftList') leftList!: any;
  @ViewChild('rightList') rightList!: any;


  @ViewChild('leftScrollDiv') leftScrollDiv!: ElementRef;
  @ViewChild('customOneScroll') customOneScroll!: ElementRef;
  @ViewChild('customTwoScroll') customTwoScroll!: ElementRef;
  @ViewChild('rightScrollDiv') rightScrollDiv!: ElementRef;
  @ViewChild('leftIndicatorsDiv') leftIndicatorsDiv!: ElementRef;
  @ViewChild('rightIndicatorsDiv') rightIndicatorsDiv!: ElementRef;


  @Input() filterObject: any = {};

  @Input() indicatorsListObj: any = {};
  @Input() indicatorsList: any = {};
  @Input() leftIndicators: any = {};
  @Input() rightIndicators: any = {};
  @Input() leftCenterIndicators: any = {};
  @Input() rightCenterIndicators: any = {};

  @Output() actionChange = new EventEmitter();
  @Output() triggerResetFilter = new EventEmitter();
  @Output() CustomizeHide = new EventEmitter();

  public filterChartDataLoading: boolean = false;
  public isActionMode: string = '';
  public geoMapKeys = geoMapKeys;

  public defaultQueryParams = geoMapKeys.defaultQueryParams;

  public filteredLeftIndicators: any = {};
  public filteredRightIndicators: any = {};
  public filteredLeftCenterIndicators: any = {};
  public filteredRightCenterIndicators: any = {};
  private leftIndicatorBackup: any = {};
  public rightIndicatorBackup: any = {};
  public leftCenterIndicatorsBackup: any = {};
  public rightCenterIndicatorsBackup: any = {};



  public scrollAmount = 100;
  public backUpOpacity: any;
  public jobAgeGroup: any = [];

  public leftIndicatorCards: string[] = ['left'];
  public rightIndicatorCards: string[] = ['right'];
  public leftIndicatorCardsOne: string[] = [];
  public rightIndicatorCardsOne: string[] = [];
  public housingUnitCompareChartData: any = [];
  public deletedCards: Record<string, {
    leftCard: boolean, rightCard: boolean,
    leftCardSecond: boolean, rightCardSecond: boolean
  }> = {};

  public insightChartStats: any = [];
  public filteredLeftChartStats: any = {};
  public filteredLeftCenterChartStats: any = {};
  public filteredRightChartStats: any = {};
  public filteredRightCenterChartStats: any = {};
  // backup for stats
  public filteredLeftChartStatsBackup: any = {};
  public filteredLeftCenterChartStatsBackup: any = {};
  public filteredRightChartStatsBackup: any = {};
  public filteredRightCenterChartStatsBackup: any = {};


  // test
  public originalLeftChartStats: any = {};
  public originalLeftCenterChartStats: any = {};
  public originalRightChartStats: any = {};
  public originalRightCenterChartStats: any = {};

  public currentFilteredLeftChartStats: any = {};
  public currentFilteredLeftCenterChartStats: any = {};
  public currentFilteredRightChartStats: any = {};
  public currentFilteredRightCenterChartStats: any = {};

  public hasActiveFilters: boolean = false;
  public isShowingOriginalData: boolean = false;
  // end test

  public eodPopulation: number = 0;
  public totalPopulation: number = 0;
  public totalPopulation15years: number = 0;
  public totalNonCitizinPopulation: number = 0;
  public totalBuildings: number = 0;
  public totalUnits: number = 0;
  public totalJobSeekersEmirati: number = 0;
  public saveUsed: boolean = false;

  private subscription = new Subscription();

  title: string = geoMapKeys.abudhabiEmirate;
  piechartLoading = false;
  barChartLoading: boolean = false;

  public domain_id: number = geoMapKeys.defaultDomain;
  public bgOpacity: number = 60;
  public cardsBg: string = '255, 255, 255';
  public buttonClass = buttonClass;
  public userAccessPermission: any = {};
  public canScrollLeftUp: boolean = false;
  public canScrollRightUp: boolean = false;
  public defualtFilter: any;

  public isLeftAtTop: boolean = true;
  public isRightAtTop: boolean = true;
  public isLeftAtBottom: boolean = false;
  public isRightAtBottom: boolean = false;
  public language: string = 'en';
  public theme: string = 'light';

  public geoConfig: Record<string, any> = {
    opacity: 60
  }

  constructor(
    private commonService: CommonService,
    private themeService: ThemeService,
    private gisSharedService: MapDataService,
    private chartDataService: ChartDataManagementService,
    private filtersDataService: FiltersDataManagementService,
    private route: ActivatedRoute,
    private router: Router
  ) {this.themeService.defaultLang$.subscribe((lang: string) => {
      this.language = lang;
    }) } 

  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      this.domain_id = Number(params.get('domain_id'));
    });
    this.subscription = this.gisSharedService._geoMapAccessPermission$.subscribe((userAccessPermission: any) => {
      this.userAccessPermission = userAccessPermission;
    });
    this.subscription = this.chartDataService.filteringDataloading$.subscribe((loading: boolean) => {
      this.filterChartDataLoading = loading;
    });

    this.themeService.defaultTheme$.subscribe((val: any) => {
      this.theme = val;
    });

    const localStorageData = this.commonService.getLocalStorageData();
    const storedData = localStorageData ? JSON.parse(localStorageData) : {};
    this.filteredLeftChartStats = cloneDeep(storedData?.leftIndicators ?? this.filteredLeftChartStats);
    this.filteredRightCenterChartStats = cloneDeep(storedData?.centerLeftIndicators ?? this.filteredRightCenterChartStats);
    this.filteredLeftCenterChartStats = cloneDeep(storedData?.centerRightIndicators ?? this.filteredLeftCenterChartStats);
    this.filteredRightChartStats = cloneDeep(storedData?.rightIndicators ?? this.filteredRightChartStats);
    this.bgOpacity = storedData?.opacity ?? this.bgOpacity;
    this.defualtFilter = cloneDeep(geoMapKeys.defaultQueryParams)
    this.changeDomain(this.domain_id);
    this.chartDataService.setCurrentDomain(this.domain_id)
  }

  ngAfterViewInit(): void {}

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  applyFilter(filter: any) {
    this.filterObject = filter;

    //this.chartDataService.setCurrentDomain(this.domain_id);
    this.chartDataService.getFilteredSummaryData(this.filterObject);

    this.hasActiveFilters = true; 
    this.isShowingOriginalData = false;
    this.chartDataService.filteredData$.subscribe(data => {
      if (Object.keys(data).length > 0) { 
        this.getFiltredSummary(data, false);
      }
    });
  }

  changeDomain(event: any) {
    if(Number(event) <= 0 || Number(event) > 3) {
      this.router.navigate(['/404']);
    }else {
      this.domain_id = Number(event);
      this.chartDataService.setCurrentDomain(this.domain_id);
      const data = this.chartDataService.getOriginalData();
      this.hasActiveFilters = false; 
      this.isShowingOriginalData = true;
      this.getFiltredSummary(data, true);
    }  
  }

  /**
   *
   * @param data get filtered data based on indicators and current domain
   */
  public getFiltredSummary(data: any, isOriginalData: boolean = true) {
    if (this.domain_id != 3) {
      this.totalPopulation = data.summary.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsList.TOTAL)?.POPULATION;
      this.eodPopulation = data.summary.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsList.EOAD)?.POPULATION;
      this.totalNonCitizinPopulation = data.summary.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsList.POPULATION_NON_CITIZEN)?.POPULATION;
      this.totalPopulation15years = data.summary.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsList.POPULATION_OVER_15)?.POPULATION;
      this.totalJobSeekersEmirati = data.summary.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsList.TOTAL_JOB_SEEKERS_EMIRATI)?.POPULATION;
    } else {
      this.totalBuildings = data.summary.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsList.BUILDING_TOTAL)?.VALUE;
      this.totalUnits = data.summary.find((x: { INDICATOR: string; }) => x.INDICATOR == this.indicatorsList.UNIT_TOTAL)?.VALUE;
    }
    // group data based on indicator
    const groupedData = data.summary.reduce((acc: any, item: any) => {
      const { INDICATOR } = item;
      if (!acc[INDICATOR]) {
        acc[INDICATOR] = [];
      }
      acc[INDICATOR].push(item);
      return acc;
    }, {});


    this.insightChartStats = Object.keys(groupedData).map(indicator => ({
      indicator,
      data: groupedData[indicator]
    }));

    // Process indicators based on data type
    if (isOriginalData) {
      this.storeOriginalData();
    } else {
      this.storeFilteredData();
    }

    // store both (original or filtered) data for view 
    this.applyCurrentDataView();
  }

  // store original data when loading initial data
  private storeOriginalData(): void {
    // Get right indicators
    let rightIndicators: any = {};
    if (this.filteredRightChartStatsBackup[this.domain_id]?.length > this.filteredRightChartStats[this.domain_id]?.length) {
      rightIndicators = this.filteredRightChartStatsBackup[this.domain_id]?.length ? this.filteredRightChartStatsBackup[this.domain_id] : this.rightIndicators;
    } else {
      rightIndicators = this.filteredRightChartStats[this.domain_id]?.length ? this.filteredRightChartStats[this.domain_id] : this.rightIndicators;
    }
    const originalRightIndicators = rightIndicators.filter((indicator: any) => indicator.domains.includes(this.domain_id));

    // Get left indicators  
    let leftIndicators: any = {};
    if (this.filteredLeftChartStatsBackup[this.domain_id]?.length > this.filteredLeftChartStats[this.domain_id]?.length) {
      leftIndicators = this.filteredLeftChartStatsBackup[this.domain_id]?.length ? this.filteredLeftChartStatsBackup[this.domain_id] : this.leftIndicators;
    } else {
      leftIndicators = this.filteredLeftChartStats[this.domain_id]?.length ? this.filteredLeftChartStats[this.domain_id] : this.leftIndicators;
    }
    const originalLeftIndicators = leftIndicators.filter((indicator: any) => indicator.domains.includes(this.domain_id));

    // Get left Center indicators  
    let originalLeftCenterIndicators: any = {};
    if (Object.keys(this.filteredLeftCenterChartStats).length && Array.isArray(this.filteredLeftCenterChartStats[this.domain_id])) {
      originalLeftCenterIndicators = this.filteredLeftCenterChartStats[this.domain_id];
    } else {
      originalLeftCenterIndicators = this.leftCenterIndicators;
    }

    // Get right Center indicators  
    let originalRightCenterIndicators: any = {};
    if (Object.keys(this.filteredRightCenterChartStats).length && Array.isArray(this.filteredRightCenterChartStats[this.domain_id])) {
      originalRightCenterIndicators = this.filteredRightCenterChartStats[this.domain_id];
    } else {
      originalRightCenterIndicators = this.rightCenterIndicators;
    }

    // Store original mapped data
    this.originalLeftChartStats[this.domain_id] = this.mapIndicatorsToCharts(originalLeftIndicators);
    this.originalLeftCenterChartStats[this.domain_id] = this.mapIndicatorsToCharts(originalLeftCenterIndicators);
    this.originalRightChartStats[this.domain_id] = this.mapIndicatorsToCharts(originalRightIndicators);
    this.originalRightCenterChartStats[this.domain_id] = this.mapIndicatorsToCharts(originalRightCenterIndicators);
  }

  // store filtered data after filters applied
  private storeFilteredData(): void {
    // Get right indicators
    let rightIndicators: any = {};
    if (this.filteredRightChartStatsBackup[this.domain_id]?.length > this.filteredRightChartStats[this.domain_id]?.length) {
      rightIndicators = this.filteredRightChartStatsBackup[this.domain_id]?.length ? this.filteredRightChartStatsBackup[this.domain_id] : this.rightIndicators;
    } else {
      rightIndicators = this.filteredRightChartStats[this.domain_id]?.length ? this.filteredRightChartStats[this.domain_id] : this.rightIndicators;
    }
    const filteredRightIndicators = rightIndicators.filter((indicator: any) => indicator.domains.includes(this.domain_id));

    // Get left indicators  
    let leftIndicators: any = {};
    if (this.filteredLeftChartStatsBackup[this.domain_id]?.length > this.filteredLeftChartStats[this.domain_id]?.length) {
      leftIndicators = this.filteredLeftChartStatsBackup[this.domain_id]?.length ? this.filteredLeftChartStatsBackup[this.domain_id] : this.leftIndicators;
    } else {
      leftIndicators = this.filteredLeftChartStats[this.domain_id]?.length ? this.filteredLeftChartStats[this.domain_id] : this.leftIndicators;
    }
    const filteredLeftIndicators = leftIndicators.filter((indicator: any) => indicator.domains.includes(this.domain_id));

    // Get left Center indicators  
    let filteredLeftCenterIndicators: any = {};
    if (Object.keys(this.filteredLeftCenterChartStats).length && Array.isArray(this.filteredLeftCenterChartStats[this.domain_id])) {
      filteredLeftCenterIndicators = this.filteredLeftCenterChartStats[this.domain_id];
    } else {
      filteredLeftCenterIndicators = this.leftCenterIndicators;
    }

    // Get right Center indicators  
    let filteredRightCenterIndicators: any = {};
    if (Object.keys(this.filteredRightCenterChartStats).length && Array.isArray(this.filteredRightCenterChartStats[this.domain_id])) {
      filteredRightCenterIndicators = this.filteredRightCenterChartStats[this.domain_id];
    } else {
      filteredRightCenterIndicators = this.rightCenterIndicators;
    }

    // Store current filtered data
    this.currentFilteredLeftChartStats[this.domain_id] = this.mapIndicatorsToCharts(filteredLeftIndicators);
    this.currentFilteredLeftCenterChartStats[this.domain_id] = this.mapIndicatorsToCharts(filteredLeftCenterIndicators);
    this.currentFilteredRightChartStats[this.domain_id] = this.mapIndicatorsToCharts(filteredRightIndicators);
    this.currentFilteredRightCenterChartStats[this.domain_id] = this.mapIndicatorsToCharts(filteredRightCenterIndicators);
  }


  private applyCurrentDataView(): void {
    if (this.isShowingOriginalData && this.hasActiveFilters) {
      // Show original data
      this.filteredLeftChartStats[this.domain_id] = cloneDeep(this.originalLeftChartStats[this.domain_id]);
      this.filteredLeftCenterChartStats[this.domain_id] = cloneDeep(this.originalLeftCenterChartStats[this.domain_id]);
      this.filteredRightChartStats[this.domain_id] = cloneDeep(this.originalRightChartStats[this.domain_id]);
      this.filteredRightCenterChartStats[this.domain_id] = cloneDeep(this.originalRightCenterChartStats[this.domain_id]);
    } else if (this.hasActiveFilters) {
      // Show filtered data
      this.filteredLeftChartStats[this.domain_id] = cloneDeep(this.currentFilteredLeftChartStats[this.domain_id]);
      this.filteredLeftCenterChartStats[this.domain_id] = cloneDeep(this.currentFilteredLeftCenterChartStats[this.domain_id]);
      this.filteredRightChartStats[this.domain_id] = cloneDeep(this.currentFilteredRightChartStats[this.domain_id]);
      this.filteredRightCenterChartStats[this.domain_id] = cloneDeep(this.currentFilteredRightCenterChartStats[this.domain_id]);
    } else {
      // No filters applied, show original data
      this.filteredLeftChartStats[this.domain_id] = cloneDeep(this.originalLeftChartStats[this.domain_id]);
      this.filteredLeftCenterChartStats[this.domain_id] = cloneDeep(this.originalLeftCenterChartStats[this.domain_id]);
      this.filteredRightChartStats[this.domain_id] = cloneDeep(this.originalRightChartStats[this.domain_id]);
      this.filteredRightCenterChartStats[this.domain_id] = cloneDeep(this.originalRightCenterChartStats[this.domain_id]);
    }

    // Update backups
    this.updateBackups();
  }
  
  // Update backups after applying data view
  private updateBackups(): void {

    if (this.filteredRightChartStats[this.domain_id]?.length == this.filteredRightChartStatsBackup[this.domain_id]?.length ||
        !this.filteredRightChartStatsBackup[this.domain_id]?.length) {
      this.filteredRightChartStatsBackup[this.domain_id] = this.filteredRightChartStats[this.domain_id];
    }

    if (this.filteredLeftChartStats[this.domain_id]?.length == this.filteredLeftChartStatsBackup[this.domain_id]?.length ||
        !this.filteredLeftChartStatsBackup[this.domain_id]?.length) {
      this.filteredLeftChartStatsBackup[this.domain_id] = this.filteredLeftChartStats[this.domain_id];
    }

    if (this.filteredLeftCenterChartStats[this.domain_id]?.length == this.filteredLeftCenterChartStatsBackup[this.domain_id]?.length ||
        !this.filteredLeftCenterChartStatsBackup[this.domain_id]?.length) {
      this.filteredLeftCenterChartStatsBackup[this.domain_id] = this.filteredLeftCenterChartStats[this.domain_id];
    }

    if (this.filteredRightCenterChartStats[this.domain_id]?.length == this.filteredRightCenterChartStatsBackup[this.domain_id]?.length ||
        !this.filteredRightCenterChartStatsBackup[this.domain_id]?.length) {
      this.filteredRightCenterChartStatsBackup[this.domain_id] = this.filteredRightCenterChartStats[this.domain_id];
    }
  }

  // get combined charts stats 
  getCombinedChartStatsData(filteredChartStats: any, originalChartStats: any): Array<{filtered: any, original: any, index: number}> {
    const filtered = filteredChartStats[this.domain_id] || [];
    const original = originalChartStats[this.domain_id] || [];
    const maxLength = Math.max(filtered.length, original.length);
    const combined = [];
    for (let i = 0; i < maxLength; i++) {
      combined.push({
        filtered: filtered[i] || null,
        original: original[i] || null,
        index: i
      });
    }
    return combined;
  }

  mapIndicatorsToCharts(indicators: any[]): any[] {
    return indicators
      .map((indicator: any) => ({
        indicator: indicator.indicator,
        domains: indicator.domains,
        charts: this.insightChartStats.filter(
          (stat: any) => stat.indicator === indicator.indicator
        )
      }))
      .filter(indicator => indicator.charts.length > 0); // Only keep indicators with charts
  }

  isObjectEmpty(obj: object): boolean {
    return Object.keys(obj).length === 0;
  }

  setCardAction(action: string) {
    this.isActionMode = action;
    if (this.isActionMode == geoMapKeys.customize) {
      this.leftIndicatorBackup = cloneDeep(this.filteredLeftChartStats[this.domain_id]);
      this.rightIndicatorBackup = cloneDeep(this.filteredRightChartStats[this.domain_id]);
      this.leftCenterIndicatorsBackup = cloneDeep(this.filteredRightCenterChartStats[this.domain_id]);
      this.rightCenterIndicatorsBackup = cloneDeep(this.filteredLeftCenterChartStats[this.domain_id]);
      this.backUpOpacity = cloneDeep(this.bgOpacity);
    }
  }

  drop(event: CdkDragDrop<string[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex
      );
    }
  }

  deleteSection(type: string) {
    if (!this.deletedCards[this.domain_id]) {
      this.deletedCards[this.domain_id] = {
        leftCard: false,
        rightCard: false,
        leftCardSecond: false,
        rightCardSecond: false
      };
    }
    if (type == 'left') {
      this.deletedCards[this.domain_id].leftCardSecond = cloneDeep(this.deletedCards?.[this.domain_id]?.leftCard);
      this.deletedCards[this.domain_id].leftCard = !this.deletedCards?.[this.domain_id]?.leftCard;
    } else {
      this.deletedCards[this.domain_id].rightCardSecond = cloneDeep(this.deletedCards?.[this.domain_id]?.rightCard);
      this.deletedCards[this.domain_id].rightCard = !this.deletedCards?.[this.domain_id].rightCard;
    }
  }

  scroll(type: string, side: string): void {

    let element: any;
    if (side == 'left') {
      element = this.leftScrollDiv.nativeElement;
    } else if (side == 'customOne') {
      element = this.customOneScroll.nativeElement;
    } else if (side == 'customTwo') {
      element = this.customTwoScroll.nativeElement;
    } else {
      element = this.rightScrollDiv.nativeElement;
    }

    if (side == 'left') {
      if (element.scrollTop == 0) {
        this.canScrollLeftUp = false;
      }
    }

    if (side == 'right') {
      if (element.scrollTop == 0) {
        this.canScrollRightUp = false;
      } else {
        this.canScrollRightUp = true;
      }
    }

    element.scrollTop = type == 'up' ? element.scrollTop + 50 : element.scrollTop - 50;
  }

  setBgTransparent(opacity: number) {
    this.bgOpacity = opacity;
  }

  saveChanges() {
    this.geoConfig['opacity'] = this.bgOpacity;
    this.actionChange.emit({ event: 'save', config: this.geoConfig });
    this.isActionMode = 'save';
    const storData = {
      leftIndicators: this.filteredLeftChartStats,
      centerLeftIndicators: this.filteredRightCenterChartStats,
      centerRightIndicators: this.filteredLeftCenterChartStats,
      rightIndicators: this.filteredRightChartStats,
      opacity: this.bgOpacity
    };
    this.commonService.setLocalStorage(storData);
    this._themeService.geoConfig.set(this.geoConfig);
    this.saveUsed = true;
  }

  cancelCustomize() {
    // temporary fix, to be revised later 
    window.location.reload();
    // if (!this.deletedCards[this.domain_id]) {
    //   this.deletedCards[this.domain_id] = {
    //     leftCard: false,
    //     rightCard: false,
    //     leftCardSecond: false,
    //     rightCardSecond: false
    //   };
    // }
    // this.filteredLeftChartStats[this.domain_id] = cloneDeep(this.leftIndicatorBackup);
    // this.filteredRightChartStats[this.domain_id] = cloneDeep(this.rightIndicatorBackup);
    // this.filteredRightCenterChartStats[this.domain_id] = cloneDeep(this.rightCenterIndicatorsBackup);
    // this.filteredLeftCenterChartStats[this.domain_id] = cloneDeep(this.leftCenterIndicatorsBackup);
    // //this.rightCenterIndicatorsBackup = cloneDeep(this.rightCenterIndicatorsBackup);
    // this.bgOpacity = cloneDeep(this.backUpOpacity);
    // this.isActionMode = 'cancel';
    // this.actionChange.emit(this.isActionMode);
  }


  onScroll(event: Event, position: string): void {
    const target = event.target as HTMLElement;
    const scrollHeight = target.scrollHeight;
    const scrollTop = target.scrollTop;
    const clientHeight = target.clientHeight + 2;
    const currentPos = Math.round(scrollTop + clientHeight);

    const atBottom = currentPos >= scrollHeight;
    const atTop = scrollTop === 0;

    if (position === 'left') {
      this.isLeftAtTop = atTop;
      this.isLeftAtBottom = atBottom;
    } else if (position === 'right') {
      this.isRightAtTop = atTop;
      this.isRightAtBottom = atBottom;
    }
  }

  regionChangeFromChart(event: any) {
    this.regionChangedFromChart.emit(event);
  }

  resetFilters(event: any) {
    this.triggerResetFilter.emit();
  }

}

