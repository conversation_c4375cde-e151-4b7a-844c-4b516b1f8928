import { Location, NgClass } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { buttonClass, buttonColor } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { IfpDashboardBuilderComponent } from '../../pages/ifp-dashboard-builder/ifp-dashboard-builder.component';

@Component({
    selector: 'ifp-dashboard-view',
    templateUrl: './ifp-dashboard-view.component.html',
    styleUrl: './ifp-dashboard-view.component.scss',
    imports: [TranslateModule, IfpButtonComponent, NgClass, IfpDashboardBuilderComponent]
})
export class IfpDashboardViewComponent {
  @Input() mode: 'preview' | 'detail' = 'preview';
  @Input() selectedCards: any = [];
  @Output() previewAction: EventEmitter<boolean> = new EventEmitter<boolean>();
  public message: string = 'Would you like to proceed and save the current dashboard?';
  public buttonClass = buttonClass;
  public buttonColor = buttonColor;


  constructor(public location: Location) { }

  onMessageResponse(status: boolean) {
    this.previewAction.emit(status);
  }
}
