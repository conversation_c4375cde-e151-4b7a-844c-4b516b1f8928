import { Component, EventEmitter, Input, Output} from '@angular/core';
import { DbToolbarIcon } from '../../organism/ifp-chart-toolbar/ifp-chart-toolbar.interface';
import { NgClass } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { chartTypes } from '../../organism/ifp-chart-toolbar/ifp-chart-toolbar.constants';

@Component({
    selector: 'ifp-icon-selector',
    imports: [NgClass, TranslateModule],
    templateUrl: './ifp-icon-selector.component.html',
    styleUrl: './ifp-icon-selector.component.scss'
})
export class IfpIconSelectorComponent {

  @Input() icon!: DbToolbarIcon;
  @Input() isSmall: boolean = false;
  @Input() enableTitle: boolean = true;


  @Output() selectIcon: EventEmitter<DbToolbarIcon> = new EventEmitter<DbToolbarIcon>();


  private chartTypes = chartTypes;

  selectChartType(item: DbToolbarIcon) {
    this.selectIcon.emit(item);
    this.chartTypes.map(x => x.selected = false);
    item.selected = true;
  }
}
