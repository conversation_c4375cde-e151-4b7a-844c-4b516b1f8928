@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
}

.ifp-db-list-card {
  $card-border-radius: 40px;
  position: relative;
  border: 6px solid $ifp-color-white;
  border-radius: $card-border-radius;
  background-color: $ifp-color-white;
  box-shadow: inset 0 39px 44px -20px rgba(203, 212, 209, 0.2), 0 104px 95px -50px rgba(98, 110, 106, 0.16);
  padding-bottom: $spacer-3;

  &__options {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1;
  }

  &__thumb {
    border-radius: $card-border-radius $card-border-radius 0 0;
    background-color: $ifp-color-grey-bg;
    padding: $spacer-2 $spacer-2 $spacer-4;
  }

  &__thumb-img {
    width: 100%;
    height: 220px;
    border-radius: 30px 30px 0 0;
    background-size: cover;
    background-position: top left;
  }

  &__footer {
    padding: $spacer-3 $spacer-3 $spacer-0;
  }

  &__title {
    font-size: $ifp-fs-4;
    font-weight: $fw-semi-bold;
    @include lineLimit(2);
    min-height: 40px;
    margin-bottom: $spacer-2;
  }

  &__sub-title,
  &__text {
    color: $ifp-color-grey-9;
  }

  &__sub-title {
    margin-bottom: $spacer-1;
  }
  &__sub-title-value{
    font-weight: $fw-semi-bold;
  }

  &__action-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-db-list-card {
    background-color: $ifp-color-grey-7;
    border: 6px solid $ifp-color-grey-7;

    &__text {
      color: $ifp-color-grey-2;
    }
  }
}
