@use "../../../../assets/ifp-styles/abstracts" as *;
.ifp-footer {
  padding: 10px $spacer-0 $spacer-5;
  background-color: $ifp-color-grey-4;
  &__logo-sec,
  &__wrapper {
    display: flex;
    justify-content: space-between;
  }
  &__logo-sec {
    align-items: flex-end;
    .ifp-footer__copyright-links {
      padding-bottom: $spacer-3;
    }
  }
  &__wrapper {
    padding: $spacer-4 $spacer-0;
    margin: $spacer-0 (-$spacer-3);
  }
  // &__sec-1,
  // &__sec-2,
  // &__sec-3 {
  //   padding: $spacer-0 $spacer-3;
  // }
  // &__sec-1,
  // &__sec-3 {
  //   width: 30%;
  // }
  // &__sec-2 {
  //   display: flex;
  //   width: 40%;
  // }
  &__contact-text,
  &__sub-head,
  &__desc,
  &__link {
    color: $ifp-color-secondary-grey;
  }

  &__sub-head {
    font-size: 2rem;
    font-weight: $fw-semi-bold;
    margin-bottom: $spacer-4;
  }
  &__desc {
    margin-bottom: $spacer-4;
  }
  &__links-wrappper {
    display: flex;
    margin: $spacer-0 (-$spacer-3);
  }
  &__quick-links {
    margin: $spacer-0 $spacer-3;
  }
  &__link-item {
    min-width: 130px;
    margin-bottom: $spacer-2;
    &:last-child {
      margin-bottom: $spacer-0;
    }
  }
  &__link {
    transition: 0.3s;
    display: inline-block;
    &--active,
    &:hover {
      color: $ifp-color-blue-hover;
    }
    &--active {
      font-weight: $fw-medium;
    }
  }
  &__contact-text {
    display: flex;
    margin-bottom: $spacer-2;
    color: $ifp-color-black;
  }
  &__link-icon {
    position: relative;
    top: 3px;
    margin-right: $spacer-2;
    min-width: 16px;
  }
  &__inupt-sec {
    display: flex;
    position: relative;
    ifp-button {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  &__input-btn {
    padding: $spacer-2 $spacer-4;
    border-radius: 30px;
    color: $ifp-color-white;
    background-color: $ifp-color-secondary-blue;
    position: absolute;
    top: 1px;
    right: 0;
    cursor: pointer;
    &:hover {
      background-color: $ifp-color-link;
    }
  }
  &__input-text {
    display: block;
    width: 100%;
    color: $ifp-color-black;
    border-radius: 30px;
    border: 1px solid $ifp-color-grey-3;
    padding: $spacer-2 $spacer-3;
    @include placeholder($ifp-color-grey-2);
  }
  &__copyright {
    padding-top: $spacer-3;
    border-top: 1px solid $ifp-color-grey-1;
    display: flex;
    justify-content: space-between;
  }
  &__copyright-text {
    color: $ifp-color-grey-5;
  }
  &__copyright-links {
    margin: $spacer-0 (-$spacer-3);
    .ifp-footer__link {
      margin: $spacer-0 $spacer-3;
    }
  }

  &__logo {
    background-color: $ifp-color-grey-4;
    padding-right: $spacer-2;
    position: relative;
    bottom: -20px;
  }
}

:host-context([dir="rtl"]) {
  .ifp-footer {
    &__link-icon {
      margin-left: $spacer-2;
      margin-right: $spacer-0;
    }
    &__inupt-sec {
      ifp-button {
        left: 0;
        right: auto;
      }
    }
    &__input-btn {
      left: 0;
      right: auto;
    }
    &__logo {
      padding-left: $spacer-2;
      padding-right: $spacer-0;
    }
  }
}

@include mobile-tablet {
  .ifp-footer {
    &__copyright {
      flex-direction: column-reverse;
      text-align: center;
    }
    &__copyright-links {
      margin-bottom: $spacer-3;
      .ifp-footer__link {
        display: inline-block;
        padding: $spacer-0 $spacer-3;
        margin: $spacer-1 $spacer-0;
      }
    }
    &__link-item {
      min-width: 0;
    }
  }
}

@include mobile {
  .ifp-footer {
   padding: $spacer-3 $spacer-0;
    &__logo-sec {
      display: block;
      text-align: center;
      img {
        width: 90px;
      }
      a:first-child img {
        width: 130px;
      }
    }
    &__sub-head {
      font-size: 1.8rem;
    }
    &__wrapper {
      padding: $spacer-3 $spacer-0;
      flex-direction: column-reverse;
    }
    &__sec-1 {
      display: none;
    }
    &__sec-2 {
      margin-left: $spacer-0;
      margin-bottom: $spacer-0;
    }
    &__newsletter-head {
      margin-bottom: $spacer-2;
    }
    &__copyright {
      padding: $spacer-3 $spacer-0 $spacer-2;
    }
    &__logo {
      display: none;
    }
  }
}
