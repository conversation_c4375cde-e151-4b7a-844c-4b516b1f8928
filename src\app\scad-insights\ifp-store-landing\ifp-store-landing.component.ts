import { animate, style, transition, trigger } from '@angular/animations';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit, Signal, viewChild } from '@angular/core';
import { buttonClass, buttonIconPosition } from '../core/constants/button.constants';
import { Title } from '@angular/platform-browser';
import { title } from '../core/constants/header.constants';
import { CardsList, IfpToolsPageComponent } from 'src/app/shared/organism/ifp-tools-page/ifp-tools-page.component';
import { slaService } from '../core/services/sla/sla.service';
import { IfpModalComponent } from "../ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component";
import { IfpExploratorySelectPageComponent } from "../../ifp-analytics/ifp-exploratory/ifp-exploratory-select-page/ifp-exploratory-select-page.component";
import { ifpColors } from '../core/constants/color.constants';

@Component({
  selector: 'app-ifp-store-landing',
  templateUrl: './ifp-store-landing.component.html',
  styleUrls: ['./ifp-store-landing.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IfpToolsPageComponent, IfpModalComponent, IfpExploratorySelectPageComponent],
  animations: [trigger('fadeIn', [
    transition(':enter', [
      style({ opacity: 0 }),
      animate('400ms', style({
        opacity: 1
      }))
    ])
  ])]
})
export class IfpStoreLandingComponent implements OnInit {

  private readonly _dashboardModal: Signal<IfpModalComponent> = viewChild.required<IfpModalComponent>('dashboardModal');

  public currentIndex: number = 0;
  public timeInterval!: any;
  public descIndex: number = 0;
  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;

  public cards: CardsList[] = [
    {
      id: 'myBookmarks',
      title: 'My Bookmarks',
      description: 'MY Bookmarks feature empowers users to organise and utilize their saved indicators, analytical apps and dashboards  efficiently.',
      img: '../../../assets/images/prep-help/my_bookmarks.svg',
      url: '/my-apps',
      buttonLabel: 'View My Bookmarks',
      buttonSecondLabel: '',
      secondUrl: '',
      visible: true,
      external: false
    },
    // {
    //   id: 'visualizationBuilder',
    //   title: 'Advanced Visualization Builder',
    //   description: 'Easily create and customize interactive dashboards from multiple data sources. Design personalized visual experiences that help you explore insights, track performance, and make informed decisions faster.',
    //   img: '../../../assets/images/prep-help/my_dashboards.svg',
    //   url: '/store/dashboards',
    //   buttonLabel: 'View Visualizations',
    //   buttonSecondLabel: '',
    //   secondUrl: '',
    //   visible: this._sla.permission().dashBoardBuilder ?? false,
    //   beta: true,
    //   external: false
    // },
    {
      id: 'visualizationBuilder',
      title: 'Visualization Builder',
      description: 'Easily create and customize interactive dashboards from multiple data sources. Design personalized visual experiences that help you explore insights, track performance, and make informed decisions faster.',
      img: '../../../assets/images/prep-help/my_dashboards.svg',
      url: '/store/dashboards-basic',
      secondUrl: '/store/dashboards',
      buttonLabel: 'Basic',
      buttonSecondLabel: 'Advanced',
      isSecondButton: true,
      visible: this._sla.permission().dashBoardBuilder,
      beta: true,
      external: false
    },
    {
      id: 'dataPreparation',
      title: 'Data Preparation',
      description: 'Refine Your Data: Seamlessly extract, transform, and load your datasets for optimal analysis and insights.',
      img: '../../../assets/images/prep-help/data_preparation.svg',
      url: this._sla.permission().dataPrepBasic ? '/analytics/data-preparation/upload-data' : (this._sla.permission().dataPrepAdv ? '/analytics/advance' : ''),
      secondUrl: '/analytics/advance',
      buttonLabel: this._sla.permission().dataPrepBasic ? 'Basic Mode' : (this._sla.permission().dataPrepAdv ? 'Expert Mode' : ''),
      buttonSecondLabel: 'Expert Mode',
      isSecondButton: this._sla.permission().dataPrepBasic && this._sla.permission().dataPrepAdv,
      visible: (this._sla.permission().dataPrepAdv || this._sla.permission().dataPrepBasic) ?? false,
      beta: true,
      external: false
    },
    {
      id: 'dataExploration',
      title: 'Data Exploration',
      description: 'Investigate relationships between variables through correlation analysis, gaining deeper insights into your data.',
      img: '../../../assets/images/prep-help/find_folder.svg',
      url: 'analytics/exploratory',
      buttonLabel: 'Explore Data',
      buttonSecondLabel: '',
      secondUrl: '',
      visible: this._sla.permission().dataExploration ?? false,
      beta: true,
      external: false
    },
    {
      id: 'advancedAnalytics',
      title: 'Advanced Analytics',
      description: 'Effortlessly generate powerful machine learning models with advanced automation and optimization.',
      img: '../../../assets/images/prep-help/analytics.svg',
      url: 'analytics/auto-ml/upload-data?enableAutoMl=true',
      // url: 'analytics/exploratory',
      buttonLabel: 'Start',
      buttonSecondLabel: '',
      secondUrl: '',
      visible: this._sla.permission().autoML ?? false,
      beta: true,
      external: false
    },
    {
      title: 'Table Builder',
      description: 'A Table Builder is a tool or interface that allows users to create and customize data tables easily—often without writing code.',
      img: '../../../assets/images/prep-help/data_preparation.svg',
      // url: 'https://zoho.scad.gov.ae:8443/login/login.jsp?LOGIN_MODE=saml&ORGID=307',
      url: 'table-builder',
      buttonLabel: 'Start',
      buttonSecondLabel: '',
      secondUrl: '',
      visible: this._sla.permission().autoML ?? false,
      beta: true,
      external: false,
      id: ''
    }
  ];

    public dashboardBuilderCard = [
    {
      id: 'biDasboard',
      title: 'Publish BI Dashboard',
      description: 'Seamlessly integrate and display interactive dashboards with Embed Dashboard. Access real-time insights within your platform effortlessly.',
      url: '',
      color: ifpColors.purple,
      icon: 'ifp-icon-dashboard-circle',
      external: false,
      hide: false
    },
    {
      id: 'createDashboard',
      title: 'Create Native Dashboard',
      description: 'Build fully integrated, high-performance dashboards with Create Native Dashboard. Tailor insights to your platform for a seamless user experience.',
      url: '/store/dashboards',
      color: ifpColors.cyan,
      icon: 'ifp-icon-native-dashboard',
      external: false,
      hide:false
    }
    // {
    //   id: 'tableBuilder',
    //   title: 'Build Tables',
    //   description: 'Effortlessly create and customize data tables with Build Tables. Organize, analyze, and present information with clarity and precision.',
    //   url: 'https://***********:8443/login/login.jsp?LOGIN_MODE=saml&ORGID=307',
    //   color: ifpColors.yellow,
    //   icon: 'ifp-icon-table-margin',
    //   external: true,
    //   hide: false
    // }
  ]

  constructor(private _cdr: ChangeDetectorRef, private _titleService: Title, private _sla: slaService) {
    this._titleService.setTitle(`${title.bayaan} | Store`);
  }

  ngOnInit() {
    this.timeInterval = setInterval(() => {
      if (this.cards.length) {
        this.currentIndex = (this.currentIndex + 1) % this.cards.length;
        this._cdr.detectChanges();
      }
      this.descIndex++;
      // Check if we have processed all elements in the array
      if (this.descIndex === (this.cards.length - 1)) {
        // Clear the interval after one cycle
        clearInterval(this.timeInterval);
      }
    }, 1000); // Automatically switch items every 1` seconds
  }

  onSelectCard(id: string) {
    if (id === 'visualizationBuilder') {
      this._dashboardModal().createElement();
    }
  }

  closeModal() {
    if (this._dashboardModal()) {
      this._dashboardModal().removeModal();
    }
  }

  ngOnDestroy(): void {
    this.closeModal();
  }

}

