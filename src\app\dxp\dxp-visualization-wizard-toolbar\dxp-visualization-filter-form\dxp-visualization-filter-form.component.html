<form class="ifp-filter-form__new-filter" [formGroup]="addFilterForm">
  <div class="ifp-filter-form__filter-item">
    <p class="ifp-filter-form__filter-label">{{'Column' | translate}}</p>
    <app-ifp-db-dropdown  [searchEnable]="true" [bodyBind]="true"  [autoCalculatePosition]="true" [disableTranslation]="true" formControlName="column" class="ifp-adv-tool__dropdown" [isMultiSelect]="false" [placeholder]="'Enter Filter Name'" [options]="columnList()" (singleSelected)="onSelectColumn($event)" [key]="'name'" [defaultSelect]="false"></app-ifp-db-dropdown>
  </div>

  <div class="ifp-filter-form__filter-item">
    <p class="ifp-filter-form__filter-label">{{'Filter Label' | translate}}</p>
    <input type="text" [placeholder]="'Enter Filter Name' | translate" formControlName="label" class="ifp-input">
  </div>

  <div class="ifp-filter-form__filter-item">
    <p class="ifp-filter-form__filter-label">{{'Default Filter Value' | translate}}</p>
    <app-ifp-db-dropdown [defaultSelect]="false"  [searchEnable]="true" [bodyBind]="true" [autoCalculatePosition]="true" [isMultiSelect]="true" [disabled]="!columnfiltervalues().length" [options]="columnfiltervalues()" [multipleSelectedItems]="addFilterForm.value.values"  [placeholder]="'Select Default Filter Value'"  [key]="'id'" formControlName="values"   [disableTranslation]="true"></app-ifp-db-dropdown>
  </div>
  <div class="ifp-filter-form__btn-sec">
    <ifp-button [label]="isEdit() ? ('Update Filter' | translate) : ('Add Filter' | translate)" [iconClass]="isEdit() ? '' : 'ifp-icon-plus'" (ifpClick)="onAddFilter()" [buttonClass]="buttonClass.normalAplabetic +' '+ (addFilterForm.valid && !loader() ? buttonClass.primary : buttonClass.disabled)" class="ifp-filter-form__filter-btn"></ifp-button>
    @if(isEdit() || enableCancel()) {
    <ifp-button [label]="'Cancel'" (ifpClick)="onCancelFilter()" [buttonClass]="buttonClass.normalAplabetic +' '+ buttonClass.secondary" class="ifp-filter-form__filter-btn"></ifp-button>
    }

  </div>
</form>
