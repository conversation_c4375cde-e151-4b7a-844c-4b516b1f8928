export interface DashboadListItem {
  createdDate: string;
  sharedDate?:string;
  recepientEmails?:string[];
  shareEmail?:string;
  id: string;
  logo: {
    content: string;
    type: string;
  },
  logoType: string;
  name: string;
  thumbnailLight: {
    content: string;
    type: string;
  },
  thumbnailDark: {
    content: string;
    type: string;
  },
}

export interface DashboardDetail {
  id: string;
  logo: {
    content: string;
    type: string;
  },
  name: string;
  nodes: [];
}
