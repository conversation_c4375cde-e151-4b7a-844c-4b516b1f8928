import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { CommonService } from '../../../services/common.service';
import { FiltersDataManagementService } from '../../../services/filters-data-management.service';
import { geoMapKeys } from '../../../constants/geospatial.contants';

interface Quarter {
  YEAR_CODE: number;
  YEAR_AR: string;
  YEAR_EN: string;
  QUARTER_CODE: string;
  QUARTER_AR: string;
  QUARTER_EN: string;
}

interface Year {
  YEAR_CODE: number;
  YEAR_AR: string;
  YEAR_EN: string;
  quarters?: Quarter[];
}

@Component({
  selector: 'ifp-compare-filter',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule
  ],
  templateUrl: './compare-filter.component.html',
  styleUrl: './compare-filter.component.scss'
})
export class CompareFilterComponent {

  quarters: Quarter[] = [];
  years: Year[] = [];
  lastQuarter: string = '';
  lastYear: number = 0;
  lastQuarterData: any;

  @Output() selectionChange = new EventEmitter<Quarter[]>();

  @Input() menuId!: string;
  @Input() openMenu!: string | null;
  @Input() filtersData: any = [];
  @Input() mapTheme: string = 'light';
  @Output() toggleDropdownMenu = new EventEmitter<string>();

  selectedQuarters: Quarter[] = [];
  disableCheckBox = false;
  disableSelectAll = false;
  filteredQuarters: Quarter[] = [];
  filteredYears: Year[] = [];
  public compareFilterLabel: any = geoMapKeys.compareFilterLabel;
  public language: string = '';
  public theme: string = 'light';


  constructor(
    private commonService: CommonService,
    private filtersDataService: FiltersDataManagementService,
    public themeService: ThemeService,
  ) {
  }

  ngOnInit() {
    this.processData();
    this.themeService.defaultLang$.subscribe((lang: string) => {
      this.language = lang;
    });
    this.themeService.defaultTheme$.subscribe((val: any) => {
      this.theme = val;
    });
  }


  private processData() {
    // Create a map of year with their quarters
    this.years = this.filtersData?.years.map((year: Year) => ({
      ...year,
      quarters: this.filtersData?.quarters.filter((quarter: Quarter) => quarter.YEAR_CODE === year.YEAR_CODE)
        .sort((a: Quarter, b: Quarter) => a.QUARTER_CODE.localeCompare(b.QUARTER_CODE))
    })).sort((a: Year, b: Year) => a.YEAR_CODE - b.YEAR_CODE);

    this.quarters = this.filtersData?.quarters;
    this.filteredYears = [...this.years];
    this.filteredQuarters = [...this.quarters];

    this.lastQuarterData = this.filtersDataService.getLastQuarterAndYear(this.filteredQuarters);
    this.lastYear = this.lastQuarterData.YEAR_CODE;
    // force Q3 as the base Quarters
    this.lastQuarter = this.lastQuarterData.QUARTER_CODE == 'Q4' ? 'Q3' : this.lastQuarterData.QUARTER_CODE;

    const selectedQuarter = this.filteredQuarters.find((q: any) => q.YEAR_CODE == this.lastYear && q.QUARTER_CODE == this.lastQuarter)
    if(selectedQuarter) {
      this.selectedQuarters.push(selectedQuarter);
      this.disableCheckBox = this.selectedQuarters.length === 1;
    }
  }

  get isMenuOpen(): boolean {
    return this.openMenu === this.menuId; // Check if this menu is currently open
  }

  toggleMenu(): void {
    this.toggleDropdownMenu.emit(this.menuId);
  }

  isQuarterSelected(quarter: Quarter): boolean {
    return this.selectedQuarters.some(r => r.YEAR_CODE === quarter.YEAR_CODE && r.QUARTER_CODE === quarter.QUARTER_CODE);
  }

  updateSelectedYearByCustomFilters(year: number) {
    this.selectedQuarters = this.filteredQuarters.filter((r: any) => r.YEAR_CODE == year);
  }

  toggleQuartersSelection(quarter: Quarter) {
    const index = this.selectedQuarters.findIndex(r => r.YEAR_CODE === quarter.YEAR_CODE && r.QUARTER_CODE === quarter.QUARTER_CODE);
    if (index === -1) {
      this.selectedQuarters.push(quarter);
    } else {
      if (this.selectedQuarters.length === 1) {
        this.disableCheckBox = true;
        return;
      } else {
        this.selectedQuarters.splice(index, 1);
      }
    }
    this.disableCheckBox = this.selectedQuarters.length === 1;
    this.selectionChange.emit(this.selectedQuarters);
  }

  selectAll() {
    this.selectedQuarters = [...this.quarters];
    this.selectionChange.emit(this.selectedQuarters);
  }

  resetSelectedYears() {
    this.selectedQuarters = this.quarters.filter((item: any) => item.YEAR_CODE == this.lastYear);
  }

  clearAll() {
    this.selectedQuarters = [];
    const selectedQuarter = this.filteredQuarters.find((q: any) => q.YEAR_CODE == this.lastYear && q.QUARTER_CODE == this.lastQuarter)
    if(selectedQuarter) {
      this.selectedQuarters.push(selectedQuarter);
      this.disableCheckBox = this.selectedQuarters.length === 1;
    }
    //this.selectionChange.emit(this.selectedQuarters);
  }

}
