@if (accessType && accessType !== role.normalUser) {
  <div class="ifp-access-control">
    <div class="ifp-container">
      <div class="ifp-access-control__header">
        <div>
          <h1 class="ifp-access-control__heading">{{ pageHeading | translate}}</h1>
          @if (accessType !== role.pePrimary) {
            <span class="ifp-access-control__tag">{{_adminService.userEntity.name | translate}}</span>
          }
        </div>
        @if (accessTabs.length) {
          <app-ifp-tab [tabData]="accessTabs" [isSmall]="true" [tooltipDisabled]="true" [selectionType]="'key'" [selectedTab]="selectedTabView.event.key"
          [showIcon]="false" (selectedTabEvent)="changeTabView($event)"
          class="ifp-access-control__switch-view"></app-ifp-tab>
        }
      </div>
      <div class="ifp-access-control__actions">
          <div class="ifp-access-control__filter-wrapper">
            @if (selectedTabView.event.key !== 'invite') {
              <div class="ifp-access-control__dropdown-wrapper">
                @if (accessType === role.pePrimary) {
                <div class="ifp-access-control__dropdown-inner">
                  <p class="ifp-access-control__label">{{'Filter by Entity' | translate}}</p>
                  <div class="ifp-access-control__dropdown-outer">
                    <app-ifp-dropdown class="ifp-access-control__dropdown" [placeHolder]="'Select'" [singleDefaultSelect]="false" [selectedValue]="entity" [key]="'NAME'" (dropDownItemClicked)="filterCards($event)" [dropDownItems]="entityList" [searchEnable]="true" [showTitle]="false"></app-ifp-dropdown>
                    <em class="ifp-icon ifp-icon-refresh-round ifp-access-control__reset" (click)="filterCards('')" [appIfpTooltip]="'Reset' | translate"></em>
                  </div>
                </div>
                }
                @if (selectedTabView.event.key === 'existing') {
                  <div class="ifp-access-control__dropdown-inner">
                    <p class="ifp-access-control__label">{{'Filter by User Type' | translate}}</p>
                    <div class="ifp-access-control__dropdown-outer">
                      <app-ifp-dropdown class="ifp-access-control__dropdown" [placeHolder]="'Select'" [singleDefaultSelect]="false" [selectedValue]="selectedUserType" (dropDownItemClicked)="onSelectUserType($event)" [dropDownItems]="exisitingUserType" [showTitle]="false"></app-ifp-dropdown>
                      <em class="ifp-icon ifp-icon-refresh-round ifp-access-control__reset" (click)="onSelectUserType('')" [appIfpTooltip]="'Reset' | translate"></em>
                    </div>
                  </div>
                  <div class="ifp-access-control__dropdown-inner">
                    <p class="ifp-access-control__label">{{'Filter by User Role' | translate}}</p>
                    <div class="ifp-access-control__dropdown-outer">
                      <app-ifp-dropdown class="ifp-access-control__dropdown" [placeHolder]="'Select'" [singleDefaultSelect]="false" [selectedValue]="selectedUserRole" (dropDownItemClicked)="onSelectUserRole($event)" [dropDownItems]="userRoleList" [showTitle]="false"
                      [key]="'label'"></app-ifp-dropdown>
                      <em class="ifp-icon ifp-icon-refresh-round ifp-access-control__reset" (click)="onSelectUserRole('')" [appIfpTooltip]="'Reset' | translate"></em>
                    </div>
                  </div>
                }
              </div>
            }

            <div class="ifp-access-control__right-sec">
              @if (!excludeSearch.includes(selectedTabView.event.key)) {
                <ifp-search [boxType]="true" class="ifp-access-control__search" (searchEvent)="onSearchUser($event)" [onSearch]="searchUser" [isKeypress]="true" [placeholderText]="'Search user' | translate"></ifp-search>
              }
              <!-- Export Users start -->
              <ifp-kebab-menu class="ifp-access-control__options" (optionSelected)="onSelectOptions($event)" [options]="selectedTabView.event.key === 'existing' ? kebabOptionsExisting : kebabOptions" [position]="'left'"></ifp-kebab-menu>
              <!-- Export Users end -->
            </div>
          </div>
        <!-- } -->
      </div>

      <div class="ifp-access-control__card-wrapper" [ngClass]="{'ifp-access-control__card-wrapper--top': selectedTabView.event.key === 'invite'}">
        @switch (selectedTabView.event.key) {
          @case('invite') {
            <ifp-invite-user-form [role]="accessType" [entityList]="entityList" [accessLevels]="accessPolicyList" [dataClassificationList]="classificationList" [isSubmitted]="isSubmitted" [exisitingUserList]="existingUserEmails" [inviteFormData]="inviteFormValue" [userRoleList]="userRoleList" (inviteSuperUser)="inviteSuperUser($event)" (inviteDg)="inviteDg($event)" (inviteUser)="inviteUser($event)" (saveFormValue)="setInviteFormData($event)" class="ifp-access-control__invite"></ifp-invite-user-form>
          }
          @case('new') {
            @for (user of newRequestList; track user.id; let i = $index) {
              <ifp-access-control-card [userRoleList]="userRoleList" [approverLevel]="user.approvalLevel ?? ''" [direction]="user.direction === 'revert' ? 'response' : 'approval'" [requestDate]="user.createdDate" [message]="user.reason ?? ''" [user]="accessType" [requestRole]="user.role"  [secondaryRole]="user?.secondary_role ? user.secondary_role.label : 'Not configured'" [defaultAccess]="user.maxClassification" [status]="superUserRoles.includes(accessType) ? 'pending' : 'new'" [username]="user.name" [entity]="accessType === role.pePrimary ? user.entityName : ''" [designation]="user.designation" [from]="user.reverted_by ? user.reverted_by : (user.approved_by ?? '')" [email]="user.email" [accessLevels]="accessPolicyList" [domainsAccessable]="user.access" (setAccess)="setUserAccess($event)" (deleteUser)="onDeleteUser($event, i, user)" [id]="user.id" class="ifp-access-control__card"></ifp-access-control-card>
            }
            @empty {
              <app-ifp-no-data [message]="'No new request'" class="ifp-access-control__no-data"></app-ifp-no-data>
            }
          }
          @case('existing') {
            @for (user of completedRequestList; track user; let i = $index) {
              <ifp-access-control-card [userRoleList]="userRoleList" (editRoleEvent)="getCompletedList()" [user]="accessType" [isEid]="!user.isLinked" [isForm]="superUserRoles.includes(accessType) && user.role === role.normalUser && !user.isLinked" [requestRole]="user.role"  [defaultAccess]="user.maxClassification" [status]="'approved'" [username]="user.name" [designation]="user.designation" [entity]="accessType === role.pePrimary ? user.entityName : ''" [domainsAccessable]="user.access" [email]="user.email" [accessLevels]="accessPolicyList" [secondaryRole]="user?.secondary_role" [id]="user.id" class="ifp-access-control__card" (setAccess)="setUserAccess($event, i)" (deleteUser)="onDeleteUser($event, i, user)" (linkUser)="onLinkUser($event, i)" (editAccess)="updateUserAccess($event)"></ifp-access-control-card>
            } @empty {
              <app-ifp-no-data [message]="'There are no existing users'" class="ifp-access-control__no-data"></app-ifp-no-data>
            }
          }
          @case('pending') {
            @for (user of approvedList; track i; let i = $index) {
              <ifp-access-control-card [userRoleList]="userRoleList" [user]="accessType" [requestRole]="user.role" [message]="user.reverted_by ? (user.reason ?? '')  : ''" [id]="user.id" [defaultAccess]="user.maxClassification" [status]="'pending'" [username]="user.name" [entity]="accessType === role.pePrimary ? user.entityName : ''" [designation]="user.designation" [approverLevel]="user.approvalLevel ?? ''" [accessLevels]="accessPolicyList" [direction]="user.direction === 'revert' ? 'response' : 'approval'" class="ifp-access-control__card" [secondaryRole]="user?.secondary_role ? user.secondary_role.label : 'Not configured'" (setAccess)="setUserAccess($event)" (deleteUser)="onDeleteUser($event, i, user)"></ifp-access-control-card>
            } @empty {
              <app-ifp-no-data [message]="'No pending requests'" class="ifp-access-control__no-data"></app-ifp-no-data>
            }
          }
          @case('deleted') {
            @if (deletedUsersList.length) {
              <ifp-deleted-invites [tableData]="deletedUsersList"></ifp-deleted-invites>
            } @else {
              <app-ifp-no-data [message]="'No data to display'" class="ifp-access-control__no-data"></app-ifp-no-data>
            }
          }
        }
      </div>
      @if (!excludePagination.includes(selectedTabView.event.key)) {
        <app-pagination class="ifp-access-control__pagination" [offset]="page" [limit]="limit" [size]="size" (pageChange)="onPageChange($event)"
        (limitChange)="limitChanged($event)"></app-pagination>
      }
    </div>
  </div>
}
