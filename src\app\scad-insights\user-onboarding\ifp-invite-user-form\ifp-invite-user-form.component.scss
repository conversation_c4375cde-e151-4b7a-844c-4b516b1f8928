@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-invite {
  &__form-outer {
    margin-bottom: $spacer-3;
  }
  &__title,
  &__user-icon {
    font-size: $ifp-fs-6;
  }
  &__title {
    font-weight: $fw-semi-bold;
    margin-bottom: $spacer-2;
    &--info {
      display: flex;
    }
  }
  &__input-wrapper {
    display: flex;
    // align-items: flex-end;
    margin: $spacer-0 (-$spacer-2) $spacer-4;
    flex-wrap: wrap;
  }
  &__input-sec {
    padding: $spacer-2;
    width: 25%;
    position: relative;
    .ifp-input-error {
      margin-top: $spacer-1;
    }
    &--2x {
      width: 50%;
      .ifp-invite__input {
        padding: $spacer-2 $spacer-3;
        min-height: 52px;
        display: flex;
        justify-content: space-between;
      }
    }
     &--direct_hire {
          position: absolute;
          top: 40px;
          left: 320px;
        }
  }
  &__input,
  &__auto-fill {
    background-color: $ifp-color-white;
    padding: $spacer-3;
    border-radius: 5px;
  }
  &__input {
    font-size: $ifp-fs-3;
    display: block;
    width: 100%;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 5px;
    @include reset-number-input;
  }
  &__auto-fill {
    padding-inline-start: $spacer-0;
  }
  &__input-label {
    color: $ifp-color-tertiary-text;
    margin-bottom: $spacer-1;
    &--info {
      display: flex;
      align-items: center;
      .ifp-invite__label-info {
        margin-top: -3px;
      }
    }
  }
  &__input-outer {
    position: relative;
    // display: flex;
    // border-radius: 5px;
    // background-color: $ifp-color-white;
    // border: 1px solid $ifp-color-grey-7;
    // .ifp-invite__input {
    //   border: none;
    //   padding-inline-end: $spacer-0;
    // }
  }
  &__dropdown {
    display: block;
  }
  &__label-info {
    margin-inline-start: $spacer-2;
    font-size: $ifp-fs-4;
    position: relative;
    top: 3px;
  }
  &__box {
    padding: $spacer-3;
    background-color: $ifp-color-section-white;
    border: 1px solid $ifp-color-grey-7;
    border-radius: 10px;
    margin-bottom: $spacer-3;
    .ifp-invite {
      &__input-wrapper {
        margin-bottom: $spacer-0;
      }
    }
  }
  &__add-btn {
    display: inline-block;
    margin-top: $spacer-3;
  }
  &__main-btn {
    margin-top: $spacer-4;
  }
  &__remove-user {
    width: 30px;
    height: 30px;
    line-height: 30px !important;
    text-align: center;
    background-color: $ifp-color-red;
    color: $ifp-color-white-global;
    position: absolute;
    top: 0;
    right: 0;
  }
  &__mobile {
    display: flex;
    border-radius: 5px;
    overflow: hidden;
    .ifp-invite__input {
      margin-inline-start: -($spacer-1 + 1px);
    }
  }
  &__country-code {
    display: flex;
    align-items: center;
    background-color: $ifp-color-grey-3;
    padding: $spacer-3 $spacer-4 $spacer-3 $spacer-3;
    padding-top: $spacer-3;
    padding-inline-end: $spacer-4;
    padding-bottom: $spacer-3;
    padding-inline-start: $spacer-3;
    cursor: pointer;
    z-index: 1;
    .ifp-icon {
      font-size: $ifp-fs-2;
      margin-inline-start: auto;
    }
  }
  &__country-flag {
    width: 30px;
    height: 20px;
    margin-inline-end: $spacer-2;
  }
  &__input-tag {
    display: inline-flex;
    // align-items: center;
    margin: ($spacer-1 - 2px) $spacer-1;
    white-space: nowrap;
    .ifp-icon {
      display: none;
    }
    &--close {
      padding: ($spacer-1 - 2px) $spacer-2;
      border: 1px solid;
      border-radius: 20px;
      .ifp-icon {
        display: block;
      }
    }
  }
  &__tag-dot {
    width: 10px;
    min-width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-inline-end: $spacer-1;
    position: relative;
    top: 4px;
  }
  &__close {
    font-size: $ifp-fs-1;
    margin-inline-start: $spacer-2;
    color: $ifp-color-red;
    cursor: pointer;
    position: relative;
    top: 4px;
  }
  &__tag-wrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin: $spacer-0 (-$spacer-1);
  }
  &__table-wrapper {
    width: 100%;
    @include ifp-scroll-x(transparent, $ifp-color-grey-5, 10px, 5px);
  }
  &__table-input {
    padding: $spacer-0;
    margin: $spacer-0;
    border: none;
    background-color: transparent;
    display: flex;
    font-size: $ifp-fs-3;
    @include reset-number-input;
  }
  &__col-text-head {
    color: $ifp-color-black;
    font-weight: $fw-medium;
    margin-bottom: $spacer-1;
  }

  &__table-actions {
    display: flex;
    justify-content: flex-end;
    .ifp-icon {
      font-size: $ifp-fs-6;
      color: $ifp-color-tertiary-text;
      display: inline-block;
      margin-inline-start: $spacer-4;
      cursor: pointer;
    }
  }

  &__table-col {
    padding: $spacer-2 $spacer-3;
    border-bottom: 1px solid $ifp-color-grey-7;
    // vertical-align: middle;
    &:last-child {
      text-align: end;
      position: sticky;
      top: 0;
      right: 0;
    }
    .ifp-input-error {
      margin-top: $spacer-1;
    }
    &--info {
      min-width: 200px;
    }
  }

  &__table-row {
    .ifp-invite__text-area {
      pointer-events: none;
    }
    &--head {
      .ifp-invite__table-col {
        color: $ifp-color-grey-2;
        white-space: nowrap;
      }
    }
    &--edit {
      .ifp-invite {
        &__table-col {
          background-color: $ifp-color-pale-blue;
        }
        &__table-input,
        &__text-area {
          padding-bottom: $spacer-1;
          border-bottom: 1px solid $ifp-color-grey-5;
          &:disabled {
            border-bottom: 1px solid transparent;
          }
        }
        &__input--tag-wrapper {
          border-bottom: none;
        }
        &__text-area {
          pointer-events: all;
        }
      }
    }
  }

  &__table {
    width: 100%;
    &--sent {
      .ifp-invite {
        &__table-col {
          padding: ($spacer-3 - $spacer-1) $spacer-3;
          border: none;
          background-color: $ifp-color-section-white;
          &:first-child {
            border-radius: 7px 0 0 7px;
          }
          &:last-child {
            border-radius: 0 7px 7px 0;
          }
        }
        &__table-row {
          &--head {
            .ifp-invite__table-col {
              font-weight: $fw-semi-bold;
              position: sticky;
              top: 0;
              left: 0;
              background-color: $ifp-color-section-white;
            }
          }
          &:nth-of-type(even) {
            .ifp-invite__table-col {
              background-color: $ifp-color-grey-bg;
            }
          }
        }
      }
    }
  }

  &__overlay {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-color: $ifp-color-grey-bg-2;
    border-radius: 10px;
    min-height: 300px;
    border: 1px solid $ifp-color-grey-7;
  }

  &__msg-icon {
    font-size: $ifp-fs-13;
    margin-bottom: $spacer-2;
  }
  &__overlay-message { // to be removed
    font-size: $ifp-fs-6;
    font-weight: $fw-semi-bold;
    text-align: center;
    max-width: 600px;
  }
  &__switch-tab {
    margin-bottom: $spacer-4;
  }

  &__sent {
    padding: $spacer-3;
    background-color: $ifp-color-section-white;
    border-radius: 15px;
    .ifp-invite {
      &__table-wrapper {
        max-height: 650px;
        @include ifp-scroll(transparent, $ifp-color-grey-5, 10px, 5px);
      }
      &__table-input {
        font-size: inherit;
        padding: $spacer-1;
        border-radius: 5px;
        border: 1px solid transparent;
        color: $ifp-color-primary-grey;
      }
      &__table-row {
        border-top: 1px solid transparent;
        border-bottom: 1px solid transparent;
        margin-top: -1px;
        &:last-child {
          margin-top: $spacer-0;
        }
      }
      &__table-row--edit {
        .ifp-invite {
          &__table-col {
            background-color: $ifp-color-pale-blue !important;
          }
          &__table-input:not([readonly]) {
            background-color: $ifp-color-white;
            border: 1px solid $ifp-color-blue-med;
          }
        }
      }
      &__mobile {
        align-items: center;
      }
    }
  }

  &__button-icon {
    font-size: $ifp-fs-4;
    color: $ifp-color-blue-hover;
    margin: $spacer-0 $spacer-2;
    cursor: pointer;
    transition: 0.3s;
    width: 30px;
    height: 30px;
    line-height: 30px !important;
    text-align: center;
    border-radius: 50%;
    border: 1px solid $ifp-color-blue-hover;
    &:hover {
      color: $ifp-color-white-global;
      background-color: $ifp-color-blue-hover;
    }
    &--disabled {
      color: $ifp-color-white;
      background-color: $ifp-color-grey-disabled;
      border: 1px solid $ifp-color-grey-disabled;
      pointer-events: none;
    }
  }

  &__button-sec {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: $spacer-0 (-$spacer-2);
    .ifp-invite__button {
      margin: $spacer-0 $spacer-2;
    }
    &--disabled {
      .ifp-invite__button-icon {
        color: $ifp-color-white;
        background-color: $ifp-color-grey-disabled;
        border: 1px solid $ifp-color-grey-disabled;
        pointer-events: none;
      }
    }
  }

  &__button {
    &.ifp-icon {
      cursor: pointer;
      transition: 0.3s;
      &:hover {
        color: $ifp-color-red;
      }
    }
  }
  &__text-area {
    border: none;
    resize: none;
    padding: $spacer-0;
    display: block;
    width: 100%;
    background-color: transparent;
  }
  &__dg-modal-close {
    position: absolute;
    top: 16px;
    right: 16px;
    cursor: pointer;
  }
  &__info-box {
    display: flex;
    padding: $spacer-3;
    background-color: $ifp-color-pale-blue;
    border-radius: 7px;
    margin-bottom: $spacer-4;
  }
  &__info-icon {
    display: inline-block;
    margin-top: 1px;
    margin-inline-end: $spacer-2;
  }
  // &__table-input {
  //   color: $ifp-color-primary-text;
  //   transition: 0.3s;
  //   border-radius: 3px;
  //   padding: $spacer-1 $spacer-2;
  //   background-color: transparent;
  // }
  &__box-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: $spacer-3;
    .ifp-invite__add-btn {
      margin-top: $spacer-0;
    }
  }
  &__title-wrapper {
    margin-inline-end: $spacer-3;
    .ifp-invite__title {
      margin-bottom: $spacer-0;
    }
  }
  &__su-form {
    .ifp-invite {
      &__btn-sec {
        display: flex;
        align-items: flex-end;
        padding: $spacer-0 $spacer-3;
      }
      &__input-wrapper {
        flex-wrap: wrap;
      }
    }
    // &--disabled {
    //   pointer-events: none;
    //   position: relative;
    //   .ifp-invite__overlay {
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    //     background-color: $ifp-color-grey-bg-2;
    //     border-radius: 10px;
    //     position: absolute;
    //     top: 0;
    //     left: 0;
    //     width: 100%;
    //     height: 100%;
    //     z-index: 1;
    //     pointer-events: none;
    //   }
    // }
  }
  &__pe-form {
    .ifp-invite {
      &__input-sec {
        width: 25%;
        flex: none;
      }
      &__input-wrapper {
        margin-bottom: $spacer-3;
      }
      &__form-outer {
        margin-bottom: $spacer-5;
      }
    }
  }
  &__dg-form {
    &.ifp-invite__box {
      padding: $spacer-5;
      margin-bottom: $spacer-0;
    }
    .ifp-invite {
      &__input-wrapper {
        flex-wrap: wrap;
      }
      &__input-sec {
        width: 50%;
      }
      &__btn-sec {
        margin-top: $spacer-3;
      }
    }
  }
  &__dg-box {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    min-height: 150px;
    margin-bottom: $spacer-0;
  }
  &__input-refresh {
    display: flex;
    align-items: center;
    margin-inline-start: $spacer-1;
    em {
      cursor: pointer;
      &:hover {
        color: $ifp-color-hover-blue;
      }
    }
  }
}

:host {
  display: block;
  width: 100%;
  &::ng-deep {
    .ifp-invite {
      &__dropdown {
        .ifp-dropdown {
          min-width: 220px;
          max-width: none;
          border-radius: 5px;
          &__selected {
            padding: $spacer-3;
          }
          &__title {
            font-weight: $fw-regular;
            font-size: $ifp-fs-3;
            margin: $spacer-0 $spacer-0 $spacer-1;
          }
          &__list {
            width: 100%;
          }
        }
        &--user {
          .ifp-dropdown {
            min-width: 0;
            background-color: transparent;
            border: none;
            min-height: 0;
            &__selected {
              padding: $spacer-0;
            }
          }
        }
      }
      &__su-form,
      &__dg-form {
        .ifp-btn {
          padding: $spacer-3;
          min-width: 200px;
        }
      }
      &__table-col {
        .ifp-access-list__selected {
          padding: $spacer-0;
        }
      }
    }
  }
}

:host-context([dir="rtl"]) {
    .ifp-invite {
    &__input-sec {
      &--direct_hire {
        left:auto;
          right : 325px;
        }
    }
  }
  .ifp-invite__table--sent {
    .ifp-invite__table-row {
      .ifp-invite__table-col {
        &:first-child {
          border-radius: 0 7px 7px 0;
        }
        &:last-child {
          border-radius: 7px 0 0 7px;
        }
      }
    }
  }
}


@include desktop-sm {
  .ifp-invite {
    &__input-wrapper {
      flex-wrap: wrap;
    }
    &__input-sec {
      position: relative;
      padding: $spacer-2;
      &--2x {
        width: 100%;
      }
      &--direct_hire {
          position: absolute;
          top: 40px;
          left: 320px;
        }

    }
    &__dg-form {
      .ifp-invite {
        &__input-wrapper {
          flex-wrap: nowrap;
        }
        &__btn-sec {
          margin-top: $spacer-5 - 2;
        }
      }
    }
    &__su-form {
      .ifp-invite__input-sec {
        flex: none;
        width: 50%;
      }
    }
    &__pe-form {
      .ifp-invite__input-sec {
        width: 33.33%;
      }
    }
  }
}

.ifp-byn-db{
&__level-label {
  font-size: $ifp-fs-3;
  font-weight: $fw-medium;
  padding-inline-start: $spacer-5;
  position: relative;
  cursor: pointer;
  &::before,
  &::after {
    content: "";
    position: absolute;
    top: 50%;
    border-radius: 50%;
    transform: translateY(-50%);
    transition: 0.3s;
  }
  &::before {
    width: 24px;
    height: 24px;
    border: 2px solid $ifp-color-grey-1;
    left: 0;
  }
  &::after {
    width: 14px;
    height: 14px;
    background-color: $ifp-color-grey-1;
    left: 5px;
  }
  &:hover {
    &::before {
      border-color: $ifp-color-hover-blue;
    }
    &::after {
      background-color: $ifp-color-hover-blue;
    }
  }
}
&__level-tab {
  display: flex;
  justify-content: flex-start;
  border: 1px solid $ifp-color-grey-7;
  border-radius: 10px;
  padding: $spacer-3 $spacer-0;
}
&__level-tab-item {
  margin: $spacer-0 $spacer-4;
}
&__level-radio {
  display: none;
  &:checked + .ifp-byn-db__level-label {
    pointer-events: none;
    &::before {
      border-color: $ifp-color-active-blue;
    }
    &::after {
      background-color: $ifp-color-active-blue;
    }
  }
}
&__mt-32{
  margin-top: $spacer-5;
}
}
