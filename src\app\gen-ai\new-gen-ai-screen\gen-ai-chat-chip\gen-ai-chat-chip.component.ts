import { Component, input, output, signal, inject, ElementRef, viewChild, ChangeDetectorRef } from '@angular/core';
import { MapConfigGeoAi, MessageChat } from '../gen-ai-screen-chat-wraper/interface/chat.interface';
import { IfpMarkDownComponent } from '../../../scad-insights/ifp-chat-bot/ifp-mark-down/ifp-mark-down.component';
import { genAiRole } from '../constant/api-gen-ai.const';
import { NgClass } from '@angular/common';
import { IfpAiChartCardComponent } from '../../../scad-insights/ifp-chat-bot/ifp-ai-chart-card/ifp-ai-chart-card.component';
// import { GenAiIndicatorPillsComponent } from '../gen-ai-indicator-pills/gen-ai-indicator-pills.component';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { TranslateModule } from '@ngx-translate/core';
import {ClipboardModule} from '@angular/cdk/clipboard';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { ApiGenAiService } from 'src/app/scad-insights/core/services/api-gen-ai.service';
import { genAiTestingApi } from 'src/app/gen-ai-dashboard/constents/gen-ai-testing.constant';
import { GenAiLikeFeedbackComponent } from '../gen-ai-like-feedback/gen-ai-like-feedback.component';
import { AppendToBodyDirective } from 'src/app/scad-insights/core/directives/append-body.directive';
import { OutsideClickDirective } from 'src/app/scad-insights/core/directives/outsideClick.directive';
import { GenAiScreenSourcePillComponent } from '../gen-ai-screen-source-pill/gen-ai-screen-source-pill.component';
import { TabBadgeComponent } from 'src/app/usage-dashboard/tab-badge/tab-badge.component';
import { GenAiIndicatorPillsComponent } from '../gen-ai-indicator-pills/gen-ai-indicator-pills.component';
import { GenAiMapChipComponent } from '../gen-ai-map-chip/gen-ai-map-chip.component';

@Component({
  selector: 'ifp-gen-ai-chat-chip',
  templateUrl: './gen-ai-chat-chip.component.html',
  styleUrl: './gen-ai-chat-chip.component.scss',

  imports: [
    IfpMarkDownComponent,
    NgClass,
    IfpAiChartCardComponent,
    // GenAiIndicatorPillsComponent,
    IfpTooltipDirective,
    TranslateModule,
    ClipboardModule,
    GenAiLikeFeedbackComponent,
    AppendToBodyDirective,
    OutsideClickDirective,
    GenAiScreenSourcePillComponent,
    TabBadgeComponent,
    GenAiIndicatorPillsComponent,
    GenAiMapChipComponent
  ],
})
export class GenAiChatChipComponent {
  public chat = input<MessageChat>({});
  public aiPrompt = input<boolean>(false);
  public loader = input<boolean>(false);
  public loaderData = input<string>('');
  public reasoning = input<string>('');
  public index = input<number>(0);
  public actionEnable = input(false);
  public regenerate = output<MessageChat>();
  public prompt = output<string>();
  public dataLoaded = output();
  public sourceClick = output<{keys: string[]; message?: MessageChat} >();
  public genAiRole = genAiRole;
  public liked = signal<boolean| undefined>(undefined);
  public reasoningOpen = signal(false);
  public feedbackPopUp = signal(false);
  public sourceKeys = signal<string[]>([]);
  public messagePosition = signal<{top: number, left: number, parentWidth: number}>({top: 0, left: 0, parentWidth: 0});
  public _toaster = inject(ToasterService);
  public _api =  inject(ApiGenAiService);
  public currentTab = signal<string>('');
  public currentTabValue = signal<string>('');
  public  tabs = signal<{name: string, key?: string, node?: string}[]>([]);
  public  mapClick= output<MapConfigGeoAi| undefined>();
  public map= signal<boolean>(false);
  private  dislikeRef= viewChild<ElementRef>('dislikeRef');
  private  outsideRef= viewChild<ElementRef>('outsideRef');
  private  actionRef= viewChild<ElementRef>('action');
  private changeDetect = signal(false);
  private _cdr = inject(ChangeDetectorRef);

  ngOnChanges(): void {
    this.tabs.set([]);
    this.chat().data?.tab_response?.forEach((element) => {
      this.tabs.update((tabs) => [...tabs, {name: element.name, key: element.name, node: element.response}]);
    });
    this.currentTab.set(this.tabs()[0]?.key ?? '');
    this.currentTabValue.set(this.tabs()[0]?.node ?? '');
    if (Object.keys(this.chat()?.data?.related_sources ?? {}).length) {
      this.sourceKeys.set(this.chat()?.data?.order ?  this.chat()?.data?.order ?? [] :Object.keys(this.chat()?.data?.related_sources ?? {}));
    }
    const filteredSourceKeys = this.sourceKeys().filter(key => this.chat()?.data?.related_sources?.[key]?.length);
    this.sourceKeys.set(filteredSourceKeys);
    this.map.set(Object.keys(this.chat()?.data?.map_config ?? {}).length ? true : false);
    this.changeDetect.set(true);
  }

  ngAfterViewChecked(): void {
    if (this.actionEnable() && this.changeDetect()) {
      this.changeDetect.set(false);
      setTimeout(() => {
        this.dataLoaded.emit();
      });
    }
  }

  toaster(value: string) {
    this._toaster.success(value);
  }

  postChatFeedback( liked: string, content: string = '') {
    const payload = {
      feedback_type: 'Others',
      liked: liked == 'like' ? true : false,
      content: content,
      chat_id: this.chat().object_id
    };
    this._api.postMethodRequest(genAiTestingApi.like, payload).subscribe({
      next: () => {
        this.liked.set(payload.liked);
      },
      error: (error: {error:{message: string}}) => {
        this._toaster.error(error.error.message);
      }
    });
  }

  postFeedbackOpen() {
    this.feedbackPopUp.set(true);
    const targetElement = this.actionRef()?.nativeElement;
    const rect = targetElement.getBoundingClientRect();
    this.messagePosition.set({
      top: rect.top,
      left: rect.left,
      parentWidth: targetElement.offsetWidth
    });
  }


  outsideClick(event: Event) {
    if (!this.dislikeRef()?.nativeElement?.contains(event?.target) && this.outsideRef()?.nativeElement.innerText !== (event?.target as HTMLElement)?.innerText) {
      this.feedbackPopUp.set(false);
    }

  }

}
