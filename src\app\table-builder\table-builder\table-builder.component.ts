import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'ifp-table-builder',
  imports: [],
  templateUrl: './table-builder.component.html',
  styleUrl: './table-builder.component.scss'
})
export class TableBuilderComponent implements OnInit {

  public url = this.sanitizer.bypassSecurityTrustResourceUrl(`${environment.zohoUrl}/ZDBHome.cc?login_mode=saml&orgid=304&frameorigin=${environment.baseUrl}/`);
  public isTableBuilder: boolean = false;


  constructor(protected sanitizer: DomSanitizer, private _cdr: ChangeDetectorRef) {
  }


  ngOnInit(): void {
    this.isTableBuilder = false;
    this.bypassCredentials();
  }




  bypassCredentials() {
    let top = window.innerHeight + 300;
    let left = window.innerWidth + 600;
    const authWindow = window.open(`${environment.zohoUrl}/ZDBHome.cc?login_mode=saml&orgid=304`, '_blank', 'width=1,height=1, left=' + left + ', top=' + top + '');
    authWindow?.resizeBy(0, 0)
    if (authWindow) {
      let that = this;
      setTimeout(function () {
        authWindow?.close();
        that.isTableBuilder = true;
        that._cdr.detectChanges();
      }, 2000);
    }
  }

}
