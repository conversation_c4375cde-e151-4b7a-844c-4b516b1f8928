import { RadioGroupItems } from '../ifp-widgets/ifp-molecules/ifp-radio-group-progress/ifp-radio-group-progress.interface';

export interface AccessDetails {
  totalCount: number;
  results: UserDetails[];
}

// export interface UserDetails {
//   id: number;
//   name: string;
//   designation: string;
//   phone: string;
//   email: string;
//   classification: string;
//   entity: string;
//   accessRequestDate: string;
//   superUserApprovalStatus: string;
//   superUserApprovalDate: string;
//   productEngagementApprovalStatus: string;
//   productEngagementApprovalDate: string;
//   sanadkomApprovalStatus: string;
//   sanadkomApprovalDate?: any;
//   intraIdInviteStatus: string;
//   intraIdInviteSentDate?: any;
//   intraIdInviteAcceptDate?: any;
//   requestStatus: string;
//   requestApprovalDate?: any;
//   accessLevels?: RadioGroupItems[];
// }

export interface UserDetails {
  id: string;
  name: string;
  email: string;
  phone: number;
  activationStatus: string;
  entityId: string;
  entityName: string;
  designation: string;
  createdDate: string;
  classification: string;
  accessLevels: RadioGroupItems[];
}

export interface SubmitUserData {
  userAccessIds: string[];
  userId: string;
  action: 'approve' | 'reject' | 'revert' | 'revoke';
  reason: string;
 }

export interface Accesses {
  name: string;
  classification: string;
}

export interface FilterItems {
  ID: string;
  NAME: string;
  CREATED_AT: string;
  DOMAINS: string[];
}

export interface AccessList {
  name: string;
  value: string;
  access: Access[];
}

export interface Access {
  name: string;
  value: string;
  enabled: boolean;
}

export interface ClassificationDetail {
  label: string;
  value: string;
  color: string;
  isSelected?: boolean;
  description?: string;
  name?: string;
  [key: string]: any;
}
export interface RequestData {
  total: number;
  data: RequestList[];
}

export interface RequestList {
  id: string;
  name: string;
  email: string;
  role: string;
  entityId: string;
  entityName: string;
  designation: string;
  createdDate: string;
  access: AccessRequests[];
  maxClassification: string;
  totalCount: number;
  reason?: string;
  reverted_by?: string;
  approved_by?: string;
  isLinked?: boolean;
  user_role?: any;
  primary_role?: any;
  secondary_role?: any;

  /** Used in 'Pending' tab to display which
   * user request is pending on.
   */
  approvalLevel?: string;

  /** Used in 'Pending' tab to identify if
   * request is pending approval or revert
   */
  direction?: string;
}

export interface AccessRequests {
  name: string;
  levels: Level[];
  classification?: Level;
  isSelected?: boolean;
  // radioGroup?:RadioGroupItems;
}

export interface AccessReq {
  name: string;
  levels: Level;
}

export interface Level {
  classification: string;
  accessId: null | string;
  selected: boolean;
  isPending?: boolean;
}

export interface InviteList {
  inviteeEmail: string;
  inviteeRole: string;
  entityName: string;
  status: string;
  invitationId: string;
  inviteeJobLevel: string;
  inviteeDepartment: string;
  createdAt: string;
  inviteePhone: string;
  inviteeDesignation: string;
  entityDomain: string[];
  inviteeUserRole: string;
  inviteePrimaryRole: string;
  inviteeSecondaryRole: string;
}

export interface EditAccess {
  userId: string;
  accessInfo: AccessInfo;
}

export interface AccessInfo {
  grant: Grant[];
  remove: string[];
}

export interface Grant {
  domain: string;
  classification: string;
}

export interface DeletedList {
  userName: string;
  userEmail: string;
  // userRole: string;
  entityName: string;
  primaryRole: string;
  secondaryRole: string;
  deletedBy: string;
  deletedAt: string;
  deletedByRole: string;
}

export interface SecondaryRoleList {
  id: number;
  object_id: string;
  name: string;
  label: string;
  description: string;
}
