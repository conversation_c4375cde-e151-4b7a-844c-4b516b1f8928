import { cloneDeep } from 'lodash';
import { <PERSON><PERSON><PERSON><PERSON>, DecimalPipe, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, ElementRef, EventEmitter, HostListener, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { chartConstants } from 'src/app/scad-insights/core/constants/chart.constants';
import { IfpCheckBoxComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { ShortNumberPipe } from 'src/app/scad-insights/core/pipes/shortNumber.pipe';
import { SubSink } from 'subsink';
import { Store } from '@ngrx/store';
import { selectIndicatorGetById } from 'src/app/scad-insights/store/incidicator-list/incidicator-list.selector';
import { IfpDomainIconComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-domain-icon/ifp-domain-icon.component';
import { selectStatisticsInsightsGetById } from 'src/app/scad-insights/store/statistics-insights/statistics-insights-list.selector';
import { Subject, debounceTime, distinctUntilChanged } from 'rxjs';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { classifications } from 'src/app/scad-insights/core/constants/domain.constants';
import { numberType } from 'src/app/scad-insights/core/constants/numberType.constant';
import { CurrencyData, ValueMeta } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-analysis-card/ifp-analysis-card.component';
import { IfpIndicatorDetailWidgetComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-indicator-detail-widget/ifp-indicator-detail-widget.component';
import { IfpInsightDiscoveryCardComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-insight-discovery-card/ifp-insight-discovery-card.component';
import { IfpScenarioDriverCardComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-scenario-driver-card/ifp-scenario-driver-card.component';
import { IfpGeoMapComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-geo-map/ifp-geo-map.component';
import { loadAnalyticalSuccess } from 'src/app/scad-insights/home/<USER>/Analytical apps/analyticalApps.action';
import { PagesService } from 'src/app/scad-insights/core/services/pages/pages.service';
import { selectAnalyticGetById } from 'src/app/scad-insights/home/<USER>/Analytical apps/analyticalApps.selector';
import { IfpCompareChartCardComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-compare-chart-card/ifp-compare-chart-card.component';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';
import { contentTypeDashboard } from 'src/app/scad-insights/core/constants/contentType.constants';
import { IfpCardLoaderComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { IfpIconTextComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-icon-text/ifp-icon-text.component';
import { IfpUnauthorizedCardComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-unauthorized-card/ifp-unauthorized-card.component';
import { customChartTypes } from '../../organism/ifp-chart-toolbar/ifp-chart-toolbar.constants';
import { DbToolbarIcon } from '../../organism/ifp-chart-toolbar/ifp-chart-toolbar.interface';
import { IfpCustomDashboardCardComponent } from '../../organism/ifp-custom-dashboard-card/ifp-custom-dashboard-card.component';



@Component({
  selector: 'app-ifp-db-card',
  templateUrl: './ifp-db-card.component.html',
  styleUrls: ['./ifp-db-card.component.scss'],
  providers: [DatePipe],
  imports: [IfpCheckBoxComponent, NgStyle, TranslateModule, ShortNumberPipe, DecimalPipe,
    IfpDomainIconComponent, NgIf, NgClass, IfpIndicatorDetailWidgetComponent,
    IfpInsightDiscoveryCardComponent, IfpScenarioDriverCardComponent, IfpGeoMapComponent,
    IfpCompareChartCardComponent, IfpCardLoaderComponent, IfpIconTextComponent,
    IfpUnauthorizedCardComponent, IfpCustomDashboardCardComponent]
})
export class IfpDbCardComponent implements OnInit, OnChanges {

  @ViewChild('isTitleHead') isTitleHead: ElementRef | undefined;
  @ViewChild('isDescription') isDescription: ElementRef | undefined;

  @HostListener('document:click', ['$event'])
  onClick(event: Event) {
    if (event.target != this.isTitleHead?.nativeElement) {
      if (this.isTitleEdit) {
        this.isTitleEdit = false;
      }
    }
  }

  @HostListener('window:pointerup', ['$event.target'])
  onOutsideClick(target: HTMLElement) {
    if (target.classList?.length > 0 && !target.classList.contains('ifp-db-card__header-description-container') && !target.classList.contains('ifp-db-card__header-description-container--placeholder') && !target.classList.contains('ifp-db-card__description') && !target.classList.contains('ifp-richtext-viewer')) {
      if (this.isDescriptionEdit) {
        this.isDescriptionEdit = false;
      }
    }
  }


  @Input() id!: any;
  @Input() title: string = '';
  @Input() chartType: string = 'line';
  @Input() value!: number;
  @Input() cardTitle!: string;
  @Input() comparedValue!: number;
  @Input() isSelectMode: boolean = false;
  @Input() checked: boolean = false;
  @Input() isShowchart: boolean = false;
  @Input() selectedId!: string;
  @Input() cntType: string = '';
  @Input() isDashboardCard: boolean = false;
  @Input() mode: string = '';
  @Input() isCheckboxDisabled: boolean = false;
  @Input() accessPending: boolean = false;
  @Input() enableCheckbox: boolean = false;
  @Input() customChartType: string | undefined = 'line';
  @Input() dashboardView!: string;
  @Input() iconData: { url: string; bgColor?: string } = {
    url: '../../../../assets/images/economy-icon.svg'
  };

  @Input() isCustom: boolean = false;

  @Output() cardSelected: EventEmitter<{ id: string, select: boolean, cnt_type: string, type: string, indicatorId: string, title: string }> = new EventEmitter<{ id: string, select: boolean, cnt_type: string, type: string, indicatorId: string, title: string }>();
  @Output() updatedCardTitle: EventEmitter<string> = new EventEmitter<string>();
  @Output() updatedCardDescription: EventEmitter<string> = new EventEmitter<string>();
  @Output() deleteCardEmit: EventEmitter<{ cntType: string, id: string }> = new EventEmitter<{ cntType: string, id: string }>();
  @Output() openFilter: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() openTextArea: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() updateCustomChartType: EventEmitter<string> = new EventEmitter<string>();

  public chartConstants = chartConstants;
  public subs = new SubSink();
  public cardData: any = [];
  public cardDataValues: any;
  public classifications = classifications;
  public colors = ifpColors;
  public numberType = numberType.shotKeyFormat;
  public tagName!: string;
  public visualizationData: any = [];
  public ratingMeta!: ValueMeta;
  public comparison!: string;
  public tableData: any = [];
  public visualaizationValues: any = [];
  public visualaizationFilters: any = [];
  public chartData: any = [];
  public format!: string;
  public showFilterOnCard: boolean = false;
  public dataSource!: string;
  public visualaizationfilterPanel: any = [];
  public isTitleEdit: boolean = false;
  public isDescriptionEdit: boolean = false;
  public isRequestAccess: boolean = false;
  headerTitle = new Subject<string>();
  descriptionUpdate = new Subject<string>();
  public visulaizationId!: number;
  public placeHolderText: string = this._translate.instant('Please enter your text');
  public rating: any = {
    color: '#00873C',
    value: '',
    percentage: undefined
  };

  public currencyData: CurrencyData = {
    currency: null,
    currencyWords: '',
    currencySubTitle: '',
    color: ''
  };

  public comparisonValues: Record<string, { change: string, compare: string }> = {
    'Y/Y': {
      change: 'yearlyChangeValue',
      compare: 'yearlyCompareValue'
    },
    'M/M': {
      change: 'monthlyChangeValue',
      compare: 'monthlyCompareValue'
    },
    'Q/Q': {
      change: 'quarterlyChangeValue',
      compare: 'quarterlyCompareValue'
    }
  };

  public customChartTypes: DbToolbarIcon[] = customChartTypes;
  public selectedCustomChart!: DbToolbarIcon;

  constructor(private store: Store, private _pageService: PagesService, private _dashboardService: DashboardService, private elementRef: ElementRef, private _datePipe: DatePipe,
    private _translate: TranslateService, private _cdr: ChangeDetectorRef) {
    this.subs.add(this.headerTitle
      .pipe(debounceTime(800))
      .subscribe((value) => {
        this.cardData.title = value;
        this.updatedCardTitle.emit(value);
      }));

    this.subs.add(this.descriptionUpdate
      .pipe(debounceTime(800))
      .subscribe((value) => {
        // this.cardData.cardDescription = value;
        this.updatedCardDescription.emit(value);
      }));

    this._dashboardService.settingsChanged.subscribe(resp => {
      if (this.id === resp.id && resp.tools && this._dashboardService.dashboardTools.includes(resp.tools)) {
        const chartSettings = (type: string, id: string, property: string) => {
          return this._dashboardService.chartSettings[type]?.find((x: { id: string; }) => x.id === id)?.[property];
        };
        switch (resp.tools) {
        case 'icon':
          this.cardData.icon = chartSettings(resp.type, resp.id, 'icon').icon;
          break;
        case 'cardTitle':
          this.cardData.title = chartSettings(this.cntType, this.id, 'title');
          break;
        case 'textColor':
          this.cardData.textColor = chartSettings(resp.type, resp.id, 'textColor');
          break;
        case 'textSize':
          this.cardData.textSize = chartSettings(resp.type, resp.id, 'textSize');
          break;
        case 'cardDescription':
          this.cardData.cardDescription = chartSettings(resp.type, resp.id, 'cardDescription');
          break;
        case 'descriptionColor':
          this.cardData.descriptionColor = chartSettings(resp.type, resp.id, 'descriptionColor');
          break;
        case 'descriptionFontSize':
          this.cardData.descriptionFontSize = chartSettings(resp.type, resp.id, 'descriptionFontSize');
          break;
        default:
          break;
        }
      }
    });
  }




  ngOnInit() {
    this.isRequestAccess = false;
    if (this.id) {
      this.subs.add(
        this.store.select(selectIndicatorGetById(this.id))
          .pipe()
          .subscribe((data) => {
            if (data.errorMessage && !data.errorMessage?.error?.access) {
              this.isRequestAccess = true;
            }
            if (data.body) {
              this.showFilterOnCard = false;
              this.setData(data.body);
              if (this.cardData.content_classification_key == this.classifications.innovativeStatistics || data.body?.overView) {
                this.cardDataValues = this.cardData.overView;
                this.setCardValue();
                this._cdr.detectChanges();
              }
            }
          }));

      this.store.select(selectStatisticsInsightsGetById(this.id))
        .pipe(distinctUntilChanged((prev, curr) => prev.loader === curr.loader))
        .subscribe((data) => {
          if (data.body) {
            if (data.errorMessage && !data.errorMessage?.error) {
              this.isRequestAccess = true;
            }
            this.cardDataValues = data.body;
            this.setCardValue();
          }
        });
    }
    if (this.isCustom) {
      this.setCustomDataConfig();
    }
  }


  setData(data: any) {
    this.cardData = cloneDeep(data);
    if (this.cardData?.content_classification_key == chartConstants.COMPARE_STATISTICS) {
      this._dashboardService.setIndicatorTitles(this.cardData.component_title, chartConstants.COMPARE_STATISTICS, this.cardData.id);
    }
    this.cardData.checked = this.checked;
    if (this.cardData?.indicatorVisualizations?.visualizationsMeta?.[0]?.seriesMeta?.[0]?.xMax) {
      this.cardData.publish = this._datePipe.transform(new Date(this.cardData?.indicatorVisualizations?.visualizationsMeta?.[0]?.seriesMeta?.[0]?.xMax), 'MMM yyyy');
    }
    if (this.cardData.type != this.chartConstants.INSIGHT_DISCOVERY && this.cardData?.filterPanel?.properties?.length > 0) {
      this.showFilterOnCard = true;
    }
    if (this.cardData.content_classification != chartConstants.COMPARE_STATISTICS_CNT) {
      this.cntType = this.cardData.content_classification_key ? this.cardData.content_classification_key : contentTypeDashboard['analytical-apps'];
      if (this.cardData.content_classification_key == contentTypeDashboard['analytical-apps'] && this.cardData?.type == 'coi') {
        this.cntType = contentTypeDashboard['analytical-apps'];
      }
    } else {
      this.cntType = chartConstants.COMPARE_STATISTICS;
    }
    this.cardData.icon = this.getSavedIcon();
    this.cardData.textColor = this.getSavedColor('textColor');
    this.cardData.descriptionColor = this.getSavedColor('descriptionColor');
    this.cardData.textSize = this.getSavedSize();
    this.cardData.descriptionFontSize = this.getSavedSize('descriptionFontSize');
    this.cardData.cardDescription = this.getSavedDescValues('cardDescription');
    if (!this.cardData.cardDescription) {
      this.cardData.cardDescription = '';
    }
    this.cardData.title = this.cardData.component_title;
    if (this.cntType && this.id && this._dashboardService.chartSettings[this.cntType]?.length) {
      if (this._dashboardService.chartSettings[this.cntType].find((x: { id: any; }) => x.id == this.id)?.title) {
        this.cardData.title = this._dashboardService.chartSettings[this.cntType].find((x: { id: any; }) => x.id == this.id)?.title;
      }
    }
    if (!this.cardData.content_classification_key || this.cardData.content_classification_key == this.classifications.analyticalApps || this.cardData.content_classification_key == ' ') {
      this.visulaizationId = this.cardData?.default_visualisation;
      this.store.select(selectAnalyticGetById(this.id.toString())).subscribe(resp => {
        if (resp.body) {
          this.cardData = cloneDeep(resp.body);
          if (this._dashboardService.chartSettings[this.cntType]?.find((x: { id: string; }) => x.id == this.id)?.title) {
            this.cardData.title = this._dashboardService.chartSettings[this.cntType]?.find((x: { id: string; }) => x.id == this.id).title;
          } else {
            this.cardData.title = this.cardData.component_title;
          }
          if (this.cardData && this.selectedId == this.id) {
            if (this._dashboardService.chartSettings[chartConstants.ANALYTICAL_APPS]?.find((x: { id: string; }) => x.id == this.id).selectedVisualId) {
              this.visulaizationId = this._dashboardService.chartSettings[chartConstants.ANALYTICAL_APPS]?.find((x: { id: string; }) => x.id == this.id)?.selectedVisualId;
            }
            this.createInsightDiscoveryChart();
          }
          this.cardData.checked = this.checked;
        }
      });
      this.value = this.cardData?.value;
      if (this.cardData) {
        this.tagName = this.cardData?.tagName;
        this.currencyData = {
          currency: this.value ? this.value : this.getCurrencyValue((this.cardData?.indicatorValues?.overviewValuesMeta?.[0]?.values), this.cardData?.indicatorValues?.overviewValuesMeta?.[0].aggregation),
          color: '#FFA200'
        };
        if (this.cardData?.indicatorValues?.overviewValuesMeta && this.cardData?.indicatorValues?.overviewValuesMeta.length > 0) {
          this.ratingMeta = this.cardData?.indicatorValues?.overviewValuesMeta[1];
        }
      }
      if (this.isShowchart) {
        if (this.cardData.type == this.chartConstants.INSIGHT_DISCOVERY) {
          this.createInsightDiscoveryChart();
        }
      }
    }
    this._cdr.detectChanges();
  }


  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.['checked']) {
      this.cardData.checked = this.checked;
    }
    if (this.isCustom) {
      this.resetChartTypesSelection();
      this.setCustomDataConfig();
    }
  }

  resetChartTypesSelection() {
    this.customChartTypes.map(x => x.selected = false);
  }


  setCardValue() {
    this.format = this.cardDataValues?.valueFormat?.split('_')[1];
    this.comparison = this.cardDataValues?.compareFilters;
    const comparisonValue = this.comparisonValues[this.comparison];
    this.rating.value = this.cardDataValues?.[comparisonValue?.compare];
    this.rating.percentage = this.cardDataValues?.[comparisonValue?.change];
    this.rating.color = this.rating.value >= 0 ? this.colors.greenDark : (this.rating.value === 0 ? this.colors.colorGreyBg : this.colors.red);
  }

  isNumeric(value: any): boolean {
    return !isNaN(parseFloat(value)) && isFinite(value);
  }

  getCurrencyValue(value: any | [], aggregation: string) {
    const filterIndex = value?.findIndex((data: { type: string }) => data.type === 'ABSOLUTE');
    if (filterIndex !== -1) {
      this.numberType = numberType.shotKeyFormat;
      return +value?.[filterIndex].value;
    }
    const filterPercentage = value?.findIndex((data: { type: string, aggregation: string }) => data.type === 'PERCENTAGE' && data.aggregation === aggregation);
    if (filterPercentage !== -1) {
      this.numberType = numberType.percentage;
      return +value?.[filterPercentage].value;
    }

    return '';
  }

  setChecked(event: any, state: boolean) {
    if (this.isSelectMode) {
      event.stopPropagation();
      this.cardData.checked = !this.isCheckboxDisabled ? state : false;
      this.cardSelected.emit({ id: this.id, select: state, cnt_type: this.cardData.content_classification_key ? this.cardData.content_classification_key : this.classifications.analyticalApps, type: this.cardData?.type, indicatorId: this.cardData?.indicatorId, title: this.cardData?.component_title });
    }
  }


  // #### chart functions #### //

  createInsightDiscoveryChart() {
    this.dataSource = this.cardData?.data_source;
    this.visualizationData = this.cardData?.visualizations?.find((x: { id: number; }) => x.id == this.visulaizationId);
    if (this.visualizationData?.filterPanel?.properties?.length > 0) {
      this.showFilterOnCard = true;
    }
    this.tableData = this.visualizationData?.tableFields;
    this.visualaizationfilterPanel = cloneDeep(this.visualizationData?.filterPanel);
    this.visualaizationValues = this.visualizationData?.indicatorValues?.valuesMeta;
    this.visualaizationFilters = this.visualizationData?.indicatorFilters;
    if (this.visualizationData?.indicatorVisualizations?.visualizationsMeta?.length > 0) {
      this.chartData = this.visualizationData?.indicatorVisualizations?.visualizationsMeta.find((y: { id: any; }) => y.id == this.visualizationData?.indicatorVisualizations?.visualizationDefault);
    }
  }

  changeFilterData(filter: any) {
    const data = {
      meta: [
        {
          dbColumn: this.visualizationData.indicatorVisualizations?.visualizationsMeta[0].dbColumn,
          dbIndicatorId: this.visualizationData.indicatorVisualizations?.visualizationsMeta[0].seriesMeta[0].dbIndicatorId,
          viewName: this.visualizationData.indicatorVisualizations?.visualizationsMeta[0].viewName,
          filterBy: filter
        }
      ]
    };

    this._pageService.getFilterCall(data.meta).subscribe(filterResp => {
      let updatedResponse: any = [];
      this.store.select(selectIndicatorGetById(this.id.toString())).subscribe(resp => {
        updatedResponse = [];
        updatedResponse = cloneDeep(resp);
        if (updatedResponse.body && updatedResponse.body.visualizations?.length > 0) {
          const dataIndex = updatedResponse.body.visualizations.findIndex((x: { id: number; }) => x.id == this.visualizationData.id);
          const metaIndex = updatedResponse.body.visualizations[dataIndex].indicatorVisualizations.visualizationsMeta.findIndex((y: { id: any; }) => y.id == updatedResponse.body.visualizations[dataIndex].indicatorVisualizations.visualizationDefault);
          updatedResponse.body.visualizations[dataIndex].indicatorVisualizations.visualizationsMeta[metaIndex].seriesMeta[0].data = filterResp[0].data;
        }
      });
      const id = this.id;
      const initialization = filterResp.type !== 'insights-discovery' ? updatedResponse.body : updatedResponse.body.visualizations?.[0];
      const postData = { data: { ...updatedResponse.body, id }, status: { status: true, errorMessage: '', loader: false }, initialData: initialization, isRender: true };
      this.store.dispatch(loadAnalyticalSuccess({ data: postData }));
      this.setData(updatedResponse.body);
    });
  }

  changeHeadTitle(event: any) {
    event.stopPropagation();
    this.isTitleEdit = true;
    setTimeout(() => {
      if (this.isTitleHead) {
        this.isTitleHead.nativeElement.style.height = `${this.isTitleHead.nativeElement.scrollHeight + 2}px`;
      }
    }, 100);
  }

  changeDescription(event: any) {
    event.stopPropagation();
    this.openTextArea.emit(true);
    this.isDescriptionEdit = true;
    if (!this.cardData.cardDescription) {
      this.cardData.cardDescription = '';
    }
  }

  getTitle(value: string) {
    this.headerTitle.next(value);
    if (this.isTitleHead) {
      this.isTitleHead.nativeElement.style.height = `${this.isTitleHead.nativeElement.scrollHeight + 2}px`;
    }
  }

  updateDescription(event: any) {
    this.cardData.cardDescription = event.target.value;
    this.descriptionUpdate.next(event.target.value);
  }

  getSavedIcon() {
    let icon: any;
    if (this._dashboardService.chartSettings[this.cntType]?.find((x: { id: string; }) => x.id == this.id)) {
      const savedIcon = this._dashboardService.chartSettings[this.cntType]?.find((x: { id: string; }) => x.id == this.id)?.icon?.icon;
      icon = savedIcon ?? undefined;
    }
    return icon;
  }

  getSavedColor(key: string = 'textColor') {
    let textColor: any;
    const id = this._dashboardService.chartSettings[this.cntType]?.find((x: { id: string; }) => x.id == this.id);
    if (id) {
      textColor = id?.[key];
      textColor = textColor ?? undefined;
    }
    return textColor;
  }

  getSavedSize(key: string = 'textSize') {
    let textSize: any;
    if (this._dashboardService.chartSettings[this.cntType]?.find((x: { id: string; }) => x.id == this.id)) {
      textSize = this._dashboardService.chartSettings[this.cntType]?.find((x: { id: string; }) => x.id == this.id)?.[key];
      textSize = textSize ? textSize : undefined;
    }
    return textSize;
  }

  getSavedDescValues(key: string) {
    let value: any;
    if (this._dashboardService.chartSettings[this.cntType]?.find((x: { id: string; }) => x.id == this.id)) {
      value = this._dashboardService.chartSettings[this.cntType]?.find((x: { id: string; }) => x.id == this.id)[key];
      value = value ? value : undefined;
    }
    return value;
  }


  deleteCard(event: any) {
    event.stopPropagation();
    this.deleteCardEmit.emit({ cntType: this.cntType, id: this.id });
  }

  openFilterPanel() {
    this.openFilter.emit(true);
  }

  getCustomChart(customChart: DbToolbarIcon) {
    this.selectedCustomChart = customChart;
    this.customChartType = this.selectedCustomChart.key;
    this.updateCustomChartType.emit(this.selectedCustomChart.key);
  }

  // **for custom data functions start //

  setCustomDataConfig() {
    if (this._dashboardService.chartSettings[this.cntType]?.find((x: { id: any; }) => x.id == this.id)?.title) {
      this.cardData.title = this._dashboardService.chartSettings[this.cntType].find((x: { id: any; }) => x.id == this.id)?.title;
      this.customChartType = this._dashboardService.chartSettings[this.cntType].find((x: { id: any; }) => x.id == this.id)?.chartType;
      this.cardData.cardDescription = this._dashboardService.chartSettings[this.cntType].find((x: { id: any; }) => x.id == this.id)?.cardDescription;
      if (this.customChartType && !this._dashboardService.chartSettings[this.cntType].find((x: { id: any; }) => x.id == this.id)?.data) {
        this.updateCustomChartType.emit(this.customChartType);
      }
    } else {
      const index = this._dashboardService.chartSettings[this.cntType]?.findIndex((x: { id: any; }) => x.id == this.id);
      if (index >= 0) {
        const cardTitle: string = this._dashboardService.chartSettings[this.cntType][index].title ?? 'Custom Card';
        const cardDescription: string = this._dashboardService.chartSettings[this.cntType][index].cardDescription ?? '';
        this.cardData.title = cardTitle;
        this.cardData.cardDescription = cardDescription;
        this._dashboardService.chartSettings[this.cntType][index].title = this.cardData.title;
      }
    }
  }


}
