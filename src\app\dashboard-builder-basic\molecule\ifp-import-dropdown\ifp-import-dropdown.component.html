<ul class="ifp-db__add-type" [ngClass]="{'ifp-db__add-type--show': showImportTypes}">
  <li class="ifp-db__add-type-item" (click)="openImportIndicators('myApps')">
    <em class="ifp-icon ifp-icon-apps-plus"></em>{{'Import from my bookmarks' | translate}}
  </li>
  <li class="ifp-db__add-type-item" (click)="openImportIndicators('browse')">
    <em class="ifp-icon ifp-icon-browse"></em>{{'Browse Indicators' | translate}}
  </li>
  <!-- <li class="ifp-db__add-type-item" (click)="openImportIndicators('upload')">
  <em class="ifp-icon ifp-icon-upload-thick"></em>{{'Insert/Upload Data' | translate}}
  </li> -->
</ul>
