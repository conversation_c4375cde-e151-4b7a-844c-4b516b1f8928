import { cloneDeep } from 'lodash';
import { CommonModule, NgClass, NgFor } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { buttonClass, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { IfpDbDropdownComponent } from '../../molecule/ifp-db-dropdown/ifp-db-dropdown.component';
import { IfpDbCardComponent } from '../../molecule/ifp-db-card/ifp-db-card.component';
import { Classification, Domain, Items, Product, SubTheme, Theme } from 'src/app/scad-insights/core/interface/self-service.interface';
import { DomainsService } from 'src/app/scad-insights/core/services/domains/domains.service';
import { DbDropDown } from '../../molecule/ifp-db-dropdown/ifp-db-dropdown.interface';
import { Store } from '@ngrx/store';
import { getIndicator } from 'src/app/scad-insights/store/incidicator-list/incidicator-list.action';
import { getStatisticsInsights } from 'src/app/scad-insights/store/statistics-insights/statistics-insights-list.action';
import { SubSink } from 'subsink';
import { selectCategoryResponse } from 'src/app/scad-insights/domains/store/domain.selector';
import { classifications } from 'src/app/scad-insights/core/constants/domain.constants';
import { ScreenerService } from 'src/app/scad-insights/core/services/screener/screener.service';
import { PaginationComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/pagination/pagination.component';
import { selectCounterMyapps } from 'src/app/scad-insights/my-apps/store/myappsList/myappsList.selector';
import { chartConstants } from 'src/app/scad-insights/core/constants/chart.constants';
import { contentType, contentTypeDashboard } from 'src/app/scad-insights/core/constants/contentType.constants';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { IfpNoDataComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { dashboardConstants } from 'src/app/scad-insights/core/constants/dashboard.constants';



@Component({
  selector: 'app-ifp-import-indicators',
  templateUrl: './ifp-import-indicators.component.html',
  styleUrls: ['./ifp-import-indicators.component.scss'],
  imports: [TranslateModule, IfpButtonComponent, NgClass, IfpDbDropdownComponent, IfpDbCardComponent, PaginationComponent,
    NgFor, CommonModule, IfpNoDataComponent]
})

export class IfpImportIndicatorsComponent implements OnInit, OnChanges {

  @Output() closeImport: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() cancel: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() addToDashboard: EventEmitter<number[] | any> = new EventEmitter<number[] | any>();
  @Input() importType: string = 'browse';
  @Input() customaisedCard: any = [];
  @Input() displayedCards: any = [];
  @Input() heading: string = '';
  @Input() primaryButtonText: string = 'Add to Dashboard';
  @Input() selectionLimit: number = 12;
  @Input() isSingleSelect: boolean = false;
  @Input() loader = false;
  @Input() isPrep: boolean = false;
  @Input() isHideOfficialScreener: boolean = false;
  @Input() noDataText = 'You dont have any indicators available';
  @Input() isDashboardTool = false;

  public search = '';
  public showFilter: boolean = true;
  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public selectAllIndicators: boolean = false;
  public selectedIndicators: number[] = [];
  public classificationList: Classification[] = [];
  public domainList: Domain[] = [];
  public themeList: Theme[] = [];
  public subThemeList: SubTheme[] = [];
  public productList: Product[] = [];
  public itemsList: Items[] = [];
  public selectedClassification!: any;
  public selectedTopic!: DbDropDown;
  public selectedTheme!: DbDropDown | undefined;
  public selectedSubTheme!: DbDropDown | undefined;
  public selectedProduct!: DbDropDown | undefined;
  public page: number = 1;
  public subs = new SubSink();
  public screenerFilters: any = [];
  public classifications = classifications;
  public limit: number = 5;
  public chartConstants = chartConstants;
  public offsetPage: number = 1;
  public totalCount: number = 0;
  public chkbxDisable: boolean = false;
  public isEmpty: boolean = true;
  public dashboardConstants = dashboardConstants;
  public activeCard!: string;
  public selectedCards: any = {
    experimental_statistics: [],
    official_statistics: [],
    analytical_apps: [],
    compare_statistics: []
  };


  constructor(private _domainService: DomainsService, private store: Store, private _screenerService: ScreenerService,
    private _dashboardService: DashboardService, private _toaster: ToasterService, private _cdr: ChangeDetectorRef) {
    this._dashboardService.deleteSelectedCard.subscribe(resp => {
      if (resp.cntType && resp.id) {
        const index = this.selectedCards[resp.cntType].findIndex((x: { id: any; }) => x.id == resp.id);
        this.selectedCards[resp.cntType].splice(index, 1);
      }
    });
  }

  ngOnInit() {
    if (this.importType == 'browse') {
      this.getinitaialList();
    } else {
      this.getmyAppsData();
    }
  }



  ngOnChanges(_changes: SimpleChanges): void {
    if (_changes['displayedCards'] && (this.displayedCards?.length > 0 || Object.keys(this.displayedCards).length > 0)) {
      const keys = Object.keys(this.displayedCards);
      keys.forEach(element => {
        this.selectedCards[element] = cloneDeep(this.displayedCards[element]);
      });
    }
    this.chkbxDisable = this.checkDisable() >= this.selectionLimit ? true : false;
    this.isEmpty = this.checkLength();
  }

  // onSearch(keyword: string) {
  // }

  getinitaialList() {
    this.subs.add(this.store.select(selectCategoryResponse).subscribe((data: any) => {
      if (data?.length > 0) {
        if (this.isPrep || this.isDashboardTool) {
          data = data.filter((x: { key: string; }) => x.key != 'analytical_apps');
        }
        if (this.isDashboardTool) {
          data = data.filter((x: { key: string; }) => x.key != 'experimental_statistics');
        }
        if (this.isPrep) {
          data = data.filter((x: { key: string; }) => x.key != 'analytical_apps');
        }
        this.classificationList = cloneDeep(data);
        if (this.classificationList?.length > 0) {
          const index = this.classificationList.findIndex(x => x.key == 'reports');
          this.classificationList.splice(index, 1);
        }
        this.selectedClassification = this.classificationList[0];
        this.domainList = this.selectedClassification.domains;
        this.selectedTopic = this.domainList[0];
        if (this.selectedTopic?.subdomains) {
          this.themeList = this.selectedTopic.subdomains;
        }
        this.selectedTheme = this.themeList[0];
        if (this.selectedTheme?.subthemes) {
          this.subThemeList = this.isHideOfficialScreener ? this.selectedTheme.subthemes.filter((x: { screener: any; }) => !x.screener) : this.selectedTheme.subthemes;
        }
        this.selectedSubTheme = this.subThemeList[0];
        this._cdr.detectChanges();
        this.getProductandItem();
      }
    }));
  }

  closeModal() {
    this.closeImport.emit(false);
  }

  cancelOnly() {
    this.cancel.emit();
  }

  addAllIndicators(indicators: number[]) {
    this.addToDashboard.emit(this.selectedCards);
    this.closeModal();
  }



  selectClassification(event: DbDropDown) {
    this.selectedClassification = event;
    this.resetValues(true);
    setTimeout(() => {
      this.domainList = this.selectedClassification.domains;
      this.selectedTopic = this.domainList[0];
      if (this.selectedTopic?.subdomains) {
        this.themeList = this.selectedTopic.subdomains;
      }
      this.selectedTheme = this.themeList[0];
      if (this.selectedTheme?.subthemes) {
        this.subThemeList = this.isHideOfficialScreener ? this.selectedTheme.subthemes.filter((x: { screener: any; }) => !x.screener) : this.selectedTheme.subthemes;
      }
      this.selectedSubTheme = this.subThemeList[0];
      this._cdr.detectChanges();
      this.getProductandItem();
    }, 100);
  }

  selectTopic(event: DbDropDown) {
    this.selectedTopic = event;
    this.resetValues();
    setTimeout(() => {
      if (this.selectedTopic?.subdomains) {
        this.themeList = this.selectedTopic.subdomains;
      }
      this.selectedTheme = this.themeList[0];
      if (this.selectedTheme?.subthemes) {
        this.subThemeList = this.isHideOfficialScreener ? this.selectedTheme.subthemes.filter((x: { screener: any; }) => !x.screener) : this.selectedTheme.subthemes;
      }
      this.selectedSubTheme = this.subThemeList[0];
      this._cdr.detectChanges();
      this.getProductandItem();
    }, 100);

  }

  selectTheme(event: DbDropDown) {
    this.selectedTheme = event;
    this.subThemeList = [];
    setTimeout(() => {
      if (this.selectedTheme?.subthemes) {
        this.subThemeList = this.isHideOfficialScreener ? this.selectedTheme.subthemes.filter((x: { screener: any; }) => !x.screener) : this.selectedTheme.subthemes;
      }
      this.productList = [];
      this.page = 1;
      this.screenerFilters = [];
      this.selectedSubTheme = undefined;
      this.selectedProduct = undefined;
      this.selectedSubTheme = this.subThemeList[0];
      this._cdr.detectChanges();
      this.getProductandItem();
    }, 100);
  }

  selectSubTheme(event: DbDropDown) {
    this.selectedSubTheme = event;
    this.screenerFilters = [];
    setTimeout(() => {
      this.selectedProduct = undefined;
      this.productList = [];
      this.page = 1;
      this._cdr.detectChanges();
      this.getProductandItem();
    }, 100);

  }

  selectProduct(event: DbDropDown) {
    this.selectedProduct = event;
    this.page = 1;
    this._cdr.detectChanges();
    this.getProductandItem();
  }

  resetValues(isDomain: boolean = false) {
    if (isDomain) {
      this.domainList = [];
    }
    this.selectedSubTheme = undefined;
    this.selectedTheme = undefined;
    this.selectedProduct = undefined;
    this.themeList = [];
    this.subThemeList = [];
    this.productList = [];
    this.screenerFilters = [];
    this.offsetPage = 1;
    this.page = 1;
    this._cdr.detectChanges();
  }


  getThemeAndSubtheme() {
    const classificationId = this.selectedClassification.id;
    const topicId = this.selectedTopic.id;
    this._domainService.getDomainDetailFilter(topicId, classificationId).subscribe(resp => {
      this.themeList = resp;
      this.selectedTheme = this.themeList[0];
      this.subThemeList = this.isHideOfficialScreener ? this.themeList[0].subthemes.filter((x: { screener: any; }) => !x.screener) : this.selectedTheme.subthemes;
      this._cdr.detectChanges();
      this.getProductandItem();
    });
  }

  getProductandItem(initial: boolean = true) {
    const params: any = {
      classification: this.selectedClassification?.id,
      page: this.page,
      limit: this.selectedClassification.key == this.classifications.analyticalApps ? 9999 : this.limit
    };
    if (this.selectedTheme?.id) {
      params['subdomain'] = this.selectedTheme?.id;
    }
    if (this.selectedSubTheme?.id) {
      params['subtheme'] = this.selectedSubTheme?.id;
    }

    if (this.selectedProduct?.pid) {
      params['product'] = this.selectedProduct?.pid;
    }
    if (this.selectedClassification.key != this.classifications.innovativeStatistics) {
      if (this.selectedSubTheme?.screener && !this.isHideOfficialScreener) {
        if (initial) {
          this.callOfficialIndicatorScreener();
        } else {
          this.callScreenerFilters(false)
        }
        return;
      }
      this._domainService.getDomainDetail(this.selectedTopic.id, params).subscribe(resp => {
        this.productList = resp.products;
        this.itemsList = resp.results;
        this.totalCount = resp.total_count;
        const ids: any = [];
        if (this.itemsList?.length > 0) {
          this.itemsList.forEach(element => {
            element.content_classification_key = contentTypeDashboard[element.content_type];
            const dispatchPayload: any = {
              id: element.id,
              contentType: element.content_type
            };
            this.store.dispatch(getIndicator(dispatchPayload));
            ids.push(element.id);
          });
          if (this.selectedClassification.key == this.classifications.officialStatistics) {
            this.store.dispatch(getStatisticsInsights({ id: ids, name: this.selectedClassification.key }));
          }
        }
        this._cdr.detectChanges();
      });
    }

    if (this.selectedClassification.key == this.classifications.innovativeStatistics) {
      if (initial) {
        this._domainService.getFilter('innovative-insights', this.selectedTheme?.screenerConfiguration?.screenerIndicator).subscribe(resp => {
          this.screenerFilters = resp;
          this.callScreenerFilters(true);
        });
      } else {
        this.callScreenerFilters(false);
      }
    }
  }

  callOfficialIndicatorScreener() {
    this._domainService.getFilter('official-insights', this.selectedSubTheme?.screenerConfiguration?.screenerIndicator).subscribe(resp => {
      this.screenerFilters = resp;
      this.callScreenerFilters(true);
    });
  }

  callScreenerFilters(initial: boolean = false) {
    const screenerView = this.selectedClassification.key == this.classifications.innovativeStatistics ? this.selectedTheme : this.selectedSubTheme;
    const payload: any = {
      viewName: screenerView?.screenerConfiguration?.screenerView,
      filters: {},
      sortBy: {
        alphabetical: 'asc'
      }
    };
    if (this.screenerFilters?.length > 0) {
      this.screenerFilters.forEach((element: any) => {
        if (initial) {
          element.value = [element?.default?.value];
          element.options = element.items;
          element.items.find((x: { value: any; }) => x.value == element?.default?.value).checked = true;
        }
        payload.filters[element.key] = element.value;
      });
      this._screenerService.screener(payload, this.limit, this.page, true).subscribe(resp => {
        this.itemsList = resp.data;
        this.totalCount = resp.totalCount;
        if (this.itemsList?.length > 0) {
          const cntType = this.selectedClassification.key == this.classifications.innovativeStatistics ? 'innovative-insights' : 'official-insights';
          this.itemsList.forEach((element: any) => {
            element.content_classification_key = contentTypeDashboard[cntType];
            element.id = element.indicatorId;
            const dispatchPayload: any = {
              id: element.indicatorId,
              contentType: cntType,
              visa: true,
              visaView: this.selectedTheme?.screenerConfiguration?.screenerView
            };
            this.store.dispatch(getIndicator(dispatchPayload));
          });
          this._cdr.detectChanges();
        }
      });
    }
  }


  expFilterSelected(event: any, key: any) {
    let opts: any = [];
    if (event?.length > 0) {
      opts = event.map((element: any) => element.value);
    }
    this.screenerFilters.find((x: { key: any; }) => x.key == key).value = opts;
    this.callScreenerFilters();
  }

  onPageChange(event: any) {
    this.offsetPage = event + 1;
    this.page = (event / this.limit) + 1;
    this.getProductandItem(false);
  }

  limitChanged(event: any) {
    this.offsetPage = 1;
    this.page = 1;
    this.limit = event;
    this.getProductandItem(false);
  }

  selectCards(event: any) {
    const isScreener = this.selectedClassification?.key == this.classifications?.innovativeStatistics ? true : this.selectedSubTheme?.screener;
    if (this.isSingleSelect) {
      this.resetSelectedCards();
      this.selectedCards = { id: event.id, cols: 3, rows: 11, y: 0, x: 0, key: event.cnt_type, type: event.type, indicatorId: event.indicatorId, title: event.title, screener: isScreener };
      this.activeCard = event.id;
    } else {
      if (event.select) {
        if (!this.chkbxDisable) {
          this.selectedCards[event.cnt_type].push({ id: event.id, cols: 3, rows: 11, y: 0, x: 0, key: event.cnt_type, type: event.type, indicatorId: event.indicatorId, title: event.title, screener: isScreener });
        }
      } else {
        const index = this.selectedCards[event.cnt_type].findIndex((x: any) => x.id == event.id);
        if (index >= 0) {
          this.selectedCards[event.cnt_type].splice(index, 1);
        }
      }
      if (this.chkbxDisable && event.select) {
        this._toaster.warning(`Maximum Limit Reached. The maximum limit for this action is ${this.selectionLimit}`);
      }
      this.chkbxDisable = this.checkDisable() >= this.selectionLimit ? true : false;
    }
    this.isEmpty = this.checkLength();
  }

  resetSelectedCards() {
    this.selectedCards = {
      experimental_statistics: [],
      official_statistics: [],
      analytical_apps: [],
      compare_statistics: []
    };
  }

  checkDisable() {
    return Object.keys(this.selectedCards).reduce((acc, key) => acc + this.selectedCards[key]?.length, 0);
  }

  getObjectKeys(obj: any): string[] {
    return Object.keys(obj);
  }

  getChecked(id: string, cntType: any, category: any = '') {
    cntType = (category != '' && category.name == contentType['Forecasts']) ? contentType['analytical_apps']
      : (cntType == contentTypeDashboard['official-insights'] ? contentTypeDashboard['statistics-insights'] : cntType);
    return this.selectedCards[cntType]?.find((x: any) => x.id == id);
  }

  checkLength() {
    let isShow = true;
    for (const key in this.selectedCards) {
      if (this.selectedCards[key]?.length > 0) {
        isShow = false;
      }
    }
    return isShow;
  }




  // import from myApps //

  getmyAppsData() {
    this.itemsList = [];
    this.store.select(selectCounterMyapps).subscribe(resp => {
      this.totalCount = 0;
      const response = cloneDeep(resp);
      if (this.importType == 'myApps') {
        if (response?.node?.length > 0) {
          const ids: any = [];
          response.node.forEach(element => {
            element.id = element.NODE_ID;
            element.content_type = element.CONTENT_TYPE;
            element.content_classification_key = contentTypeDashboard[element.CONTENT_TYPE];
            if (element.content_type != this.classifications.innovativeStatistics) {
              if (element.APP_TYPE != chartConstants.LIVEABILITY && element.CONTENT_TYPE != this.chartConstants.COMPARE_STATISTICS_CHECK) {
                const dispatchPayload: any = {
                  id: element.id,
                  contentType: element.content_type
                };
                this.store.dispatch(getIndicator(dispatchPayload));
              } else if (element.APP_TYPE == chartConstants.LIVEABILITY) {
                this._dashboardService.getLiveabilityData(element.id);
              }
            }
            if (element.content_type == 'statistics-insights') {
              ids.push(element.id);
            }
            if (element.CONTENT_TYPE == this.chartConstants.COMPARE_STATISTICS_TYPE) {
              this._dashboardService.getCompareIndicatorData(element.id, element.TITLE);
            }
            if (element.content_type == this.classifications.innovativeStatistics) {
              const dispatchPayload: any = {
                id: element.id,
                contentType: 'innovative-insights',
                visa: true,
                visaView: null
              };
              this.store.dispatch(getIndicator(dispatchPayload));
            }
          });
          if (ids?.length > 0) {
            this.store.dispatch(getStatisticsInsights({ id: ids, name: 'official_statistics' }));
          }
          this.itemsList = response.node.filter(x => x.content_type != 'publications');
          this._cdr.detectChanges();
        }
      }
    });
  }
}
