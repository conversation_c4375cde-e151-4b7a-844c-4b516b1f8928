@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-import {
  background-color: $ifp-color-white;
  &__inner {
    display: flex;
    min-height: 450px;
  }
  &__card-item {
    width: calc(33.33% - (2 * $spacer-2));
    margin: $spacer-2;
    display: flex;
  }
  &__filter-btn {
    margin-inline-start: auto;
  }
  &__sec-1,
  &__sec-2 {
    padding: $spacer-4;
    max-height: 650px;
    @include ifp-scroll-y(transparent, $ifp-color-grey-7, 4px, 8px);
  }
  &__sec-1 {
    width: 70%;
  }
  &__sec-2 {
    width: 30%;
    background-color: $ifp-color-dropdown-select;
    &--empty {
      display: flex;
      flex-direction: column;
      .ifp-db-list__no-data {
        display: flex;
        margin: auto;
      }
    }
    .ifp-import__card-item {
      width: 100%;
      margin: $spacer-3 $spacer-0;
    }
  }
  &__heading {
    font-size: $ifp-fs-8;
    font-weight: $fw-bold;
    margin-bottom: $spacer-4;
  }
  &__action-wrapper {
    position: relative;
  }
  &__action-sec {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacer-3;
  }
  &__filter-sec {
    background-color: $ifp-color-grey-10;
    border-radius: 10px;
    max-height: 0;
    @include ifp-scroll-y(transparent, $ifp-color-grey-1, 4px, 8px);
    transition: 0.3s;
    position: relative;
    &::after {
      content: "";
      width: 0;
      height: 0;
      border-right: 9px solid transparent;
      border-bottom: 15px solid $ifp-color-grey-10;
      border-inline-start: 9px solid transparent;
      position: absolute;
      bottom: 100%;
      right: 40px;
    }
    &--show {
      overflow: visible;
      max-height: 1000px;
    }
  }
  &__filter-inner {
    display: flex;
    flex-wrap: wrap;
    padding: $spacer-2;
  }
  &__filter-item {
    width: calc(33.33% - (2 * $spacer-3));
    margin: $spacer-2 $spacer-3;
  }
  &__card-list {
    margin-top: $spacer-3;
  }
  &__card-wrapper {
    margin: $spacer-2 (-$spacer-2);
    display: flex;
    flex-wrap: wrap;
  }
  &__footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacer-4;
    border-top: 1px solid $ifp-color-grey-7;
  }
  &__btn-sec {
    display: flex;
    margin-inline-start: auto;
  }
  &__modal-btn {
    margin-inline-start: $spacer-4;
  }
  &__no-data-main {
    margin: 0 auto;
  }
  &--single-select {
    .ifp-import {
      &__sec-1 {
        width: 100%;
      }
    }
  }
  &__loading {
    pointer-events: none;
  }
}

:host::ng-deep {
  .ifp-import {
    &__card-item .ifp-db-card{
      width: 100%;
    }
    &--single-select {
      .ifp-import {
        &__sec-1 {
          @include ifp-scroll-y(transparent, $ifp-color-grey-7, 8px, 8px);
        }
      }
      .ifp-db-card {
        &__arrows {
          display: none !important;
        }
        &--selected {
          background-color: $ifp-color-grey-bg;
          border: 1px solid $ifp-color-grey-7;
        }
      }
    }
  }
}

:host-context(.ifp-dark-theme) {
  ::ng-deep .ifp-import--single-select .ifp-db-card--selected {
       background-color: var(--ifp-color-grey-10);
    }
}
