import { Title } from '@angular/platform-browser';
import { CommonModule, Location } from '@angular/common';
import { AfterViewInit, Component, ElementRef, EventEmitter, inject, OnDestroy, Output, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IfpIndicatorDetailWidgetComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-indicator-detail-widget/ifp-indicator-detail-widget.component';
import { SubSink } from 'subsink';
import { IfpBreadcrumbsComponent } from '../../ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { IFPMsalService } from '../../core/services/IFP-msal.service';
import { PageData } from '../../core/interface/molecule/breadcrumb.interface';
import { IfpGlobalService } from '../../core/services/ifp-global.service';



declare const ga: any;


@Component({
  selector: 'app-ifp-indicator-detail',
  templateUrl: './ifp-indicator-detail.component.html',
  styleUrls: ['./ifp-indicator-detail.component.scss'],
  imports: [CommonModule, IfpIndicatorDetailWidgetComponent, IfpBreadcrumbsComponent]
})
export class IfpIndicatorDetailComponent implements OnDestroy, AfterViewInit {
  @ViewChild('detailPage') detailPage!: ElementRef;

  indicatorId!: string;
  indicatorData: any = [];
  @Output() pageDataEmit = new EventEmitter();
  contentType: string = 'scad_official_indicator';
  public subs = new SubSink();

  public globalService = inject(IfpGlobalService);
  pageData: PageData[] = [
    {
      title: 'Home',
      route: '/home'
    }
  ];

  constructor(private _activatedRoute: ActivatedRoute, private _titleService: Title, private router: Router, private _msalService: IFPMsalService,
    private location: Location, private _router: Router, private elRef: ElementRef
  ) {
    this.subs.add(
      this._activatedRoute.params.subscribe((params) => {
        this.indicatorId = params['id'];
      })
    );
  }


  ngAfterViewInit(): void {
    const observer = new MutationObserver(() => {
      this.passWindowHieght();
    });

    observer.observe(this.elRef.nativeElement, {
      childList: true,
      subtree: true,
    });
  }

  passWindowHieght() {
    const target = window.parent; // or use `window.top` if needed
    const height = this.detailPage?.nativeElement?.offsetHeight + 50;
    target.postMessage({ type: 'DIV_HEIGHT', height: height }, '*');
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }

  createPageData(data: any) {
    if (data?.title) {
      // this._titleService.setTitle(`IFP | ${data.title}`);
      if (data?.title) {
        (window as any)?.dataLayer?.push({
          'event': 'page_load',
          'page_title_var': data.title,
          'userId': this._msalService.convertEmail(this._msalService.getLoginData.account.username)
        });
      }
      this.pageData = [
        {
          title: 'Home',
          route: '/home'
        },
        {
          title: data.parent,
          route: `/domain-exploration/${data.parent}/${data.id}`,
          queryParams: { key: data.content_classification_key, tabName: data.content_classification }
        },
        {
          title: data.title,
          route: ''
        }
      ];
    }
  }

  updateQueryParams(id: string) {
    this.indicatorId = id;
    const queryParams = {
      contentType: 'scad_official_indicator'
    };
    this._router.navigate([`/statistics-insights/'+contentType+'/${id}`], { queryParams: queryParams });
  }
}
