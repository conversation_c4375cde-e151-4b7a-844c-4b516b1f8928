import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnIni<PERSON>,
  signal,
  viewChild,
} from '@angular/core';
import { IfpBgPageComponent } from '../../scad-insights/ifp-widgets/ifp-organism/ifp-bg-page/ifp-bg-page.component';
import { IfpBreadcrumbsComponent } from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IFPHighChartsComponent } from '../../scad-insights/ifp-widgets/charts/ifp-highcharts.component';
import { IfpDropdownComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { IfpIconTextComponent } from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-icon-text/ifp-icon-text.component';
import { IfpUserTagComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-user-tag/ifp-user-tag.component';
import { IfpUserTagGroupComponent } from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-user-tag-group/ifp-user-tag-group.component';
import { IfpButtonComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { DatePipe, NgClass } from '@angular/common';
import { IfpInputAutoResizeDirective } from 'src/app/scad-insights/core/directives/ifp-input-auto-height.directive';
import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';
import { IfpAbbreviationTagComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-abbreviation-tag/ifp-abbreviation-tag.component';
import { FormsModule } from '@angular/forms';
import { dateFormat } from 'src/app/scad-insights/core/constants/date.constants';
import { IfpToggleButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-toggle-button/ifp-toggle-button.component';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { DxpIntegrationApiService } from 'src/app/scad-insights/core/services/dxp-integration-api.service';
import {
  approveConst,
  approveStatus,
  chartConst,
  chartType,
  dxpApi,
  generalizedRoles,
} from '../dxp.constants';
import { ActivatedRoute, Router } from '@angular/router';
import { SubSink } from 'subsink';
import {
  ConditionDxp,
  ConvertedChartData,
  DxpComments,
  DxpDetail,
  DxpFilterConfig,
  DxpFilterPanel2,
  GroupDxp,
  LegendPanelDxpDetail,
} from '../dxp.interface';
import { IfpSpinnerComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { IfpNoDataComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { IfpMarkDownComponent } from 'src/app/scad-insights/ifp-chat-bot/ifp-mark-down/ifp-mark-down.component';
import { DxpValidationPopUpComponent } from '../dxp-validation-pop-up/dxp-validation-pop-up.component';
import { IfpModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpCardLoaderComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { Subscription } from 'rxjs';
import { IfpModalService } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { DxpDonutService } from '../dxp-donut.service';
import { numberChartDatePipe } from 'src/app/scad-insights/core/pipes/numberChart.pipe';
import { Chart } from 'highcharts';
import { AdminService } from 'src/app/scad-insights/core/services/sla/admin.service';

@Component({
  selector: 'ifp-dxp-admin-panel',
  imports: [
    IfpBgPageComponent,
    IfpBreadcrumbsComponent,
    TranslateModule,
    IFPHighChartsComponent,
    IfpDropdownComponent,
    IfpIconTextComponent,
    IfpUserTagComponent,
    IfpUserTagGroupComponent,
    IfpButtonComponent,
    NgClass,
    IfpInputAutoResizeDirective,
    IfpAbbreviationTagComponent,
    FormsModule,
    DatePipe,
    IfpToggleButtonComponent,
    IfpSpinnerComponent,
    IfpNoDataComponent,
    IfpMarkDownComponent,
    DxpValidationPopUpComponent,
    IfpModalComponent,
    IfpCardLoaderComponent
  ],
  templateUrl: './dxp-admin-panel.component.html',
  styleUrl: './dxp-admin-panel.component.scss',
  providers: [DxpDonutService, numberChartDatePipe]
})
export class DxpAdminPanelComponent implements OnInit, OnDestroy {
  // Inject IFPMsalService to get current user information
  private readonly _msalService: IFPMsalService = inject(IFPMsalService);

  // Store current user's email and name
  public currentUserEmail = this._msalService.getLoginData.account.username;
  public currentUserName = this._msalService.getLoginData.account.name;

  // Inject ApiService for API calls
  public _apiService = inject(ApiService);
  public _dxpIntegrationApiService = inject(DxpIntegrationApiService);
  public _donutService = inject(DxpDonutService);

  // Inject ActivatedRoute to access route parameters
  public _activeRoute = inject(ActivatedRoute);

  // Inject Router for navigation
  public _route = inject(Router);

  // Signal to hold DxpDetail data
  public data = signal<DxpDetail | undefined>(undefined);

  // Signal to enable/disable review section
  public enableReview = signal(false);

  // Signal to show/hide main loader
  public loader = signal(true);

  // Signal to show/hide chart loader
  public chartLoader = signal(false);

  // Supported chart types
  public chartType: Record<string, string> = chartType;
  public currentChartType = signal('line');

  // Breadcrumb page data
  public pageData: PageData[] = [
    {
      title: 'Home',
      route: '/home',
    },
    {
      title: 'Government Affairs',
      route: '/dxp',
    },
    {
      title: '',
      route: '',
    },
  ];

  // Signal to hold comments
  public comments = signal<DxpComments[] | []>([]);

  // Date format constant
  public dateFormat = dateFormat;

  // Button class constant
  public buttonClass = buttonClass;

  // Signal to show/hide sidebar
  public hideSideBar = signal(false);

  // Holds new comment input value
  public newComment: string = '';

  // Signal to hold current status
  public currentStatus = signal('');

  // SubSink for managing subscriptions
  public subs = new SubSink();

  // Signal to hold current id from route
  public id = signal('');

  // Holds filter group data
  public filter: GroupDxp[] = [];

  // Approval constants
  public approveConst = approveConst;

  // Signal to enable/disable chat
  public enableChat = signal(false);

  // Inject TranslateService for i18n
  private _translate = inject(TranslateService);

  public _toasterService = inject(ToasterService);

  // Subscription for filter API
  private filterApi!: Subscription;

  // Signal for update popup description
  public updateDec = signal('');

  // Signal for update popup title
  public updateTitle = signal('');

  // Signal for update popup icon
  public updateIcon = signal('');

  // Decide which type of chart was plot
  public chartTypeConfigValue = signal('lineChart');

  public enableCommentBox = signal(false);

  public viewBar = signal(false);

  // series for donut chart
  public seriesDonutChart = signal<{
    name: string;
    colorByPoint: boolean;
    data: { name: string; y: number }[];
  }>({ name: '', colorByPoint: true, data: [] });

  // Highcharts legend options
  public legends: Highcharts.LegendOptions = {
    rtl: this._translate.currentLang === 'ar',
    itemStyle: {
      fontSize: '14px', // set the desired font size here,
      fontFamily: 'Noto Sans',
      fontWeight: '600',
    },
    enabled: true,
    align: 'center',
  };

  // Reference to modal update component
  public modalUpdate = viewChild<IfpModalComponent>('modalUpdate');

  // Signal for Highcharts xAxis options
  public xAxis = signal<Highcharts.XAxisOptions | undefined>(undefined);

  // Approval status constants
  public approveStatus = approveStatus;

  // Generalized roles constants
  public generalizedRoles = generalizedRoles;

  // Signal for default legend selection
  public defaultLegend = signal<LegendPanelDxpDetail | undefined>(undefined);

  // Inject IfpModalService for modal management

  private readonly _router = inject(Router);
  public _modelService = inject(IfpModalService);
  public _themeService = inject(ThemeService);
  public _cdr = inject(ChangeDetectorRef);
  public role =  inject(AdminService).generalizedRole;
  // chart name constant
  public chartConst = chartConst;

  public commentSubscription!: Subscription;

  public centerValue = signal<((chart: Chart) => void) | undefined>(undefined);
  public darkColor = signal<string>(ifpColors.primaryGrey);
  public plotOptions = signal<Highcharts.PlotOptions | undefined>(undefined);

  // Angular lifecycle hook: OnInit
  ngOnInit(): void {
    // Subscribe to route params and fetch detail data
    this.subs.add(
      this._activeRoute.params.subscribe((params) => {
        this.id.set(params['id']);
        this.detailApiCall();
      })
    );
    this.subs.add(
      this._themeService.defaultTheme$.subscribe((val) => {
        if (val == 'dark') {
          this.darkColor.set(ifpColors.white);
        } else {
          this.darkColor.set(ifpColors.primaryGrey);
        }
        this.updatePlotOptions();
        this._cdr.detectChanges();
      })
    );

  }



  // Add a comment or update current status
  addComment() {
    this.addCommentApi(this.newComment.trim());
    this.newComment = '';
  }

  // Show update popup based on status
  createUpdatePop(status: string) {
    if (status === approveConst.approve) {
      this.updateDec.set('The KPI has been successfully approved.');
      this.updateTitle.set('Approved');
      this.updateIcon.set('ifp-icon ifp-icon-tick ');
      this.enableCommentBox.set(false);
      this.modalUpdate()?.createElement();
    } else if (status === approveConst.reject) {
      this.updateDec.set('Are you sure you want to reject this KPI? This action cannot be undone.');
      this.updateTitle.set('Reject KPI');
      this.updateIcon.set('ifp-icon ifp-icon-round-cross');
      this.enableCommentBox.set(true);
      this.modalUpdate()?.createElement();
    } else if (status === approveConst.revert) {
      this.updateDec.set('Are you sure you want to return this KPI? This action cannot be undone.');
      this.updateTitle.set('Return KPI');
      this.updateIcon.set('ifp-icon ifp-icon-refresh');
      this.enableCommentBox.set(true);
      this.modalUpdate()?.createElement();
    }
  }

  // Handle popup button click (navigate to /dxp)
  popupBtnClick() {
    this._route.navigate(['/dxp'], {
      queryParams: {
        tab: 'kpis',
        sideMenu: 'kpis'
      }
    });
  }

  // Close modal and remove all modals
  closeModal() {
    this.modalUpdate()?.removeModal();
    this._modelService.removeAllModal();
  }

  // Call API to add a comment
  addCommentApi(data: string) {
    this.commentSubscription = this._apiService
      .postMethodRequest(
        dxpApi.getCommentList(this.data()?.approvalRequest?.id ?? ''),
        {
          content: data,
        }
      )
      .subscribe({next: () => {
        this.getComments();
        this.commentSubscription?.unsubscribe();
      },
      error: ()=> {
        this.commentSubscription?.unsubscribe();
      }
      });
  }

  // Handle legend changes and filter data
  legendChanges(event: LegendPanelDxpDetail) {
    this.defaultLegend.set(event);
    this.filterData();
  }

  callApproveApi(status = this.currentStatus()) {
    this.subs.add(
      this._dxpIntegrationApiService
        .patchMethodRequest(dxpApi.approve(this.data()?.objectId ?? ''), {
          approvalRequestObjectId: this.data()?.approvalRequest?.id
        }).subscribe(() => {
          this.createUpdatePop(status);
          if (
            this.data() && this.data()?.approvalRequest && this.data()?.approvalRequest?.status
          ) {
            this.data()!.approvalRequest!.status = status;
            if (this.data() && this.data()?.approvalRequest?.approvalActions) {
              this.data()!.approvalRequest!.approvalActions = {};
              this.viewBar.set(false);
            }
          }
        })
    );

  }

  // Update current status via API
  currentStatusUpdate(status = this.currentStatus(), comment: string = '') {
    this.subs.add(
      this._apiService
        .postMethodRequest(
          dxpApi.action(this.data()?.approvalRequest?.id ?? ''), (status === approveConst.reject ||              status === approveConst.revert) ? {
            action: status,
            comment: comment.trim(),
            title: this.data()?.title ?? ''
          } :          {
            action: status,
            title: this.data()?.title ?? ''
          }
        )
        .subscribe(() => {
          this.getComments();
          this.currentStatus.set('');

          if (
            this.data() &&            this.data()?.approvalRequest &&            this.data()?.approvalRequest.status &&            (status === approveConst.approve ||              status === approveConst.reject ||              status === approveConst.revert)
          ) {
            this.data()!.approvalRequest!.status = status;
          }
          if (status === approveConst.approve) {
            this.createUpdatePop(status);
          } else if (status === approveConst.reject){
            this._route.navigate(['/dxp'], {
              queryParams: {
                tab: 'rejected',
                sideMenu: 'approvalStatus'
              }
            });
          } else if (status === approveConst.revert) {
            if (generalizedRoles.approver === this.role()) {
              this._route.navigate(['/dxp'], {
                queryParams: {
                  tab: 'reverted',
                  sideMenu: 'approvalStatus'
                }
              });
            } else {
              this._route.navigate(['/dxp'], {
                queryParams: {
                  tab: 'pending',
                  sideMenu: 'approvalStatus'
                }
              });
            }
          }
        })
    );
  }

  // Fetch detail data from API
  detailApiCall() {
    this.subs.add(
      this._dxpIntegrationApiService
        .getMethodRequest(dxpApi.getDetails(this.id()))
        .subscribe((data: DxpDetail) => {
          this.enableReview.set(
            data?.approvalRequest?.approvalActions?.claim?.active || this.role() === generalizedRoles.builder ? true : false
          );
          this.data.set(data);
          if (this.data()?.approvalRequest?.is_reverted) {
            if (this.data() && this.data()!.approvalRequest) {
              this.data()!.approvalRequest.status = approveStatus.revert;
            }
          }
          this.viewBar.set(
            Object.values(data?.approvalRequest?.approvalActions ?? {}).length              ? true              : false
          );

          this.data()?.filterPanel?.forEach((filterData) => {
            const defaultValues = filterData.default ? (Array.isArray(filterData.default) ? filterData.default : [filterData.default]) : [];
            filterData['selected'] = defaultValues.length ? defaultValues : [];
            filterData['defaultArray'] = defaultValues.length ? defaultValues : [];
          });
          this.defaultLegend.set(
            this.data()?.legendPanel?.find((data) => data.default)
          );
          if (
            this.data()?.visualizationConfig?.chart_configuration
              ?.default_chart_type === chartConst.donut ||            this.data()?.visualizationConfig?.chart_configuration
              ?.default_chart_type == chartConst.pie
          ) {
            this.seriesClick({
              item: this.data()?.series?.xAxis?.categories?.[0] ?? '',
              index: 0,
            });
          }
          this.chartTypeConfigValue.set(
            chartType[
              this.data()?.visualizationConfig?.chart_configuration
                ?.default_chart_type ?? ''
            ] === chartConst.pie              ? 'pieChart'              : 'lineChart'
          );
          this.currentChartType.set(
            chartType[
              this.data()?.visualizationConfig?.chart_configuration
                ?.default_chart_type ?? ''
            ]
          );
          this.pageData[2].title = this.data()?.title ?? '';
          this.xAxis.set({
            lineColor: '#D9DCDD',
            tickColor: '#D9DCDD',
            type: 'category',
            tickLength: 1,
            categories: data?.series?.xAxis?.categories ?? [],
            gridLineWidth: 30,
            lineWidth: 1,
            gridLineColor: '#F8F8F8',
            tickPosition: 'outside',
            startOnTick: false,
            endOnTick: false,
            offset: 20,
            title: {
              text: this.data()?.visualizationConfig?.chart_configuration
                ?.x_axis?.label,
              style: {
                fontSize: '1.5rem',
                fontWeight: '500',
                color: ifpColors.primaryGrey,
              },
            },
            labels: {
              style: {
                fontSize: '1.5rem',
                fontWeight: '600',
              },
              formatter: (value: any) => {
                return value.value;
              },
            },
          });
          this.loader.set(false);
          this.getComments();
          this.updatePlotOptions();
        })
    );
  }

  updatePlotOptions() {
    this.plotOptions.set(this._donutService.updatePlotOptions(this.data()?.visualizationConfig?.chart_configuration?.unit ? ` (${this.data()?.visualizationConfig?.chart_configuration?.unit})`:'', this.data()?.visualizationConfig?.chart_configuration?.default_chart_type ?? '', this.darkColor));
  }

  goToEditPage() {
    this._router.navigateByUrl(
      `/dxp/visualization-wizard?objectId=${this.id()}`
    );
  }

  // Handle sub-filter changes
  changeSubFilter(event: string[]) {
    this.defaultLegend.update(data => {
      if (data) {
        data.selected_values = event;
      }
      return data;
    });
    this.filterData();
  }

  // Fetch comments from API
  getComments() {
    if (!this.data()?.approvalRequest?.id) {
      this.hideSideBar.set(true);
      return;
    }
    this.subs.add(
      this._apiService
        .getMethodRequest(
          dxpApi.getCommentList(this.data()?.approvalRequest?.id ?? '')
        )
        .subscribe({
          next: (data: DxpComments[]) => {
            this.comments.set(data);
            this.enableChat.set(true);
            this.hideSideBar.set(false);
          },
          error: () => {
            this.hideSideBar.set(true);
          }
        })
    );
  }

  // Handle filter changes and update filter group
  changeFilter(data: string[], filter: DxpFilterPanel2) {
    filter['selected'] = data;
    const conditions: ConditionDxp[] = [];
    this.data()?.filterPanel.forEach((filterData) => {
      if (filterData?.selected && filterData?.selected?.length) {
        conditions.push({
          column: filterData.column,
          data_type: filterData.data_type,
          comparator: 'in',
          value: filterData?.selected ?? [],
        });
      }
    });

    this.filter = [{ conditions: conditions, operator: 'and' }];
    this.filterData();
  }

  // click on series dropdown
  seriesClick(event: { item: string; index: number }) {
    const graphData:ConvertedChartData = {
      category: this.data()?.series?.xAxis?.categories ?? [],
      unit: '',
      series: this.data()?.series?.series ?? [],
    };
    const result = this._donutService.seriesClick(event, graphData);
    this.seriesDonutChart.set( result.series);
    this.centerValue.set(this._donutService.centerValue(this.data()?.visualizationConfig?.chart_configuration?.default_chart_type === chartConst.pie ? '' :result.total ));
  }

  // Fetch filtered data for chart
  filterData() {
    this.chartLoader.set(true);
    this.filterApi?.unsubscribe();
    this.filterApi = this._dxpIntegrationApiService
      .postMethodRequest(
        dxpApi.getFilterData(
          this.data()?.product.id ?? '',
          this.data()?.asset?.id ?? ''
        ),
        {
          chart_type:
            this.data()?.visualizationConfig?.chart_configuration
              ?.default_chart_type,
          filters: {
            groups: [
              ...(this.data()?.visualizationConfig?.source_filter?.groups ??                []),
              ...this.filter,
            ],
            global_operator:
              this.data()?.visualizationConfig?.source_filter?.global_operator,
          },
          x_axis: this.data()?.visualizationConfig?.chart_configuration?.x_axis,
          y_axis: this.data()?.visualizationConfig?.chart_configuration?.y_axis,
          legend:
            this.defaultLegend()?.column &&              this.defaultLegend()?.selected_values?.length              ? {
              column: this.defaultLegend()?.column,
              data_type: this.defaultLegend()?.data_type,
              selected_values: this.defaultLegend()?.selected_values,
            }              : null,
        }
      )
      .subscribe((dxpFilterData: DxpFilterConfig) => {
        this.data.update((data) => {
          this.chartLoader.set(false);
          if (data) {
            data['series'] = dxpFilterData?.series;
            this.xAxis.set({
              lineColor: '#D9DCDD',
              tickColor: '#D9DCDD',
              type: 'category',
              tickLength: 1,
              categories: this.data()?.series?.xAxis?.categories ?? [],
              gridLineWidth: 30,
              lineWidth: 1,
              gridLineColor: '#F8F8F8',
              tickPosition: 'outside',
              startOnTick: false,
              endOnTick: false,
              offset: 30,
              title: {
                text: this.data()?.visualizationConfig?.chart_configuration
                  ?.x_axis?.label,
                style: {
                  fontSize: '1.5rem',
                  fontWeight: '500',
                  color: ifpColors.primaryGrey,
                },
              },
              labels: {
                style: {
                  fontSize: '1.5rem',
                  fontWeight: '600',
                },
                formatter: (value: any) => {
                  return value.value;
                },
              },
            });
          }
          if (
            this.data()?.visualizationConfig?.chart_configuration
              ?.default_chart_type === chartConst.donut ||            this.data()?.visualizationConfig?.chart_configuration
              ?.default_chart_type == chartConst.pie
          ) {
            this.seriesClick({
              item: this.data()?.series?.xAxis?.categories?.[0] ?? '',
              index: 0,
            });
          }
          return data;
        });
      });
  }



  // Angular lifecycle hook: OnDestroy
  ngOnDestroy(): void {
    this.subs.unsubscribe();
    this.filterApi?.unsubscribe();
    this.closeModal();
  }
}
