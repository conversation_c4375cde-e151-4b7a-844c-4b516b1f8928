
export const geoMapKeys = {
  selectedRegion: 'abu-dhabi',

  stagingCmsUrl: 'https://bayaan-cms-staging.scad.gov.ae',
  prodCmsUrl: 'https://bayaan-cms-prod-g42.scad.gov.ae',

  popMapKeyCommunity: '861c6c8174d4485da365196ed7f3a317',
  popMapKeyDistrict: '9d143108b3bf4a6da68729474784d8ca',
 
  laborforceMapKeyCommunity: '9d36fb74fa25470487c6f5a814f8d764',
  laborforceMapKeyDistrict: '7499bd551c7e41119664a0aaf767b8d1',
 
  realestateMapKeyCommunity: '2a7f589b1e404a4a8e2870ec866d9d27',
  realestateMapKeyDistrict: '28691cd45763433a98aa0ac487daf87a',

  domains: [ 
    {SELECT_CODE: 1, SELECT_EN: "Population", SELECT_AR: "السكان"},
    {SELECT_CODE: 2, SELECT_EN: "Labour Force", SELECT_AR: "القوى العاملة"},
    {SELECT_CODE: 3, SELECT_EN: "Real Estate", SELECT_AR: "العقارات"},
  ],

  selectionLevels: [
    {SELECT_CODE: 1, SELECT_EN: 'Region', SELECT_AR: 'المنطقة'},
    {SELECT_CODE: 2, SELECT_EN: 'District', SELECT_AR: 'المنطقة الفرعية'},
    {SELECT_CODE: 3, SELECT_EN: 'Community', SELECT_AR: 'التجمع'}
  ],

  mapLayers: [
    {SELECT_CODE: 1, SELECT_EN: 'Heat Map', SELECT_AR: 'خريطة حرارية', DOMAIN_ID: 1, LAYER_NAME: 'HEATMAP'},
    {SELECT_CODE: 2, SELECT_EN: 'Dot Density', SELECT_AR: 'خريطة الكثافة', DOMAIN_ID: 1, LAYER_NAME: 'DOTDENSITY'},
    {SELECT_CODE: 3, SELECT_EN: 'Thematic Map', SELECT_AR: 'خريطة موضوعية', DOMAIN_ID: 1, LAYER_NAME: 'THEMATIC'},

    {SELECT_CODE: 1, SELECT_EN: 'Heat Map', SELECT_AR: 'خريطة حرارية', DOMAIN_ID: 2, LAYER_NAME: 'LABORFORCE_HEATMAP'},
    {SELECT_CODE: 2, SELECT_EN: 'Dot Density', SELECT_AR: 'خريطة الكثافة', DOMAIN_ID: 2, LAYER_NAME: 'LABORFORCE_DOT_DENSITY'},
    {SELECT_CODE: 3, SELECT_EN: 'Thematic Map', SELECT_AR: 'خريطة موضوعية', DOMAIN_ID: 2, LAYER_NAME: 'LABORFORCE_THEMATIC'},

    {SELECT_CODE: 1, SELECT_EN: 'Heat Map', SELECT_AR: 'خريطة حرارية', DOMAIN_ID: 3, LAYER_NAME: 'RER_HEATMAP_BUILDINGS'},
    {SELECT_CODE: 2, SELECT_EN: 'Dot Density', SELECT_AR: 'خريطة الكثافة', DOMAIN_ID: 3, LAYER_NAME: 'RER_BUILDING_DOT_DENSITY'},
    {SELECT_CODE: 3, SELECT_EN: 'Thematic Map', SELECT_AR: 'خريطة موضوعية', DOMAIN_ID: 3, LAYER_NAME: 'RER_BUILDING_THEMATIC'}
  ],

  allRegions: {
    REGION_CODE: 0,
    REGION_AR: 'امارة أبوظبي',
    REGION_EN: 'Abu Dhabi Emirate'
  },

  allDistricts: {
    REGION_CODE: 0,
    DISTRICT_CODE: 0,
    DISTRICT_EN: 'Abu Dhabi Emirate',
    DISTRICT_AR: 'امارة أبوظبي'
  },

  mapRegionsTitle: 'REGION',
  mapDistrictsTitle: 'DISTRICTS',
  mapCommunitiesTitle: 'COMMUNITIES',
  mapDotDensityTitle: "DOTDENSITY",
  mapHeatDefaultRealData: "HEATMAP",
 
  defaultDomain: 1,

  genderIndicator: {
    maleCode: 1,
    maleColor: '#5329E3',
    femaleCode: 2,
    femaleColor: '#DC6F6F'
  },

  citizenshipIndicator: {
    emiratiCode: 1,
    emiratiColor: '#DC6F6F',
    nonEmiratiCode: 2,
    nonEmiratiColor: '#5329E3'
  },

  regionIndicator: {
    abudhabiCode: 101,
    abudhabiColor: '#5329E3',
    alainCode: 102,
    alainColor: '#FF16A2',
    aldhafraCode: 103,
    aldhafraColor: '#DC6F6F'
  },

  defaultQueryParams: {
    'REGION_CODE': [101, 102, 103],
    'DISTRICT_CODE': [],
    'COMMUNITY_CODE': [],
    'CITIZEN_CODE': [],
    'GENDER_CODE': [],
    'MARITAL_CODE': [],
    'ATTAINMENT_CODE': [],
    'HOUSEHOLD_CODE': [], 
    'ENROLLMENT_TYPE_CODE': [],
    'SPECIALIZATION_CODE': [],
    'SCHOOL_ENROLLMENT_LEVEL_CODE': [],

    'BUILDING_TYPE_CODE': [],
    'BUILDING_USE_CODE': [], 

    'UNITS_TYPE_CODE': [],
    'UNITS_USE_CODE': [], 
    
    'YEAR_CODE': [''],
    'QUARTER_CODE': ['']
  },


  defaultRegionCodes: [101, 102, 103],

  domainsMenuId: 'domain',
  regionMenuId: 'region',
  districtMenuId: 'district',
  heatMap: "HEAT_MAP",
  communityMenuId: 'community',
  compareMenuId: 'compare',
  otherMenuId: 'other',

  abudhabiEmirate: 'Abu Dhabi Emirate',
  district: 'District',
  region: 'Region',
  community: 'Community',
  customize: 'Customize',
  preview: 'Preview',
  yearsFilter: 'years',
  citizenshipFilter: 'citizenship',
  genderFilter: 'gender',
  maritalFilter: 'marital',
  attainmentFilter: 'attainment',
  houseHoldFilter: 'houseHold',
  buildingsTypeFilter: 'buildingsType',
  buildingsUseFilter: 'buildingsUse',
  unitsTypeFilter: 'unitsType',
  unitsUseFilter: 'unitsUse',
  mapButtons: ['Customize', 'Preview'],
  mapButtonsAr: ['تخصيص', 'معاينة'],

  regionsFilterLabel: {EN: 'Regions', AR: 'المنطقة', PlaceholderEN: 'Search for Regions', PlaceholderAR: 'ابحث عن المنطقة'},
  districtsFilterLabel: {EN: 'Districts', AR: 'المنطقة الفرعية', PlaceholderEN: 'Search for Districts', PlaceholderAR: 'ابحث عن المنطقة الفرعية'},
  communitiesFilterLabel: {EN: 'Communities', AR: 'التجمع', PlaceholderEN: 'Search for Communities', PlaceholderAR: 'ابحث عن التجمع'},
  compareFilterLabel: {EN: 'Compare', AR: 'مقارنة'},
  otherFilterLabel: {EN: 'Other Filters', AR: 'فلاتر أخرى'},
  selectionLevelLabel: {EN: 'Level', AR: 'المستوى'},
  mapTypeLabel: {EN: ' Type', AR: ' النوع'},
};
