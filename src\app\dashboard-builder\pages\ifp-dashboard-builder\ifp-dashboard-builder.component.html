<div class="ifp-db" #dbPlayGround
  [ngClass]="{'ifp-db--preview': mode === 'preview', 'ifp-db--detail': mode === 'detail', 'ifp-db--tool-open': isToolbarExpanded, 'ifp-db--saving': dashboardSavingState()}">
  @if (!_globalService.isMobileView() &&(dashboardConfig && dashboardConfig.name.length>1)&& (mode !==
  dashboardActions.edit && mode !== dashboardActions.preview)) {
  <div class="ifp-container">
    <app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs>
  </div>
  }
  @if (mode === 'edit') {
  <div class="ifp-db__page-header">
    <div class="ifp-container">
      <div class="ifp-db__header-wrapper">
        <div class="ifp-db__header-left">
          @if (!_globalService.isMobileView()) {
          <span (click)="goBack()" class="ifp-link ifp-db__header-back" [title]="'Back' | translate"><em
              class="ifp-icon ifp-icon-left-arrow"></em> {{'Back' | translate}}</span>
          }
          <div class="ifp-beta-title">
            <p class="ifp-db__page-title">{{'My Dashboards' | translate}}</p>
            <!-- @if ((themeService.defaultLang$|async) === 'en') {
                <img src="../../../assets/images/beta-icon.svg" alt="BETA" class="ifp-beta-icon">
              } @else {
                <img src="../../../assets/images/beta-icon-arabic.svg" alt="BETA" class="ifp-beta-icon">
              } -->
          </div>
        </div>

        <div class="ifp-db__header-right">
          <div class="ifp-db__header-action-sec">
            <!-- <div class="ifp-db__header-action-btn" [title]="'Reset'| translate"><em
                  class="ifp-icon ifp-icon-reset"></em></div> -->
            <!-- <div class="ifp-db__header-action-btn" [title]="'Download'| translate" (click)="exportPDF()"
              [ngClass]="{'ifp-db__header-action-btn--disabled' : cardsArrayEmpty()}"><em
                class="ifp-icon ifp-icon-download-line"></em></div> -->
          </div>
          <div class="ifp-db__header-btn-sec">
            <ifp-button [label]="'Preview' | translate" (ifpClick)="preview('preview')"
              [buttonClass]="!checkAnyCardConfigured() ? buttonClass.secondary : buttonClass.disabled +' '+ buttonIconPosition.right"
              [iconClass]="'ifp-icon-eye'" class="ifp-db__header-btn"></ifp-button>
            @if (isGovAffairs) {
            <ifp-button [label]="'Save as Draft' | translate" (ifpClick)="buttonActionClicked(statusParams.draft)"
              [buttonClass]="!checkAnyCardConfigured() ? buttonClass.secondary : buttonClass.disabled +' '+ buttonIconPosition.right"
              [iconClass]="'ifp-icon-save'" class="ifp-db__header-btn"></ifp-button>
            <!-- <ifp-button [label]="'Save' | translate" (ifpClick)="saveDashboard()"
              [buttonClass]="!cardsArrayEmpty() ? buttonClass.secondary : buttonClass.disabled +' '+ buttonIconPosition.right"
              [iconClass]="'ifp-icon-save'" class="ifp-db__header-btn"></ifp-button> -->
            @if (role === generalizedRoles.builder) {
            <ifp-button [label]="'Proceed' | translate" (ifpClick)="buttonActionClicked(statusParams.draft, true)"
              [buttonClass]="!checkAnyCardConfigured() ? buttonClass.primary : buttonClass.disabled +' '+ buttonIconPosition.right"
              [iconClass]="'ifp-icon-rightarrow'" [loader]="dashboardSavingState()"
              class="ifp-db__header-btn"></ifp-button>
            } @else {
            <ifp-button [label]="'Complete' | translate" (ifpClick)="buttonActionClicked(statusParams.completed)"
              [buttonClass]="!checkAnyCardConfigured() ? buttonClass.primary : buttonClass.disabled +' '+ buttonIconPosition.right"
              [iconClass]="'ifp-icon-save'" class="ifp-db__header-btn"></ifp-button>
            }
            } @else {
            <ifp-button [label]="'Save As Draft' | translate" (ifpClick)="buttonActionClicked(statusParams.draft)"
              [buttonClass]="!checkAnyCardConfigured()  ? buttonClass.secondary : buttonClass.disabled +' '+ buttonIconPosition.right"
              [iconClass]="'ifp-icon-save'" class="ifp-db__header-btn"></ifp-button>

            <ifp-button [label]="'Complete' | translate" (ifpClick)="buttonActionClicked(statusParams.completed)"
              [buttonClass]="!checkAnyCardConfigured() ? buttonClass.primary : buttonClass.disabled +' '+ buttonIconPosition.right"
              [iconClass]="'ifp-icon-save'" class="ifp-db__header-btn"></ifp-button>
            }
          </div>
        </div>
      </div>
    </div>
  </div>
  }



  <div class="ifp-container ifp-db__outer">
    @if (mode !== 'edit' && !isAiDashboard) {
    <div class="ifp-db__head">
      @if (mode === dashboardActions.detail || mode === dashboardActions.preview) {
      <div class="ifp-db__head-left">
        @if (!_globalService.isMobileView() && mode === dashboardActions.preview) {
        <span (click)="mode === dashboardActions.detail ? goBack(): preview('edit')"
          class="ifp-link ifp-db__header-back" [title]="'Back' | translate"><em
            class="ifp-icon ifp-icon-left-arrow"></em> {{'Back' | translate}}</span>
        }
        @if (mainTab === mainTabParams.nativeRecieved && dataHistory.created_by?.email !== '') {
        <div class="ifp-db__recieved-detail">
          <p class="ifp-db__text"><em class="ifp-icon ifp-icon-user-normal"></em>{{'Received from' | translate}}:</p>
          <ifp-user-tag [name]="dataHistory.created_by?.email ?? ''" class="ifp-db__user-detail-tag"></ifp-user-tag>
        </div>
        }
        @if (sendData && sendData?.key) {
        <p class="ifp-db__header-back-label">{{sendData?.key}} : <span
            class="ifp-db__header-back-link">{{sendData?.value}}</span></p>
        }
      </div>

      }
      <!-- @if (mode === 'preview') {
      <div class="ifp-db__message">
        <p class="ifp-db__message-text">{{(!dashboardId ? message : (isPreviousMode == 'edit' ? editMessage:
          '' )) | translate}}</p>
        <div class="ifp-db__message-btn-sec">
          @if (!dashboardId) {
          <ifp-button class="ifp-db__message-btn" [label]="'Yes'" [buttonClass]="buttonClass.primary"
            (ifpClick)="saveDashboard()"></ifp-button>
          }
        </div>
      </div>
      } -->
      <div class="ifp-db__header-right">


        @if (dataHistory?.shared_with_users?.length && this.mode !== dashboardActions.edit && !dashboardSavingState() &&
        ( dataHistory?.created_by?.email === currentUserEmail || (role === generalizedRoles.approver && mainTab !==
        mainTabParams.nativeShared ))) {
        <div class="ifp-db__header-users">
          <ifp-panel-display-dropdown [userDataHistory]="dataHistory"></ifp-panel-display-dropdown>
        </div>
        }

        @if ((selectTab == mainTabParams.nativeMyDashboards || selectTab === mainTabParams.nativeDraft || selectTab ===
        statusParams.draft
        ||
        subTab == statusParams.reverted) && currentUserEmail === dataHistory.created_by?.email && mode ===
        dashboardActions.detail) {
        <div class="ifp-db__btn-round" (click)="preview('edit'); isEdit =true;">
          <em class="ifp-icon ifp-icon ifp-icon-edit"></em>
          <span class="ifp-db__btn-round-text">{{'Edit' | translate}}</span>
        </div>
        }



        @if (((dashboardType === dashboardTypes.native && dashboardStatus === statusParams.completed) || (enableExport))
        && !_globalService.isMobileView())
        {
        <div class="ifp-db__btn-round" (click)="exportPDF()">
          <em class="ifp-icon ifp-icon-download-line"></em>
          <span class="ifp-db__btn-round-text">{{'Export' | translate}}</span>
        </div>
        }
      </div>
    </div>
    <span class="ifp-db__preview-tag">{{'Preview' | translate}}</span>
    }
    <div class="ifp-db__wrapper" [style.height]="isAiDashboard ? 'auto' : gridsterHeight+'px'" #downloadPrint
      (drop)="onDrop($event)" (dragover)="onDragOver($event)">
      <!-- header start -->


      <!-- header end -->

      <!-- dashboard start -->
      <!-- @if (mode === 'edit' || _dashboardService.dashboardProperties.logo !== '' ||
      _dashboardService.dashboardProperties.title !== '') { -->
      <div class="ifp-db__header">
        <div class="ifp-db__header-title-container">
          @if (mode === 'edit') {

          <!-- TO BE UNCOMMENTED AND FIXED START-->
          <div class="ifp-db__db-name-wrapper">
            <input type="text" (click)="isTitleUpdate =true"
              [value]="!dashboardConfig.name.trim() || dashboardConfig.name.trim() === ''  ? (!isTitleUpdate ? 'Untitled' : '') : dashboardConfig.name.trim()"
              class="ifp-db__header-title" [(ngModel)]="dashboardName" [maxlength]="dashboardNameLimit"
              (keyup)="updateDashboardTitle()" #inputTitle>
            <em class="ifp-icon ifp-icon-edit" (click)="inputTitle.focus()"></em>
          </div>

          <!-- TO BE UNCOMMENTED AND FIXED END-->
          @if (dashboardName.trim().length >= dashboardNameLimit) {
          <div class="ifp-db__validation">
            {{'*Maximum limit reached' | translate}}
          </div>
          }
          <!-- @if (dashboardName.trim() === '') {
            <div class="ifp-db__validation">
              {{'*Remove unwanted white spaces' | translate}}
            </div>
          } -->
          } @else {

          <p class="ifp-db__header-title">{{dashboardConfig.name !== '' ? dashboardConfig.name : ('Untitled' |
            translate)}}</p>
          }
        </div>

        <!-- @if (dataHistory && this.mode !== dashboardActions.edit) {
        <div class="ifp-db__header-users">
          <ifp-panel-display-dropdown [userDataHistory]="dataHistory"></ifp-panel-display-dropdown>
        </div>
        } -->


        @if(!dashboardSavingState() || dashboardConfig.logo){
        <div class="ifp-db__logo-sec">
          @if (mode === 'edit') {
          <p class="ifp-db__logo-text">{{'Your logo' | translate}}</p>
          <app-ifp-db-file-uploader [allowedExtensions]="logoFormat" [isImage]="true" (fileUpload)="uploadLogo($event)"
            [previewUrl]="dashboardConfig.logo ?? ''" (removeFile)="deleteLogo($event)"></app-ifp-db-file-uploader>
          } @else {
          <img [src]="dashboardConfig.logo" class="ifp-db__logo">
          }
        </div>
        }
      </div>


      <div class="ifp-db__tabs-wrapper">
        @for (item of dashboardBuilderStore.getTabs(); let index = $index; track $index) {
        <!-- {'ifp-db__tab--active': dashboardBuilderStore.currentTab()?.page_object_id === item.page_object_id} -->
        <div class="ifp-db__tab" [ngClass]="
          {'ifp-db__tab--active': currentTabIndex === index, 'ifp-db__tab--active-prev': index === (currentTabIndex - 1)}
          " (pointerdown)="changeTab(item.page_object_id, index, true, $event)">
          <input type="text" [value]="(item.page_title ?? 'Untitled tab ' + (index+1))" class="ifp-db__tab-title"
            [readOnly]="((mode === dashboardActions.edit) && (currentTabIndex !== index)) || mode !== dashboardActions.edit"
            (keyup)="updateTabTitle($event, item)"
            [title]="item.page_title && item.page_title !== ('Untitled' | translate) ? (item.page_title | translate) : ''"
            (focus)="clearDefaultTabValue($event)" (blur)="setTabValue($event)">
          @if (mode === 'edit' && (dashboardBuilderStore.getTabs().length !== 1)) {
          <em class="ifp-icon ifp-icon-cross ifp-db__tab-icon" (click)="tabAction(index, $event)"></em>
          }
        </div>
        <!-- <div class="ifp-db__tab"
          [ngClass]="{'ifp-db__tab--active': dashboardBuilderStore.currentTab()?.page_object_id === item.page_object_id}"
          (click)="changeTab(item.page_object_id, index)">
          <input type="text" [value]="(item.page_title ?? 'Untitled tab ' + (index+1))" class="ifp-db__tab-title"
            [disabled]="mode != dashboardActions.edit" (keyup)="updateTabTitle($event, item)">
          @if (mode === 'edit') {
          <em
            class="ifp-icon ifp-db__tab-icon {{ index != dashboardBuilderStore.getTabs().length - 1  ? 'ifp-icon-cross' : 'ifp-db__tab-icon--plus ifp-icon-plus'}}"
            (click)="tabAction(index)"></em>
          }
        </div> -->
        }

        @if (mode === 'edit') {
        <div class="ifp-db__tab ifp-db__tab--add" [ngClass]="{'ifp-db__tab--add-disabled' : checkAnyCardConfigured()}">
          <em class="ifp-icon ifp-icon-plus ifp-db__tab-icon" (click)="tabAction(-1, $event)"></em>
        </div>
        }
        <div class="ifp-db__tab ifp-db__tab--spacer"></div>
      </div>


      <!-- card listing start -->
      @if (dashboardPageLoader) {
      <div class="ifp-db__loader">
        <app-ifp-spinner></app-ifp-spinner>
      </div>
      } @else {
      <gridster [options]="options" [style.height]="isAiDashboard ? 'auto' : gridsterHeight+'px'">
        <div class="ifp-db__inner-wrapper" #dbHeader appResizeObserver (sizeChanged)="setGridsterOption()">
          @for (card of createdCards(); let j=$index; track card) {
          <gridster-item class="ifp-db__gridster-item" [item]="card"
            [ngClass]="{'ifp-db__gridster-item--selected': selectedCardId() == card.metadata.cardId && mode == 'edit', 'ifp-db__gridster-item--preview': mode !== 'edit', 'ifp-db__gridster-item--hide': (!card?.metadata?.x_axis_category?.length && !card?.metadata?.y_axis_series?.length) && card.type === cardTypes.chart && mode !== 'edit'}">
            <!-- <app-ifp-db-card [id]="card.id" [isSelectMode]="false" class="ifp-db__card"
              [ngClass]="{'ifp-db__card--selected' : selectedId == card && mode == 'edit'}" [isShowchart]="true"
              (click)="selectCard(card.id, card)" (touchend)="selectCard(card.id, card)" [selectedId]="selectedId"
              [cntType]="card.key != dashboardConstants.customCard ? contentType : dashboardConstants.customCard"
              [isDashboardCard]="true" (updatedCardTitle)="updateTitle($event, 'title')"
              (deleteCardEmit)="deletaCard($event)" (openFilter)="openFilterPanel($event)" [mode]="mode"
              (updatedCardDescription)="updateTitle($event, 'cardDescription')" (openTextArea)="openTextArea($event)"
              [accessPending]="card.requestNode" [isCustom]="card.key == dashboardConstants.customCard"
              [customChartType]="card.chartType" (updateCustomChartType)="updateCustomChart($event, j)"
              [dashboardView]="dashboardId"></app-ifp-db-card> -->
            @if (card.type == cardTypes.widget) {
            <ifp-kpi-template-card [ngClass]="{
            'ifp-db__card--template-switch': (card?.metadata?.cardConfig?.customConfig?.bgColor === ifpColors.white && currentTheme() === 'dark') || (card?.metadata?.cardConfig?.customConfig?.bgColor === ifpColors.sectionWhiteDark && currentTheme() === 'light')
            }" [cardConfig]="card?.metadata?.cardConfig?.config" [name]="card?.metadata?.customConfig?.name"
              (selectEvent)="onSelectKpiEvent($event, card)" class="ifp-db__card ifp-db__card--template"
              (pointerup)="selectCard(card.metadata.cardId, card, $event)"
              [isSelected]="selectedCardId() === card.metadata.cardId"
              [alignment]="card?.metadata?.cardConfig?.customConfig?.alignment"
              [isPrecisePrimary]="card?.metadata?.cardConfig?.customConfig?.isPrecisePrimary"
              [isPreciseSecondary]="card?.metadata?.cardConfig?.customConfig?.isPreciseSecondary" [mode]="mode"
              [ngStyle]="{'background-color': card?.metadata?.cardConfig?.customConfig?.bgColor ?? widgetCardBackgroundColor(), 'color': card?.metadata?.cardConfig?.customConfig?.textColor ?? widgetCardTextColor()}"></ifp-kpi-template-card>
            }

            @if (card.type == cardTypes.chart) {
            <ifp-dashboard-chart-card class="ifp-db__card" [isActiveCard]="selectedCardId() == card.metadata.cardId"
              [mode]="mode" (pointerup)="selectCard(card.metadata.cardId, card, $event)"
              (emitConfigureData)="openConfigurePanel($event, card)" [cardConfig]="card"
              (cardActionCliked)="cardActionClicked($event, card, j)"
              [seriesOptions]="selectedCard()?.metadata?.['x_axis_category']"
              (updatePieSeries)="updatePieSeries($event)" (emitUpdateText)="updateText($event)"
              (selectCard)="selectTextCard(card)"></ifp-dashboard-chart-card>
            }

            @if (card.type == cardTypes.element) {
            <ifp-rich-text-editor class="ifp-db__card ifp-db__card--rich-text"
              [ngClass]="{'ifp-db__card--active': selectedCardId() === card.metadata.cardId && mode === dashboardActions.edit}"
              [content]="card.metadata.cardConfig['element_text'] ?? ''"
              [style]="card.metadata.cardConfig['element_style'] ?? {}" [mode]="mode"
              [isActiveCard]="selectedCardId() === card.metadata.cardId && mode === dashboardActions.edit"
              [isEditMode]="selectedCardId() === card.metadata.cardId && cardEditMode && mode === dashboardActions.edit"
              (contentChange)="card.metadata.cardConfig['element_text'] = $event" (pointerup)="selectRichTextCard(j)"
              (editorActionClicked)="cardActionClicked($event, card, j)"
              (saveConfig)="saveTextCardConfig($event, card)"></ifp-rich-text-editor>
            }
          </gridster-item>
          }
          @if (cardsArrayEmpty() && mode === 'edit') {
          <div class="ifp-db__card ifp-db__card--add-indicator" (click)="openAddIndicatorsModal(cardTypes.chart)">
            <em class="ifp-icon ifp-icon-plus"></em>
            <p class="ifp-db__add-text">{{'Add Card' | translate}}</p>

            <!-- @if (showImportTypes()) {
            <ifp-import-dropdown [sourceList]="dataSourceList" [showImportTypes]="showImportTypes()"
              (openImportType)="openImportIndicators($event)" class="ifp-db__import-pop-up"></ifp-import-dropdown>
            } -->

          </div>
          }

        </div>
      </gridster>
      }
      <!-- card listing end -->
      <!-- dashboard start -->
    </div>
    @if (mode === 'detail' && (role === generalizedRoles.approver|| generalizedRoles.builder) &&
    !_globalService.isMobileView()) {
    <!-- <div class="ifp-node__txt-icon">
      <ifp-icon-text class="ifp-node__source" [icon]="'ifp-icon-text'" [text]="'Sample Product name'"
        [key]="'Product'"></ifp-icon-text>
      <ifp-icon-text class="ifp-node__source" [icon]="'ifp-icon-text'" [text]="'sample asset name'"
        [key]="'Asset'"></ifp-icon-text>
    </div> -->

    <!-- Hide entire footer for read-only tabs (unpublished/rejected) -->
    @if (shouldShowFooter() && dashboardType === dashboardTypes.govAffairs && role !== generalizedRoles.explorer) {
    <div class="ifp-db__footer-box" [ngClass]="{'ifp-db__footer-box--revert-only': shouldApplySingleButtonStyling()}">
      <!-- Show toggle only when not on approver reverted tab -->
      @if (shouldShowToggleButton()) {
      <div class="ifp-db__footer-toggle">
        {{'Click to assign or unassign this Dashboard for review'}} <app-ifp-toggle-button
          (toggleChange)="enableReview.set($event);currentStatusUpdate(enableReview()? approveConst.claim: approveConst.unclaim);"
          [enable]="enableReview()" class="ifp-db__footer-toggle-btn"></app-ifp-toggle-button>
      </div>
      }
      <!-- Show footer buttons only when not on read-only tabs (unpublished/rejected) -->
      @if (shouldShowAnyFooterButtons()) {
      <div class="ifp-db__footer-btn-sec"
        [ngClass]="{'ifp-db__footer-btn-sec--single': shouldApplySingleButtonStyling()}"
        [ngStyle]="shouldApplySingleButtonStyling() ? {'justify-content': 'flex-end', 'margin-left': 'auto'} : {'margin-left': 'auto'}">
        <!-- Show Revert button only for approvers/creators on approval-status -> reverted tab -->
        @if (shouldShowRevertButton()) {
        <ifp-button [label]="'Return'" (ifpClick)="onSelectToolbarAction(approveConst.revert)"
          [buttonClass]="(enableReview() ? buttonClass.secondary : buttonClass.disabled) +' '+ buttonIconPosition.right"
          [iconClass]="'ifp-icon-refresh'" class="ifp-db__footer-btn"></ifp-button>
        }

        <!-- Show Reject and Approve buttons for all cases except approvers/creators on approval-status -> reverted tab -->
        @if (shouldShowRejectApproveButtons()) {
        <ifp-button [label]="'Reject'" (ifpClick)="onSelectToolbarAction(approveConst.reject)"
          [buttonClass]="(enableReview() ? buttonClass.secondary : buttonClass.disabled) +' '+ buttonIconPosition.right"
          [iconClass]="'ifp-icon-round-cross'" class="ifp-db__footer-btn"></ifp-button>
        @if(dashboardConfig.approval_status == approveStatus.pending){
        <ifp-button [label]="'Approve'" (ifpClick)="onSelectToolbarAction(approveConst.approve)"
          [buttonClass]="(enableReview() ? buttonClass.primary : buttonClass.disabled) +' '+ buttonIconPosition.right"
          [iconClass]="'ifp-icon-tick-border'" class="ifp-db__footer-btn"></ifp-button>
        }
        }
      </div>
      }
    </div>
    }
    }
  </div>

  @if (!cardsArrayEmpty() && mode === 'edit') {
  <div class="ifp-db__add-indicator" (click)="openAddIndicatorsModal(cardTypes.chart)"
    [ngClass]="{'ifp-db__add-indicator--right' : isLeftCut}">
    <div class="ifp-db__add-indicator-relative">
      <em class="ifp-icon ifp-icon-plus"></em><span class="ifp-db__add-indicator-text">{{'Add Card' |
        translate}}</span>

      <!-- @if (showImportTypes()) {
      <ifp-import-dropdown [showImportTypes]="showImportTypes()" [sourceList]="dataSourceList"
        (openImportType)="openImportIndicators($event)" class="ifp-db__import-pop-up"></ifp-import-dropdown>
      } -->
    </div>
  </div>
  }

</div>

<!-- toolbar start -->
@if (mode === 'edit') {
<div class="ifp-db__toolbar ifp-db__toolbar--right resizable" #toolbar (mousedown)="onMouseDown($event)"
  (document:mouseup)="onMouseUp()" (document:mousemove)="onMouseMove($event)" [ngClass]="{'ifp-db__toolbar--expand': isToolbarExpanded, 'ifp-db__toolbar--filter': isFilterPanel, 'ifp-db__toolbar--sticky': isSticky,
    'ifp-db__toolbar--resize': isSelectedTool == 'data'
  }" #sideBar (mousedown)="onMouseResizeDown($event)" (document:mouseup)="onResizeMouseUp($event)"
  (document:mousemove)="onResizeMouseMove($event)"
  [ngStyle]="{'max-width': isToolbarExpanded ? toolbarWidth+'px': 30+'px'}">
  <div class="ifp-db__toolbar-inner " [ngClass]="{'ifp-db__toolbar-inner--drag' : isDragging}">
    <em class="ifp-icon ifp-icon-left-arrow ifp-db__toolbar-toggle content"
      [appIfpTooltip]=" (isToolbarExpanded ? 'Collapse' : 'Expand')| translate"
      (click)="expandOrCollapseToolbar(!isToolbarExpanded)" [zIndex]="2000"></em>
    <ifp-chart-toolbar #toolbarCmp [selectedTabId]="selectedTab().page_object_id" [selectedCard]="selectedCard()"
      [sourceName]="sourceName()" [aggregationLoader]="chartAggregationLoader"
      [selectedSourceFilter]="selectedSourceFilter()" (createCardActionOutput)="createCardAction($event)"
      (emitChartToolUpdate)="updateChartSettings($event)" (viewSourceData)="openViewSourceDataModal()"
      (selectWidgetTemplate)="selectWidgetTemplate($event)" (toolbarActions)="onSelectToolbarAction($event)"
      (updateFormValues)="updateCardAxisConfig($event)"></ifp-chart-toolbar>
  </div>
</div>
}
<!-- toolbar end -->
<!-- Comment panel - show based on conditional logic -->
@if (shouldShowCommentBox()) {
<div class="ifp-db__comment-panel" [ngClass]="{'ifp-db__comment-panel--collapse': hideSideBar()}">
  @if(enableChat() || shouldShowCommentIcon()) {
  <span class="ifp-db__tool-bar-opener" (click)="hideSideBar.set(!hideSideBar())"> <em
      class="ifp-icon ifp-icon-chat-empty ifp-db__tool-bar-opener-icon"></em></span>
  }
  <div class="ifp-node__card-right">
    <div class="ifp-chart-toolbar__action-box ifp-db__tool-bar-header">
      <span class="ifp-db__revert-inline">{{'Comments' | translate}}</span>
    </div>
    @if (comments().length) {
    <div class="ifp-chart-toolbar__action-box ifp-db__comment-wrapper">
      @for (comment of comments(); track comment; let index = $index; let last = $last) {
      <div class="ifp-db__comment-item"
        [ngClass]="{'ifp-db__comment-item--self':comment?.userDetails?.email == currentUserEmail}">
        @if(comment.commentType === commentType.system) {
        <div class="ifp-db__comment-name">
          <ifp-abbreviation-tag [text]="comment?.userDetails?.name?? ''"
            class="ifp-db__name-tag"></ifp-abbreviation-tag>
          <p class="ifp-db__comment-desc">
            <ifp-mark-down [data]="comment.content"></ifp-mark-down>
          </p>
        </div>
        <p class="ifp-db__comment-time">{{comment.createdAt | date: dateFormat.dateAndTime}}</p>
        } @else {
        <div class="ifp-db__comment-name">
          <ifp-abbreviation-tag [text]="comment?.userDetails?.name?? ''"
            class="ifp-db__name-tag"></ifp-abbreviation-tag>
          <p class="ifp-db__comment-desc">{{comment?.userDetails?.name }}</p>
        </div>
        <p class="ifp-db__comment">{{comment.content}}</p>
        <p class="ifp-db__comment-time">{{comment.createdAt | date: dateFormat.dateAndTime}}</p>
        <!-- <div class="ifp-db__comment-name">
            @if(index !== 0 && comment?.userDetails?.email !== comments()[index-1].userDetails?.email) {

            } @else if(comment?.userDetails?.email !== currentUserEmail){
            <ifp-abbreviation-tag [text]="comment?.userDetails?.name?? ''" class="ifp-db__name-tag"></ifp-abbreviation-tag>
            <p class="ifp-db__comment-desc">{{comment?.userDetails?.name }}</p>
            }

          </div>
          <p class="ifp-db__comment">{{comment.content}}</p>
          <p class="ifp-db__comment-time">{{comment.createdAt | date: dateFormat.mediumDate}}</p> -->
        }

      </div>
      }
    </div>
    }
    <!-- @if( data()?.approvalRequest?.status === approveStatus.pending || data()?.approvalRequest?.status ===
        approveStatus.revert) { -->
    <!-- Show comment input section only when not on read-only tabs (unpublished/rejected) -->
    @if (shouldShowCommentPanelButtons()&& !hideSideBar()) {
    <div class="ifp-chart-toolbar__action-box ifp-db__comment-edit">

      <p class="ifp-db__textarea-label">{{'Add a comments' | translate}}</p>
      <textarea [placeholder]="'Type your comment here' | translate" appIfpInputAutoResize
        class="ifp-input-textarea ifp-db__textarea" [(ngModel)]="newComment"></textarea>
      <div class="ifp-db__btn-sec">
        <ifp-button [label]="'Cancel'"
          [buttonClass]="newComment.trim() === '' ? buttonClass.disabled : buttonClass.secondary"
          class="ifp-db__comment-btn" (ifpClick)="newComment = '';hideSideBar.set(true)"></ifp-button>
        <ifp-button [label]="'Submit'"
          [buttonClass]="newComment.trim() === '' ? buttonClass.disabled : buttonClass.primary"
          class="ifp-db__comment-btn" (ifpClick)="addComment()"></ifp-button>
      </div>

    </div>
    }
    <!-- } -->
  </div>
</div>

}


<app-ifp-modal #loaderModal [bgColor]="ifpColors.colorGreyBg" [modalClass]="'ifp-modal__import-indicators'">
  <div class="ifp-db__loader">
    <app-ifp-spinner [text]="'Saving' | translate"></app-ifp-spinner>
  </div>
</app-ifp-modal>

<!-- Confirmation Modals start -->

<app-ifp-modal #alertModal>
  <app-ifp-remove-card (firstButtonEvent)="confirmRemoveTab($event)" (secondButtonEvent)="alertModal.removeModal()"
    [text]="'Are you sure you want to close the tab?'" [firstButton]="'Continue'" [secondButton]="'Cancel'">
  </app-ifp-remove-card>
</app-ifp-modal>

<app-ifp-modal #alertCardModal>
  <app-ifp-remove-card (firstButtonEvent)="confirmRemoveCard($event)" (secondButtonEvent)="alertCardModal.removeModal()"
    [text]="'Are you sure you want to remove this card?'" [firstButton]="'Continue'" [secondButton]="'Cancel'">
  </app-ifp-remove-card>
</app-ifp-modal>

<app-ifp-modal #alertCardModal>
  <app-ifp-remove-card (firstButtonEvent)="confirmRemoveCard($event)" (secondButtonEvent)="alertCardModal.removeModal()"
    [text]="'Are you sure you want to remove this card?'" [firstButton]="'Continue'" [secondButton]="'Cancel'">
  </app-ifp-remove-card>
</app-ifp-modal>

<app-ifp-modal #unConfiguredCardWarningModal>
  <app-ifp-remove-card (firstButtonEvent)="saveDashboard(saveType)"
    (secondButtonEvent)="unConfiguredCardWarningModal.removeModal()"
    [text]="'By proceeding, you will lose any unconfigured cards.'" [discription]="unconfiguredMessage | translate"
    [firstButton]="'Continue'" [secondButton]="'Cancel'">
  </app-ifp-remove-card>
</app-ifp-modal>

<!-- Confirmation Modals end -->

<app-ifp-modal [modalClass]="'ifp-modal__edit-kpi'" [overlayType]="'transparent'" #editKpiModal>
  @if (isKpiModalOpen) {
  <ifp-edit-kpi-modal [isEditMode]="kpiIsEdit" [style.visibility]="isImportIndicatorsOpen ? 'hidden' : 'visible'"
    (sourceSelection)="selectedSource = $event; openAddIndicatorsModal(cardTypes.widget)"
    [kpiDetails]="selectedKpiData()" [sourceList]="dataSourceList" [selectedCardConfig]="selectedCard()"
    [possibleAggregationColumns]="selectedKpiData()['metadata']?.possibleAggregations"
    (closeModal)="closeKpiEditModal()" (updateChanges)="getUpdatedWidgetDetails($event)"
    (applyChanges)="setWidgetCardConfig($event)"
    [widgetAggregationLoader]="widgetAggregationLoader"></ifp-edit-kpi-modal>
  }
</app-ifp-modal>

<app-ifp-modal #addIndicatorsModal [overlayType]="'transparent'" [modalClass]="'ifp-modal__import-indicators'">
  <ifp-import-data-modal [dataSourceList]="dataSourceList" [(selectedSource)]="selectedSource"
    [isEmpty]="(selectedSourcesCards()?.length ?? 0) <= 0 && sourceModelType() != cardTypes.widget"
    (closeImport)="closeImportDataModal()" (addToDashboard)="addAllIndicators()">
    @if (isImportIndicatorsOpen) {
    <div class="ifp-add-data__import-sec">
      @switch (selectedSource) {
      @case ('library') {
      <ifp-prep-library [isEmbedded]="true" (selectFileAll)="getFromLibrary($event)"
        (closeModal)="closeImportDataModal()" [isLibrary]="true" [viewTab]="false" [cardView]="true"
        [enableCheckBox]="true" (emitSelectedCard)="getSelectedIndicators($event)"
        [selectedCards]="selectedSourcesCards()"
        [singleSelect]="sourceModelType() == cardTypes.widget"></ifp-prep-library>
      }
      @case ('upload') {
      <ifp-sc-upload-model class="ifp-db__upload-model" (closeUploadModel)="closeImportDataModal()"
        [selectedCards]="selectedSourcesCards()" (uploadOrAddData)="uploadOrAddData($event)" [emitFileEnable]="true"
        (emitUploadFile)="uploadFile($event)" (emitRemoveFile)="removeUploadedFile($event)" [hideFooter]="true"
        [uploadDataProgess]="uploadDataProgess()" [isFailed]="isUploadFail"></ifp-sc-upload-model>
      }
      @case ('dxp') {
      @if (dataHistory && dataHistory.shared_with_users?.length) {
      <app-ifp-no-data [message]="'Cannot Import Data!'"
        [description]="'This dashboard is being shared with other users.'"></app-ifp-no-data>
      } @else {
      <ifp-dxp-popup (selectedProductDetail)="onSelectedProduct($event)" [loader]="productListLoader"
        [isEmbedded]="true" [assetList]="productdetailList" (searchAsset)="getAssetList($event)"
        [isMultiSelect]="selectedCard().type !== cardTypes.widget" (emitMultipleAssets)="getSelectedIndicators($event)"
        (emitSingleAsset)="getSelectedIndicators($event)" [displayedCards]="selectedSourcesCards()"
        [isMultiSelect]="sourceModelType() !== cardTypes.widget"></ifp-dxp-popup>
      }
      }
      @case ('myApps') {
      <app-ifp-import-indicators [isDashboard]="true" [isEmbedded]="true" (closeImport)="closeImportDataModal()"
        (addToDashboard)="getSelectedIndicators($event)" [importType]="selectedSource"
        [displayedCards]="selectedSourcesCards()" [emitBasedSelection]="true" [sourceModelType]="sourceModelType()"
        [isSingleSelect]="sourceModelType() == cardTypes.widget" [isDashboardTool]="true"></app-ifp-import-indicators>
      }
      @case ('browse') {
      <app-ifp-import-indicators [isDashboard]="true" [isEmbedded]="true" (closeImport)="closeImportDataModal()"
        (addToDashboard)="getSelectedIndicators($event)" [importType]="selectedSource"
        [displayedCards]="selectedSourcesCards()" [emitBasedSelection]="true" [sourceModelType]="sourceModelType()"
        [isSingleSelect]="sourceModelType() == cardTypes.widget" [isDashboardTool]="true"></app-ifp-import-indicators>
      }
      @default {}
      }
    </div>
    }
    <div class="ifp-add-data__selected-list">
      @for (card of selectedSourcesCards(); track card; let index= $index) {
      @switch (card.sourceType) {
      @case (dataTypes.dataset) {
      <ifp-dashboard-library-preview (checked)="removeSelect(index)" [title]="card.title" [type]="card.fileType"
        class="ifp-db__import-item"></ifp-dashboard-library-preview>
      }
      @case (dataTypes.dxp) {
      <ifp-dxp-card (selectCard)="removeSelect(index)" [isSelected]="true" [isSelectMode]="true" [isList]="true"
        [title]="card.title" [subTitle]="card?.source?.org_name || ''" class="ifp-db__import-item"
        [cardData]="card"></ifp-dxp-card>
      }
      @default {
      <!-- @if(card.cardType ==='myApps' || card.cardType ==='browse' ) { -->
      <!-- Used card.cardId (node id) so the left-side card can load its metadata via selectIndicatorGetById -->
      <app-ifp-db-card (cardSelected)="removeSelect(index)" [id]="card.cardId" [isSelectMode]="true"
        class="ifp-db__import-item" [checked]="true" [enableCheckbox]="true"></app-ifp-db-card>
      <!-- } -->
      }
      }
      <!-- <app-ifp-db-card [id]="card.id" [isSelectMode]="true" class="ifp-db__import-item" [checked]="true"
        [enableCheckbox]="true"></app-ifp-db-card> -->
      }

    </div>
  </ifp-import-data-modal>

</app-ifp-modal>

<app-ifp-modal #approverActionModal [overlayType]="'transparent'" [modalClass]="'ifp-modal__small-template'">
  <ifp-dxp-validation-pop-up (close)="closeModal()" [textEnable]="enableCommentBox()" [desc]="approvalDesc()"
    [icon]="approvalIcon()" [secondaryBtnLabel]="'Cancel'" [heading]="approvalTitle()" [enableCloseButton]="true"
    [enableDoubleButton]="true" (secondaryBtn)="closeModal()"
    (primaryBtn)="approverPrimaryAction(currentStatus(),$event)"
    [primaryBtnLabel]="enableCommentBox() ?  (currentStatus() == approveConst.revert ? 'Return' : 'Submit'): 'View Published Dashboards'"
    [successPopup]="currentStatus() == approveConst.approve"></ifp-dxp-validation-pop-up>
</app-ifp-modal>

<app-ifp-modal [modalClass]="'ifp-modal__list-template'" [overlayType]="'transparent'" #viewSourceDataModal>
  <ifp-source-data-modal [(filters)]="sourceDataFilters" (closeModal)="onCloseSourceDataModal()"
    [columns]="selectedCard()?.metadata?.['possibleAggregations']" [fileName]="sourceName()"
    [sourceSampleData]="sampleSourceDate()" [sourceDataHead]="sampleSourceDataHead()" [tableLoader]="sourceTableLoader"
    [sourceType]="selectedCard()?.source_type || ''" [source]="selectedCard()?.source"
    (applySourceFilter)="applySourceFilter($event)"></ifp-source-data-modal>
</app-ifp-modal>



<!-- <app-ifp-modal #importInidcators [overlayType]="'transparent'" [modalClass]="'ifp-modal__import-indicators'">
  @if (isImportDropdown) {
  <app-ifp-import-indicators [heading]="'Select the indicators you want to add'| translate"
    (closeImport)="closeImport()" [importType]="importType"
    [displayedCards]="selectedCards"></app-ifp-import-indicators>
  }
</app-ifp-modal> -->
<!-- <app-ifp-modal #uploadData [overlayType]="'transparent'" [modalClass]="'ifp-modal__upload-data'">
  <ifp-sc-upload-model class="ifp-db__upload-model" (closeUploadModel)="closeUploadModel()" (uploadOrAddData)="uploadOrAddData($event)"></ifp-sc-upload-model>
</app-ifp-modal> -->
<!-- <app-ifp-modal [overlayClass]="'ifp-modal__bg-grey'" [modalClass]="'ifp-modal__import-indicators'" [isClose]="true"
  #libraryListModal>
  @if (isLaibraryModelOpen()) {
  <ifp-prep-library [isModal]="true" (selectFileAll)="getFromLibrary($event)" (closeModal)="closeLibrary()"
    [isLibrary]="true"></ifp-prep-library>
  }
</app-ifp-modal> -->
