<section class="ifp-tools-page">
  <img
    [src]="(themeService.defaultTheme$ | async)!=='dark' ? '../../../../assets/images/dashboard-builder/landing-bg.png' : '../../../../assets/images/prep-help/landing_page_dark.svg'"
    alt="" class="ifp-tools-page__bg">
  <div class="ifp-container ifp-container--sm ifp-tools-page__header">
    <h1 class="ifp-tools-page__heading">{{heading | translate}}</h1>
    <p class="ifp-tools-page__desc">{{description | translate}}</p>
  </div>
  <div class="ifp-tools-page__card-wrapper">
    @for(card of cards ; track card.title; let i = $index) {
    @if (card.visible) {
    <div class="ifp-tools-page__card-inner">
      <div class="ifp-tools-page__card" (click)="onSelectCard($event, i)">
        @if (card.beta) {
        <div class="ifp-tools-page__beta">
          @if ((themeService.defaultLang$|async) === 'en') {
          <img src="../../../../assets/images/beta-icon.svg" alt="BETA" class="ifp-beta-icon">
          } @else {
          <img src="../../../../assets/images/beta-icon-arabic.svg" alt="BETA" class="ifp-beta-icon">
          }
        </div>
        }

        <img [src]="card.img" [alt]="card.title | translate" class="ifp-tools-page__card-logo">
        <div class="ifp-tools-page__text-sec">
          <h2 class="ifp-tools-page__card-title">{{ card.title | translate}}</h2>
          <p class="ifp-tools-page__card-desc">{{ card.description | translate}}</p>
        </div>
        <div class="ifp-tools-page__card-btn-sec">
          <ifp-button [label]="card.buttonLabel | translate" 
            [buttonClass]="buttonClass.primary +' '+ buttonIconPosition.right"
            [iconClass]="'ifp-icon-rightarrow'" (click)="onSelectCard($event, i)"></ifp-button>
          @if (card.isSecondButton) {
          <ifp-button class="ifp-tools-page__btn" [label]="card.buttonSecondLabel | translate"
            (click)="openUrl($event, card.secondUrl)" [buttonClass]="buttonClass.primary +' '+ buttonIconPosition.right"
            [iconClass]="'ifp-icon-rightarrow'"></ifp-button>
          }
        </div>
      </div>
    </div>
    }
    }
  </div>

  <!-- <div class="ifp-tools-page__card-wrapper">
    @for(card of cards | slice:2:5; track card.title; let i = $index) {
      @if (card.visible) {
        <div class="ifp-tools-page__card" (click)="route(card.url)" >
          <img [src]="card.img" alt="card.title | translate" class="ifp-tools-page__card-logo">
          <div class="ifp-tools-page__text-sec">
            <h2 class="ifp-tools-page__card-title">{{ card.title | translate}}</h2>
            <p class="ifp-tools-page__card-desc">{{ card.description | translate}}</p>
          </div>
          <div class="ifp-tools-page__card-btn-sec">
            <ifp-button class="ifp-tools-page__btn" [label]="card.buttonLabel | translate" [link]="true" [url]="card.url" [buttonClass]="buttonClass.primary +' '+ buttonIconPosition.right" [iconClass]="'ifp-icon-rightarrow'"></ifp-button>
            @if (card.isSecondButton) {
              <ifp-button class="ifp-tools-page__btn" [label]="card.buttonSecondLabel | translate" (click)="openUrl($event, card.secondUrl)" [buttonClass]="buttonClass.primary +' '+ buttonIconPosition.right" [iconClass]="'ifp-icon-rightarrow'"></ifp-button>
            }
          </div>
        </div>
      }

    }
  </div> -->
</section>
