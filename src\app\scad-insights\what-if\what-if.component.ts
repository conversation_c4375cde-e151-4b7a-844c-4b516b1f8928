import { CommonModule } from '@angular/common';
import { Component, OnInit, AfterViewInit } from '@angular/core';
import { IfpBreadcrumbsComponent } from '../ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { PageData } from '../core/interface/molecule/breadcrumb.interface';
import { SubSink } from 'subsink';
import { ActivatedRoute } from '@angular/router';
import { Store } from '@ngrx/store';
import { selectAnalyticGetById } from '../home/<USER>/Analytical apps/analyticalApps.selector';
import { getIndicatorDetail } from '../home/<USER>/Analytical apps/analyticalApps.action';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IfpTabComponent } from '../ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';
import { LabelData } from '../core/interface/atom/ifp-category-label.interface';
import { WhatIfCardComponent } from '../ifp-widgets/ifp-molecules/what-if-card/what-if-card.component';
import { cloneDeep } from 'lodash';
import { chartConstants } from '../core/constants/chart.constants';
import { buttonClass, buttonIconPosition } from '../core/constants/button.constants';
import { Title } from '@angular/platform-browser';
import { IFPMsalService } from '../core/services/IFP-msal.service';
import { UsageDashboardLogService } from '../core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from '../core/services/usage-dashboard-log/usage-dashboard.constants';
import { Security } from '../core/interface/indicator.interface';
import { IfpIndicatorCardService } from '../core/services/indicator-card/ifp-indicator-card.service';
import { environment } from 'src/environments/environment';
import { title } from '../core/constants/header.constants';


@Component({
    selector: 'app-what-if',
    templateUrl: './what-if.component.html',
    styleUrls: ['./what-if.component.scss'],
    imports: [IfpBreadcrumbsComponent, CommonModule, TranslateModule, IfpTabComponent, WhatIfCardComponent]
})
export class WhatIfComponent implements OnInit, AfterViewInit {


  public response: any = [];
  public isChartLoader: boolean | undefined = false;
  public analyticalId: any;
  public selectedTab: any = 0;
  public tabData: LabelData[] = [];
  public chartCards: any = [];
  public ratingValues: any = [];
  public periodFilters: any = [];
  public buttonIconPosition = buttonIconPosition;
  public xAxisLabelType: string = chartConstants.xAxisDateTime;
  public showQuarterInterwell: any = false;
  public isViewOnly: boolean = false;
  public publicationDate: string = '';
  public indicatorDrivers: any = [];
  public appType: any = '';
  public defualtDriverValues: any = [];
  public buttonClass = buttonClass;
  public source: string = '';
  public scenarioCount: number = 0;
  public selectedChart: any;
  public isDirective: boolean = false;
  public title: string = this._translate.instant('What if scenario');
  public security!: Security;
  pageData!: PageData[];

  public subsink = new SubSink();
  private sessionId!: string;

  constructor(private route: ActivatedRoute, private store: Store, private _titleService: Title, private _translate: TranslateService, private _msalService: IFPMsalService, private log: UsageDashboardLogService, private _cardService: IfpIndicatorCardService) {
    this._titleService.setTitle(`${title.bayaan} | ${this.title}`);
    if (this.title) {
      (window as any)?.dataLayer?.push({
        'event': 'page_load',
        'page_title_var': this.title,
        'userId': this._msalService.convertEmail(this._msalService.getLoginData.account.username)
      });
    }
  }


  ngOnInit() {
    this.subsink.add(
      this.route.params.subscribe(val => {
        this.store.select(selectAnalyticGetById(val['id'].toString())).subscribe(resp => {
          this.analyticalId = val['id'];
          if (this.sessionId) {
            this.log.logEnds(this.sessionId, this.log.currentTime );
          }
          this.sessionId = this.log.createUUid;
          this.log.logStart(this.sessionId, logType.indicator, this.log.currentTime,  this.analyticalId );
          this.response = resp;
          this.title = resp?.body?.component_title;
          this.isChartLoader = resp.loader;
          this.createTabData();
          this.chartCards = this.selectedTab == 0 ? this.response.body?.indicatorVisualizations?.visualizationsMeta : [this.response.body?.indicatorVisualizations?.visualizationsMeta[this.selectedTab - 1]];

          this.ratingValues = this.response?.body?.indicatorValues?.multiValuesMeta;
          this.periodFilters = cloneDeep(this.response?.body?.indicatorFilters?.[0]?.options);
          this.showQuarterInterwell = this.response?.body?.indicatorVisualizations?.visualizationsMeta[0]?.showQuarterlyIntervals;
          this.publicationDate = this.response?.body?.publication_date;
          this.source = this.response?.body?.data_source;
          if (this.response?.body?.indicatorDrivers) {
            this.indicatorDrivers = JSON.parse(JSON.stringify(this.response?.body?.indicatorDrivers));
            this.isViewOnly = this.indicatorDrivers[0]?.viewOnly ? this.indicatorDrivers[0].viewOnly : false;
            this.appType = this.response?.body?.type;
            this.scenarioCount = this.scenarioCount + 1;
            if (this.scenarioCount == 1) {
              this.createDefualtDRrivers();
            }
          }
          if (this.response?.body?.security && environment.env !== 'demo') {
            this.security = this._cardService.setSecurity(this.response?.body?.security);
          }
        });
      })
    );
  }

  createDefualtDRrivers() {
    this.indicatorDrivers.forEach((element: { id: any; options: any[]; }) => {
      const defaultData = {
        id: element.id,
        label: element.options.find((y: { isSelected: any; }) => y.isSelected).label
      };
      this.defualtDriverValues.push(defaultData);
    });
  }

  ngAfterViewInit(): void {
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
    this.dispatchApp();
  }

  dispatchApp(driver: any = {}) {
    this.store.dispatch(getIndicatorDetail({
      id: this.analyticalId.toString(),
      contentType: 'analytical-apps',
      indicatorDrivers: driver
    }));
  }


  tabClick(event: any) {
    this.selectedTab = event.index;
    this.selectedChart = event;
    this.chartCards = this.response.body?.indicatorVisualizations?.visualizationsMeta;
    if (event.event?.id != 'All') {
      this.chartCards = [this.chartCards.find((x: { seriesMeta: { label: any; }[]; }) => x.seriesMeta[0].label == event.event.name)];
    }
  }

  createTabData() {
    this.tabData = [];
    if (this.response.body?.indicatorVisualizations?.visualizationsMeta?.length) {
      if (this.response.body?.domain) {
        this.createPageData();
      }
      this.response.body?.indicatorVisualizations?.visualizationsMeta.forEach((element: { seriesMeta: { label: any; id: any; }[]; }) => {
        const tab = {
          name: element.seriesMeta[0].label,
          id: element.seriesMeta[0].id
        };
        this.tabData.push(tab);
      });
      const allData = {
        name: 'All',
        id: 'All'
      };
      this.tabData.unshift(allData);
    }
  }

  createPageData() {
    this.pageData = [
      {
        title: 'Home',
        route: '/home'
      },
      {
        title: this.response.body?.domain,
        route: `/domain-exploration/${this.response.body?.domain}/${this.response.body?.domain_id}`,
        queryParams: { key: 'analytical_apps', tabName: 'Analytical Apps' }
      },
      {
        title: this.response.body?.component_title,
        route: '/',
        disable: false
      }
    ];
  }

  changingDriverApi(event: any) {
    this.dispatchApp(event);
  }


  identify(_index: number, _item: any) {
    return '';
  }



}
