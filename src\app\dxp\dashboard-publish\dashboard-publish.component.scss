@use "../../../assets/ifp-styles/abstracts/index" as *;
.ifp-dashboard-publish {
  &__title {
    font-size: $ifp-fs-13;
    font-weight: $fw-bold;
  }
  &__desc {
    font-size:$ifp-fs-4;
    color: $ifp-color-grey-14;
    margin-top: $spacer-3;
  }
  &__card {
    margin-top:$spacer-4;
    border-radius: 20px;
    background-color: $ifp-color-white;
  }
  &__card-inner {
    display: flex;

  }
    &__sec-1 {
      padding: $spacer-5;
    }
  &__sec-1 ,&__sec-2{
      width: 50%;
  }

  &__header-wrapper {
    margin-top:$spacer-6;
     text-align: center;
  }
  &__input-label {
    margin-bottom: $spacer-2;
    color: $ifp-color-grey-14;
      font-size: $ifp-fs-4;
  }
     &__dropdown {
      margin-top: $spacer-2;
     }

     &__add-dropdown-section {
      margin-bottom: $spacer-3;
     }


    // Content type radio styles (Power BI)
    &__radio-group {
      display: flex;
      align-items: center;
      gap: $spacer-6;
      margin-top: $spacer-1;
    }

    &__radio {
      display: inline-flex;
      align-items: center;
      gap: $spacer-2;
      font-size: $ifp-fs-4;
      color: $ifp-color-grey-14;

      input[type="radio"] {
        width: 16px;
        height: 16px;
        accent-color: $ifp-color-secondary-blue;
        margin-right: $spacer-1;
      }

      span {
        line-height: 1.2;
      }
    }

     &__add-dropdown {
      margin-top: $spacer-2;
      max-width: 100%;
     }
     &__input {
      margin-bottom: $spacer-3;
     }
     &__user {
          margin-top: $spacer-3;
    text-align: left;
  }

  &__user-list {
    display: block;
  }
  &__user-group {
    display: block;
    margin-top: $spacer-3;
  }
  &__user-icon {
    font-size: $ifp-fs-8;
  }
  &__wrapper {
    height:calc(100% - ($spacer-2 + $spacer-7));
    width: calc(100% - $spacer-3);
     background-color: $ifp-color-grey-bg;
     margin: $spacer-2 $spacer-2 $spacer-7;
     border-radius: 10px;
     display: flex;
     flex-direction: column;
  }

  // Inline Power BI Preview Styles
  &__preview-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  &__component-container,
  &__tableau-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin: $spacer-3;
    border-radius: 8px;
    background: $ifp-color-white;
    box-shadow: 0 2px 8px $ifp-color-black-16;
    max-height: 500px;
    min-height: 400px;
  }

  &__report {
    width: 100% !important;
    height: 100% !important;
    flex: 1;
    min-height: 400px;
    max-height: 500px;

    :global(iframe) {
      width: 100% !important;
      height: 100% !important;
    }
  }

  &__tableau {
    width: 100%;
    height: 100%;
    flex: 1;
    min-height: 400px;

    :global(.ifp-dashboard) {
      height: 100%;

      :global(tableau-viz) {
        width: 100% !important;
        height: 100% !important;
      }
    }
  }

  &__loading {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacer-5;
    text-align: center;
    color: $ifp-color-grey-6;

    p {
      margin-top: $spacer-3;
      font-size: $ifp-fs-4;
    }
  }

  &__placeholder {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  &__preview-title {
    text-align: center;
    padding-top: $spacer-5;
      font-size: $ifp-fs-4;
    font-weight: $fw-semi-bold;
  }
  &__note-text {

 font-size: $ifp-fs-3;
 font-weight: $fw-bold;
 margin: $spacer-0 $spacer-2;
  }
  &__note{
     margin-top: auto;
        font-size: $ifp-fs-3;
        text-align: center;
        margin-bottom: $spacer-5;
  }

  &__note-icon {
    font-weight: $fw-bold;
  }
  &__img {
    margin: $spacer-7;
  }
  &__footer {
    border-top: 1px solid $ifp-color-grey-7;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: $spacer-0 $spacer-3;
    flex-wrap: wrap;
  }
  &__btn {
    margin: $spacer-3 $spacer-3;
  }

}
:host::ng-deep {
  .ifp-panel-dropdown__label,.ifp-dropdown__title-text  {
    font-size: $ifp-fs-4 !important;
    font-weight: $fw-regular;
     color: $ifp-color-grey-14;
  }
  .ifp-dashboard-publish {

    &__dropdown {
      .ifp-dropdown__title {
        width: 100%;
      }
      .ifp-dropdown {

      max-width: 100%;
      }
    }

    &__add-dropdown {
      .ifp-add-dropdown {
        max-width: 100%;
      }
  // Crop Power BI trial banner within iframe (visual-only)
  $pbi-banner-height: 44px;
  :host::ng-deep {
    .ifp-dashboard-publish__component-container { overflow: hidden; }
    .ifp-dashboard-publish__report { overflow: hidden; }
    .ifp-dashboard-publish__report .powerbi-frame,
    .ifp-dashboard-publish__report iframe {
      position: relative;
      top: -$pbi-banner-height;
      height: calc(100% + #{$pbi-banner-height});
      display: block;
    }
  }

    }
  }

  // Global Power BI component overrides to fix whitespace issues
  .ifp-dashboard-publish__report {
    height: 100% !important;
    max-height: 500px !important;

    iframe {
      height: 100% !important;
      max-height: 500px !important;
    }

    .embed-container {
      height: 100% !important;
      max-height: 500px !important;
    }
  }

  powerbi-report {
    height: 100% !important;
    max-height: 500px !important;
    display: block !important;

    iframe {
      height: 100% !important;
      max-height: 500px !important;
    }


  }

}

// Suggestion list positioning for Tableau workspace search
.ifp-source-filter__input-wrapper {
  position: relative;
}
.ifp-source-filter__suggestion-list {
  position: relative;
  z-index: 6000;
}


// fix for Zoho dropdowns only
:host::ng-deep {
  .ifp-dashboard-publish__add-dropdown-section {
    .ifp-dropdown.active {
      position: relative;
      z-index: 6000;
      .ifp-dropdown__list { z-index: 6002 !important; }
      .ifp-dropdown__list-ul { z-index: 6003 !important; }
    }
  }
}



// Zoho preview height fix
:host::ng-deep {
  .ifp-dashboard-publish__zoho-container {
    max-height: none !important;
    min-height: 800px !important;
    overflow: auto !important;
  }
  .ifp-dashboard-publish__zoho-container .ifp-newsletter__iframe {
    min-height: 800px !important;
    height: 100% !important;
    width: 100% !important;
  }
}
