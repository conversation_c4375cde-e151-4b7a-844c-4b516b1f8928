import { IfpImgComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-img/ifp-img.component';
import { SubSink } from 'subsink';
import { Store } from '@ngrx/store';

import { CommonModule } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, inject, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpCardLoaderComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { IfpBreadcrumbsComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { distinctUntilChanged } from 'rxjs';
import { classifications, cntTypes } from 'src/app/scad-insights/core/constants/domain.constants';
import { IfpModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { DomainsService } from 'src/app/scad-insights/core/services/domains/domains.service';
import { analyticsClasses } from 'src/app/scad-insights/core/constants/analytics-class.constants';
import { selectDomainDetailsNewGetById } from '../store/domain-details-api/domain-details-api.selector';
import { IfpIndicatorTabApiComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-indicator-tab-new/ifp-indicator-tab-api.component';
import { DomainClassification } from 'src/app/scad-insights/core/interface/domain.interface';
import { IFPMsalService } from '../../core/services/IFP-msal.service';
import { UsageDashboardLogService } from '../../core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from '../../core/services/usage-dashboard-log/usage-dashboard.constants';
import { title } from '../../core/constants/header.constants';
import { IfpGlobalService } from '../../core/services/ifp-global.service';
import { reportTypesDropdown } from '../../ifp-widgets/ifp-molecules/ifp-report-card/ifp-report-card.constants';

@Component({
  selector: 'app-domain-details',
  templateUrl: './domain-details-new.component.html',
  styleUrls: ['./domain-details-new.component.scss'],
  imports: [
    CommonModule,
    TranslateModule,
    IfpIndicatorTabApiComponent,
    IfpImgComponent,
    IfpCardLoaderComponent,
    IfpBreadcrumbsComponent
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    '(window:resize)': 'passWindowHieght()'
  }
})
export class DomainDetailsNewComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('modalSla', { static: true }) modalSla!: IfpModalComponent;
  @ViewChild('detailPage') detailPage!: ElementRef;
  public heading!: string;

  public subs = new SubSink();
  public id!: string | null;
  public darkIcon!: string;
  public lightIcon!: string;
  public loader = true;
  public status = false;
  public domainDetails: any;


  public themeListSingle: any;
  public selectedDomainSingle: any = '';
  public tabName = '';
  public domainSelector$ = this.store.select(selectDomainDetailsNewGetById);
  public singleDomain = false;
  public compareChartData: any = [];
  public notApply = true;
  public contentType!: string;
  public classification = classifications;
  public compareValues: any = [];
  public analyticsClasses = analyticsClasses;
  public productData: any;
  public productIndex: any;
  public key = '';
  public tab: any = [];
  public filter: any = [];
  pageData: PageData[] = [];
  public type = {
    subDomain: 'subDomain',
    subTheme: 'subTheme'
  };

  public tabIndex = 0;
  private sessionId!: string;

  // Domain Details Dropdown Configuration
  public domainDropdownOptions = reportTypesDropdown;

  public selectedDropdownOption = { label: 'Publication', value: 'publication' }; // Default to Publication
  public selectedDateRange: { startDate: Date | null; endDate: Date | null } | null = null;

  // Hies variables //
  public _globalService = inject(IfpGlobalService);

  constructor(private store: Store, private _cdr: ChangeDetectorRef, private _titleService: Title, private _activeRoute: ActivatedRoute, private _route: Router, private domainService: DomainsService, private _msalService: IFPMsalService,
    private log: UsageDashboardLogService, private elRef: ElementRef
  ) {
  }


  ngOnInit(): void {
    this.compareValues = [];
    // Check URL parameters for dropdown option
    this.subs.add(
      this._activeRoute.queryParams.subscribe(params => {
        if (params['publication_type']) {
          // Find the matching option from domainDropdownOptions based on the publication_type value
          const matchingOption = this.domainDropdownOptions.find(option => option.value === params['publication_type']);
          if (matchingOption) {
            this.selectedDropdownOption = matchingOption;
          }
        }else {
          this.selectedDropdownOption = { label: 'Publication', value: 'publication' };
        }
        this._cdr.detectChanges();
      })
    );

    this.subs.add(
      this.domainSelector$.pipe(distinctUntilChanged((prev, curr) => prev.id === curr.id && prev.loader === prev.loader && prev.tabName === curr.tabName && prev.subdomain === curr.subdomain && prev.subTheme === curr.subTheme
      )).subscribe((data) => {
        if (this.notApply && data.status) {
          this.id = data.id;
          if (data.tabName) {
            this.tabName = data.tabName;
          } else {
            this.tabName = '';
          }
          if (data.key) {
            this.key = data.key;
          } else {
            this.key = '';
          }
          this.domainDetails = data.body?.domain;
          this.heading = data.body?.domain.name;
          this._titleService.setTitle(`${title.bayaan} | ${this.heading}`);
          (window as any)?.dataLayer?.push({
            'event': 'page_load',
            'page_title_var': this.heading,
            'userId': this._msalService.convertEmail(this._msalService.getLoginData.account.username)
          });
          this.tab = data.body?.classification;
          this.tabIndex = this.tab.findIndex(((tabNames: DomainClassification) => tabNames?.key === this.key));
          if (this.tabIndex == -1) {
            this.tabIndex = this.tab.findIndex((tabNames: DomainClassification) => tabNames.nodeCount !== 0);
          }
          this.filter = data.body?.filter;
          if (data['product']) {
            this.productData = JSON.parse(data['product']).product;
            this.productIndex = JSON.parse(data['product']).index;
          }
          const subdomainId = this.key == this.classification.analyticalApps ? data['subdomain'] : data['subdomainId'];
          const subThemeId = data['subThemeId'];
          const filterData = data.body?.filter[this.tab[this.tabIndex]?.id]?.data ? data.body?.filter[this.tab[this.tabIndex]?.id]?.data : [];
          const currentFilter = this.filter ? [...filterData] : [];
          this.selectedDomainSingle = currentFilter?.find((sub: any) => sub.id === subdomainId);
          if (!this.selectedDomainSingle) {
            this.selectedDomainSingle = currentFilter[0];
          }
          this.themeListSingle = this.selectedDomainSingle?.subthemes?.find((theme: any) => theme.id === subThemeId);
          this.setBreadCrumb();
          this.status = data.status ?? false;
          this.loader = data.loader ?? true;
          this.lightIcon = '';
          this.darkIcon = '';
          this._cdr.detectChanges();
          this.darkIcon = data.body?.domain.light_icon ?? '';
          this.lightIcon = data.body?.domain.dark_icon ?? '';
          this._cdr.detectChanges();

          // Ensure URL includes smart_publisher parameter for initial load with Publication default
          this.updateUrlWithPublicationDefault();
        }
        this.notApply = true;
      })
    );
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.domain, this.log.currentTime);
  }

  ngAfterViewInit(): void {
    const observer = new MutationObserver(() => {
      this.passWindowHieght();
    });

    observer.observe(this.elRef.nativeElement, {
      childList: true,
      subtree: true,
    });

  }

  setBreadCrumb() {
    this.pageData = [
      {
        title: 'Home',
        route: '/home'
      },
      {
        title: this.heading,
        disable: true
      }
    ];
    if (this.selectedDomainSingle && this.selectedDomainSingle !== '') {
      this.pageData.push({
        title: this.selectedDomainSingle?.name,
        event: this.selectedDomainSingle,
        type: this.type.subDomain,
        key: this.key
      });
    }
    if (this.themeListSingle && this.themeListSingle !== '' && this.classification.innovativeStatistics !== this.key) {
      this.pageData.push({
        title: this.themeListSingle?.name,
        event: {
          parentName: this.selectedDomainSingle,
          theme: this.themeListSingle
        },
        type: this.type.subTheme,
        key: this.key
      });
    }
  }

  itemClick(event: PageData) {
    if (event.type === this.type.subDomain) {
      const tabEvent = {
        tabName: this.tabName,
        key: this.key,
        subDomain: event.event,
        index: this.tabIndex
      };
      this.apply(tabEvent);
    } else {
      const tabEvent = {
        tabName: this.tabName,
        key: this.key,
        subDomain: event.event.parentName,
        subTheme: event.event.theme,
        index: this.tabIndex
      };
      this.apply(tabEvent, true);
    }

  }

  apply(event: any, apply: boolean = false) {
    this.contentType = event?.tabName;
    this.notApply = apply;
    this.tabName = event.tabName;
    this.key = event?.key;
    this.selectedDomainSingle = event.category ? event.category : event.subDomain;
    this.themeListSingle = event.subTheme ? event.subTheme : '';
    this.tabIndex = event.index;

    // Build query parameters including new dropdown and date range parameters
    const queryParams: any = {
      key: event.key,
      subdomain: event.key == this.classification.analyticalApps ? this.selectedDomainSingle.id : event?.subDomain?.name,
      tabName: event?.tabName,
      subTheme: this.classification.innovativeStatistics === event?.key ? undefined : event?.subTheme?.name,
      subId: event?.subDomain?.id,
      themeId: this.classification.innovativeStatistics === event?.key ? undefined : event?.subTheme?.id,
      product: JSON.stringify(event.product)
    };
    // Add smart_publisher parameter if Publication is selected AND tab name is Reports
    if (this.selectedDropdownOption?.value && event?.key === cntTypes.reports) {
      queryParams.publication_type = this.selectedDropdownOption?.value;
    }

    // Add date range parameters if dates are selected
    if (this.selectedDateRange?.startDate) {
      queryParams.from_date = this.formatDate(this.selectedDateRange.startDate);
    }
    if (this.selectedDateRange?.endDate) {
      queryParams.to_date = this.formatDate(this.selectedDateRange.endDate);
    }

    this._route.navigate([`domain-exploration/${this.heading}/${this.domainDetails?.id}`], { queryParams });
    this.setBreadCrumb();
  }

  onDropdownChange(selectedOption: any) {
    this.selectedDropdownOption = selectedOption;
    // Trigger a re-apply with current settings to update the API call
    this.triggerReapply();
  }

  onDateRangeChange(dateRange: { startDate: Date | null; endDate: Date | null }) {
    this.selectedDateRange = dateRange;
    // Trigger a re-apply with current settings to update the API call
    this.triggerReapply();
  }

  private formatDate(date: Date): string {
    return date.toISOString().split('T')[0]; // YYYY-MM-DD format
  }

  private triggerReapply() {
    // Re-trigger the apply method with current state to update query parameters
    const currentEvent = {
      tabName: this.tabName,
      key: this.key,
      subDomain: this.selectedDomainSingle,
      subTheme: this.themeListSingle,
      index: this.tabIndex,
      product: { product: this.productData, index: this.productIndex }
    };
    this.apply(currentEvent);
  }

  private updateUrlWithPublicationDefault() {
    // Check if smart_publisher parameter is missing from URL and add it for Publication default AND tab name is Reports
    this._activeRoute.queryParams.subscribe(params => {
      if (!params['publication_type'] && this.selectedDropdownOption?.value && this.key === this.classification.reports) {
        const currentParams = { ...params };
        currentParams['publication_type'] = this.selectedDropdownOption?.value;

        this._route.navigate([], {
          relativeTo: this._activeRoute,
          queryParams: currentParams,
          queryParamsHandling: 'merge'
        });
      }
    }).unsubscribe(); // Unsubscribe immediately since this is a one-time check
  }


  passWindowHieght() {
    const target = window.parent; // or use `window.top` if needed
    const height = this.detailPage?.nativeElement?.offsetHeight + 50;
    target.postMessage({ type: 'DIV_HEIGHT', height: height }, '*');
  }


  ngOnDestroy(): void {
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
    this.subs.unsubscribe();
  }
}
