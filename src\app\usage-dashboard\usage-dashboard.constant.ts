export const userApi = {
  dashboardData: 'kpi/dashboard',
  // activeUsers: 'kpi/users/status',
  activeUsers: 'kpi/active/users/list',
  totalUsers: 'kpi/users',
  totalUsage: 'kpi/users/usage',
  peakValueUsage: 'kpi/users/usage-by-hour',
  topEntityUsage: 'kpi/entities/usage',
  // topEntity: 'kpi/entities',
  topEntity: 'kpi/active/entity/list',
  domains: 'kpi/domains',
  entitiesList: 'kpi/entities-list',
  officialStatistics: 'kpi/official-statistics',
  usersVisits: 'kpi/official-statistics/product-visits',
  experimentalStatistics: 'kpi/experimental-statistics',
  analyticalApps: 'kpi/analytical-apps',
  reports: 'kpi/reports',
  selfService: 'kpi/self-service-tools',
  selfServiceUser: 'kpi/self-service-tools/user',
  geoSpecial: 'kpi/geo-spatial',
  geoSpatialOverview: 'kpi/geospatil/overview/list',
  geoUser: 'kpi/geo-spatial/user',
  userGroupList: 'kpi/users/by-group',
  usersCount: 'kpi/users-count',
  userAccessList: 'kpi/users-list',
  mostDownloadedIndicators: 'kpi/download/indicators',
  mostIndicatorsDownloaders: 'kpi/download/indicators/details',
  mostEnabledIndicators: 'kpi/notification/indicators',
  mostSubscribedUsers: 'kpi/notification/indicators/details',
  hiesVisits: 'kpi/hies/visits',
  hiesDuration: 'kpi/hies/users/duration',
  hiesDurationEntity: 'kpi/hies/entities/duration',
  hiesVisitDetails: 'kpi/hies/visits/details',
};

export const userAccessKpi = {
  entityDetails: 'kpi/superusers',
  entityOverview: 'kpi/entity-classification',
  nodeList: 'kpi/nodes-list',
  usersList: 'kpi/users-list',
  entitiesUserCount: `kpi/entity/users/count`,
  userDetails: 'kpi/users/detail',
  allUsers: 'kpi/all/users',
  allEntities: 'kpi/all/entities',
  entityDomain: 'kpi/entity/domain',
  entityProducts: 'kpi/entity/domain/products',
}


export const initData = {
  'totalUsers': {
    'total': 0,
    'rateChange': 0,
    'monthlySplit': [
    ]
  },
  'totalEntities': {
    'total': 0,
    'rateChange': 0,
    'monthlySplit': [
    ]
  },
  'activeInactiveUsers': {
    'active': 0,
    'inActive': 0,
    'total': 0
  },
  'topUsers': [
  ],
  'topEntities': [
  ],
  'peakTimeOfUsage': {
    'avgUsage': {
      'value': 0,
      'unit': ''
    },
    'hourlySplit': [
    ]
  },
  'overallDomains': [
  ],
  'officialStatistics': [
  ],
  'experimentalStatistics': [
  ],
  'reports': [
  ],
  'analyticalApps': [
  ],
  'entities': [
  ]
};

export const initDataNew = {
  'totalUsersNew': {
    'total': 0,
    'active': 0,
    'inActive': 0,
    'monthly_users_data': [{
      'month': '',
      'total_users': 0,
      'total_active_users': 0
    }]
  },
  'totalEntitiesNew': {
    'active_domains': [],
    'month': '',
    'count': 0
  },
  'activeInactiveUsers': {
    'active': 0,
    'inActive': 0,
    'total': 0,
    'monthly_users_data': [{
      'month': '',
      'total_users': 0,
      'total_active_users': 0
    }]
  },
  'topUsers': [
  ],
  'topEntities': [
  ],
  'peakTimeOfUsage': {
    'avgUsage': {
      'value': 0,
      'unit': ''
    },
    'hourlySplit': [
    ]
  },
  'overallDomains': [
  ],
  'officialStatistics': [
  ],
  'experimentalStatistics': [
  ],
  'reports': [
  ],
  'analyticalApps': [
  ],
  'entities': [
  ]
};

export const monthConst = 	['Jan',	'Feb',	'Mar',	'Apr',	'May',	'June',	'July', 'Aug', 'Sept', 'Oct', 'Nov', 'Dec'];

export const sunburstMockData = [
  {
    "id": "non-oil",
    "parent": "",
    "name": "Non-oil",
    "value": 286,
    "color": "#dadada",
    "change": 11.71875,
    "contribution": 100,
    "ogValue": 286
  },
  {
    "id": "professional-scientific-technical-activities",
    "parent": "business-services",
    "name": "Professional, scientific and technical activities",
    "value": 61,
    "color": "#007506",
    "change": 15.094339370727539,
    "contribution": 21.328671328671327,
    "ogValue": 61
  },
  {
    "id": "business-services",
    "parent": "non-oil",
    "name": "Business Services",
    "value": 61,
    "color": "#0BB7FF",
    "change": 5.1724138259887695,
    "contribution": 21.328671328671327,
    "ogValue": 61
  },
  {
    "id": "commerce-goods-transport",
    "parent": "non-oil",
    "name": "Commerce and Goods Transport",
    "value": 201,
    "color": "#7BE279",
    "change": 40.55944061279297,
    "contribution": 70.27972027972028,
    "ogValue": 201
  },
  {
    "id": "wholesale-retail-repair-motor-vehicles-motorcycles",
    "parent": "commerce-goods-transport",
    "name": "Wholesale and retail trade; repair of motor vehicles and motorcycles",
    "value": 201,
    "color": "#7BE279",
    "change": 40.55944061279297,
    "contribution": 70.27972027972028,
    "ogValue": 201
  },
  {
    "id": "construction",
    "parent": "construction-real-estate",
    "name": "Construction",
    "value": 6,
    "color": "#007506",
    "change": 20,
    "contribution": 2.097902097902098,
    "ogValue": 6
  },
  {
    "id": "construction",
    "parent": "construction-real-estate",
    "name": "Construction",
    "value": 6,
    "color": "#007506",
    "change": 20,
    "contribution": 2.097902097902098,
    "ogValue": 6
  },
  {
    "id": "construction-real-estate",
    "parent": "non-oil",
    "name": "Construction and Real estate",
    "value": 6,
    "color": "#007506",
    "change": 20,
    "contribution": 2.097902097902098,
    "ogValue": 6
  },
  {
    "id": "education",
    "parent": "non-oil",
    "name": "Education",
    "value": 1,
    "color": "#0BB7FF",
    "contribution": 0.34965034965034963,
    "ogValue": 1
  },
  {
    "id": "education-industry",
    "parent": "education",
    "name": "Education",
    "value": 1,
    "color": "#0BB7FF",
    "contribution": 0.34965034965034963,
    "ogValue": 1
  },
  {
    "id": "healthcare",
    "parent": "non-oil",
    "name": "Healthcare",
    "value": 4,
    "color": "#870101",
    "change": -55.55555725097656,
    "contribution": 1.3986013986013985,
    "ogValue": 4
  },
  {
    "id": "human-health-social-work-activities",
    "parent": "healthcare",
    "name": "Human Health and Social Work Activities",
    "value": 4,
    "color": "#870101",
    "change": -55.55555725097656,
    "contribution": 1.3986013986013985,
    "ogValue": 4
  },
  {
    "id": "leisure",
    "parent": "non-oil",
    "name": "Leisure",
    "value": 12,
    "color": "#610000",
    "change": -70.73170471191406,
    "contribution": 4.195804195804196,
    "ogValue": 12
  },
  {
    "id": "accommodation-food-service-activities",
    "parent": "leisure",
    "name": "Accommodation and Food Service Activities",
    "value": 6,
    "color": "#610000",
    "change": -84.21052551269531,
    "contribution": 2.097902097902098,
    "ogValue": 6
  },
  {
    "id": "arts-entertainment-recreation",
    "parent": "leisure",
    "name": "Arts, entertainment and recreation",
    "value": 6,
    "color": "#003b00",
    "change": 100,
    "contribution": 2.097902097902098,
    "ogValue": 6
  },
  {
    "id": "manufacturing-activities",
    "parent": "non-oil",
    "name": "Manufacturing Activities",
    "value": 1,
    "color": "#0BB7FF",
    "contribution": 0.34965034965034963,
    "ogValue": 1
  },
  {
    "id": "manufacturing",
    "parent": "manufacturing-activities",
    "name": "Manufacturing",
    "value": 1,
    "color": "#0BB7FF",
    "contribution": 0.34965034965034963,
    "ogValue": 1
  }
]

export const userCountInitial = {
  'total': {
    'entity': {
      'total_entities': 0,
      'total_sensitive_entities': 0
    },
    'user': {
      'total_users': 0,
      'total_director_general_users': 0,
      'total_primary_super_users': 0,
      'total_secondary_super_users': 0
    }
  },
  'entity_count': {
    'total': 0,
    'inActive': 0,
    'active': 0
  }
}
