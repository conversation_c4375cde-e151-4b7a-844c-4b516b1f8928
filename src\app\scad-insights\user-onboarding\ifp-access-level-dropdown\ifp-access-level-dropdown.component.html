<div class="ifp-access-list" [ngClass]="{'ifp-access-list--view': viewOnly, 'ifp-access-list--inline': isInline, 'ifp-access-list--single': !ischeckBoxShow}">
  @if (ischeckBoxShow) {
  <div #dropdown class="ifp-access-list__selected" (click)="collapse($event)">
    <p class="ifp-access-list__item-name"><span class="ifp-access-list__dot" [style.backgroundColor]="selectedItem.color"></span>{{selectedItem.label | translate}}</p><em class="ifp-icon ifp-icon-down-arrow"></em>
  </div>
  <ul #dropdownList class="ifp-access-list__box" [ngClass]="{'ifp-access-list__box--expanded': isExpanded, 'ifp-access-list__box--body': isAppendBody}" appOutsideClick (outsideClick)="outsideClick($event)">
    @if (dropDownItems.length) {
      @for (item of dropDownItems; track $index) {
        <li class="ifp-access-list__item" [ngClass]="{'ifp-access-list__item--disabled': item.value.toLowerCase() === 'open', 'ifp-access-list__item--selected': item.isSelected}" (click)="onSelect(!item.isSelected, $index)">
          <div class="ifp-access-list__item-outer">
            <div class="ifp-access-list__inner">
              <p class="ifp-access-list__item-name"><span class="ifp-access-list__dot" [style.backgroundColor]="item.color"></span>{{item.label | translate}}</p>
              <app-ifp-check-box class="ifp-adv-tool__checkbox" [isWhiteBox]="true" [hideLabel]="true" [checkedData]="item.isSelected ?? false"></app-ifp-check-box>
            </div>
            @if (item.value === dataClassification.sensitive.toLowerCase()) {
              <em class="ifp-access-list__note">{{(roleList.dgOrUnderSecretary + ' approval required') | translate}}</em>
            }
          </div>
        </li>
      }
    }
  </ul>

 }@else{
  <div #dropdown class="ifp-access-list__selected" (click)="collapse($event)">
    <p class="ifp-access-list__item-name" [ngClass]="{'ifp-access-list__item-name-grey': !selectedItem}">{{selectedItem ? (selectedItem.label | translate) : 'Select' | translate}}</p>
    <div class="ifp-access-list__selected-right">
      @if (enableClearValue() && selectedItem) {
        <em class="ifp-icon ifp-icon-cross ifp-access-list__clear" (click)="clearValues()"></em>
      }
      <em class="ifp-icon ifp-icon-down-arrow"></em>
    </div>
  </div>
  <ul #dropdownList class="ifp-access-list__box" [ngClass]="{'ifp-access-list__box--expanded': isExpanded, 'ifp-access-list__box--body': isAppendBody}" appOutsideClick (outsideClick)="outsideClick($event)">
    @if (dropDownItems.length) {
      @for (item of dropDownItems; track $index) {
        <li class="ifp-access-list__item" [ngClass]="{'ifp-access-list__item--active': item[selectionKey()] === selectedItem[selectionKey()]}" (click)="ondropdownSelect($index)">
          <div class="ifp-access-list__item-outer">
            <div class="ifp-access-list__inner">
              <p class="ifp-access-list__item-name"><span class="ifp-access-list__item-label">{{item?.label | translate}}</span> <em class="ifp-icon ifp-icon-tick ifp-access-list__select-tick"></em></p>
            </div>
            @if (item?.description) {
                <p class="ifp-access-list__desc">{{item.description | translate}}</p>
              }
          </div>
        </li>
      }
      @if (enableClearValue()) {
        <li class="ifp-access-list__item ifp-access-list__item--clear" [ngClass]="{'ifp-access-list__item--clear-disabled': !selectedItem}" (click)="clearValues()"><div class="ifp-access-list__item-outer">{{'Clear selection' | translate}}</div></li>
      }
    }
  </ul>
}
</div>

<app-ifp-modal #modal>
  <app-ifp-remove-card [isRemove]="true" [isUserOnboard]="true" [text]="'Confirm Role Removal'" [isDescription]="true" [discription]="'You are about to remove the user`s visualization role. This will immediately revoke their access permissions and associated privileges.'" (firstButtonEvent)="firstButtonEvent($event)" [firstButton]="'Cancel'" [secondButton]="'Proceed'"
    (secondButtonEvent)="closeModel($event)">
  </app-ifp-remove-card>
</app-ifp-modal>