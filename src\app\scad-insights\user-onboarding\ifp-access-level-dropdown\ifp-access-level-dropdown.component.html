<div class="ifp-access-list" [ngClass]="{'ifp-access-list--view': viewOnly, 'ifp-access-list--inline': isInline}">
  @if (ischeckBoxShow) {
  <div #dropdown class="ifp-access-list__selected" (click)="collapse($event)">
    <p class="ifp-access-list__item-name"><span class="ifp-access-list__dot" [style.backgroundColor]="selectedItem.color"></span>{{selectedItem.label | translate}}</p><em class="ifp-icon ifp-icon-down-arrow"></em>
  </div>
  <ul #dropdownList class="ifp-access-list__box" [ngClass]="{'ifp-access-list__box--expanded': isExpanded, 'ifp-access-list__box--body': isAppendBody}" appOutsideClick (outsideClick)="outsideClick($event)">
    @if (dropDownItems.length) {
      @for (item of dropDownItems; track $index) {
        <li class="ifp-access-list__item" [ngClass]="{'ifp-access-list__item--disabled': item.value.toLowerCase() === 'open', 'ifp-access-list__item--selected': item.isSelected}" (click)="onSelect(!item.isSelected, $index)">
          <div class="ifp-access-list__inner">
            <p class="ifp-access-list__item-name"><span class="ifp-access-list__dot" [style.backgroundColor]="item.color"></span>{{item.label | translate}}</p>
            <app-ifp-check-box class="ifp-adv-tool__checkbox" [isWhiteBox]="true" [hideLabel]="true" [checkedData]="item.isSelected ?? false"></app-ifp-check-box>
          </div>
          @if (item.value === dataClassification.sensitive.toLowerCase()) {
            <em class="ifp-access-list__note">{{(roleList.dgOrUnderSecretary + ' approval required') | translate}}</em>
          }
        </li>
      }
    }
  </ul>

 }@else{
  <div #dropdown class="ifp-access-list__selected" (click)="collapse($event)">
    @if (selectedItem) {
    <p class="ifp-access-list__item-name">{{selectedItem.label | translate}}</p><em class="ifp-icon ifp-icon-down-arrow"></em>
    }@else{
       <p class="ifp-access-list__item-name ifp-access-list__item-name-grey">{{ 'Select' | translate}}</p><em class="ifp-icon ifp-icon-down-arrow"></em>
    }
  </div>
  <ul #dropdownList class="ifp-access-list__box" [ngClass]="{'ifp-access-list__box--expanded': isExpanded, 'ifp-access-list__box--body': isAppendBody}" appOutsideClick (outsideClick)="outsideClick($event)">
    @if (dropDownItems.length) {
      @for (item of dropDownItems; track $index) {
        <li class="ifp-access-list__item"  (click)="ondropdownSelect(item, $index)">
          <div class="ifp-access-list__inner">
            <p class="ifp-access-list__item-name">{{item.label | translate}}</p>
          </div>       
        </li>
      }
    }
  </ul>
}
</div>