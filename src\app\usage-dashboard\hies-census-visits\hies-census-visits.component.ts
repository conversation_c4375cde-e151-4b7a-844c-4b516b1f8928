import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnChanges, SimpleChanges, inject, ChangeDetectorRef, OnInit, signal, InputSignal, input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { IfpDropdownComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { IfpNoDataComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { IfpCardLoaderComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { IfpPillsTabComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-pills-tab/ifp-pills-tab.component';
import { IfpPanelDropdownComponent, PanelDropdownOptions } from 'src/app/ifp-analytics/molecule/ifp-panel-dropdown/ifp-panel-dropdown.component';
import { IfpTitleCasePipe } from 'src/app/scad-insights/core/pipes/title-case.pipe';

@Component({
  selector: 'ifp-hies-visits',
  standalone: true,
  imports: [CommonModule, TranslateModule, IfpDropdownComponent, IfpNoDataComponent, IfpCardLoaderComponent, IfpPillsTabComponent, IfpPanelDropdownComponent],
  templateUrl: './hies-census-visits.component.html',
  styleUrl: './hies-census-visits.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HiesCensusVisitsComponent implements OnChanges, OnInit {
  constructor(private cdr: ChangeDetectorRef) {
    
  }
  loader: InputSignal<boolean> = input<boolean>(true);
  @Input() tabData?: any = [];
  @Input() nodeData: hiesVisitNodeData[] = [];
  @Input() title: any = 'HIES Visits';
  @Input() downloadTypes: any = [{ id: "xls", label: "Export XLS" }];
  @Input() panelOptions: any = []; // options for right-side panel dropdown in hiesSummary
  @Input() type: string = 'hiesVisits'; // 'hiesVisits' | 'hiesSummary'
  // use any[] to avoid type/import issues
  public tableColumnsUsers: any[] = [];
  public tableColumnsEntities: any[] = [];
  public visibleColumns: Set<string> = new Set<string>();
  @Output() downloadHiesReport = new EventEmitter<any>();
  @Output() visistDetails = new EventEmitter<any>();
  @Output() selectedTabHies = new EventEmitter<any>();
  selectedTabKey: string = 'users';
  public selectedEntityOptions: PanelDropdownOptions[] = [];
  private readonly _ifpTitleCasePipe: IfpTitleCasePipe = inject(IfpTitleCasePipe);
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['nodeData']) {
      this.updateTableColumns();
    }
  }
  ngOnInit(): void {
    // this.updateTableColumns();
  }

  get totalVisits(): number {
    if (!this.nodeData || !Array.isArray(this.nodeData)) { return 0; }
    return this.nodeData.reduce((sum: number, it: any) => sum + (Number(it.visits) || 0), 0);
  }

  get durationValue(): number {
    if (!this.nodeData || !Array.isArray(this.nodeData) || this.nodeData.length === 0) return 0;
    const first = this.nodeData[0];
    if (first && (first.duration || first['totalDuration'])) {
      return Number(first.duration || first['totalDuration']) || 0;
    }
    const sum = this.nodeData.reduce((s: number, it: any) => s + (Number(it.duration) || 0), 0);
    return this.nodeData.length ? Math.round((sum / this.nodeData.length) * 10) / 10 : 0;
  }

  onDownload(type: string = 'CSV') {
    let exportData= this.nodeData.map(({ id, key, total, ...rest }) => rest);
    this.downloadHiesReport.emit({ downloadType: type, data: Array.isArray(exportData) ? exportData : [], selectedTab: (this.tabData && this.tabData[0] && this.tabData[0].key) || null });
  }
  selectedKey(event: any) {
    this.selectedTabKey = event.key;
    this.nodeData = [];
    this.selectedTabHies.emit(this.selectedTabKey);
    this.selectedEntityOptions = [];
    this.updateTableColumns();

  }
  onVisitClick(item: hiesVisitNodeData): void {
         this.visistDetails.emit({sessionType:item.id});
  }
  updateTableColumns(): void {
    if (this.type === 'hiesSummary' && this.nodeData.length>0) {
      if(this.selectedTabKey === 'entities' ) {
        this.tableColumnsEntities =  Object.keys(this.nodeData[0])
        //  .filter(key => !['total'].includes(key))
         .map(key => ({key: key.toLowerCase(), value: this._ifpTitleCasePipe.transform(key), checked: true}));
         console.log('this.tableColumnsEntities', this.tableColumnsEntities);
      }
      else{
        this.tableColumnsUsers =  Object.keys(this.nodeData[0])
          // .filter(key => !['total'].includes(key))
          .map(key => ({key: key.toLowerCase(), value: this._ifpTitleCasePipe.transform(key), checked: true}));
          console.log('this.tableColumnsUsers', this.tableColumnsUsers);
      }
      // initialize visible columns
      this.updateVisibleColumns();
      this.cdr.detectChanges();
  }
  }
  
  // update visibleColumns set based on current tableColumns arrays
  updateVisibleColumns(): void {
    const list = this.selectedTabKey === 'entities' ? this.tableColumnsEntities : this.tableColumnsUsers;
    const keys = list && list.length ? list.filter(c => c.checked).map(c => c.key) : [];
    this.visibleColumns = new Set(keys);
  }

  isColumnVisible(key: string): boolean {
    // if visibleColumns is empty treat as all visible
    if (!this.visibleColumns || this.visibleColumns.size === 0) return true;
    return this.visibleColumns.has(key.toLowerCase());
  }
  onSelectColumns(event: PanelDropdownOptions[]): void {
    // event can be a single item or an array
    if (!event) { return; }
    const items = Array.isArray(event) ? event : [event];
    // extract keys from items
    const keys = items.map((it: any) => it?.key ?? (typeof it === 'string' ? it : it?.value ?? it?.label)).filter(Boolean).map((k: any) => String(k).toLowerCase());
    const list = this.selectedTabKey === 'entities' ? this.tableColumnsEntities : this.tableColumnsUsers;
    if (!list) return;
    if (Array.isArray(event)) {
      // set checked for provided keys
      list.forEach(col => col.checked = keys.includes(col.key));
    } else {
      // toggle single
      const k = keys[0];
      const idx = list.findIndex(c => c.key === k);
      if (idx >= 0) {
        list[idx].checked = !list[idx].checked;
      }
    }
    this.updateVisibleColumns();
    this.cdr.detectChanges();
   }
  trackByIndex(_: number, __: any) { return _; }
}

export interface hiesVisitNodeData {
  id: string;
  indicator: string;
  key: string;
  visits?: number | string;
  total?: number | string;
  [k: string]: any;
  duration?: string;
  name?: string;
  email?: string;
  usage_share?: string;
  entity?: string;
}
