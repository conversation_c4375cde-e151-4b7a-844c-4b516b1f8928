import { Location } from '@angular/common';
import { Component, inject, On<PERSON><PERSON>roy, OnInit, signal, viewChild, WritableSignal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { DxpUserConfigComponent } from 'src/app/dxp/dxp-user-config/dxp-user-config.component';
import { UserDetail } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-user-tag-group/ifp-user-tag-group.component';
import { IfpButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { SubSink } from 'subsink';
import { ActivatedRoute, Router } from '@angular/router';
import { dashboardEndpoints } from 'src/app/scad-insights/core/apiConstants/dashboard.api.constants';
import { IfpVisulizationBuilderService } from 'src/app/scad-insights/core/services/ifp-visulization-builder.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { mainTabParams, statusParams } from '../../core/constants/dashboard-builder.constants';
import { IfpModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { DxpValidationPopUpComponent } from 'src/app/dxp/dxp-validation-pop-up/dxp-validation-pop-up.component';
import { IfpModalService } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { dashboardActions } from 'src/app/scad-insights/core/constants/dashboard.constants';

@Component({
  selector: 'ifp-ifp-dashboard-user-config',
  imports: [DxpUserConfigComponent, TranslateModule, IfpButtonComponent, IfpModalComponent, DxpValidationPopUpComponent],
  templateUrl: './ifp-dashboard-user-config.component.html',
  styleUrl: './ifp-dashboard-user-config.component.scss'
})
export class IfpDashboardUserConfigComponent implements OnInit, OnDestroy {
  private readonly _router = inject(Router);
  private readonly _activeRoute = inject(ActivatedRoute);
  private readonly _location = inject(Location);
  private readonly _dashboardApiService = inject(IfpVisulizationBuilderService);
  private readonly _toasterService = inject(ToasterService);
  public readonly _themeService = inject(ThemeService);
  private readonly _modalService = inject(IfpModalService);
  private readonly _apiService = inject(ApiService);

  public modalPopUp = viewChild<IfpModalComponent>('modalPopUp');

  selectedUsers: WritableSignal<UserDetail[]> = signal([]);
  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public subs = new SubSink();
  public id = signal('');
  public dashboardDetail: any = signal({});
  public dashboardPreview: { light_thumbnail: string, dark_thumbnail: string } = { light_thumbnail: '', dark_thumbnail: '' };
  public updateIcon = signal('');
  public buttonLabel = signal('');
  public buttonSecondLabel = signal('');
  public image = signal(true);
  // Signal for update popup description
  public updateDec = signal('');
  public currentStatus = signal('');
  public enableDoubleButton = signal(false);
  public enableCloseButton: boolean = true;
  public enableCommentBox = signal(false);
  public isSuccessPopup = signal(false);
  // Signal for update popup title
  public updateTitle = signal('');
  public statusParams = statusParams;
  public isFromList: boolean = false;

  ngOnInit(): void {
    this.subs.add(
      this._activeRoute.params.subscribe((params) => {
        this.id.set(params['dashboard_id']);
        this.getDashboardDetail(this.id());
      }),
      this._activeRoute.queryParams.subscribe((queryParams) => {
        this.isFromList = queryParams?.['edit'] === 'false';
      })
    );
  }

  getDashboardDetail(id: string) {
    this._dashboardApiService.getMethodRequest(`${dashboardEndpoints.dashboard + (id ?? '')}/`).subscribe({
      next: next => {
        this.dashboardDetail.set(next);
        this.dashboardPreview = this.dashboardDetail().thumbnail_url;
        if (this.dashboardDetail()?.shared_with_users?.length) {
          this.selectedUsers.set(this.dashboardDetail()?.shared_with_users);
        }
      },
      error: error => {
        this._toasterService.error(error.error);
      }
    });
  }

  submitForApproval(comment: string = '') {
    const selectedUserList = this.selectedUsers().map((user: UserDetail) => user.email);
    const payload = {
      object_id: this.id(),
      users: selectedUserList.length ? selectedUserList : [],
      comment: comment,
      approval_request_id: this.dashboardDetail()?.approval_request_id ?? ''
    };
    this.subs.add(
      this._dashboardApiService.postMethodRequest(`${dashboardEndpoints.manageRequests}`, payload).subscribe({
        next: () => {
          this.updateDec.set('Your dashboard has been submitted for approval and will be published once approved by the approver. You can track its status under the \'Pending\' tab.'
          );
          this.updateTitle.set('Submitted Successfully');
          this.updateIcon.set('ifp-icon ifp-icon-long-time');
          this.buttonLabel.set('Create New Dashboard');
          this.enableDoubleButton.set(true);
          this.buttonSecondLabel.set('Go To Pending Tab');
          this.enableCloseButton = true;
          this.enableCommentBox.set(false);
          this.currentStatus.set('send');
          this.isSuccessPopup.set(true);
          this.modalPopUp()?.createElement();
        },
        error: error => {
          this._toasterService.error(error.error.error);
        }
      })
    );
  }

  navigateToList(params: Record<string, any> = { 'government-affairs': true }) {
    this._router.navigate(['/dxp/dashboards'], { queryParams: params });


    //   this.subs.add(
    //   this._apiService.getDeleteRequest(`${dashboardEndpoints.deleteDashboard(objectId)}`).subscribe({
    //     next: () => {
    //       this.dashboardList.splice(this.selectedIndex, 1);
    //       this._toaster.success('Dashboard deleted Successfully');
    //     },
    //     error: error => {
    //       this._toaster.error(error?.error?.message);
    //     }
    //   })
    // );
  }

  async createUpdatePop(status: string) {
    this.image.set(false);
    this.enableCloseButton = true;
    this.currentStatus.set(status);
    this.enableCommentBox.set(false);
    this.enableDoubleButton.set(false);
    if (status === 'cancel') {
      this.updateDec.set('This action cannot be undone, and your progress will be lost.');
      this.updateTitle.set('Are You Sure You Want To Discard?');
      this.updateIcon.set('ifp-icon ifp-icon-exclamation');
      this.enableDoubleButton.set(true);
      this.buttonLabel.set('Discard');
      this.buttonSecondLabel.set('Cancel');
      this.isSuccessPopup.set(false);
      this.modalPopUp()?.createElement();
    } else if (status === statusParams.completed) {
      this.updateDashboardStatus(status).then(() => {
      this.updateDec.set('Dashboard saved successfully. View it in the "Completed" section of the My Dashboards tab.'
      );
      this.updateTitle.set('Dashboard Saved Successfully!');
      // this.updateIcon.set('ifp-icon ifp-icon-tick');
      this.buttonLabel.set('View in My Dashboard');
      this.updateIcon.set('../../../assets/tick-animation.gif');
      this.isSuccessPopup.set(true);
      this.image.set(true);

      this.currentStatus.set(statusParams.completed);
      this.modalPopUp()?.createElement();
      });
    } else if (status === 'send') {
      if (this.dashboardDetail()?.status !== statusParams.completed) {
        await this.updateDashboardStatus(statusParams.completed);
      }
      if (this.dashboardDetail()?.approval_request_id === '') {
        this.submitForApproval();
      } else {
        this.updateDec.set('Please confirm if you want to submit this dashboard for approval? This action cannot be undone.');
        this.updateTitle.set('Are You Sure You Want To Submit?');
        this.updateIcon.set('ifp-icon ifp-icon-refresh');
        this.enableCommentBox.set(true);
        this.buttonLabel.set('Submit');
        this.enableDoubleButton.set(true);
        this.buttonSecondLabel.set('Cancel');
        this.currentStatus.set('revert');
        this.isSuccessPopup.set(false);
        this.modalPopUp()?.createElement();
      }
      // if (this.dashboardDetail()?.approval_request_id === '') {
      //   this.submitForApproval();
      // }
    }

    // else if (status === 'save') {
    //   this.updateDec.set(
    //     'KPI saved successfully. View it under the “Completed” section of the “My KPI” tab.'
    //   );
    //   this.updateTitle.set('KPI Saved Successfully!');
    //   this.updateIcon.set('../../../assets/tick-animation.gif');
    //   this.buttonLabel.set(
    //     !this.editData?.approvalRequest?.approvalActions?.revert?.visible          ? 'View in My KPI'          : 'View in Reverted Tab'
    //   );
    //   this.image.set(true);
    //   this.modalPopUp()?.createElement();
    // }
  }

  popupSecondaryBtnClick() {
    if (this.currentStatus() === this.statusParams.completed) {
      this.closeModal();
      // this.navigateToList();
    } else if (this.currentStatus() === 'send') {
      const params = {
        'government-affairs': true,
        tab: mainTabParams.approvalStatus,
        status: statusParams.pending
      };
      this.closeModal();
      this.navigateToList(params);
    } else {
      this.closeModal();
    }
    this.currentStatus.set('');
  }

  popupPrimaryBtnClick(event: string) {
    let params = {};
    switch (this.currentStatus()) {
    case this.statusParams.completed:
      params = {
        'government-affairs': true,
        tab: mainTabParams.govMyDashboards,
        status: statusParams.completed
      };
      this.closeModal();
      this.navigateToList(params);
      break;
    case 'send':
      params = {
        'government-affairs': true,
        tab: mainTabParams.dxpPublishedDashboards,
        subTab: statusParams.govAffairsDashboards
      };
      this.closeModal();
      this._router.navigate(['/store/dashboard-builder'], { queryParams: params });
      return;
    case 'revert':
      this.closeModal();
      this.submitForApproval(event);
      return;
    case 'cancel':
      this.subs.add(
        this._apiService.getDeleteRequest(`${dashboardEndpoints.deleteDashboard(this.id())}`).subscribe({
          next: () => {
            this._router.navigate(['/dxp/dashboards'], { queryParams: { 'government-affairs': true } });
          },
          error: error => {
            this._toasterService.error(error?.error?.message);
          }
        })
      );
      break;
    default:
      break;
    }
    this.currentStatus.set('');
  }

  closeModal() {
    this.modalPopUp()?.removeModal();
    this._modalService.removeAllModal();
    if (this.currentStatus() === 'send') {
      const queryParams = {
        ['government-affairs']: true,
        tab: mainTabParams.dxpPublishedDashboards,
        status: statusParams.govAffairsDashboards
      };
      this._router.navigate(['/dxp/dashboards'], { queryParams: queryParams});
    }
  }

  updateDashboardStatus(status: string) {
    return new Promise((resolve, reject) => {
      try {
        const selectedUserList = this.selectedUsers().map((user: UserDetail) => user.email);
        const params = {
          status: status,
          users: selectedUserList.length ? selectedUserList : []
        };
        this.subs.add(
          this._dashboardApiService.patchMethodRequest(dashboardEndpoints.updateStatus(this.id()), params).subscribe({
            next: () => {
              resolve(true);
            },
            error: (error) => {
              this._toasterService.error(error.error.message);
              reject(error);
            }
          })
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  goBack() {
    if (this.isFromList) {
      this._location.back();
    } else {
      const queryParams = {
        id: this.id(),
        mode: dashboardActions.edit,
        tab: mainTabParams.govMyDashboards,
        mainTab: mainTabParams.govMyDashboards,
        subTab: statusParams.completed,
        ['government-affairs']: true
      };
      this._router.navigate(['/store/dashboard-builder'], {queryParams: queryParams});
    }
  }

  onCloseMessageModal() {
    this.closeModal();
    if (this.currentStatus() === 'sent' || this.currentStatus() === 'revert') {
      const queryParams = {
        ['government-affairs']: true,
        tab: mainTabParams.dxpPublishedDashboards,
        status: statusParams.govAffairsDashboards
      };
      this._router.navigate(['/dxp/dashboards'], { queryParams: queryParams});
    }
  }

  ngOnDestroy(): void {
    this._modalService.removeAllModal();
    this.subs.unsubscribe();
  }
}
