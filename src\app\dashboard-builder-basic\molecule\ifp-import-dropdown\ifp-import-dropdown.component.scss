@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: block;
}


.ifp-db {
  &__add-type {
    box-shadow: 0 32px 96px -12px $ifp-color-black-16;
    border-radius: 24px;
    padding: $spacer-0 $spacer-3;
    background-color: $ifp-color-white;
    opacity: 0;
    visibility: hidden;
    z-index: 1;
    transition: 0.3s;

    &--show {
      opacity: 1;
      visibility: visible;
    }
  }

  &__add-type-item {
    font-size: $ifp-fs-6;
    color: $ifp-color-secondary-grey;
    display: flex;
    align-items: center;
    padding: $spacer-3 $spacer-0;
    border-bottom: 1px solid $ifp-color-grey-7;
    white-space: nowrap;
    transition: 0.3s;

    &:last-child {
      border-bottom: none;
    }

    .ifp-icon {
      color: $ifp-color-blue-hover;
      margin-right: $spacer-2;
      font-size: inherit;
    }

    &:hover {
      color: $ifp-color-blue-hover;
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-db {
    &__add-type-item {
      .ifp-icon {
        margin-left: $spacer-2;
        margin-right: $spacer-0;
      }
    }
  }
}
