import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'comparatorDisplay',
  standalone: true,
  pure: true,
})
export class ComparatorDisplayPipe implements PipeTransform {
  transform(cmp: unknown): string {
    if (cmp && typeof cmp === 'object') {
      const obj = cmp as { display_name?: string; label?: string; name?: string; value?: string; key?: string };
      return obj.display_name ?? obj.label ?? obj.name ?? obj.value ?? obj.key ?? '';
    }
    return (cmp as string) ?? '';
  }
}

