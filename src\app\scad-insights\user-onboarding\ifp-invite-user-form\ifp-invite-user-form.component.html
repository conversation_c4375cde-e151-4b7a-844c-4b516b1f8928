<div class="ifp-invite">

  <app-ifp-tab [tabData]="inviteTabs" [transparent]="true" [showIcon]="false" (selectedTabEvent)="onInviteTabChange($event.event)" [selectedTab]="selectedTab.name" [selectionType]="'name'" [tooltipDisabled]="true" class="ifp-invite__switch-tab"></app-ifp-tab>
  @if(role === userRole.pePrimary) {
  <!-- Product Engagement start -->
   @if (selectedTab.key === 'newInvite') {
    <div class="ifp-invite__pe-form">
      <form [formGroup]="peForm" class="ifp-invite__input-wrapper">
        <div class="ifp-invite__input-sec">
          <app-ifp-dropdown [title]="'Select Entity'" [singleDefaultSelect]="false" [placeHolder]="'Select'" [searchEnable]="true" [showTitle]="true" [key]="'NAME'" class="ifp-invite__dropdown ifp-invite__dropdown--entity" (dropDownItemClicked)="selectEntity($event)" [dropDownItems]="entityList" formControlName="entity"></app-ifp-dropdown>
          @if (peForm.controls['entity'].errors && peForm.controls['entity'].touched) {
            <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid email' | translate}}</p>
          }
        </div>
      </form>

      <form [formGroup]="peFormPrimary" class="ifp-invite__form-outer ifp-invite__box">
        <em class="ifp-icon ifp-icon-user-alt ifp-invite__user-icon"></em>
        <h4 class="ifp-invite__title">{{'Superuser - Primary' | translate}}</h4>
        <div class="ifp-invite__input-wrapper">
          <div class="ifp-invite__input-sec">
            <p class="ifp-invite__input-label">{{'Email' | translate}}</p>
            <div class="ifp-invite__input-outer">
              <input type="email" [placeholder]="'Email' | translate" formControlName="primarySuEmail" class="ifp-invite__input" appSearchSuggestion [suggestions]="peForm.value.suggestionList" [searchQuery]="peFormPrimary.value.primarySuEmail" [isGlobalSearch]="false" (setValue)="setSuggestion($event, 'peForm', 'primarySuEmail')" (focus)="onFocus('peForm', 'primaryEmailFocus')" (blur)="onblur('peForm', 'primaryEmailFocus')" [isFocusInput]="peFormPrimary.value.primaryEmailFocus">
            </div>
            @if (peFormPrimary.controls['primarySuEmail'].errors?.['domain'] && peFormPrimary.controls['primarySuEmail'].touched) {
              <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>@if(allowedDomain().length > 1 ) {
                  {{'Email domain must be either' | translate}}
                  @for (item of allowedDomain(); track $index) {
                    <strong>{{item}}</strong> {{$index === allowedDomain().length - 1 ? '' : ($index === allowedDomain().length - 2 ? ('or' | translate) : ',')}}
                  }
                } @else {
                  {{'Email domain must be' | translate}} <strong>{{allowedDomain()}}</strong>
                }
              </p>
            }  @else if ((peFormPrimary.controls['primarySuEmail'].errors?.['required'] || peFormPrimary.controls['primarySuEmail'].errors?.['email']) && peFormPrimary.controls['primarySuEmail'].touched) {
              <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid email' | translate}}</p>
            }
          </div>
          <div class="ifp-invite__input-sec">
            <p class="ifp-invite__input-label">{{'Job Title' | translate}}</p>
            <input type="text" [placeholder]="'Job title' | translate" formControlName="primarySuJobTitle" class="ifp-invite__input">
            @if (peFormPrimary.controls['primarySuJobTitle'].errors && peFormPrimary.controls['primarySuJobTitle'].touched) {
              <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a job title' | translate}}</p>
            }
          </div>
          <div class="ifp-invite__input-sec">
            <p class="ifp-invite__input-label">{{'Job Level' | translate}}</p>
            <!-- <input type="text" [placeholder]="'Job Level' | translate" formControlName="primaryJoblevel" class="ifp-invite__input"> -->
            <ifp-access-level-dropdown [dropDownItems]="jobLevelList" (dropDownItemClicked)="selectprimarySuJoblevel($event)" formControlName="primarySuJoblevel" [selectedValue]="peFormPrimary.get('primaryJoblevel')?.value" [clearValue]="clearDropdowns"></ifp-access-level-dropdown>
            @if (peFormPrimary.controls['primarySuJoblevel'].errors && peFormPrimary.controls['primarySuJoblevel'].touched) {
              <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a Job level' | translate}}</p>
            }
          </div>
          <div class="ifp-invite__input-sec">
            <p class="ifp-invite__input-label">{{'Department' | translate}}</p>
            <!-- <input type="text" [placeholder]="'Department' | translate" formControlName="primarySuDepartment" class="ifp-invite__input"> -->
           <ifp-access-level-dropdown [dropDownItems]="departmentList" (dropDownItemClicked)="selectprimarySuDepartment($event)" formControlName="primarySuDepartment" [selectedValue]="peFormPrimary.get('primarySuDepartment')?.value" [clearValue]="clearDropdowns"></ifp-access-level-dropdown>

            @if (peFormPrimary.controls['primarySuDepartment'].errors && peFormPrimary.controls['primarySuDepartment'].touched) {
              <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a Department' | translate}}</p>
            }
          </div>
          <div class="ifp-invite__input-sec">
            <p class="ifp-invite__input-label">{{'Mobile Number linked with UAE Pass' | translate}}</p>
            <div class="ifp-invite__mobile">
              <div class="ifp-invite__country-code">
                <img [src]="selectedCountry.flagIcon" [alt]="selectedCountry.country| translate" class="ifp-invite__country-flag">
                <span class="ifp-invite__code">{{selectedCountry.code}}</span>
              </div>
              <input type="text" [placeholder]="'555500000' | translate" formControlName="primarySuMobile" class="ifp-invite__input" [readOnly]="isReadOnly" (click)="isReadOnly = false" [max]="9" appNumberOnly>
            </div>
            @if (peFormPrimary.controls['primarySuMobile'].errors && peFormPrimary.controls['primarySuMobile'].touched) {
              <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid mobile number' | translate}}</p>
            }
          </div>
          <!-- <div class="ifp-invite__input-sec">
            <p class="ifp-invite__input-label">{{'Select Primary User Role' | translate}}</p>

            <ifp-access-level-dropdown [dropDownItems]="primaryUserRoleList" (dropDownItemClicked)="selectprimarySuRole($event)" formControlName="primarySuRole" [selectedValue]="peFormPrimary.get('primarySuRole')?.value" [clearValue]="clearDropdowns"></ifp-access-level-dropdown>
            @if (peFormPrimary.controls['primarySuRole'].errors && peFormPrimary.controls['primarySuRole'].touched) {
              <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a User Role' | translate}}</p>
            }
          </div> -->
          <div class="ifp-invite__input-sec">
            <p class="ifp-invite__input-label">{{'Select Secondary User Role' | translate}}</p>
            <!-- <input type="text" [placeholder]="'Job Level' | translate" formControlName="primaryJoblevel" class="ifp-invite__input"> -->
            <ifp-access-level-dropdown [selectionKey]="'name'" [enableClearValue]="true" [dropDownItems]="userRoleList()" (dropDownItemClicked)="selectSecondarySuRole($event)" formControlName="secondarySuRole" [selectedValue]="peFormPrimary.get('secondarySuRole')?.value" [clearValue]="clearDropdowns"></ifp-access-level-dropdown>
            @if (peFormPrimary.controls['secondarySuRole'].errors && peFormPrimary.controls['secondarySuRole'].touched) {
              <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a User Role' | translate}}</p>
            }
          </div>
          <!-- <div class="ifp-invite__input-sec">
            <p class="ifp-invite__input-label">{{'Platform' | translate}}</p>
            <app-ifp-dropdown [showTitle]="false" [key]="'key'" class="ifp-invite__dropdown" (dropDownItemClicked)="selectPlatform($event, -1, 'pePrimary')" [dropDownItems]="platforms" formControlName="pePrimaryPlatform"></app-ifp-dropdown>
          </div> -->
        </div>
        <ifp-button [label]="'Send Invite'" (ifpClick)="onInviteSuperUser('primary')"  class="ifp-invite__button" [buttonClass]="((peForm.valid && peFormPrimary.valid) ? buttonClass.primary : buttonClass.disabled) +' '+ buttonClass.large +' '+ buttonIconPosition.left" [iconClass]="'ifp-icon-send'"></ifp-button>
      </form>

      <form [formGroup]="peFormSecondary" class="ifp-invite__form-outer ifp-invite__box">
        <em class="ifp-icon ifp-icon-user-alt ifp-invite__user-icon"></em>
        <h4 class="ifp-invite__title">{{'Superuser - Secondary' | translate}}</h4>
        <div class="ifp-invite__input-wrapper">
          <div class="ifp-invite__input-sec">
            <p class="ifp-invite__input-label">{{'Email' | translate}}</p>
            <div class="ifp-invite__input-outer">
              <input type="email" [placeholder]="'Email' | translate" formControlName="secondarySuEmail" class="ifp-invite__input" appSearchSuggestion [suggestions]="peForm.value.suggestionList" [searchQuery]="peFormSecondary.value.secondarySuEmail" [isGlobalSearch]="false" (focus)="onFocus('peForm', 'secondaryEmailFocus')" (blur)="onblur('peForm', 'secondaryEmailFocus')" (setValue)="setSuggestion($event, 'peForm', 'secondarySuEmail')" [isFocusInput]="peFormSecondary.value.secondaryEmailFocus">
            </div>
            @if (peFormSecondary.controls['secondarySuEmail'].errors?.['domain'] && peFormSecondary.controls['secondarySuEmail'].touched) {
              <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Email domain must be' | translate}} <strong>{{allowedDomain()}}</strong></p>
            }  @else if ((peFormSecondary.controls['secondarySuEmail'].errors?.['required'] || peFormSecondary.controls['secondarySuEmail'].errors?.['email']) && peFormSecondary.controls['secondarySuEmail'].touched) {
              <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid email' | translate}}</p>
            }
          </div>
          <div class="ifp-invite__input-sec">
            <p class="ifp-invite__input-label">{{'Job Title' | translate}}</p>
            <input type="text" [placeholder]="'Job title' | translate" formControlName="secondarySuJobTitle" class="ifp-invite__input">
            @if (peFormSecondary.controls['secondarySuJobTitle'].errors && peFormSecondary.controls['secondarySuJobTitle'].touched) {
              <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a job title' | translate}}</p>
            }
          </div>
            <div class="ifp-invite__input-sec">
            <p class="ifp-invite__input-label">{{'Job Level' | translate}}</p>
            <!-- <input type="text" [placeholder]="'Job Level' | translate" formControlName="primaryJoblevel" class="ifp-invite__input"> -->
            <ifp-access-level-dropdown [dropDownItems]="jobLevelList" (dropDownItemClicked)="selectsecondarySuJoblevel($event)" formControlName="secondarySuJoblevel" [selectedValue]="peFormSecondary.get('secondarySuJoblevel')?.value" [clearValue]="clearDropdowns"></ifp-access-level-dropdown>
            @if (peFormSecondary.controls['secondarySuJoblevel'].errors && peFormSecondary.controls['secondarySuJoblevel'].touched) {
              <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a Job level' | translate}}</p>
            }
          </div>
          <div class="ifp-invite__input-sec">
            <p class="ifp-invite__input-label">{{'Department' | translate}}</p>
            <!-- <input type="text" [placeholder]="'Department' | translate" formControlName="primarySuDepartment" class="ifp-invite__input"> -->
           <ifp-access-level-dropdown [dropDownItems]="departmentList" (dropDownItemClicked)="selectSecodarySuDepartment($event)" formControlName="secondarySuDepartment" [selectedValue]="peFormSecondary.get('secondarySuDepartment')?.value" [clearValue]="clearDropdowns"></ifp-access-level-dropdown>

            @if (peFormSecondary.controls['secondarySuDepartment'].errors && peFormSecondary.controls['secondarySuDepartment'].touched) {
              <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a Department' | translate}}</p>
            }
          </div>
          <div class="ifp-invite__input-sec">
            <p class="ifp-invite__input-label">{{'Mobile Number linked with UAE Pass' | translate}}</p>
            <div class="ifp-invite__mobile">
              <div class="ifp-invite__country-code">
                <img [src]="selectedCountry.flagIcon" [alt]="selectedCountry.country| translate" class="ifp-invite__country-flag">
                <span class="ifp-invite__code">{{selectedCountry.code}}</span>
              </div>
              <input type="text" [placeholder]="'555500000' | translate" formControlName="secondarySuMobile" class="ifp-invite__input" [readOnly]="isReadOnly" (click)="isReadOnly = false" [max]="9" appNumberOnly>
            </div>
            @if (peFormSecondary.controls['secondarySuMobile'].errors && peFormSecondary.controls['secondarySuMobile'].touched) {
              <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid mobile number' | translate}}</p>
            }
          </div>
          <!-- <div class="ifp-invite__input-sec">
            <p class="ifp-invite__input-label">{{'Select Primary User Role' | translate}}</p>

            <ifp-access-level-dropdown [dropDownItems]="primaryUserRoleList" (dropDownItemClicked)="selectPrimarySuRole($event)" formControlName="primarySuRole" [selectedValue]="peFormPrimary.get('primarySuRole')?.value" [clearValue]="clearDropdowns"></ifp-access-level-dropdown>
            @if (peFormPrimary.controls['primarySuRole'].errors && peFormPrimary.controls['primarySuRole'].touched) {
              <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a User Role' | translate}}</p>
            }
          </div> -->
          <div class="ifp-invite__input-sec">
            <p class="ifp-invite__input-label">{{'Select Secondary User Role' | translate}}</p>
            <!-- <input type="text" [placeholder]="'Job Level' | translate" formControlName="primaryJoblevel" class="ifp-invite__input"> -->
            <ifp-access-level-dropdown [selectionKey]="'name'" [enableClearValue]="true" [dropDownItems]="userRoleList()" (dropDownItemClicked)="selectsecondarySuRole($event)" formControlName="secondarySuRole" [selectedValue]="peFormSecondary.get('secondarySuRole')?.value" [clearValue]="clearDropdowns"></ifp-access-level-dropdown>
            @if (peFormSecondary.controls['secondarySuRole'].errors && peFormSecondary.controls['secondarySuRole'].touched) {
              <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a User Role' | translate}}</p>
            }
          </div>
          <!-- <div class="ifp-invite__input-sec">
            <p class="ifp-invite__input-label">{{'Platform' | translate}}</p>
            <app-ifp-dropdown [showTitle]="false" [key]="'key'" class="ifp-invite__dropdown" (dropDownItemClicked)="selectPlatform($event, -1, 'peSecondary')" [dropDownItems]="platforms" formControlName="peSecondaryPlatform"></app-ifp-dropdown>
          </div> -->
        </div>
        <ifp-button [label]="'Send Invite'" (ifpClick)="onInviteSuperUser('secondary')"  class="ifp-invite__button" [buttonClass]="((peForm.valid && peFormSecondary.valid) ? buttonClass.primary : buttonClass.disabled) +' '+ buttonClass.large +' '+ buttonIconPosition.left" [iconClass]="'ifp-icon-send'"></ifp-button>
      </form>

      <!-- <ifp-button [label]="'Send Invite'" (ifpClick)="onInviteSuperUser()"  class="ifp-invite__button" [buttonClass]="(((peForm.valid && peFormPrimary.valid) || (peForm.valid && peFormSecondary.valid)) ? buttonClass.primary : buttonClass.disabled) +' '+ buttonClass.large +' '+ buttonIconPosition.left" [iconClass]="'ifp-icon-send'"></ifp-button> -->
    </div>
   } @else {
    <div class="ifp-invite__sent">
      <em class="ifp-icon ifp-icon-user-alt ifp-invite__user-icon"></em>
      <h2 class="ifp-invite__title">{{'Sent Invites' | translate}}</h2>
      @if (sentInviteList.controls.length) {
        <form [formGroup]="invitesForm" class="ifp-invite__table-wrapper">
          <table class="ifp-invite__table ifp-invite__table--sent">
            <tr class="ifp-invite__table-row ifp-invite__table-row--head">
              <th class="ifp-invite__table-col">{{'Email' | translate}}</th>
              <th class="ifp-invite__table-col">{{'Mobile Number linked with UAE Pass' | translate}}</th>
              <th class="ifp-invite__table-col">{{'Job Title' | translate}}</th>
                 <th class="ifp-invite__table-col">{{'Job Level' | translate}}</th>
                <th class="ifp-invite__table-col">{{'Department' | translate}}</th>
              <!-- <th class="ifp-invite__table-col">{{'Role' | translate}}</th> -->
               <!-- <th class="ifp-invite__table-col">{{'Primary Role' | translate}}</th> -->
               <th class="ifp-invite__table-col">{{'Secondary Role' | translate}}</th>
              <th class="ifp-invite__table-col">{{'Entity' | translate}}</th>
              <!-- <th class="ifp-invite__table-col">{{'Sent On' | translate}}</th> -->
              <th class="ifp-invite__table-col">{{'Status' | translate}}</th>
              <th class="ifp-invite__table-col"></th>
            </tr>
            <ng-container formArrayName="sentInvites">
            @for (invite of sentInviteList.controls; track invite; let i = $index) {
              <tr class="ifp-invite__table-row" [ngClass]="{'ifp-invite__table-row--edit': editInviteIndex === i}" [formGroupName]="i">
                <td class="ifp-invite__table-col">
                  <input type="text" formControlName="inviteeEmail" [readOnly]="editInviteIndex !== i" class="ifp-invite__table-input">
                  @if (invite.controls.inviteeEmail.errors?.['domain'] && invite.controls.inviteeEmail.touched) {
                    <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>@if(inviteList[i].entityDomain.length > 1 ) {
                        {{'Email domain must be either' | translate}}
                        @for (item of inviteList[i].entityDomain; track $index) {
                          <strong>{{item}}</strong> {{$index === inviteList[i].entityDomain.length - 1 ? '' : ($index === inviteList[i].entityDomain.length - 2 ? ('or' | translate) : ',')}}
                        }
                    } @else {
                        {{'Email domain must be' | translate}} <strong>{{inviteList[i].entityDomain[0]}}</strong>
                      }
                    </p>
                  } @else if ((invite.controls.inviteeEmail.errors?.['required'] || invite.controls.inviteeEmail.errors?.['inviteeEmail']) && invite.controls.inviteeEmail.touched) {
                    <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid email' | translate}}</p>
                  }
                </td>
                <td class="ifp-invite__table-col">
                  <div class="ifp-invite__mobile">
                    <span class="ifp-invite__code">{{selectedCountry.code}}</span>
                    <!-- <input type="text" formControlName="phoneNumber" appNumberOnly [readOnly]="editInviteIndex !== i" class="ifp-invite__table-input"> -->

                    <input type="text" formControlName="phoneNumber" class="ifp-invite__table-input" [readOnly]="editInviteIndex !== i" [max]="9" appNumberOnly>
                  </div>
                  @if (invite.controls.phoneNumber.errors) {
                    <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid phone number' | translate}}</p>
                  }
                </td>
                <td class="ifp-invite__table-col"><input type="text" formControlName="designation" [readOnly]="editInviteIndex !== i" class="ifp-invite__table-input">
                  @if (invite.controls.designation.errors) {
                    <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid phone number' | translate}}</p>
                  }
                </td>
                 <td class="ifp-invite__table-col">
                    @if(editInviteIndex !== i){
                     <span>{{invite.get('jobLevel')?.value?.value ? invite.get('jobLevel')?.value?.label : invite.get('jobLevel')?.value}}</span>
                    }@else{
                      <ifp-access-level-dropdown [dropDownItems]="jobLevelList" (dropDownItemClicked)="selectsentInvitJobLevel($event, i)"  formControlName="jobLevel" [isAppendBody]="true"  [isInline]="true" [clearValue]="clearDropdowns" [selectedValue]="invite.get('jobLevel')?.value?.value"></ifp-access-level-dropdown>
                     @if (invite.controls.jobLevel.errors) {
                      <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a job level' | translate}}</p>
                    }
                    }

                  </td>
                  <td class="ifp-invite__table-col">
                   @if(editInviteIndex !== i){
                     <span> {{invite.get('department')?.value?.value ? invite.get('department')?.value?.label : invite.get('department')?.value}}</span>
                    }@else{
                      <ifp-access-level-dropdown [dropDownItems]="departmentList" (dropDownItemClicked)="selectSentInvitDeptList($event, i)" [isAppendBody]="true"  [isInline]="true" formControlName="department"  [clearValue]="clearDropdowns" [selectedValue]="invite.get('department')?.value?.value"></ifp-access-level-dropdown>
                    @if (invite.controls.department.errors) {
                      <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a department' | translate}}</p>
                    }
                    }

                  </td>
                <!-- <td class="ifp-invite__table-col">
                    {{ invite.get('primaryRole')?.value?.label || invite.get('primaryRole')?.value || '-' }}
                  </td> -->
                  <td class="ifp-invite__table-col">
                    {{ invite.get('secondaryRole')?.value?.label || invite.get('secondaryRole')?.value || '-' }}
                  </td>
                <td class="ifp-invite__table-col"><input type="text" formControlName="entity" class="ifp-invite__table-input" readOnly="true"></td>
                <td class="ifp-invite__table-col"><input type="text" formControlName="status" class="ifp-invite__table-input" readOnly="true"></td>
                <td class="ifp-invite__table-col">
                  <div class="ifp-invite__button-sec" [ngClass]="{'ifp-invite__button-sec--disabled': inviteList[i].status === 'awaiting_entraid'}">
                    <em class="ifp-icon ifp-invite__button-icon" [class]="editInviteIndex !== i ? 'ifp-icon-edit' : 'ifp-icon-cross'" [appIfpTooltip]="(editInviteIndex !== i ? 'Edit Invite' : 'Cancel Edit') | translate" [extraSpaceTop]="25" (click)="editInviteIndex === i ? cancelEditInvite(i) : editInvite(i)"></em>
                    <em class="ifp-icon ifp-icon-send ifp-invite__button-icon" [ngClass]="{'ifp-invite__button-icon--disabled': invite.invalid }" [appIfpTooltip]="'Resend Invite'" [extraSpaceTop]="25" (click)="resendInvite(inviteList[i], i)"></em>
                    <em class="ifp-icon ifp-icon-trash ifp-invite__button-icon" [appIfpTooltip]="'Cancel Invite'" [extraSpaceTop]="25" (click)="openCancelInviteModal(i)"></em>
                    <!-- <ifp-button [label]="'Resend Invite'" class="ifp-invite__button" [buttonClass]="buttonClass.primary" (ifpClick)="resendInvite(invite)"></ifp-button>
                    <ifp-button [label]="'Cancel Invite'" class="ifp-invite__button" [buttonClass]="buttonClass.secondary" (ifpClick)="deleteInvite(invite, i)"></ifp-button> -->
                  </div>
                </td>
              </tr>
            }
            </ng-container>
          </table>
        </form>
      } @else {
        <app-ifp-no-data [message]="'No Pending Invites'" class="ifp-invite__no-data"></app-ifp-no-data>
      }
    </div>
   }
  <!-- Product Engagement end -->
  } @else if(this.superUserRoles.includes(role)) {
  <!-- Super user start -->
    @if (selectedTab.key === 'newInvite') {
      <!-- Add user Form start -->
      <form [formGroup]="suForm" class="ifp-invite__su-form">
          <!-- Invite Users start -->
        <div class="ifp-invite__box">
          <div class="ifp-invite__box-header">
            <div class="ifp-invite__title-wrapper">
              <em class="ifp-icon ifp-icon-user-alt ifp-invite__user-icon"></em>
              <h2 class="ifp-invite__title">{{'Users Invitation' | translate}}</h2>
            </div>
            @if (isDgRequired && (dgStatus === dgStatusConst.invitePending)) {
              <ifp-button [label]="'Add '+roleList.dgOrUnderSecretary" [buttonClass]="buttonClass.secondary +' '+ buttonIconPosition.left" [iconClass]="'ifp-icon-plus'" (ifpClick)="openDgModal()" class="ifp-invite__add-btn"></ifp-button>
            }
          </div>
            <div class="ifp-invite__input-wrapper">
              <div class="ifp-invite__input-sec">
                <p class="ifp-invite__input-label">{{'Email' | translate}}</p>
                <input type="text" [placeholder]="'Email' | translate" formControlName="email" class="ifp-invite__input">
                @if (suForm.controls['email'].errors && suForm.controls['email'].touched) {
                  <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid email' | translate}}</p>
                }
              </div>
              <div class="ifp-invite__input-sec">
                <p class="ifp-invite__input-label">{{'Emirates ID' | translate}}</p>
                <input type="text" [placeholder]="'Emirates ID' | translate" formControlName="emiratesId" class="ifp-invite__input" [readOnly]="isReadOnly" [max]="15" appNumberOnly>
                @if (suForm.controls['emiratesId'].errors && suForm.controls['emiratesId'].touched) {
                  <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid Emirates ID' | translate}}</p>
                }
              </div>
              <div class="ifp-invite__input-sec">
                <p class="ifp-invite__input-label">{{'Job Title' | translate}}</p>
                <input type="text" [placeholder]="'Job title' | translate" formControlName="jobTitle" class="ifp-invite__input">
                @if (suForm.controls['jobTitle'].errors && suForm.controls['jobTitle'].touched) {
                  <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a job title' | translate}}</p>
                }
              </div>
                 <div class="ifp-invite__input-sec">
                <p class="ifp-invite__input-label ifp-invite__input-label--info">{{'Job Level' | translate}} </p>
                  <ifp-access-level-dropdown [dropDownItems]="jobLevelList" (dropDownItemClicked)="selectJobList($event)" formControlName="jobLevel" [selectedValue]="suForm.get('jobLevel')?.value" [clearValue]="clearDropdowns"></ifp-access-level-dropdown>

               @if (suForm.controls['jobLevel'].errors && suForm.controls['jobLevel'].touched) {
                  <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please Select a job level' | translate}}</p>
                }
                 </div>

              <div class="ifp-invite__input-sec">
                <p class="ifp-invite__input-label ifp-invite__input-label--info">{{'Department' | translate}} </p>
                  <ifp-access-level-dropdown [dropDownItems]="departmentList" (dropDownItemClicked)="selectDepartment($event)" formControlName="department" [selectedValue]="suForm.get('department')?.value" [clearValue]="clearDropdowns"></ifp-access-level-dropdown>
               @if (suForm.controls['department'].errors && suForm.controls['department'].touched) {
                  <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a department' | translate}}</p>
                }
                </div>
              <!-- <div class="ifp-invite__input-sec">
                <p class="ifp-invite__input-label">{{'Platform' | translate}}</p>
                <app-ifp-dropdown [singleDefaultSelect]="true" [showTitle]="false" [key]="'key'" class="ifp-invite__dropdown ifp-invite__dropdown--entity" formControlName="platform" (dropDownItemClicked)="selectPlatform($event)" [dropDownItems]="platforms"></app-ifp-dropdown>
              </div> -->
              <div class="ifp-invite__input-sec">
                <p class="ifp-invite__input-label ifp-invite__input-label--info">{{'Data Classification' | translate}} <app-ifp-info class="ifp-invite__label-info" [infoContent]="classificationInfo" [widthInfo]="infoWidth"></app-ifp-info></p>
                  <ifp-access-level-dropdown [ischeckBoxShow]="true" [dropDownItems]="dataClassificationList" (dropDownItemClicked)="selectDataClassification($event)" formControlName="classification" [selectedValue]="suForm.get('classification')?.value?.value" ></ifp-access-level-dropdown>
              </div>
              <div class="ifp-invite__input-sec"> <!--removed the ifp-invite__input-sec--2x class for reduce the length of the domain box-->
                <p class="ifp-invite__input-label">{{'Domains' | translate}}</p>
                <div class="ifp-invite__input ifp-invite__input--tag-wrapper">
                  <div class="ifp-invite__tag-wrapper">
                    @if(suForm.value.classification) {
                      <span class="ifp-invite__input-tag ifp-invite__input-tag--all">
                        @if (suForm.value.classification.value !== dataClassification.sensitive.toLowerCase()) {
                          <span class="ifp-invite__tag-dot" [style.backgroundColor]="suForm.value.classification.color"></span>
                        } @else if (suForm.value.classification.value === dataClassification.open.toLowerCase()) {
                          <span class="ifp-invite__tag-dot" [style.backgroundColor]="ifpColor.greenDark"></span>
                        } @else {
                          <span class="ifp-invite__tag-dot" [style.backgroundColor]="suForm.value.domains.length < domainAccessList.length ? ifpColor.orange : suForm.value.classification.color"></span>
                        }
                        {{'All' | translate}}</span>
                    }
                    @if (suForm.value.classification.value === dataClassification.sensitive.toLowerCase()) {
                      @for (domain of suForm.value.domains; track $index) {
                        <span class="ifp-invite__input-tag ifp-invite__input-tag--close" [style.borderColor]="suForm.value.classification.color"><span class="ifp-invite__tag-dot" [style.backgroundColor]="suForm.value.classification.color"></span>{{domain | translate}} <em class="ifp-icon ifp-icon-cross ifp-invite__close" (click)="removeDomain($index)"></em></span>
                      }
                    }
                  </div>
                  @if (suForm.value.classification.value === dataClassification.sensitive.toLowerCase() && (suForm.value.domains.length !== domainAccessList.length)) {
                    <span class="ifp-invite__input-refresh"><em class="ifp-icon ifp-icon-refresh-round" (click)="domainRefresh()"></em></span>
                  }
                </div>
              </div>
              <!-- <div class="ifp-invite__input-sec">
                <p class="ifp-invite__input-label ifp-invite__input-label--info">{{'Select Primary User Role' | translate}} </p>
                  <ifp-access-level-dropdown [dropDownItems]="primaryUserRoleList" (dropDownItemClicked)="selectPSuRole($event)" formControlName="primarySuRole" [selectedValue]="suForm.get('primarySuRole')?.value" [clearValue]="clearDropdowns"></ifp-access-level-dropdown>
               @if (suForm.controls['primarySuRole'].errors && suForm.controls['primarySuRole'].touched) {
                  <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please Select a user role' | translate}}</p>
                }
                </div>   -->
                <div class="ifp-invite__input-sec">
                <p class="ifp-invite__input-label ifp-invite__input-label--info">{{'Select Secondary User Role' | translate}} </p>
                  <ifp-access-level-dropdown [selectionKey]="'name'" [enableClearValue]="true" [dropDownItems]="userRoleList()" (dropDownItemClicked)="selectSSuRole($event)" formControlName="secondarySuRole" [selectedValue]="suForm.get('secondarySuRole')?.value" [clearValue]="clearDropdowns"></ifp-access-level-dropdown>
               @if (suForm.controls['secondarySuRole'].errors && suForm.controls['secondarySuRole'].touched) {
                  <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please Select a user role' | translate}}</p>
                }
                </div>
               <div class="ifp-invite__input-sec ifp-invite__input-sec--2x ifp-byn-db">
                <app-ifp-check-box class="ifp-adv-tool__checkbox ifp-byn-db__mt-32" [isWhiteBox]="false" [label]="'Direct_employee_type'" [hideLabel]="false" [checkedData]="isEmployeeTypeSelected" (checked)="onSelectItem($event)" (click)="onClick($event)"></app-ifp-check-box>
                <app-ifp-info class="ifp-invite__input-sec--direct_hire" [infoContent]="'Direct_employee_helptext'" [widthInfo]="infoWidth"></app-ifp-info>
               @if (suForm.controls['employeeType'].errors && suForm.controls['employeeType'].errors['invalidEmplyeeTypeMsg']) {
                    <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please select the checkbox' | translate}}</p>
                  }
              </div>
              @if (suForm.value.classification.value === dataClassification.sensitive.toLowerCase()) {
                <div class="ifp-invite__input-sec ifp-invite__input-sec--2x">
                  <p class="ifp-invite__input-label">{{'Justification' | translate}}</p>
                  <input type="text" [placeholder]="'Justification' | translate" formControlName="justification" class="ifp-invite__input">
                  @if (suForm.controls['justification'].errors && suForm.controls['justification'].touched) {
                    <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please provide a justification' | translate}}</p>
                  }
                </div>
              }
            </div>
            <ifp-button [label]="'Add User'" [buttonClass]="(suForm.valid ? buttonClass.primary : buttonClass.disabled) +' '+ buttonIconPosition.left" [iconClass]="'ifp-icon-plus'" (ifpClick)="addUser()" class="ifp-invite__add-btn"></ifp-button>
        </div>
        <!-- Invite Users end -->

        <!-- Added users list start -->
        @if (inviteUserList.controls.length) {
        <ng-container formArrayName="userList">
          <div class="ifp-invite__box">
            <em class="ifp-icon ifp-icon-user-alt ifp-invite__user-icon"></em>
            <h2 class="ifp-invite__title">{{'Added Users' | translate}}</h2>
            <div class="ifp-invite__table-wrapper">
              <table class="ifp-invite__table">
              <tr class="ifp-invite__table-row ifp-invite__table-row--head">
                <th class="ifp-invite__table-col">{{'Email' | translate}}</th>
                <th class="ifp-invite__table-col">{{'Emirates ID' | translate}}</th>
                <th class="ifp-invite__table-col">{{'Job Title' | translate}}</th>
                <th class="ifp-invite__table-col">{{'Job Level' | translate}}</th>
                <th class="ifp-invite__table-col">{{'Department' | translate}}</th>
                <!-- <th class="ifp-invite__table-col">{{'Platform' | translate}}</th> -->
                <th class="ifp-invite__table-col">{{'Data Classification' | translate}}</th>
                <th class="ifp-invite__table-col">{{'Domains' | translate}}</th>
                <!-- <th class="ifp-invite__table-col">{{'Primary Role' | translate}}</th> -->
                <th class="ifp-invite__table-col">{{'Secondary Role' | translate}}</th>
                <th class="ifp-invite__table-col ifp-invite__table-col--info">{{'Justification' | translate}}</th>
                <th class="ifp-invite__table-col ifp-invite__table-col--info">{{'Info' | translate}}</th>
                <th class="ifp-invite__table-col">{{'Actions' | translate}}</th>
              </tr>
              @for (user of inviteUserList.controls; track i; let i = $index) {
                <tr class="ifp-invite__table-row ifp-invite__table-row" [formGroupName]="i" [ngClass]="{'ifp-invite__table-row--edit': !user.value.viewOnly}">
                  <td class="ifp-invite__table-col">
                    <input type="text" [placeholder]="'Email' | translate" formControlName="userEmail" class="ifp-invite__table-input" [readOnly]="user.value.viewOnly">
                    @if (user.controls['userEmail'].errors && user.controls['userEmail'].touched) {
                      <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid email' | translate}}</p>
                    }
                  </td>
                  <td class="ifp-invite__table-col">
                    <input type="text" [placeholder]="'Emirates ID' | translate" formControlName="userEmiratesId" class="ifp-invite__table-input" [max]="15" appNumberOnly [readOnly]="user.value.viewOnly">
                    @if (user.controls['userEmiratesId'].errors && user.controls['userEmiratesId'].touched) {
                      <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid Emirates ID' | translate}}</p>
                    }
                  </td>
                  <td class="ifp-invite__table-col">
                    <input type="text" [placeholder]="'Job title' | translate" formControlName="userJobTitle" class="ifp-invite__table-input" [readOnly]="user.value.viewOnly">
                    @if (user.controls['userJobTitle'].errors && user.controls['userJobTitle'].touched) {
                      <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a job title' | translate}}</p>
                    }
                  </td>
                  <td class="ifp-invite__table-col">
                    <ifp-access-level-dropdown [dropDownItems]="jobLevelList" [isAppendBody]="true" [viewOnly]="user.value.viewOnly" [isInline]="true" (dropDownItemClicked)="selectJobList($event, i)" formControlName="userJobLevel" [selectedValue]="user?.get('userJobLevel')?.value?.value"></ifp-access-level-dropdown>
                    @if (user.controls['userJobLevel'].errors && user.controls['userJobLevel'].touched) {
                      <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a job level' | translate}}</p>
                    }
                  </td>
                  <td class="ifp-invite__table-col">
                    <ifp-access-level-dropdown [dropDownItems]="departmentList" [isAppendBody]="true" [viewOnly]="user.value.viewOnly" [isInline]="true" (dropDownItemClicked)="selectDepartment($event, i)" formControlName="userDepartment" [selectedValue]="user?.get('userDepartment')?.value?.value"></ifp-access-level-dropdown>

                    @if (user.controls['userDepartment'].errors && user.controls['userDepartment'].touched) {
                      <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a Department' | translate}}</p>
                    }
                  </td>
                  <!-- <td class="ifp-invite__table-col">
                    @if (user.value.viewOnly) {
                      <input type="text" [value]="user.value.userPlatform.key | translate" class="ifp-invite__table-input" readOnly="true">
                    } @else {
                      <app-ifp-dropdown [showTitle]="false" [key]="'key'" class="ifp-invite__dropdown ifp-invite__dropdown--user" (dropDownItemClicked)="selectPlatform($event, i)" [dropDownItems]="platforms" [isAppendBody]="true" formControlName="userPlatform"></app-ifp-dropdown>
                    }
                  </td> -->
                  <td class="ifp-invite__table-col"><ifp-access-level-dropdown [dropDownItems]="dataClassificationList" [isAppendBody]="true" [viewOnly]="user.value.viewOnly" [isInline]="true" (dropDownItemClicked)="selectDataClassification($event, i)" formControlName="userClassification" [selectedValue]="user?.get('userClassification')?.value?.value"></ifp-access-level-dropdown></td>
                  <td class="ifp-invite__table-col">
                    <div class="ifp-invite__table-input ifp-invite__input--tag-wrapper">
                      <div class="ifp-invite__tag-wrapper" [ngClass]="{'ifp-invite__tag-wrapper--disabled': user.value.viewOnly}">
                        @if(user.value.userClassification) {
                          <span class="ifp-invite__input-tag ifp-invite__input-tag--all">
                            @if (user.value.userClassification.value !== dataClassification.sensitive.toLowerCase()) {
                              <span class="ifp-invite__tag-dot" [style.backgroundColor]="user.value.userClassification.color"></span>
                            } @else {
                              <span class="ifp-invite__tag-dot" [style.backgroundColor]="user.value.userDomains.length < domainAccessList.length ? ifpColor.orange : user.value.userClassification.color"></span>
                            }
                            {{'All' | translate}}</span>
                        }

                        @if (user.value.userClassification.value === dataClassification.sensitive.toLowerCase()) {
                          @for (domain of user.value.userDomains; track index; let index = $index) {
                            <span class="ifp-invite__input-tag" [ngClass]="{'ifp-invite__input-tag--close': !user.value.viewOnly}" [style.borderColor]="user.value.userClassification.color"><span class="ifp-invite__tag-dot" [style.backgroundColor]="user.value.userClassification.color"></span>{{domain | translate}}<em class="ifp-icon ifp-icon-cross ifp-invite__close" (click)="removeDomain(index, i)"></em>
                          </span>
                          }
                        }
                      </div>
                      @if (user.value.userClassification.value === dataClassification.sensitive.toLowerCase() && (user.value.userDomains.length !== domainAccessList.length)) {
                        <span class="ifp-invite__input-refresh"><em class="ifp-icon ifp-icon-refresh-round" (click)="domainRefresh(i)"></em></span>
                      }
                    </div>
                  </td>
                  <!-- <td class="ifp-invite__table-col">

                      <ifp-access-level-dropdown [dropDownItems]="primaryUserRoleList" (dropDownItemClicked)="selectPrimaryRole($event, i)" [viewOnly]="user.value.viewOnly"  [isAppendBody]="true"  [isInline]="true" formControlName="primaryRole"  [selectedValue]="user.get('primaryRole')?.value?.value"></ifp-access-level-dropdown>
                    @if (user.controls.primaryRole.errors) {

                      <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a user role' | translate}}</p>
                    }

                  </td> -->
                  <td class="ifp-invite__table-col">
                      <ifp-access-level-dropdown [selectionKey]="'name'" [enableClearValue]="true" [dropDownItems]="userRoleList()" (dropDownItemClicked)="selectUserRole($event, i)" [isAppendBody]="true" [viewOnly]="user.value.viewOnly" [isInline]="true" formControlName="secondaryRole"  [selectedValue]="user.get('secondaryRole')?.value?.value"></ifp-access-level-dropdown>
                    @if (user.controls.secondaryRole.errors) {

                      <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a user role' | translate}}</p>
                    }
                  </td>
                  <td class="ifp-invite__table-col">
                    @if (user.value.userClassification.value === dataClassification.sensitive.toLowerCase()) {
                      <textarea class="ifp-invite__text-area ifp-invite__col-text" [placeholder]="'Justification' | translate" formControlName="userJustification" [disabled]="user.value.viewOnly" [value]="user.value.userJustification | translate"></textarea>
                      @if (user.controls['userJustification'].errors) {
                        <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please provide a justification' | translate}}</p>
                      }
                    }
                  </td>
                  <td class="ifp-invite__table-col">
                    @if (user.value.userClassification.value === dataClassification.sensitive.toLowerCase()) {
                      <p class="ifp-invite__col-text-head">{{(roleList.dgOrUnderSecretary + ' approval required') | translate}}</p>
                      <p class="ifp-invite__col-text">{{('Data classification to the user will be confidential until get approved by ' + roleList.dgOrUnderSecretary) | translate}}</p>
                    }
                  </td>
                  <td class="ifp-invite__table-col">
                    <div class="ifp-invite__table-actions">
                      <em class="ifp-icon" [class]="user.value.viewOnly ? 'ifp-icon-edit' : 'ifp-icon-save'" (click)="editField(user, user.value.viewOnly)" [appIfpTooltip]="user.value.viewOnly ? 'Edit' : 'Save'"></em>
                      <em class="ifp-icon ifp-icon-trash" (click)="removeUser(i)" [appIfpTooltip]="'Delete'"></em>
                    </div>
                  </td>
                </tr>
              }
              </table>
            </div>
            <div class="ifp-invite__main-btn">
              <ifp-button [label]="'Invite Users'" (ifpClick)="onInviteUser()"  class="ifp-invite__button" [buttonClass]="((inviteUserList.controls.length && enableInvite) ? buttonClass.primary : buttonClass.disabled) +' '+ buttonIconPosition.left" [iconClass]="'ifp-icon-send'"></ifp-button>
            </div>
          </div>
        </ng-container>
        }
        <!-- Added users list end -->
        <!-- } -->

      </form>
      <!-- Add user Form end -->
    } @else {
      <div class="ifp-invite__sent">
        <em class="ifp-icon ifp-icon-user-alt ifp-invite__user-icon"></em>
        <h2 class="ifp-invite__title">{{'Sent Invites' | translate}}</h2>
        @if (sentInviteList.controls.length) {
          <form [formGroup]="invitesForm" class="ifp-invite__table-wrapper">
            <table class="ifp-invite__table ifp-invite__table--sent">
              <tr class="ifp-invite__table-row ifp-invite__table-row--head">
                <th class="ifp-invite__table-col">{{'Email' | translate}}</th>
                <th class="ifp-invite__table-col">{{'Job Title' | translate}}</th>
                <th class="ifp-invite__table-col">{{'Job Level' | translate}}</th>
                <th class="ifp-invite__table-col">{{'Department' | translate}}</th>
                <!-- <th class="ifp-invite__table-col">{{'Primary Role' | translate}}</th> -->
                <th class="ifp-invite__table-col">{{'Secondary Role' | translate}}</th>
                <!-- <th class="ifp-invite__table-col">{{'User Role' | translate}}</th> -->
                <!-- <th class="ifp-invite__table-col">{{'Sent On' | translate}}</th> -->
                <th class="ifp-invite__table-col">{{'Status' | translate}}</th>
                <th class="ifp-invite__table-col"></th>
              </tr>
              <ng-container formArrayName="sentInvites">
              @for (invite of sentInviteList.controls; track invite; let i = $index) {
                <tr class="ifp-invite__table-row" [formGroupName]="i" [ngClass]="{'ifp-invite__table-row--edit': editInviteIndex === i}">
                  <td class="ifp-invite__table-col"><input type="text" formControlName="inviteeEmail" [readOnly]="editInviteIndex !== i" class="ifp-invite__table-input">
                  @if (invite.controls.inviteeEmail.errors) {
                    <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid email' | translate}}</p>
                  }
                  <td class="ifp-invite__table-col"><input type="text" formControlName="designation" [readOnly]="editInviteIndex !== i" class="ifp-invite__table-input">
                    @if (invite.controls.designation.errors) {
                      <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a job title' | translate}}</p>
                    }
                  </td>
                  <td class="ifp-invite__table-col">
                    @if(editInviteIndex !== i){
                      {{ invite.get('jobLevel')?.value?.value ? invite.get('jobLevel')?.value.label : invite.get('jobLevel')?.value }}
                    }@else{
                      <ifp-access-level-dropdown [dropDownItems]="jobLevelList" (dropDownItemClicked)="selectsentInvitJobLevel($event, i)" [isAppendBody]="true"  [isInline]="true" formControlName="jobLevel"  [clearValue]="clearDropdowns" [selectedValue]="invite.get('jobLevel')?.value?.value"></ifp-access-level-dropdown>
                    @if (invite.controls.jobLevel.errors) {
                      <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a job level' | translate}}</p>
                    }
                    }
                  </td>
                  <td class="ifp-invite__table-col">
                   @if(editInviteIndex !== i){
                    {{invite.get('department')?.value?.value ? invite.get('department')?.value?.label : invite.get('department')?.value}}
                    }@else{
                      <ifp-access-level-dropdown [dropDownItems]="departmentList" [isAppendBody]="true"  [isInline]="true" (dropDownItemClicked)="selectSentInvitDeptList($event, i)"  formControlName="department"  [clearValue]="clearDropdowns" [selectedValue]="invite.get('department')?.value?.value"></ifp-access-level-dropdown>
                      @if (invite.controls.department.errors) {
                      <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a department' | translate}}</p>
                    }
                    }
                  </td>
                  <!-- <td class="ifp-invite__table-col">
                    {{ invite.get('primaryRole')?.value?.label || invite.get('primaryRole')?.value || '-' }}
                  </td> -->
                  <td class="ifp-invite__table-col">
                    {{ invite.get('secondaryRole')?.value?.label || invite.get('secondaryRole')?.value || '-' }}
                  </td>
                <!-- <td class="ifp-invite__table-col">
                    @if(editInviteIndex !== i){
                      {{ invite.get('secondaryRole')?.value?.value ? invite.get('secondaryRole')?.value.label : invite.get('secondaryRole')?.value }}
                    }@else{
                      <ifp-access-level-dropdown [dropDownItems]="userRoleList()" (dropDownItemClicked)="selectUserRole($event, i)" readOnly="true" [isAppendBody]="true"  [isInline]="true" formControlName="secondaryRole"  [clearValue]="clearDropdowns" [selectedValue]="invite.get('secondaryRole')?.value?.value"></ifp-access-level-dropdown>
                    @if (invite.controls.secondaryRole.errors) {

                      <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a user rolee' | translate}}</p>
                    }
                    }
                  </td> -->
                  <td class="ifp-invite__table-col"><input type="text" formControlName="status" class="ifp-invite__table-input" readOnly="true"></td>
                  <td class="ifp-invite__table-col">
                    <div class="ifp-invite__button-sec" [ngClass]="{'ifp-invite__button-sec--disabled': inviteList[i].status === 'awaiting_entraid'}">
                      <!-- <ifp-button [label]="'Resend Invite'" class="ifp-invite__button" [buttonClass]="buttonClass.primary" (ifpClick)="resendInvite(invite)"></ifp-button>
                      <ifp-button [label]="'Cancel Invite'" class="ifp-invite__button" [buttonClass]="buttonClass.secondary" (ifpClick)="deleteInvite(invite, $index)"></ifp-button> -->
                      @if (inviteList[i].status === inviteStatus['registration_pending'].key || inviteList[i].status === inviteStatus['awaiting_entraid'].key) {
                        <em class="ifp-icon ifp-invite__button-icon" [class]="editInviteIndex !== i ? 'ifp-icon-edit' : 'ifp-icon-cross'" [appIfpTooltip]="editInviteIndex !== i ? 'Edit Invite' : 'Cancel'" [extraSpaceTop]="25" (click)="editInviteIndex === i ? cancelEditInvite(i) : editInvite(i)"></em>
                        <em class="ifp-icon ifp-icon-send ifp-invite__button-icon" [ngClass]="{'ifp-invite__button-icon--disabled': invite.invalid}" [appIfpTooltip]="'Resend Invite'" [extraSpaceTop]="25" (click)="resendInvite(inviteList[i], i)"></em>
                      }
                      <em class="ifp-icon ifp-icon-trash ifp-invite__button-icon" [appIfpTooltip]="'Cancel Invite'" [extraSpaceTop]="25" (click)="openCancelInviteModal(i)"></em>
                    </div>
                  </td>
                </tr>
              }
              </ng-container>
            </table>
          </form>
        } @else {
          <app-ifp-no-data [message]="'No Pending Invites'" class="ifp-invite__no-data"></app-ifp-no-data>
        }
      </div>
    }
  <!-- Super user end -->
  }
</div>

@if (superUserRoles.includes(role)) {
<app-ifp-modal [modalClass]="'ifp-modal__invite-dg'" #inviteDgModal>
<!-- @if (isDgRequired && dgStatus !== dgStatusConst.available) { -->
  <!-- Invite DG Form start -->
   <em class="ifp-icon ifp-icon-cross ifp-invite__dg-modal-close" (click)="closeDgModal()"></em>
  @if (dgStatus === dgStatusConst.inviteSent) {
    <div class="ifp-invite__dg-box ifp-invite__box">
      <div>
        <em class="ifp-icon ifp-icon-clock ifp-invite__msg-icon"></em>
        <h2 class="ifp-invite__title ifp-invite__title--dg">{{'dgInviteNotify' | translate:{role : roleList.dgOrUnderSecretary} }}</h2>
        <p class="ifp-invite__desc">{{'Once Director General is onboarded, you can start adding the users for sensitive requests' | translate}}</p>
      </div>
    </div>
  } @else {
    <form [formGroup]="dgForm" class="ifp-invite__dg-form ifp-invite__box">
      <div class="ifp-invite__info-box">
        <em class="ifp-icon ifp-icon-info-round ifp-invite__info-icon"></em>
        <p class="ifp-invite__info-message">{{'dgInviteNote' | translate:{role: roleList.dgOrUnderSecretary} }}</p>
      </div>
      <em class="ifp-icon ifp-icon-user-alt ifp-invite__user-icon"></em>
      <h2 class="ifp-invite__title ifp-invite__title--info">{{roleList.dgOrUnderSecretary | translate}} {{'Invitation' | translate}}<app-ifp-info class="ifp-invite__label-info" [infoContent]="'To approve/reject data that is classified as sensitive'"></app-ifp-info></h2>
      <!-- <em> ({{'to approve/reject data that is classified as sensitive'}})</em> -->
      <div class="ifp-invite__input-wrapper">
        <div class="ifp-invite__input-sec">
          <p class="ifp-invite__input-label">{{'Email' | translate}}</p>
          <div class="ifp-invite__input-outer">
            <!-- <input type="text" [placeholder]="'Email' | translate" formControlName="dgEmail" class="ifp-invite__input" appSearchSuggestion [suggestions]="exisitingUserList" [searchQuery]="dgForm.value.dgEmail" [isGlobalSearch]="false" (setValue)="setSuggestion($event, 'dgForm', 'dgEmail')" (focus)="onFocus('dgForm', 'dgEmailFocused')" (blur)="onblur('dgForm', 'dgEmailFocused')" [isFocusInput]="dgForm.value.dgEmailFocused"> -->
            <input type="text" [placeholder]="'Email' | translate" formControlName="dgEmail" class="ifp-invite__input">
          </div>
          @if (dgForm.controls['dgEmail'].errors && dgForm.controls['dgEmail'].touched) {
            <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid email' | translate}}</p>
          }
        </div>
        <div class="ifp-invite__input-sec">
          <p class="ifp-invite__input-label">{{'Emirates ID' | translate}}</p>
          <input type="text" [placeholder]="'Emirates ID' | translate" formControlName="dgEid" class="ifp-invite__input" [readOnly]="isReadOnly" (click)="isReadOnly = false" [max]="15" appNumberOnly>
          @if (dgForm.controls['dgEid'].errors && dgForm.controls['dgEid'].touched) {
            <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please enter a valid Emirates ID' | translate}}</p>
          }
        </div>
        <div class="ifp-invite__input-sec">
          <app-ifp-dropdown [title]="'Job Title'" [singleDefaultSelect]="true" [selectedValue]="dgTitles[0]" [showTitle]="true" [key]="'value'" class="ifp-invite__dropdown ifp-invite__dropdown--entity" (dropDownItemClicked)="selectDg($event)" [dropDownItems]="dgTitles"></app-ifp-dropdown>

          <!-- <app-ifp-dropdown [title]="'Job Title'" [showTitle]="true" [key]="'value'" class="ifp-invite__dropdown ifp-invite__dropdown--entity" (dropDownItemClicked)="selectDg($event)" [dropDownItems]="dgTitles" formControlName="dgJobTitle"></app-ifp-dropdown> -->
          <!-- @if (dgForm.controls['dgJobTitle'].errors && dgForm.controls['dgJobTitle'].touched) {
            <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please select a job title' | translate}}</p>
          } -->
        </div>
        <!-- <div class="ifp-invite__input-sec">
                <p class="ifp-invite__input-label ifp-invite__input-label--info">{{'Select Primary User Role' | translate}} </p>
                  <ifp-access-level-dropdown  [dropDownItems]="primaryUserDGList" (dropDownItemClicked)="selectPDgRole($event)" formControlName="primarySuRole" [selectedValue]="dgForm.get('primaryDgRole')?.value" [clearValue]="clearDropdowns"></ifp-access-level-dropdown>
               @if (dgForm.controls['primaryDgRole'].errors && dgForm.controls['primaryDgRole'].touched) {
                  <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please Select a user role' | translate}}</p>
                }
                </div> -->
          <div class="ifp-invite__input-sec">
                <p class="ifp-invite__input-label ifp-invite__input-label--info">{{'Select Secondary User Role' | translate}} </p>
                  <ifp-access-level-dropdown [selectionKey]="'name'" [enableClearValue]="true" [dropDownItems]="userRoleList()" (dropDownItemClicked)="selectSDgRole($event)" formControlName="secondarySuRole" [selectedValue]="dgForm.get('secondarySuRole')?.value" [clearValue]="clearDropdowns"></ifp-access-level-dropdown>
               @if (dgForm.controls['secondaryDgRole'].errors && dgForm.controls['secondaryDgRole'].touched) {
                  <p class="ifp-input-error"><em class="ifp-icon ifp-icon-exclamation-round"></em>{{'Please Select a user role' | translate}}</p>
                }
                </div>
        <!-- <div class="ifp-invite__input-sec">
          <p class="ifp-invite__input-label">{{'Mobile Number linked with UAE Pass' | translate}}</p>
          <input type="number" [placeholder]="'Mobile' | translate" formControlName="dgEid" class="ifp-invite__input" [readOnly]="isReadOnly">
        </div> -->
      </div>
      <div class="ifp-invite__btn-sec">
        <ifp-button [label]="'Send Invite'" (ifpClick)="onInviteDg()"  class="ifp-invite__button" [buttonClass]="(dgForm.valid ? buttonClass.primary : buttonClass.disabled) +' '+ buttonIconPosition.left" [iconClass]="'ifp-icon-send'"></ifp-button>
      </div>
    </form>
  }
  <!-- Invite DG Form end -->
<!-- } -->
</app-ifp-modal>
}

  <app-ifp-modal #cancelInviteModal>
    <app-ifp-remove-card [text]="('Are you sure you want to delete this invite' | translate)+ ('?' | translate)" [firstButton]="'Confirm' | translate" [secondButton]="'Cancel'" (firstButtonEvent)="deleteInvite()" (secondButtonEvent)="closeDeleteModal()">
    </app-ifp-remove-card>
  </app-ifp-modal>
