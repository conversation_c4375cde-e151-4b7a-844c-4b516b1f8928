import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpDbDropdownComponent } from '../ifp-db-dropdown/ifp-db-dropdown.component';
import { IfpDbChartPropsComponent } from '../ifp-db-chart-props/ifp-db-chart-props.component';
import { IfpInputCounterComponent } from '../../atom/ifp-input-counter/ifp-input-counter.component';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';
import { NgFor, SlicePipe, TitleCasePipe } from '@angular/common';

@Component({
    selector: 'ifp-db-chart-style',
    imports: [TranslateModule, IfpDbDropdownComponent, IfpDbChartPropsComponent, IfpInputCounterComponent, NgFor, TitleCasePipe, SlicePipe],
    templateUrl: './ifp-db-chart-style.component.html',
    styleUrl: './ifp-db-chart-style.component.scss'
})
export class IfpDbChartStyleComponent implements OnChanges {

  @Input() selectedCard!: string;
  @Input() cntType!: string;
  @Input() chartSeies!: number;
  @Input() seriesTitles: string[] = [];
  @Input() seriesColors: string[] = [];
  @Output() updateSpacingValueEmitter: EventEmitter<{ value: number, key: string }> = new EventEmitter<{ value: number, key: string }>();
  @Output() updateXaxisPosition: EventEmitter<{ value: string }> = new EventEmitter<{ value: string }>();
  @Output() updateLineColor: EventEmitter<{ value: string, index: number }> = new EventEmitter<{ value: string, index: number }>();

  public selectedXaxis!: { name: string, value: string, isSelected: boolean };


  public alignOptions = [
    {
      name: 'Left',
      value: 'right',
      isSelected: false
    },
    {
      name: 'Right',
      value: 'left',
      isSelected: true
    },
    {
      name: 'Center',
      value: 'center',
      isSelected: false
    }
  ];

  public spaceOptions = [
    {
      name: 'Spacing top',
      key: 'spacingTop',
      value: 15
    },
    {
      name: 'Spacing bottom',
      key: 'spacingBottom',
      value: 10
    },
    {
      name: 'Spacing left',
      key: 'spacingLeft',
      value: 10
    }
  ];


  constructor(private _dashboardService: DashboardService) { }

  updateSpacingValue(event: any) {
    const index = this.spaceOptions.findIndex(x => x.key == event.key);
    this.spaceOptions[index].value = event.value;
    this.updateSpacingValueEmitter.emit(event);
  }


  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedCard']) {
      const index = this._dashboardService.chartSettings[this.cntType]?.findIndex((x: { id: string; }) => x.id == this.selectedCard);
      if (index >= 0) {
        const indexoObject = this._dashboardService.chartSettings[this.cntType][index];
        this.spaceOptions[0].value = indexoObject.spacingTop ? indexoObject.spacingTop : 15;
        this.spaceOptions[1].value = indexoObject.spacingRight ? indexoObject.spacingRight : 10;
        this.spaceOptions[2].value = indexoObject.spacingBottom ? indexoObject.spacingBottom : 10;
        // this.spaceOptions[3].value = indexoObject.spacingLeft ? indexoObject.spacingLeft : 10;
        this.selectedXaxis = this.alignOptions[0];
        if (indexoObject.xAxisPos) {
          const selectIndex = this.alignOptions.findIndex(x => x.value == indexoObject.xAxisPos);
          this.selectedXaxis = this.alignOptions[selectIndex];
        }
      } else {
        this.spaceOptions[0].value = 15;
        this.spaceOptions[1].value = 10;
        this.spaceOptions[2].value = 10;
        // this.spaceOptions[3].value = 10;
        this.selectedXaxis = this.alignOptions[0];
      }
    }
  }

  selectXaxisPos(event: any) {
    this.alignOptions.map(x => x.isSelected = false);
    this.alignOptions[event.index].isSelected = true;
    this.selectedXaxis = this.alignOptions[event.index];
    this.updateXaxisPosition.emit(event.value);
  }

  getColor(event: any, index: number) {
    this.seriesColors[index] = event.target.value;
    this.updateLineColor.emit({ value: event.target.value, index: index });
  }
}
