@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-hies-census-filters {
  background: $ifp-color-white-global;
  border-radius: 8px;
  padding: $spacer-4;

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: $spacer-3;
  }
  &__title {
    margin: 0;
    font-size: $ifp-fs-6;
    font-weight: $fw-semi-bold;
  }

  &__content {
    display: flex;
    gap: $spacer-4;
  }
  &__panel {
    flex: 1;
  }
  &__panel-title {
    margin: 0 0 $spacer-3 0; // increased spacing below title
    font-weight: $fw-semi-bold;
    color: rgba($ifp-color-black-global, 0.8);
  }

  &__table-scroll {
    max-height: 320px;
    overflow: hidden;
    border-radius: $spacer-1;

    &.is-empty {
      // ensure some spacing between header and no-data placeholder
      padding-top: $spacer-2;
    }
  }
  &__table {
    width: 100%;
    border-collapse: collapse;
  }
  &__table thead th {
    padding: $spacer-2 $spacer-2;
    font-weight: $fw-semi-bold;
    color: rgba($ifp-color-black-global, 0.65);
    background: rgba($ifp-color-black-global, 0.06);
    position: sticky;
    top: 0;
    z-index: 3;
    border-bottom: 1px solid $ifp-color-grey-13; // added
    background-color: $ifp-color-grey-bg-2;     // added
    border: none;
  }
&__table thead th:first-child { border-top-left-radius: 8px; border-bottom-left-radius: 8px; }
&__table thead th:last-child { border-top-right-radius: 8px; border-bottom-right-radius: 8px;}

  // align second column (Uses) to the right
  &__table thead th:nth-child(2) {
    text-align: right;
  }
  &__row td:nth-child(2) {
    text-align: right;
  }
  &__row td {
    padding: $spacer-3 $spacer-2;
    border-bottom: 1px solid rgba($ifp-color-black-global,0.04);
  }
  &__no-data-row td { padding-top: $spacer-6; }
  &__cell--right {
    text-align: right;
    color: $ifp-color-primary-blue;
  }
}
