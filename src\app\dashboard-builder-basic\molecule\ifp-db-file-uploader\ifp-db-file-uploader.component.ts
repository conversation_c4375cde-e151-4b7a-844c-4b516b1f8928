import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild, input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { DragNDropDirective } from '../../core/directives/drag-n-drop.directive';
import { IfpDbFileUploaderService } from './ifp-db-file-uploader.service';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { NgClass, NgStyle } from '@angular/common';
import { fileFormats, fileSizeLimit } from './ifp-db-file-uploader.constants';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpRemoveCardComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-remove-card/ifp-remove-card.component';

@Component({
  selector: 'app-ifp-db-file-uploader',
  templateUrl: './ifp-db-file-uploader.component.html',
  styleUrls: ['./ifp-db-file-uploader.component.scss'],
  imports: [TranslateModule, DragNDropDirective, NgClass, NgStyle, IfpModalComponent, IfpRemoveCardComponent]
})
export class IfpDbFileUploaderComponent implements OnInit, OnChanges {

  @ViewChild('fileUploader') fileUploader!: ElementRef;
  @ViewChild('alertModal') alertModal!: IfpModalComponent;
  @ViewChild('input') input!: ElementRef;
  @Output() fileUpload: EventEmitter<FileData> = new EventEmitter<FileData>();
  @Output() removeFile: EventEmitter<File[]> = new EventEmitter<File[]>();


  @Input() isImage: boolean = true;
  @Input() allowedExtensions = fileFormats.allowedExtensions;
  @Input() maxSizeLimit = fileSizeLimit.maxSize;
  @Input() previewUrl!: any;
  @Input() progress = 0; // used for show prgress bar
  @Input() fileName!: string;
  @Input() enableAlertMessage: boolean = false;
  @Input() dragAndDropText = 'Drag and drop your file or';
  @Input() supportText = 'Supported Formats';
  @Input() iconBackgroung = '';
  @Input() alertText: string = '';
  @Input() files: File[] = [];
  @Input() hideDelete = false;
  @Input() dragOnly = false;
  public enableInfo = input(false);
  public enableInstruction = input(false);

  public selectedFile!: File;
  public acceptFormat: string = '';

  constructor(private _validator: IfpDbFileUploaderService, private _sanitizer: DomSanitizer, private _cdr: ChangeDetectorRef) { }


  ngOnChanges(changes: SimpleChanges): void {
    if (changes['files'] && this.files?.length <= 0) {
      this.previewUrl = '';
    } else if (this.files?.length > 0) {
      this.selectedFile = this.files[0];
    }
  }


  ngOnInit() {
    this.acceptFormat = this.allowedExtensions.toString();
  }

  handleDragOver(event: DragEvent) {
    event.preventDefault(); // Prevent default browser behavior
  }

  onDrop(event: DragEvent) {
    const file = event?.dataTransfer?.files[0];
    const isValidDoc = this._validator.validateDocument(file, this.allowedExtensions);
    if (file && isValidDoc) {
      this.uploadFile(file);
    } else {
      this.files = [];
      this.previewUrl = '';
    }
  }

  onBrowse(event: any) {
    const file = event?.target?.files[0];
    const isValidDoc = this._validator.validateDocument(file, this.allowedExtensions);
    if (isValidDoc) {
      this.selectedFile = file;
      this.files = [file];
      this.uploadFile(this.selectedFile);
    }
  }

  uploadFile(file: File) {
    const reader = new FileReader();
    reader.onload = (e: any) => {
      // Sanitize URL before assigning to DOM
      this.previewUrl = this._sanitizer.bypassSecurityTrustUrl(e.target.result);
      this.fileUpload.emit({ file: [file], url: this.previewUrl });
      this.input.nativeElement.value = '';
    };
    reader.readAsDataURL(file);

  }

  deleteFile() {
    this.files = [];
    this.previewUrl = '';
    this.fileUploader.nativeElement.value = '';
    this._cdr.detectChanges();
    this.removeFile.emit(this.files);
    this.input.nativeElement.value = '';
  }

  openFileUpload() {
    if (this.enableAlertMessage) {
      this.alertModal.createElement();
      return;
    }
    this.fileUploader.nativeElement.click();
  }

  openOrCloseModel(event: boolean) {
    this.alertModal.removeModal();
    if (event) {
      this.fileUploader.nativeElement.click();
    }
  }
}

export interface FileData {
  file: File[];
  url: SafeUrl;
}

export interface UploadInfo {
  title: string ;
  data:UploadInfoValue[] ;
}

export interface UploadInfoValue {event:string, item:string}
