import { NgClass } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'ifp-search-round',
    imports: [TranslateModule, FormsModule, NgClass],
    templateUrl: './ifp-search-round.component.html',
    styleUrl: './ifp-search-round.component.scss'
})
export class IfpSearchRoundComponent {
  @Input() placeholder: string = 'Search';
  @Output() getResults: EventEmitter<string> = new EventEmitter<string>();
  @Input() searchText: string = '';

  onSearch(keyword: string) {
    this.getResults.emit(keyword);
  }
}
