<div class="ifp-hies-visits ifp-download-analysis">
  <div class="ifp-hies-visits__header">
    <h3 class="ifp-hies-visits__title">{{ title | translate }}</h3>
    <div class="ifp-hies-visits__download ifp-drop-download">
      <app-ifp-dropdown [isInline]="true" [dropDownItems]="downloadTypes" [key]="'label'" [isDownload]="true"
        (dropDownItemClicked)="onDownload($event)"></app-ifp-dropdown>
    </div>
  </div>

  <ng-container>
    <div *ngIf="(type === 'hiesSummary' || type === 'durationCensus')" class="ifp-hies-visits__summary-head">
      <div class="ifp-hies-visits__summary-left">
        <ifp-pills-tab [tabData]="tabData" (selectTabItem)="selectedKey($event)" class="ifp-byn-db__top-tab"
          [isLarge]="false"></ifp-pills-tab>
      </div>
      <div class="ifp-hies-visits__summary-right">
        <ifp-panel-dropdown [multiSelect]="true" [enableSearch]="true" [isBoxType]="true" [options]="selectedTabKey === 'entities' ? tableColumnsEntities : tableColumnsUsers" [key]="'value'" [label]="'Columns'" [multipleSelectedItems]="selectedEntityOptions"  (multiSelected)="onSelectColumns($event)" ></ifp-panel-dropdown>
      </div>
    </div>
    <div *ngIf="type === 'hiesVisits'" class="ifp-hies-visits__total">
      <div class="ifp-hies-visits__total-label">{{ 'Total Visits' | translate }}</div>
      <div class="ifp-hies-visits__total-value">{{ nodeData[0]?.total | number }} <span
          class="ifp-hies-visits__small">visits</span></div>
    </div>
    <div *ngIf="type==='censusVisits'" class="ifp-hies-visits__summary-head">
      <div class="ifp-hies-visits__summary-left">
        <ifp-pills-tab [tabData]="tabData" (selectTabItem)="selectedKey($event)" class="ifp-byn-db__top-tab"
          [isLarge]="false"></ifp-pills-tab>
      </div>
      <div class="ifp-hies-visits__census-right">
        <div class="ifp-hies-visits__metrics">
          <div class="ifp-hies-visits__metric">
            <div class="ifp-hies-visits__metric-label">{{ 'Duration' | translate }}</div>
            <div class="ifp-hies-visits__metric-value">{{ durationValue }} <span
                class="ifp-hies-visits__metric-unit">Hours</span></div>
          </div>
          <div class="ifp-hies-visits__metric">
            <div class="ifp-hies-visits__metric-label">{{ 'Total Visits' | translate }}</div>
            <div class="ifp-hies-visits__metric-value">{{ nodeData[0]?.total || totalVisits | number }}</div>
          </div>
        </div>
      </div>
    </div>
  </ng-container>

  <div class="ifp-hies-visits__table-scroll" [class.is-empty]="!nodeData || nodeData.length === 0">
    @if (loader()) {
    <app-ifp-card-loader class="ifp-loader" [type]="'table'"></app-ifp-card-loader>
    }@else {
    <table class="ifp-hies-visits__table" role="table" aria-label="HIES visits table">
      <thead>

        <tr class="ifp-download-analysis__list-header" *ngIf="type === 'hiesVisits'">
          <th class="ifp-hies-visits__col-indicator">{{ 'Indicator' | translate }}</th>
          <th class="ifp-hies-visits__col-visits">{{ 'Visits' | translate }}</th>
        </tr>

        <!-- dynamic headers for summary (users/entities) -->
        <tr class="ifp-download-analysis__list-header" *ngIf="(type === 'hiesSummary' || type === 'durationCensus') && selectedTabKey === 'users'">
          <ng-container *ngFor="let col of tableColumnsUsers">
            <th *ngIf="isColumnVisible(col.key)" class="ifp-hies-visits__col-{{ col.key }}">{{ col.value }}</th>
          </ng-container>
        </tr>
        <tr class="ifp-download-analysis__list-header" *ngIf="(type === 'hiesSummary' || type === 'durationCensus') && selectedTabKey === 'entities'">
          <ng-container *ngFor="let col of tableColumnsEntities">
            <th *ngIf="isColumnVisible(col.key)" class="ifp-hies-visits__col-{{ col.key }}">{{ col.value }}</th>
          </ng-container>
        </tr>
      </thead>

      <tbody>
        <ng-container *ngIf="type === 'hiesVisits'">
          <tr *ngFor="let item of nodeData; trackBy: trackByIndex" class="ifp-hies-visits__item">
            <td class="ifp-hies-visits__item-label">{{ item.indicator }}</td>
            <td class="ifp-hies-visits__item-visits" (click)="onVisitClick(item)">{{ item.visits }}</td>
          </tr>
        </ng-container>

        <ng-container *ngIf="(type === 'hiesSummary' || type === 'durationCensus') && selectedTabKey === 'users'">
          <tr *ngFor="let item of nodeData; trackBy: trackByIndex" class="ifp-hies-visits__item">
            <ng-container *ngFor="let col of tableColumnsUsers">
              <td *ngIf="isColumnVisible(col.key)">{{ item[col.key] }}</td>
            </ng-container>
          </tr>
        </ng-container>
        <ng-container *ngIf="(type === 'hiesSummary' || type === 'durationCensus') && selectedTabKey === 'entities'">
          <tr *ngFor="let item of nodeData; trackBy: trackByIndex" class="ifp-hies-visits__item">
            <ng-container *ngFor="let col of tableColumnsEntities">
              <td *ngIf="isColumnVisible(col.key)">{{ item[col.key] }}</td>
            </ng-container>
          </tr>
        </ng-container>
        <tr *ngIf="!nodeData || nodeData.length === 0" class="ifp-hies-visits__no-data-row">
          <td [attr.colspan]="type === 'hiesVisits' ? 2 : 3">
            <app-ifp-no-data class="ifp-download-insight__no-data"></app-ifp-no-data>
          </td>
        </tr>
      </tbody>
    </table>
    }
  </div>
</div>