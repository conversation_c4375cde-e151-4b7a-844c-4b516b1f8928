@use '../../../../assets/ifp-styles/abstracts' as *;

.if-myapp-landing {
  &__drag-drop {
    display: block;
    width: 100%;
  }

  &__head {
    display: flex;
    padding: $spacer-3 $spacer-0 $spacer-0;
  }

  &__head-placeholder {
    display: flex;
  }

  &__section {
    //display: flex;
    // flex-wrap: wrap;
    min-height: 100px;
    margin: $spacer-0 (-$spacer-8) $spacer-4;
    &:last-child {
      margin-bottom: $spacer-0;
    }
  }

  &__section-card {
    background-color: $ifp-color-section-white;
    border-radius: 10px;
    margin: $spacer-4 $spacer-8 $spacer-0;
    padding: 16px;
    border: solid $ifp-color-grey-7 1px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  &__wh-new {
    height: 100%;
  }

  &__cards-head {
    display: flex;
  }

  &__cards-title {
    font-size: $ifp-fs-5;
    font-weight: $fw-bold ;
    color: $ifp-color-primary-blue;

    .ifp-icon {
      font-size: inherit;
      margin-left: $spacer-1;
      color: $ifp-color-blue-hover;
      position: relative;
      top: 1px;
    }
  }

  &__title {
    margin-left: $spacer-3;
    font-size: $ifp-fs-8;
    font-weight: $fw-bold ;
    color: $ifp-color-primary-blue;

    &--edit {
      margin-left: $spacer-0;
    }
  }

  // &__rect {
  //   cursor: pointer;
  //   display: inline-block;
  //   height: 20px;
  //   border: 2px solid $ifp-color-white-global;
  //   border-radius: 5px;
  //   transition: 0.3s;

  //   &--small {
  //     margin: $spacer-1 $spacer-1;
  //     width: 13px;
  //     min-width: 13px;
  //   }

  //   &--large {
  //     margin: $spacer-1 $spacer-1;
  //     width: 26px;
  //     min-width: 26px;
  //   }

  //   &:hover {
  //     border-color: $ifp-color-secondary-blue;
  //   }
  // }

  &__action {
    font-size: $ifp-fs-6;
    margin: $spacer-0 $spacer-2;
    cursor: pointer;
    transition: 0.3s;
    &:hover {
      color: $ifp-color-secondary-blue;
    }
  }

  &__filter {
    display: flex;
    margin: $spacer-0 (-$spacer-2) $spacer-0 auto;
    align-items: center;
    z-index: 1;
    position: relative;
  }

  &__placeholder {
    background-color: $ifp-color-white;
    border-radius: 10px;
    margin: $spacer-4 $spacer-8 $spacer-0;
    padding: 16px;
    border: solid $ifp-color-grey-7 1px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  &__heading-left {
    margin-left: auto;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__dropdown,
  &__button {
    margin: $spacer-0 $spacer-2;
  }

  &__wrapper {
    width: 100%;
    padding: $spacer-0 $spacer-3 $spacer-3;
    background-color: $ifp-color-section-white;
    height: 100%;
    margin-bottom: $spacer-5;
    border-radius: 10px;
    // position: relative;

    // &::after,
    // &::before {
    //   content: '';
    //   width: 5000px;
    //   height: 100%;
    //   background-color: $ifp-color-section-white;
    //   position: absolute;
    //   top: 0;
    // }

    // &::after {
    //   left: -5000px;
    // }

    // &::before {
    //   right: -5000px;
    // }

    &--minimize {
      height: unset;

      // &::after,
      // &::before {
      //   content: none;
      // }

      border-radius: 10px;
      border: solid $ifp-color-grey-7 1px;
      overflow: hidden;
      margin: $spacer-2;
      width: calc(33.33% - ($spacer-3));

      .if-myapp-landing {
        &__head {
          padding: $spacer-0 ;
        }

        &__wh-new,
        &__analytic {
          display: none;
        }

        &__section {
          display: block;
          margin: $spacer-0 $spacer-0 $spacer-0;
        }

        &__section-card {
          margin: $spacer-2 $spacer-0;
          box-shadow: 0px 0px 6px #00000029;
        }

        &__cards {
          margin-bottom: $spacer-2;
        }

        &__outer-wrapper {
          display: flex;

        }


      }

    }
  }

  &__outer-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-0 (
      -$spacer-2
    );
  }

  &__main {
    //background-color: $ifp-color-white;
    min-height: 500px;

  }

  &__st {
    display: flex;
    align-items: center;
    margin-top: 100px;
    justify-content: center;
    margin-bottom: 100px;
  }

  &__st-card {
    width: 100%;
    max-width: 800px;
    cursor: pointer;
  }

  &__wh-new,
  &__analytic {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-2 (
      -$spacer-1) $spacer-0;
    background-color: $ifp-color-section-white;
    border-radius: 10px;
  }

  &__wh-card {
    margin: $spacer-2 $spacer-1 $spacer-0;
  }

  &__analytic-card {
    margin: $spacer-2;
    width: calc(20% - 8px
    );
  }

  &__fill {
    border-color: $ifp-color-blue-hover;
    cursor: default;
  }

  &__geo-spatial {
    display: block;
    width: 100%;
  }

  &__btn-sec {
    display: flex;
    justify-content: space-between;
    margin-bottom: $spacer-4;
  }

  &__select-all {
    margin-bottom: $spacer-4;
    display: inline-block;
    &--mb-0 {
      margin-bottom: $spacer-0;
    }
  }
  &__view {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacer-2;
  }
  &__view-left {
    display: flex;
    align-items: center;
    .ifp-icon {
      position: relative;
      top: 1px;
    }
  }
  &__view-head {
    font-size: $ifp-fs-8;
    font-weight: $fw-bold;
    margin: $spacer-0 $spacer-3;
  }
  &__view-date {
    font-size: $ifp-fs-4;
    &--users {
      margin-bottom: $spacer-4;
    }
    .ifp-link {
      font-size: inherit;
    }
  }
  &__view-date-title {
    font-size: inherit;
    color: $ifp-color-tertiary-text;
    margin-right: $spacer-1;
  }
  &__filter-btn {
    position: relative;
    &--active {
      &::after {
        content: "";
        width: 0;
        height: 0;
        border-bottom: 15px solid $ifp-color-white;
        border-right: 15px solid transparent;
        border-left: 15px solid transparent;
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 10px;
      }
    }
  }
  &__filter-inner {
    display: flex;
    justify-content: flex-end;
    padding: $spacer-3;
    background-color: $ifp-color-white;
    border-radius: 10px;
    margin-bottom: $spacer-4;
  }
  &__filter-wrapper {
    transition: 0.3s;
    max-height: 0;
    overflow: hidden;
    &--active {
      max-height: 1000px;
      overflow: visible;
    }
  }
  &__btn-sec-left {
    display: flex;
    margin-bottom: $spacer-3;
    &--align {
      margin-bottom: -2.4%;
    }
  }
}

:host::ng-deep {
  .if-myapp-landing {
    &__dropdown {
      .ifp-dropdown {
        background-color: $ifp-color-section-white;
      }
    }
    &__switch-view {
      .ifp-tab__item {
        min-width: 120px;
      }
    }
  }

  .if-myapp-landing__wrapper--minimize {
    .ifp-card {
      margin: $spacer-2 $spacer-0;
      box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.16);
    }

  }

  .if-myapp-landing__cards {
    .ifp-card {
      // padding: $spacer-3 $spacer-2;
      background-color: $ifp-color-white;
    }
  }

  .if-myapp-landing__wh-new {
    .ifp-whats-new-card {
      max-width: 200px;
    }

    .ifp-loading-card__block {
      margin: $spacer-0 $spacer-0;
      width: 200px;
    }
  }

  .ifp-active-card {
    .ifp-whats-new-card {
      max-width: 100%;
    }
  }

  .if-myapp-landing__analytic {
    .ifp-active-card {
      width: calc(60% - $spacer-3);

    }

    .ifp-loading-card__block {
      margin: $spacer-0 $spacer-0;
    }
  }

  .if-myapp-landing__edit-button {
    em {
      font-size: $ifp-fs-6;
    }
  }
}

.ifp-icon-handler {
  svg {
    pointer-events: none;
  }

  &__icon {
    fill: $ifp-color-black;
    cursor: move;
    margin: $spacer-0 $spacer-1;

    &:hover {
      fill: $ifp-color-link;
    }

    &--disable {
      fill: $ifp-color-grey-7;
      cursor: not-allowed;
      display: none;
    }
  }


}

:host-context([dir="rtl"]) {
  .if-myapp-landing {
    &__cards-title {
      .ifp-icon {
        margin-right: $spacer-1;
        margin-left: $spacer-0;
      }
    }

    // &__title {
    //   margin-right: $spacer-3;
    //   margin-left: $spacer-0;

    //   &--edit {
    //     margin-right: $spacer-0;
    //   }
    // }

    // &__heading-left {
    //   margin-right: auto;
    //   margin-left: $spacer-0;
    // }
    &__filter {
      margin: $spacer-0 auto $spacer-0 (-$spacer-2);
    }
  }
}

@include desktop-sm {
  .if-myapp-landing {
    &__analytic-card {
      width: calc(25% - 8px);
    }

    &__wrapper {
      &--minimize {
        width: calc(50% - 16px);
        .if-myapp-landing {
          &__head {
            display: block;
          }

          &__title {
            margin-left: $spacer-0;
            margin-top: $spacer-1;
            font-size: $ifp-fs-5;
          }

          &__cards-title {
            font-size: $ifp-fs-4;
          }
        }
      }
    }
  }
}

@include mobile {
  .if-myapp-landing {
    &__title {
      font-size: $ifp-fs-5;
    }

    &__cards-title {
      font-size: $ifp-fs-3;
    }

    &__dropdown {
      margin: $spacer-1;
      width: calc(50% - 8px);
      display: block;
    }

    &__filter {
      flex-wrap: wrap;
      margin: $spacer-0 (-$spacer-1);
    ifp-button.if-myapp-landing__dropdown {
      width: auto;
    }
  }

  &__wrapper {
    padding: $spacer-3 $spacer-2;

    &::before,
    &::after {
      content: none;
    }

    &--minimize {
      width: 100%;
    }
  }

  &__main {
    background-color: $ifp-color-section-white;
  }
}

:host::ng-deep {
  .if-myapp-landing {
    &__dropdown {
      .ifp-dropdown {
        max-width: none;
      }
    }
  }
}
}
