import { AnalyticalService } from 'src/app/scad-insights/core/services/analytical.service';
import { QuotRemove } from './../../../core/pipes/quotsRemove.pipe';
import {
  As<PERSON><PERSON>ip<PERSON>,
  DateP<PERSON>e,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  NgIf
} from '@angular/common';
import {
  AfterViewChecked,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  HostListener,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
  ElementRef,
  Renderer2,
  SimpleChanges
} from '@angular/core';
import { IfpDropdownComponent } from '../../ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { IfpRatingComponent } from '../../ifp-atoms/ifp-rating/ifp-rating.component';
import { TranslateModule } from '@ngx-translate/core';
import { FilterService } from 'src/app/scad-insights/core/services/filter/filter.service';
import { IfpAnalyticLineChartComponent } from '../ifp-analytic-line-chart/ifp-analytic-line-chart.component';
import { IfpMonthSelectorComponent } from '../../ifp-atoms/ifp-month-selector/ifp-month-selector.component';
import { IfpCardLoaderComponent } from '../../ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { DownloadService } from 'src/app/scad-insights/core/services/download.service';
import { all, axisCategories, chartConstants, chartTypeHide, checkAll, checkAllArray, totalValues } from 'src/app/scad-insights/core/constants/chart.constants';
import { CustomNumberPipe } from 'src/app/scad-insights/core/pipes/customNumber.pipe';
import { IfpButtonComponent } from '../../ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { Store } from '@ngrx/store';
import { SubSink } from 'subsink';
import { selectGetMappedData } from 'src/app/scad-insights/store/notification/notification.selector';
import { distinctUntilChanged } from 'rxjs';
import {
  setMyAppsUpdate,
  unsubscribeMyAppsUpdate
} from 'src/app/scad-insights/store/myApps/myAppsGlobal.action';
import { selectGetMyAppsMappedData } from 'src/app/scad-insights/store/myApps/myAppsGlobal.selector';
import { cloneDeep, groupBy } from 'lodash';
import { IfpToggleButtonComponent } from '../../ifp-atoms/ifp-toggle-button/ifp-toggle-button.component';
import { IfpTableComponent } from '../ifp-table/ifp-table.component';
import { IfpTabComponent } from '../ifp-tab/ifp-tab.component';
import { LabelData } from 'src/app/scad-insights/core/interface/atom/ifp-category-label.interface';
import { IfpModalComponent } from '../../ifp-organism/ifp-modal/ifp-modal.component';
import { IfpPdfTemplateComponent } from '../ifp-pdf-template/ifp-pdf-template.component';
import { selectInsightResponse } from 'src/app/scad-insights/store/chart-insights/chart-insights.selector';
import { IfpCommentsComponent } from '../../ifp-organism/ifp-comments/ifp-comments.component';
import { getInsights } from 'src/app/scad-insights/store/chart-insights/chart-insights.action';
import { InsightsService } from 'src/app/scad-insights/core/services/chart-insight/chart-insight.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { CommonApiService } from 'src/app/scad-insights/core/services/common-api/commonApi.service';
import { IfpCheckBoxComponent } from '../../ifp-atoms/ifp-check-box/ifp-check-box.component';
import { IfpTncModalComponent } from '../../ifp-organism/ifp-tnc-modal/ifp-tnc-modal.component';
import { IfpNotificationSettingsComponent } from '../ifp-notification-settings/ifp-notification-settings.component';
import { CustomLetter } from 'src/app/scad-insights/core/pipes/firstLetter.pipe';
import { IfpIconTextComponent } from '../ifp-icon-text/ifp-icon-text.component';
import { analyticsClasses } from 'src/app/scad-insights/core/constants/analytics-class.constants';
import { ChartToolsService } from 'src/app/scad-insights/core/services/chart-tools/chart-tools.service';
import { IfpCircularBarChartComponent } from '../ifp-circular-bar-chart/ifp-circular-bar-chart.component';
import { PieChartSeries } from 'src/app/scad-insights/core/interface/page.interface';
import { IfpPieChartComponent } from '../ifp-pie-chart/ifp-pie-chart.component';
import { IfpCustomXlFilterComponent } from '../ifp-custom-xl-filter/ifp-custom-xl-filter.component';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { UsageDashboardLogService } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard.constants';
import { IfpTagComponent } from '../../../../ifp-analytics/atom/ifp-tag/ifp-tag.component';
import { Security } from 'src/app/scad-insights/core/interface/indicator.interface';
import { InsightList, IfpChartInsightComponent } from '../../ifp-organism/ifp-chart-insight/ifp-chart-insight.component';

@Component({
  selector: 'app-ifp-insight-discovery-card',
  templateUrl: './ifp-insight-discovery-card.component.html',
  styleUrls: ['./ifp-insight-discovery-card.component.scss'],
  providers: [CustomNumberPipe, QuotRemove],
  imports: [
    NgClass,
    IfpDropdownComponent,
    AsyncPipe,
    NgIf,
    NgFor,
    IfpRatingComponent,
    TranslateModule,
    IfpAnalyticLineChartComponent,
    IfpMonthSelectorComponent,
    IfpCardLoaderComponent,
    CustomNumberPipe,
    IfpButtonComponent,
    IfpToggleButtonComponent,
    IfpTableComponent,
    IfpTabComponent,
    IfpModalComponent,
    IfpPdfTemplateComponent,
    IfpCommentsComponent,
    IfpCheckBoxComponent,
    IfpTncModalComponent,
    IfpTncModalComponent,
    IfpNotificationSettingsComponent,
    IfpIconTextComponent,
    QuotRemove,
    IfpCircularBarChartComponent,
    IfpPieChartComponent,
    IfpCustomXlFilterComponent,
    IfpTagComponent,
    IfpChartInsightComponent
  ]
})
export class IfpInsightDiscoveryCardComponent implements OnInit, OnChanges, AfterViewChecked, OnDestroy {
  @ViewChild('chartComponent') chartComponent!: IfpAnalyticLineChartComponent;
  @ViewChild('modalSla', { static: true }) modalSla!: IfpModalComponent;
  @ViewChild('subscribeNotification') subscribeNotificationModal!: IfpModalComponent;
  @ViewChild('tncModal') tncModal!: IfpModalComponent;
  @ViewChild('filterModal') filterModal!: IfpModalComponent;
  @ViewChild('fullScreen') fullScreen: ElementRef | undefined;

  @Input() title: string = '';
  @Input() filterPanel: any = [];
  @Input() periodFilter: any = { options: [] };
  @Input() chartValues: any = [];
  @Input() description!: string;
  @Input() isRender: boolean = true;
  @Input() chartDropDownData: any = [];
  @Input() appType!: string;
  @Input() tableLabels: any = [];
  @Input() allChartsData: any = [];
  @Input() isLoader: boolean = false;
  @Input() indicatorId: any;
  @Input() disclaimerDetails: string[] = [];
  @Output() chartChanged: EventEmitter<string> = new EventEmitter<string>();
  @Output() buttonClick = new EventEmitter();
  @Output() filterChanged = new EventEmitter();
  @Input() publishDate: any = '';
  @Input() hideSuffix: boolean = false;
  @Input() domain: string = '';
  @Input() isFilterApplied: boolean = false;
  @Input() source: any = '';
  @Input() isDashboardCard: boolean = false;
  @Input() hideChartTypes: string[] = [];
  @Input() subNodeId!: string;

  @Input()
  set getChartData(value: string) {
    this.chartData = value;
    if (this.chartData?.length > 0) {
      this.loaderChart = false;
    }
  }

  get getChartData(): string {
    return this.chartData;
  }

  @Input() cntType!: string;
  @Input() security!: Security;


  public filterKeys: any = [];
  public optionsLimit = 1000;
  public buttonClass = buttonClass;
  public loaderChart: undefined | boolean = true;
  public isVisble: boolean = true;
  public format!: string;
  toolbarAction: string = 'settings';
  public myAppsStatus = false;
  public chartData: any = [];
  public chartCatogory: any = [];
  public foracstTooltip: any = [];
  public isZoomoutDisabled: boolean = true;
  public tableData: any = [];
  public autoSwitch: boolean = false;
  public seriesMeta: any = [];
  public xAxisLabelType = chartConstants.xAxisDateTime;
  public subs = new SubSink();
  public isDatalabel: boolean = true;
  public isToolTip: boolean = true;
  public cloneChartValues: any = [];
  public isPreciseValue: boolean = true;
  public isRangeSelector: boolean = false;
  public change = false;
  public initialFilterData: any = [];
  public timePeriodOptions: any = [];
  public chartConstants = chartConstants;
  public seriesData: any = [];
  public chartType: string = 'line';
  public yaxisLabel!: string;
  public isYearly: boolean = false;
  // public notificationSelector !: boolean;
  public isNotificationEnabled!: boolean;
  public isEmailEnabled!: boolean;
  public emailSelector!: boolean;
  public insightData$!: any;
  public isDataLabel: boolean = true;
  public tncState!: boolean;
  public isTncModalOpen: boolean = false;
  public isMonthSelector: boolean = true;
  public isMonthDataChange = false;
  public analyticsClasses = analyticsClasses;
  public circularChartData: any = [];
  public customTimePeriodOptions: any = [];
  public pieSelectedPeriod: any;
  public pieChartSeriesData: any = [];
  public isCustomFilter: boolean = false;
  public pieFilterIndex: any;
  public isPeriodChange: boolean = false;
  public previousSelectedFilter: any = [];
  public isShowDropDown: boolean = false;
  public previousChartType!: string;
  public createdSeries: any = [];
  public selectedTimePeriods: any = [];
  public recentEnable: boolean = true;
  public manualRecentEnable: boolean = false;
  public recentToggleValue: boolean = false;
  public isChidClicked: boolean = false;
  public selectionReset: boolean = false;
  public isOpen: boolean = false;
  public isDashboardFilter: boolean = false;
  public isFilterSet: boolean = false;
  public previousFilterOpts: any;
  public activeFilterIndex: number = -1; // Track which filter is currently being interacted with
  public selectedPeriod: any = {
    id: 'Latest-Readings',
    label: 'Recent',
    unit: chartConstants.RECENT_LABEL,
    value: '',
    isSelected: true
  };

  public recentPeriod: any = {
    id: 'Latest-Readings',
    label: 'Recent',
    unit: chartConstants.RECENT_LABEL,
    value: '',
    isSelected: true
  };

  tabData: LabelData[] = [];

  public insightList!: InsightList;
  private sessionId!: string;
  private downloadSessionId!: string;

  constructor(
    private _filterService: FilterService,
    private _cdr: ChangeDetectorRef,
    private downloadService: DownloadService,
    private store: Store,
    public insightService: InsightsService,
    private _toasterService: ToasterService,
    private _commonApiService: CommonApiService,
    private customLetter: CustomLetter,
    private el: ElementRef,
    private datePipe: DatePipe,
    private _renderer: Renderer2,
    private chartToolService: ChartToolsService,
    private _dashboardService: DashboardService,
    private _themeService: ThemeService,
    private log: UsageDashboardLogService,
    private readonly analyticalService: AnalyticalService
  ) {
    // for dashboard settings change //

    this._dashboardService.settingsChanged.subscribe((resp) => {
      if (
        resp.type == chartConstants.ANALYTICAL_APPS && this.indicatorId === resp.id && !this._dashboardService.dashboardTools.includes(resp.tools)
      ) {
        this.isFilterApplied = false;
        this.isDashboardFilter = false;
        if (resp.tools == this._dashboardService.cardFilter) {
          this.isDashboardFilter = true;
          this.isFilterApplied = true;
          this.filterKeys = this._dashboardService.chartSettings[
            resp.type
          ]?.find((x: { id: string }) => x.id == resp.id).selectedFilter;
        }
        const selectedChartType = this._dashboardService.getChartType(
          chartConstants.ANALYTICAL_APPS,
          this.indicatorId
        );
        this.isFilterApplied = selectedChartType == 'pie' || selectedChartType == 'doughnut' ? true : false;
        this.changeChart(
          this._dashboardService.getChartType(
            chartConstants.ANALYTICAL_APPS,
            this.indicatorId
          )
        );
      }
    });
  }

  ngOnInit() {
    // this.subs.add(this.store.select(selectGetMappedData(this.indicatorId)).pipe(distinctUntilChanged((prev, curr) => prev === curr)).subscribe(data => {
    //   this.notificationSelector = data[this.indicatorId].isNotification;
    //   this.emailSelector = data[this.indicatorId].isEmail;
    //   this._cdr.detectChanges();
    // }));
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(
      this.sessionId,
      logType.indicator,
      this.log.currentTime,
      this.indicatorId
    );
    this.store.dispatch(getInsights({ nodeId: this.indicatorId }));

    this.subs.add(
      this.store
        .select(selectGetMyAppsMappedData(this.indicatorId))
        .pipe(distinctUntilChanged((prev, curr) => prev === curr))
        .subscribe((myapps) => {
          this.myAppsStatus = myapps;
          this._cdr.detectChanges();
        })
    );

    this.insightData$ = this.store.select(selectInsightResponse);
    // this.insightService.getChartInsightList(this.indicatorId);
    // this.subs.add(
    //   this.store.select(selectInsightResponse).subscribe((chartInsight: any) => {
    //     this.insightData = chartInsight;
    //     this._cdr.detectChanges();
    //   })
    // );

    this.subs.add(
      this._commonApiService
        .getDownloadTermsStatus(this.indicatorId)
        .subscribe((res: any) => {
          if (res) {
            if (res.status) {
              this.tncState = res.status;
            }
          }
        })
    );
  }

  setChartTypes() {
    this.tabData = [
      {
        iconClass: 'ifp-icon-graph-line',
        name: 'line',
        machineName: 'All',
        hide: chartTypeHide('line', this.hideChartTypes)
      },
      {
        iconClass: 'ifp-icon-bar-chart',
        name: 'column',
        machineName: 'All',
        hide: this.chartData.type == 'coi' ? true : chartTypeHide('column', this.hideChartTypes)
      },
      {
        iconClass: 'ifp-icon-horizontal-bar',
        name: 'bar',
        machineName: 'All',
        hide: this.chartData.type == 'coi' ? true : chartTypeHide('bar', this.hideChartTypes)
      },
      {
        iconClass: 'ifp-icon-radial-bar',
        name: 'circular',
        machineName: 'All',
        hide: this.chartData.type == 'coi' ? true : chartTypeHide('circular', this.hideChartTypes)
      },
      {
        iconClass: 'ifp-icon-pie-chart',
        name: 'pie',
        machineName: 'All',
        hide: this.chartData.type == 'coi' ? true : chartTypeHide('pie', this.hideChartTypes)
      },
      {
        iconClass: 'ifp-icon-table',
        name: 'table',
        machineName: 'All',
        hide: chartTypeHide('table', this.hideChartTypes)
      }
    ];
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.handleFilterPanelChange(changes);
    this.handleOtherChanges(changes);
  }

  private handleFilterPanelChange(changes: SimpleChanges): void {
    const filterPanelChange = changes['filterPanel'];
    if (!filterPanelChange) return;

    const { currentValue, previousValue } = filterPanelChange;
    
    if (this.shouldCreateFilterData(currentValue, previousValue)) {
      this.createFilterData();
      // Validate selected options after getting related filters
    }
    this.validateSelectedOptionsAgainstUpdatedFilters();
  }

  private shouldCreateFilterData(currentValue: any, previousValue: any): boolean {
    return !!(
      currentValue &&
      !Array.isArray(currentValue) &&
      currentValue.related_filters_viewName &&
      currentValue.properties?.length > 0 &&
      this.isPreviousValueEmpty(previousValue)
    );
  }

  private isPreviousValueEmpty(previousValue: any): boolean {
    return !previousValue || 
           Array.isArray(previousValue) || 
           !previousValue.properties?.length;
  }

  private handleOtherChanges(changes: SimpleChanges): void {
    this.selectionReset = false;
    
    if (this.shouldResetFilters()) {
      this.resetFilterState();
    }

    if (this.isDashboardCard) {
      this.setupDashboardCard();
    }

    this.processChartData();
  }

  private shouldResetFilters(): boolean {
    return !this.isFilterApplied && !this.isDashboardFilter && !this.isFilterSet;
  }

  private resetFilterState(): void {
    this.filterKeys = [];
    this.selectedTimePeriods = [];
    this.manualRecentEnable = false;
    this.isPreciseValue = false;
    this.chartType = 'line';
  }

  private setupDashboardCard(): void {
    this.chartType = this._dashboardService.getChartType(
      chartConstants.ANALYTICAL_APPS,
      this.indicatorId
    );
    
    const savedFilter = this._dashboardService.chartSettings[
      chartConstants.ANALYTICAL_APPS
    ]?.find((x: { id: string }) => x.id == this.indicatorId)?.selectedFilter;
    
    if (savedFilter) {
      this.filterKeys = savedFilter;
      this.isDashboardFilter = true;
    }
  }

  private processChartData(): void {
    this.loaderChart = true;
    
    if (this.filterPanel?.properties?.length > 0) {
      this.setupFilters();
      this.renderChart();
    }
    
    this.finalizeSetup();
  }

  private setupFilters(): void {
    if (!this.isDashboardFilter && !this.isFilterSet) {
      this.createInitialFilter(this.checkFilterKeysLabel());
    }
    
    this.seriesMeta = this.chartData.type !== 'coi' 
      ? this.chartData.seriesMeta?.[0] 
      : this.chartData?.seriesMeta;
    
    this.yaxisLabel = this.chartData.yAxisLabel;
    this.periodFilter.options = [this.recentPeriod];
    
    if (!this.isDashboardFilter && !this.isFilterSet) {
      this.setFilter();
    }
  }

  private renderChart(): void {
    const isPieChart = this.chartType === 'pie' || this.chartType === 'doughnut';
    const isCircularChart = this.chartType === 'circular';
    const isCoiChart = this.chartData.type === 'coi';
    
    if (isPieChart && !isCoiChart) {
      this.createPieChartData();
    } else if (isCircularChart && !isCoiChart) {
      this.createCircularBarChart();
    } else {
      this.filterData();
    }
  }

  private finalizeSetup(): void {
    if (this.chartValues?.length > 0) {
      this.cloneChartValues = this.chartValues;
    }
    
    this.setupNotificationSubscription();
    this.checkFilterHide();
    this.setChartTypes();
  }

  private setupNotificationSubscription(): void {
    this.subs.add(
      this.store
        .select(selectGetMappedData(this.indicatorId))
        .pipe(distinctUntilChanged())
        .subscribe((data) => {
          const indicatorData = data[this.indicatorId];
          this.isNotificationEnabled = indicatorData?.isNotification ?? false;
          this.isEmailEnabled = indicatorData?.isEmail ?? false;
          this._cdr.detectChanges();
        })
    );
  }

  checkFilterKeysLabel() {
    let isReset = false;
    if (this.filterKeys?.length > 0) {
      const filterKeyValues = this.filterKeys.map(
        (item: { path: any }) => item.path
      );
      const filterValues = this.filterPanel?.properties.map(
        (item: { path: any }) => item.path
      );
      isReset = JSON.stringify(filterKeyValues) != JSON.stringify(filterValues);
    }
    return isReset;
  }

  setChildFilterOption() {
    if (this.filterPanel?.properties?.length > 0) {
      this.filterPanel?.properties.forEach(
        (element: { path: any; child: any }, index: number) => {
          if (
            this.filterKeys[index].value.length == 1 && (checkAll(this.filterKeys[index].value[0]))
          ) {
            return;
          }
          if (element.child) {
            let selectedValue: any = this.filterKeys.find(
              (x: { path: any }) => x.path == element.path
            ).value;
            selectedValue = selectedValue.map((opt: string) => opt.toLowerCase()
            );
            const childIndex = this.filterPanel?.properties.findIndex(
              (y: { path: any }) => y.path == element.child
            );
            if (childIndex >= 0) {
              this.filterPanel.properties[childIndex].isDisabled = true;
              if (selectedValue?.length > 0) {
                const opt: any[] = [];
                const allValues: string[] = all;
                this.filterPanel?.properties[childIndex].optionsMap.forEach(
                  (parent: { PARENT_VALUE: string; VALUE: any; PARENT_VALUE_AR: string; VALUE_AR: string; }) => {
                    // let count = 0;
                    if (this._themeService.defaultLang === 'ar') {
                      if (
                        (parent.PARENT_VALUE_AR && selectedValue.includes(
                          parent.PARENT_VALUE_AR
                        )) || allValues.includes(parent.VALUE_AR)
                      ) {
                        // count = count + 1;
                        // if (count == 1) {
                        //   this.filterKeys[childIndex].value = [];
                        //   this.filterKeys[childIndex].value.push(parent.VALUE);
                        // }
                        opt.push(parent.VALUE_AR);
                      }
                    } else if (
                      (parent.PARENT_VALUE && selectedValue.includes(
                        parent.PARENT_VALUE.toLowerCase()
                      )) || allValues.includes(parent.VALUE)
                    ) {
                      // count = count + 1;
                      // if (count == 1) {
                      //   this.filterKeys[childIndex].value = [];
                      //   this.filterKeys[childIndex].value.push(parent.VALUE);
                      // }
                      opt.push(parent.VALUE);
                    }
                  }
                );
                // setTimeout(() => {
                this.filterPanel.properties[childIndex].options = opt;
                this.filterKeys[childIndex].value = [];
                this.filterKeys[childIndex].value.push(opt[0]);
                this.filterPanel.properties[childIndex].isDisabled = false;
                // }, 10);
              }
            }
          }
        }
      );
    }
  }

  createInitialFilter(filterReset: boolean = false) {
    if (filterReset) {
      this.selectedTimePeriods = [];
      this.filterKeys = [];
    }
    this.filterPanel?.properties.forEach((element: any, index: number) => {
      element.options = cloneDeep(element.options);
      // element.isDisabled = false;
      element.default = element.options.find((x: any) => x == element.default) ? element.default : element.options[0];
      const obj = {
        label: element.label,
        value: [element.default],
        index: index,
        path: element.path,
        staticFilter: element.staticFilter, 
        isCFD: this.filterPanel?.isCFD,
        default: element.default
      };
      if (!this.isFilterApplied || filterReset) {
        this.filterKeys.push(obj);
        // const optIndex = element.options.findIndex(
        //   (x: any) => x == element.default
        // );
        // element.options.unshift(...element.options.splice(optIndex, 1));
        if (obj.path == chartConstants.TIME_PERIOD) {
          this.timePeriodOptions = element.options;
          this.selectedTimePeriods.push(element.default);
          this.previousFilterOpts = [element.default];
        }

        if (element.subFilter) {
          const optionLevels = groupBy(element.optionsMap, (a: any) => a?.PARENT_LEVEL);
          Object.entries(optionLevels).forEach(([key, values]) => {
            if (key !== 'null') {
              const parent = element.optionsMap.find((x: { LEVEL: string }) => x.LEVEL == key)?.VALUE;
              element.subFilter = [...values];
              const parentIndex = element.options.findIndex((x: any) => x === parent);
              const removedElements: any = [];
              values?.forEach(val => {
                const valIndex = element.options.findIndex((x: any) => x === val.VALUE);
                if (valIndex > -1) {
                  removedElements.push(...element.options.splice(valIndex, 1));
                }
              });
              element.options.splice(parentIndex + 1, 0, ...removedElements);
            }
          });
        }
      }
    });
  }

  checkValue() {
    let dropValue = true;
    if (this.filterKeys.length > 0) {
      if (
        this.filterKeys.some(
          //here
          (x: { value: string | any[]; path: string; isHide: boolean }) => x.value.length > 1 && x.path != this.chartConstants.TIME_PERIOD && !x.isHide 
        )
      ) {
        dropValue = false;
      }
    }
    return dropValue;
  }

  checkIndex() {
    return this.filterKeys.findIndex(
      (x: { value: string | any[]; path: string }) => x.value.length > 1 && x.path != this.chartConstants.TIME_PERIOD
    );
  }


  // FilterPanel apply filter
  applyFilter(event: any, label: string, index: number) {
    this.isChidClicked = false;
    const value = event.value ?? event;

    // Set the active filter index to prevent dropdown from closing during API response
    if (this.filterPanel?.related_filters_viewName) {
      this.activeFilterIndex = index;
    }
    if (this.filterPanel.properties[index].child) {
      this.isChidClicked = true;
    }
    if (index == this.pieFilterIndex) {
      this.pieFilterIndex = undefined;
    }
    if (this.filterPanel.properties[index].path == chartConstants.TIME_PERIOD) {
      this.setSelectedTimePeriod(value);
    }
    this.filterKeys.find((x: { path: any }) => x.path == label).value = Array.isArray(value) ? value : [value];
    this.checkFilterHide();
    this.checkTimeOptionsAreSame();
    this.setFilter(value, index);
    if (this.isChidClicked) {
      this.setChildFilterOption();
    }
    this.createFilterData();
    this.isMonthSelector = false;
    this.isMonthDataChange = true;
    if (this.chartType == 'pie') {
      this.autoSwitch = true;
    }
  }



  /** ******************************************
  Evaluate multi dependancies on CFD
  ********************************************/
  evaluateCondition(currentFilterValue: any, condition: string, expectedValue: any): boolean {
    switch (condition) {
      case '==':
        return currentFilterValue == expectedValue;
      case '!=':
        return currentFilterValue != expectedValue;
      case '>':
        return currentFilterValue > expectedValue;
      case '<':
        return currentFilterValue < expectedValue;
      case '>=':
        return currentFilterValue >= expectedValue;
      case '<=':
        return currentFilterValue <= expectedValue;
      default:
        return false;
    }
  }

  checkFilterHide() {
    this.filterPanel.properties?.forEach((element: any, index: number) => {
      if (element.dependency) {
        const depandanceObjValue = this.filterKeys.find(
          (x: { path: any }) => x.path == element.dependency?.['filter'])?.value;
        if (checkAllArray(depandanceObjValue)) {
          element.isHide = true;
          this.filterKeys[index].isHide = true;
        } else {
          const operator = element.dependency.condition;
          const isDate: boolean = this.isValidDate(element.dependency.value);
          let isValid: boolean = false;
          if (operator == '==' || operator == '===') {
            isValid = depandanceObjValue?.every((x: string | number | Date) => (isDate ? new Date(x) : x) == (isDate ? new Date(element.dependency.value) : element.dependency.value));
          } else if (operator == '!=' || operator == '!==') {
            isValid = depandanceObjValue?.every((x: string | number | Date) => (isDate ? new Date(x) : x) != (isDate ? new Date(element.dependency.value) : element.dependency.value));
          } else if (operator == '>=') {
            isValid = depandanceObjValue?.every((x: string | number | Date) => (isDate ? new Date(x) : x) >= (isDate ? new Date(element.dependency.value) : element.dependency.value));
          } else if (operator == '<=') {
            isValid = depandanceObjValue?.every((x: string | number | Date) => (isDate ? new Date(x) : x) <= (isDate ? new Date(element.dependency.value) : element.dependency.value));
          }
          if (isValid) {
            element.isHide = false;
            this.filterKeys[index].isHide = false;
            return;
          }

          element.isHide = true;
          if (this.filterKeys[index]) {
            this.filterKeys[index].isHide = true;
          }
        }
      }

      /** ****************************
      multiple dependancy for CFD ***/
      if (element.multipleDependency) {
        let makeFilterVisible = true;
        for (let i = 0; i < element.multipleDependency.length; i++) {
          const currentFilterValue = this.filterKeys.find(
            (x: { path: any }) => x.path == element.multipleDependency[i].filter
          ).value;
          if (!
            this.evaluateCondition(
              currentFilterValue,
              element.multipleDependency[i].condition,
              element.multipleDependency[i].value)
          ) {
            makeFilterVisible = false;
            break;
          }
        }
        if (makeFilterVisible) {
          element.isHide = false;
          this.filterKeys[index].isHide = false;
          return;
        }
        element.isHide = true;
        this.filterKeys[index].isHide = true;
      }

    });
  }


  isValidDate(value: any): boolean {
    const date = new Date(value);
    return !isNaN(date.getTime());
  }

  filterData() {
    this.createdSeries = [];
    if (this.filterKeys?.length > 0 && this.chartData?.axisValues) {
      const index = this.filterKeys.findIndex(
        (x: { path: string }) => axisCategories.includes(x.path)
      );
      if (
        index != -1 && this.chartData?.axisValues[this.filterKeys[index].path][
        this.filterKeys[index].value[0]
        ]
      ) {
        this.yaxisLabel = this.chartData?.axisValues[this.filterKeys[index].path][
          this.filterKeys[index].value[0]
        ];
      }
    }
    this.seriesData = [];
    if (this.chartData.type != 'coi') {
      this.seriesMeta.data = this.seriesMeta.data.filter(
        (x: any) => x[this.chartData.dbColumn] == this.seriesMeta.dbIndicatorId
      );
      this.foracstTooltip = [];
      this.checkDefaultFilter();
      if (this.isDashboardCard) {
        this.selectedPeriod = this._dashboardService.getRecentValue(
          chartConstants.ANALYTICAL_APPS,
          this.indicatorId
        );
      }
      this.createdSeries = this._filterService.createSeries(
        this.seriesMeta,
        this.selectedPeriod,
        cloneDeep(this.filterKeys)
      );
      this.initialFilterData = this.createdSeries.data;
      this.tableData = this.setTableData();
      this.createAutoAxisData();
    } else {
      this.checkDefaultFilter();
      this.getForcastData();
    }

    this.isVisble = false;
    this.change = true;
    if (this.seriesMeta.data?.length > 0) {
      this.isLoader = false;
    }
  }

  createAutoAxisData() {
    /** Change xAxis label to category for CFD */
    this.xAxisLabelType =
      this.filterPanel?.isCFD ? chartConstants.xAxisCatogory : chartConstants.xAxisDateTime;
    const catogory: (string | undefined)[] = [];
    this.initialFilterData.forEach(
      (series: Record<string, any>[], index: number) => {
        const newChart = series?.map((element: Record<string, any>) => {

          const splitDate = element['OBS_DT'].split('-');
          /** convert xAxis to Quarter for CFD */
          if (this.filterPanel?.isCFD) {
            const isYearly = element['FREQUENCY'] && element['FREQUENCY']?.toUpperCase() != 'YEARLY';
            catogory.push(this.convertDateToQuarter(element['OBS_DT'], isYearly));
            return [
              element['VALUE']
            ];
          }
          else {
            return [
              Date.UTC(splitDate[0], splitDate[1] - 1, splitDate[2]),
              element['VALUE']
            ];
          }

        });
        const seriesData: any = {
          color: !this.isDashboardCard ? this._filterService.getColor(index) : this._dashboardService.getColors(
            chartConstants.ANALYTICAL_APPS,
            this.indicatorId,
            index
          ),
          data: newChart,
          name: this.createdSeries.legends[index],
          type: this.chartType
        };
        if (this.isDashboardCard) {
          (seriesData.spacing = this._dashboardService.gettingSpaceValues(
            chartConstants.ANALYTICAL_APPS,
            this.indicatorId
          )),
            (seriesData.xAxisPositions = this._dashboardService.getXaxisPositions(
              chartConstants.ANALYTICAL_APPS,
              this.indicatorId
            )),
            (seriesData.legendPositions = this._dashboardService.getLegendPositions(
              chartConstants.ANALYTICAL_APPS,
              this.indicatorId
            ));
          seriesData.isDatalabel = this._dashboardService.getChartSettings(
            chartConstants.ANALYTICAL_APPS,
            this.indicatorId,
            chartConstants.DATALABEL
          );
          seriesData.isPrecise = this._dashboardService.getChartSettings(
            chartConstants.ANALYTICAL_APPS,
            this.indicatorId,
            chartConstants.PRESICE_VALUE
          );
        }
        if (seriesData.data?.length > 0) {
          this.seriesData.push(seriesData);
        }
        this.loaderChart = false;

        series.forEach((element) => {
          this.foracstTooltip.push({
            MonthLabel: element['CHANGE_QQ'],
            YearLabel: element['CHANGE_YY']
          });
        });
      }
    );

    /** Push xAxis categories Array for CFD  */
    this.filterPanel?.isCFD ? this.chartCatogory = [...catogory] : '';

    if (this.isDashboardCard) {
      this._dashboardService.setSeriesLength(
        chartConstants.ANALYTICAL_APPS,
        this.indicatorId,
        this.seriesData
      );
    }
  }

  createQuarterData() {
    this.xAxisLabelType = chartConstants.xAxisCatogory;
    this.chartCatogory = [];
    this.initialFilterData.forEach(
      (series: Record<string, any>[], index: number) => {
        const newChart: any[] = [];
        const catogory: (string | undefined)[] = [];
        series.forEach((element) => {
          newChart.push(element['VALUE']);
          catogory.push(this.convertDateToQuarter(element['OBS_DT']));
        });
        const seriesData = {
          color: !this.isDashboardCard ? this._filterService.getColor(index) : this._dashboardService.getColors(
            chartConstants.ANALYTICAL_APPS,
            this.indicatorId,
            index
          ),
          data: newChart,
          name: this.customLetter.transform(
            this.filterKeys.find((x: { checkbox: boolean }) => x.checkbox)
              ?.value[index] ? this.filterKeys.find((x: { checkbox: boolean }) => x.checkbox)
                ?.value[index] : ''
          ),
          type: this.chartType,
          marker: {
            enabled: true
          }
        };
        if (seriesData.data?.length > 0) {
          this.seriesData.push(seriesData);
          this.chartCatogory.push(...catogory);
        }
        series.forEach((element) => {
          this.foracstTooltip.push({
            MonthLabel: element['CHANGE_QQ'],
            YearLabel: element['CHANGE_YY']
          });
        });
      }
    );
  }

  convertDateToQuarter(date: any, EnableQ = false) {
    let label: string = '';
    if (
      new Date(date).getMonth() + 1 <= 3 && new Date(date).getMonth() + 1 > 0
    ) {
      label = `Q1<br>${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if (
      new Date(date).getMonth() + 1 > 3 && new Date(date).getMonth() + 1 <= 6
    ) {
      label = `Q2<br>${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if (
      new Date(date).getMonth() + 1 > 6 && new Date(date).getMonth() + 1 <= 9
    ) {
      label = `Q3<br>${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if (
      new Date(date).getMonth() + 1 > 9 && new Date(date).getMonth() + 1 <= 12
    ) {
      label = EnableQ ? `Q4<br>${this.datePipe.transform(new Date(date), 'yyyy')}` : `${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if (new Date(date).getMonth() + 1 == 0) {
      label = `Q1<br>${this.datePipe.transform(new Date(date), 'yyyy')}`;
    }
    return label;
  }


  convertDateToQuarterWithoutQLabel(date: any) {
    let label: string = '';
    if (
      new Date(date).getMonth() + 1 <= 3 && new Date(date).getMonth() + 1 > 0
    ) {
      label = `${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if (
      new Date(date).getMonth() + 1 > 3 && new Date(date).getMonth() + 1 <= 6
    ) {
      label = `${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if (
      new Date(date).getMonth() + 1 > 6 && new Date(date).getMonth() + 1 <= 9
    ) {
      label = `${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if (
      new Date(date).getMonth() + 1 > 9 && new Date(date).getMonth() + 1 <= 12
    ) {
      label = `${this.datePipe.transform(new Date(date), 'yyyy')}`;
    } else if (new Date(date).getMonth() + 1 == 0) {
      label = `${this.datePipe.transform(new Date(date), 'yyyy')}`;
    }
    return label;
  }

  setTableData() {
    let dynamicObject: any = {};
    let indexData: any = [];
    this.tableData = [];
    if (this.initialFilterData?.length > 0) {
      this.initialFilterData.forEach((element: any) => {
        dynamicObject = {};
        indexData = [];
        element.forEach((value: Record<string, any>) => {
          if (this.tableLabels?.length > 0) {
            this.tableLabels.forEach((cell: { label: any; path: any }) => {
              dynamicObject[cell.label] = value[cell.path];
            });
          }
          indexData.push(cloneDeep(dynamicObject));
        });
        this.tableData.push(indexData);
      });
    }
    return this.tableData;
  }

  applyPeriodFilter(event: any) {
    this.selectedPeriod = event;
    if (
      this.chartType == 'line' || this.chartType == 'column' || this.chartType == 'bar' || this.chartType == 'table'
    ) {
      this.filterData();
    } else if (this.chartType == 'circular') {
      this.createCircularBarChart();
    } else if (this.chartType == 'pie') {
      this.createPieChartData();
    }
  }

  ngAfterViewChecked(): void {
    if (this.change) {
      this.isVisble = true;
      this.isShowDropDown = false;
      this._cdr.detectChanges();
      this.change = false;
      this.changeDataLabel(false);
      this.changeTooltip(false);
      this.changePreciseLabel(false);
    }

    if (this.isMonthDataChange) {
      this.isMonthSelector = true;
      this.isMonthDataChange = false;
    }
  }

  zoom(type: string) {
    if (this.chartComponent) {
      this.chartComponent.zoom(type);
    }
  }

  fullscreen() {
    this.chartToolService.fullscreen(
      this.fullScreen,
      this.chartComponent,
      this.chartType
    );
  }

  print() {
    window.print();
  }

  download(type: string) {
    if (type != 'XL') {
      this._renderer.addClass(document.body, 'ifp-light-theme');
      this.modalSla.createElement();
    }
    if (type == 'pdf') {
      setTimeout(() => {
        const offsetHeight = document.getElementById('downloadPrint')?.offsetHeight;
        const offsetWidth = document.getElementById('downloadPrint')?.offsetWidth;
        this.downloadService
          .downloadPdf(
            document.getElementById('downloadPrint'),
            this.title,
            offsetHeight,
            offsetWidth,
            this.chartType
          )
          .then((_resp) => {
            this._renderer.removeClass(document.body, 'ifp-light-theme');
            this.modalSla.removeModal();
          });
      }, 1000);
    }
    if (type == 'XL') {
      const downloadData: any = [];
      if (this.tableData?.length > 0) {
        for (const printData of this.tableData) {
          downloadData.push(...printData);
        }
      }

      this.downloadService.exportToExcel(
        downloadData,
        this.title.replace('/', ' ')
      );
    }

    if (type == 'png') {
      setTimeout(() => {
        this.downloadService.downloadElementAsPNG(
          document.getElementById('downloadPrint'),
          this.title
        );
        this._renderer.removeClass(document.body, 'ifp-light-theme');
        this.modalSla.removeModal();
      }, 1000);
    }
    // if (type == 'ppt') {
    //   setTimeout(() => {
    //     this.downloadService.downloadAsPPT(document.getElementById('downloadPrint'), this.title);
    //     this.modalSla.removeModal();
    //   }, 1000);
    // }

    if (this.downloadSessionId) {
      this.log.logEnds(this.downloadSessionId, this.log.currentTime );
    }
    this.downloadSessionId = this.log.createUUid;
    this.log.logStart(this.downloadSessionId, logType.download, this.log.currentTime, +this.indicatorId, this.title, type);
  }

  chartChange(event: any) {
    console.log('777777777', this.filterKeys);
    this.isFilterSet = false;
    this.chartChanged.emit(event);
    console.log('666666666', this.filterKeys);
    // this.filterKeys = [];
    // setTimeout(() => {
      this.createInitialFilter(true);
      console.log('55555', this.filterKeys);
      this.createFilterData();
      console.log('4444', this.filterKeys);
    this.selectionReset = true;
    this.chartType = 'line';
    this.selectedPeriod = this.recentPeriod;


    // }, 300);
  }

  formatTitle(data: any) {
    return this._filterService.formatingTitle(data);
  }

  addData(event: string) {
    this.buttonClick.emit({
      event: event,
      data: ''
    });
  }

  addDataMyApps(status: boolean) {
    if (status) {
      this.store.dispatch(
        unsubscribeMyAppsUpdate({
          id: this.indicatorId,
          title: this.title,
          contentType: 'analytical-apps'
        })
      );
    } else {
      this.store.dispatch(
        setMyAppsUpdate({
          id: this.indicatorId,
          title: this.title,
          contentType: 'analytical-apps'
        })
      );
    }
  }

  valueRangeUpdated(event: any) {
    if (event && event.flag == 'completed') {
      const data = {
        title: 'Selected Range Value',
        value: event.value
      };
      this.chartValues = [];
      this.chartValues.push(data);
    }
    if (event && event.flag == 'inprogress') {
      this.chartValues = [];
    }
  }

  getRangeSelector(event: any) {
    this.chartValues = [];
    this.isRangeSelector = event;
    if (!this.isRangeSelector) {
      this.chartValues = cloneDeep(this.cloneChartValues);
    }
  }

  changeChart(type: string) {
    this.chartType = type;
    if (type != 'pie' && type != 'doughnut') {
      if (
        !this.autoSwitch && (this.previousChartType == 'pie' || this.previousChartType == 'doughnut')
      ) {
        this.seriesMeta.data = [];
        this.isLoader = true;
        this.filterKeys = this.previousSelectedFilter;
        this.isShowDropDown = true;
        this.createFilterData();
      }
      this.previousSelectedFilter = [];
      this.isVisble = false;
      this.change = true;
    }
    if (
      type == 'line' || type == 'column' || type == 'bar' || type == 'table'
    ) {
      this.filterData();
    } else if (type == 'circular') {
      this.createCircularBarChart();
    } else if (this.chartType == 'pie' || this.chartType == 'doughnut') {
      if (this.manualRecentEnable) {
        this.recentEnable = this.recentToggleValue;
      }
      this.isLoader = true;
      this.previousSelectedFilter = cloneDeep(this.filterKeys);
      const filterWithoutTimePeriod = this.filterKeys.filter(
        (x: { path: string }) => x.path != chartConstants.TIME_PERIOD
      );
      if (filterWithoutTimePeriod?.length > 0) {
        if (
          filterWithoutTimePeriod.every(
            (x: { path: string; value: string | any[] }) => x.value?.length == 1
          )
        ) {
          this.pieFilterIndex = this.filterPanel?.properties[0].path == chartConstants.TIME_PERIOD ? 1 : 0;
          this.filterKeys[this.pieFilterIndex].value = this.filterPanel?.properties[this.pieFilterIndex].options;

        }
        this.createFilterData();
      }
    }
    this.getRangeSelector(false);
    this.previousChartType = type;
    if (type == 'pie' || type == 'doughnut') {
      this.deactivateRecent(false);
    }
  }

  disableZoomout(event: any) {
    this.isZoomoutDisabled = event;
    this._cdr.detectChanges();
  }

  addInsight(value: string) {
    const data = {
      nodeId: this.indicatorId,
      nodeTitle: this.title,
      nodeLink: this.chartData.NODE_LINK,
      insight: value
    };
    this.subs.add(
      this.insightService.addInsights(data).subscribe(() => {
        this.store.dispatch(getInsights({ nodeId: this.indicatorId }));
      })
    );
  }

  updateInsight(event: { item: any; newVal: string }) {
    const data = {
      nodeId: this.indicatorId,
      nodeTitle: this.title,
      nodeLink: this.chartData.NODE_LINK,
      insight: event.newVal
    };
    this.subs.add(
      this.insightService
        .editInsights(data, event.item.ID)
        .subscribe((res: any) => {
          if (res.status === 'success') {
            this._toasterService.success('Insight Updated successfully');
            this.store.dispatch(getInsights({ nodeId: this.indicatorId }));
          } else {
            this._toasterService.error('Failed to update insight');
          }
          this._cdr.detectChanges();
        })
    );
  }

  deleteInsight(insightId: string) {
    this.subs.add(
      this.insightService.deleteInsights(insightId).subscribe((res: any) => {
        if (res.status === 'success') {
          this._toasterService.success('Insight deleted successfully');
          this.store.dispatch(getInsights({ nodeId: this.indicatorId }));
        } else {
          this._toasterService.error('Failed to delete insight');
        }
        this._cdr.detectChanges();
      })
    );
  }

  isTncChecked(event: boolean) {
    this.tncState = event;
  }

  showTnC() {
    if (this.isTncModalOpen) {
      return;
    }
    this.tncModal.createElement();
    this.isTncModalOpen = true;
  }

  termsResponse(response: boolean) {
    if (response) {
      this.subs.add(
        this._commonApiService
          .setDownloadTermsStatus(this.indicatorId)
          .subscribe((res: any) => {
            if (res) {
              this.tncState = true;
            }
          })
      );
    }
    // else {
    //   this.tncState = false;
    // }
    if (this.isTncModalOpen) {
      this.tncModal.removeModal();
      this.isTncModalOpen = false;
    }
  }

  getFormat(value: string) {
    let format = '1.1-1';
    if (value) {
      format = value.split('_')[1];
    }
    return format;
  }

  changeDataLabel(event: any) {
    if (event) {
      this.isDatalabel = event?.target?.checked;
    }
    this.chartComponent?.toggleDataLabel(this.isDatalabel);
  }

  changeTooltip(event: any) {
    if (event) {
      this.isToolTip = event?.target?.checked;
    }
    this.chartComponent?.toggleToolTip(this.isToolTip);
  }

  changePreciseLabel(event: any) {
    if (event) {
      this.isPreciseValue = event?.target?.checked;
    }
    this.chartComponent?.showFullValue(this.isPreciseValue);
  }

  createCircularBarChart() {
    if (this.filterKeys?.length > 0 && this.chartData?.axisValues) {
      const index = this.filterKeys.findIndex(
        (x: { path: string }) => axisCategories.includes(x.path)
      );
      if (
        index != -1 && this.chartData?.axisValues[this.filterKeys[index].path][
        this.filterKeys[index].value[0]
        ]
      ) {
        this.yaxisLabel = this.chartData?.axisValues[this.filterKeys[index].path][
          this.filterKeys[index].value[0]
        ];
      }
    }
    this.checkDefaultFilter();
    this.createdSeries = this._filterService.createSeries(
      this.seriesMeta,
      this.selectedPeriod,
      cloneDeep(this.filterKeys)
    );
    this.initialFilterData = this.createdSeries.data;
    this.tableData = this.setTableData();
    this.circularChartData = [];
    this.chartCatogory = [];
    if (this.initialFilterData?.length > 0) {
      const seriesData: any = {
        name: '',
        type: 'column',
        data: [],
        color: ''
      };
      this.initialFilterData.forEach((element: any[], index: number) => {
        seriesData.data = [];
        seriesData.name = '';
        if (this.isDashboardCard) {
          seriesData.spacing = this._dashboardService.gettingSpaceValues(
            chartConstants.ANALYTICAL_APPS,
            this.indicatorId
          );
          seriesData.xAxisPositions = this._dashboardService.getXaxisPositions(
            chartConstants.ANALYTICAL_APPS,
            this.indicatorId
          );
          seriesData.legendPositions = this._dashboardService.getLegendPositions(
            chartConstants.ANALYTICAL_APPS,
            this.indicatorId
          );
          seriesData.isDatalabel = this._dashboardService.getChartSettings(
            chartConstants.ANALYTICAL_APPS,
            this.indicatorId,
            chartConstants.DATALABEL
          );
          seriesData.isPrecise = this._dashboardService.getChartSettings(
            chartConstants.ANALYTICAL_APPS,
            this.indicatorId,
            chartConstants.PRESICE_VALUE
          );
        }
        seriesData.name = this.customLetter.transform(
          this.filterKeys.find((x: { checkbox: boolean }) => x.checkbox)?.value[
            index
          ] ? this.filterKeys.find((x: { checkbox: boolean }) => x.checkbox)
            ?.value[index] : ''
        );
        if (element?.length > 0) {
          element.forEach((series) => {
            this.chartCatogory.push(series.OBS_DT);
            seriesData.data.push(series.VALUE);
            seriesData.color = !this.isDashboardCard ? this._filterService.getColor(index) : this._dashboardService.getColors(
              chartConstants.ANALYTICAL_APPS,
              this.indicatorId,
              index
            );
          });
          this.circularChartData.push(cloneDeep(seriesData));
        }
      });
      if (this.isDashboardCard) {
        this._dashboardService.setSeriesLength(
          chartConstants.ANALYTICAL_APPS,
          this.indicatorId,
          this.circularChartData
        );
      }
      if (this.seriesMeta.data?.length > 0) {
        setTimeout(() => {
          this.isLoader = false;
        }, 300);
      }
      this.isVisble = false;
      this.change = true;
    }
  }

  createPieChartData() {
    this.selectedPeriod = {
      id: 'All',
      isSelected: false,
      label: 'ALL',
      unit: null,
      value: null
    };

    if (this.filterKeys?.length > 0 && this.chartData?.axisValues) {
      const index = this.filterKeys.findIndex(
        (x: { path: string }) => axisCategories.includes(x.path)
      );
      if (
        index != -1 && this.chartData?.axisValues[this.filterKeys[index].path][
        this.filterKeys[index].value[0]
        ]
      ) {
        this.yaxisLabel = this.chartData?.axisValues[this.filterKeys[index].path][
          this.filterKeys[index].value[0]
        ];
      }
    }
    this.selectedPeriod.isSelected = false;
    this.createdSeries = this._filterService.createSeries(
      this.seriesMeta,
      this.selectedPeriod,
      this.filterKeys
    );
    this.initialFilterData = this.createdSeries.data;
    this.tableData = this.setTableData();
    this.circularChartData = [];
    this.chartCatogory = [];
    if (!this.isPeriodChange) {
      this.isCustomFilter = true;
    }
    this.customTimePeriodOptions = [];
    let filterDataIndex: number = 0;
    for (let index = 0; index < this.initialFilterData.length; index++) {
      if (this.initialFilterData[index]?.length) {
        filterDataIndex = index;
        break;
      }
    }
    if (this.initialFilterData[filterDataIndex].length > 0) {
      this.initialFilterData[filterDataIndex].forEach(
        (element: { YEAR: any; OBS_DT: any }) => {
          this.customTimePeriodOptions.push(element.OBS_DT);
        }
      );
      this.customTimePeriodOptions = [...new Set(this.customTimePeriodOptions)];
      this.customTimePeriodOptions = this.customTimePeriodOptions.reverse();
      if (
        !this.pieSelectedPeriod || !this.customTimePeriodOptions.find(
          (x: any) => x == this.pieSelectedPeriod
        )
      ) {
        this.pieSelectedPeriod = this.customTimePeriodOptions[0];
      }
      if (
        this.customTimePeriodOptions.find(
          (x: any) => x == this.pieSelectedPeriod
        )
      ) {
        const index = this.customTimePeriodOptions.findIndex(
          (x: any) => x == this.pieSelectedPeriod
        );
        this.customTimePeriodOptions.unshift(
          this.customTimePeriodOptions.splice(index, 1)[0]
        );
      }
      this.isLoader = true;
      if (!this.isPeriodChange) {
        setTimeout(() => {
          this.isCustomFilter = false;
          this.isLoader = false;
          this._cdr.detectChanges();
        }, 300);
      }
      this.isPeriodChange = false;

      setTimeout(() => {
        this.isLoader = false;
        this._cdr.detectChanges();
      }, 300);
    }
    this.createPieSeries();
  }

  createPieSeries() {
    this.pieChartSeriesData = [];
    const chartSeries: PieChartSeries = {
      type: 'pie',
      data: []
    };
    this.pieChartSeriesData.type = 'pie';
    this.initialFilterData.forEach((val: any, index: number) => {
      const data = {
        name: this.filterKeys.find((x: { checkbox: any }) => x.checkbox) ? this.filterKeys.find((x: { checkbox: any }) => x.checkbox).value[
          index
        ] : this.title,
        y: val.find((x: any) => x['OBS_DT'] == this.pieSelectedPeriod)?.VALUE,
        color: !this.isDashboardCard ? this._filterService.getColor(index) : this._dashboardService.getColors(
          chartConstants.ANALYTICAL_APPS,
          this.indicatorId,
          index
        )
      };
      const isNotContained = !totalValues.some(keyword => data.name.includes(keyword));
      if (data.y && isNotContained) {
        chartSeries.data.push(data);
        if (this.isDashboardCard) {
          chartSeries.spacing = this._dashboardService.gettingSpaceValues(
            chartConstants.ANALYTICAL_APPS,
            this.indicatorId
          );
          chartSeries.xAxisPositions = this._dashboardService.getXaxisPositions(
            chartConstants.ANALYTICAL_APPS,
            this.indicatorId
          );
          chartSeries.legendPositions = this._dashboardService.getLegendPositions(
            chartConstants.ANALYTICAL_APPS,
            this.indicatorId
          );
          chartSeries.isDatalabel = this._dashboardService.getChartSettings(
            chartConstants.ANALYTICAL_APPS,
            this.indicatorId,
            chartConstants.DATALABEL
          );
          chartSeries.isPrecise = this._dashboardService.getChartSettings(
            chartConstants.ANALYTICAL_APPS,
            this.indicatorId,
            chartConstants.PRESICE_VALUE
          );
        }
      }
    });
    if (chartSeries.data?.length > 0) {
      this.pieChartSeriesData.push(chartSeries);
      if (this.isDashboardCard) {
        this._dashboardService.setSeriesLength(
          chartConstants.ANALYTICAL_APPS,
          this.indicatorId,
          this.pieChartSeriesData
        );
      }
    }
    this.isVisble = false;
    this.change = true;
  }

  changePeriod(event: any) {
    this.pieSelectedPeriod = event;
    this.isPeriodChange = true;
    this.createPieChartData();
  }

  deactivateRecent(selected: boolean = false) {
    this.recentEnable = selected;
    this.periodFilter.options.find(
      (x: { label: string }) => x.label == chartConstants.RECENT_LABEL
    ).isSelected = selected;
  }

  checkTimeOptionsAreSame() {
    const timerOpts = this.timePeriodOptions.filter((x: string) => !all.includes(x));
    const timeFilterIndex = this.filterKeys?.findIndex((y: { path: string }) => y?.path == chartConstants.TIME_PERIOD);
    if (timeFilterIndex >= 0) {
      const selectedOptIndex = this.filterKeys[timeFilterIndex].value.findIndex((x: string) => all.includes(x));
      if (selectedOptIndex >= 0 && !this.checkFilter(this.filterKeys[timeFilterIndex].value)) {
        this.filterKeys[timeFilterIndex].value.splice(selectedOptIndex, 1);
      }
      if (timerOpts.every((element: any) => this.filterKeys[timeFilterIndex].value.includes(element))) {
        const allIndex = this.previousFilterOpts.findIndex((x: string) => all.includes(x));
        if (allIndex >= 0) {
          this.filterKeys[timeFilterIndex].value = [this.filterKeys[timeFilterIndex].default]
        } else {
          this.filterKeys[timeFilterIndex].value.push('All');
        }

      }
      this.previousFilterOpts = cloneDeep(this.filterKeys[timeFilterIndex].value);
    }
  }

  checkFilter(value: any) {
    let isAll: boolean = false;
    if (this.previousFilterOpts?.length) {
      if (value.some((x: string) => all.includes(x)) && !this.previousFilterOpts.some((x: string) => all.includes(x))) {
        isAll = true;
      }
    }
    return isAll;
  }



  setFilter(event?: any, index: number = -1) {
    this.isFilterSet = true;
    this.filterKeys.forEach((element: any, index: number) => {
      if (element?.value?.length === 0) {
        element.value = [element?.default];
        event = element?.default;
        this._cdr.detectChanges();
      }
    });
    const timeIndex = this.filterKeys?.findIndex((y: { path: string }) => y?.path == chartConstants.TIME_PERIOD);
    // index = -1 ? timeIndex : -1;
    event = (!event || event?.length <= 0) && index !== -1 ? this.filterKeys?.[index]?.default : event;
    if (this.filterKeys?.length > 0) {
      if (this.filterPanel?.properties?.[index]?.path == chartConstants.TIME_PERIOD && index !== -1) {
        if (checkAll(event) || (this.filterKeys?.[index]?.value?.length === 1 && this.filterKeys?.[index]?.value?.[0] === this.filterKeys?.[index]?.default)) {
          if (!checkAllArray(this.filterKeys?.[index]?.value) && !checkAll(this.filterKeys?.[index]?.default)) {
            this.filterKeys[index].value = [this.filterKeys[index].default];
          } else {
            this.filterKeys[index].value = [...this.timePeriodOptions];
          }
          this._cdr.detectChanges();
        }
        if (this.filterKeys[timeIndex].value.length !== this.filterPanel.properties[timeIndex].options.length) {
          all.forEach(element => {
            if (this.filterKeys[timeIndex].value.includes(element)) {
              this.filterKeys[timeIndex].value.splice(this.filterKeys[timeIndex].value.indexOf(element), 1);
            }
          });
        }
      }
      if (
        this.filterKeys.some(
          (z: { value: string | any[] }) => z.value.length > 1
        )
      ) {
        this.filterKeys.map((x: any) => {
          x.checkbox = x.value.length > 1 && x.path != this.chartConstants.TIME_PERIOD && !x.isHide ? true : false;
        });
        if (this.filterKeys.every((y: any) => !y.checkbox)) {
          this.filterKeys[
            this.filterKeys[0].path != chartConstants.TIME_PERIOD ? 0 : 1
          ].checkbox = true;
        }
      }
      if (
        !this.filterKeys.some(
          (z: { value: string | any[] }) => z.value.length > 1
        )
      ) {
        this.filterKeys.forEach((element: any, index: number) => {
          const selectIndex = this.filterKeys[0].path == this.chartConstants.TIME_PERIOD ? 1 : 0;
         element.checkbox = index === selectIndex;
        });
      }
      if (this.filterKeys[timeIndex]?.value.find((x: string) => all.includes(x))) {
        this.filterKeys[timeIndex].value = [...this.timePeriodOptions]
      }
    }
  }

  createFilterData() {
    setTimeout(() => {
      if (this.filterKeys?.length > 0) {
        let filterBy: any = {};
        console.log('-----------------');
        console.log('filterKeys', this.filterKeys);
        this.filterKeys.forEach(
          (element: {
            value: any;
            path: string | number;
            isHide: boolean;
            staticFilter: boolean;
          }) => {
            if (!element.staticFilter) {
              if (element.isHide) {
                element.value = [];
                element.value = [
                  this._themeService.defaultLang == 'en' ? 'All' : (this.filterPanel?.isCFD ? 'الكل' : 'المجموع')
                ];
              }


              /** ******************************************************
              NONE as default value in case we hide the following filters
              (Age - Salary - GENDER  - DAYS_DELAY) for CFD app only
              ********************************************************/
              if (
                this.filterPanel?.isCFD && element.path == chartConstants.AGE_PATH && element.value?.length == 1
              ) {
                if (element.isHide) {
                  element.value = [
                    this._themeService.defaultLang == 'en' ? 'NONE' : 'لا يوجد'
                  ];
                } else if (element.value[0] == 'NONE') {
                  element.value = [
                    this._themeService.defaultLang == 'en' ? 'All' : 'المجموع'
                  ];
                }
              }
              if (
                this.filterPanel?.isCFD && element.path == chartConstants.SALARY_PATH && element.value?.length == 1
              ) {
                if (element.isHide) {
                  element.value = [
                    this._themeService.defaultLang == 'en' ? 'NONE' : 'لا يوجد'
                  ];
                } else if (element.value[0] == 'NONE') {
                  element.value = [
                    this._themeService.defaultLang == 'en' ? 'All' : 'المجموع'
                  ];
                }
              }
              if (
                this.filterPanel?.isCFD && element.path == chartConstants.DAYS_DELAY_PATH && element.value?.length == 1
              ) {
                if (element.isHide) {
                  element.value = [
                    this._themeService.defaultLang == 'en' ? 'NONE' : 'لا يوجد'
                  ];
                } else if (element.value[0] == 'NONE') {
                  element.value = [
                    this._themeService.defaultLang == 'en' ? 'All' : 'المجموع'
                  ];
                }
              }
              if (
                this.filterPanel?.isCFD && element.path == chartConstants.GENDER_PATH && element.value?.length == 1
              ) {
                if (element.isHide) {
                  element.value = [
                    this._themeService.defaultLang == 'en' ? 'NONE' : 'لا يوجد'
                  ];
                } else if (element.value[0] == 'NONE') {
                  element.value = [
                    this._themeService.defaultLang == 'en' ? 'All' : 'المجموع'
                  ];
                }
              }


              if (
                element.path == chartConstants.TIME_PERIOD && element.value?.length == 1 && (checkAll(element.value[0]))
              ) {
                element.value = this.timePeriodOptions;
              }
              filterBy[
                element.path == chartConstants.TIME_PERIOD ? chartConstants.YEAR_PERIOD : element.path
              ] = element.value.map((x: string) => x) && element.value?.length == 1 ? element.value.toString() : element.path == chartConstants.TIME_PERIOD ? this.getOptions(element.value) : element.value.map((x: string) => x);
            }
          }
        );
        console.log(')))))))))))))))))))))))))))))))))');
        console.log('filterKeys', this.filterKeys);
        console.log('filterBy', filterBy);
        if (this.filterPanel?.related_filters_viewName) {
          const relatedFilterColoumns = this.filterPanel?.properties?.filter((obj: any) => obj?.optionsMap[0].RELATED === 1)
            .map((obj: any) => obj.path)

          const relatedFilters = {
            filterBy: filterBy, related_filters_viewName: this.filterPanel?.related_filters_viewName, related_filters_columns: [...relatedFilterColoumns], selectedOptions: {
              ...this.createSelectedOptionsDynamic(filterBy, this.filterPanel?.properties)
            }
          }
          this.filterChanged.emit(relatedFilters);

          // Clear active filter index after API response is processed
          setTimeout(() => {
            this.activeFilterIndex = -1;
          }, 1000);
        } else {
        this.filterChanged.emit(filterBy);
      }
    }
    }, 300);
  }

  // Method to clear active filter index (can be called when dropdown closes)
  clearActiveFilter() {
    this.activeFilterIndex = -1;
  }

  // Listen for clicks outside the component to clear active filter
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    // Clear active filter when clicking outside the component
    if (this.activeFilterIndex !== -1 && !this.el.nativeElement.contains(event.target)) {
      this.activeFilterIndex = -1;
    }
  }

  createSelectedOptionsDynamic(filterByObj: any, filterPanelProperties: any) {
    const selectedOptions: { [key: string]: any[] } = {};

    // Exclude non-filter properties
    const excludeKeys = ['related_filters_viewName', 'related_filters_columns'];

    filterPanelProperties.forEach((property: any) => {
      const columnName = property.path;

      // Check if this column exists in filterBy (excluding special keys)
      if (filterByObj.hasOwnProperty(columnName) && !excludeKeys.includes(columnName)) {
        let filterValues: any[] = [];

        if (Array.isArray(filterByObj[columnName])) {
          filterValues = filterByObj[columnName];
        } else if (filterByObj[columnName]) {
          filterValues = [filterByObj[columnName]];
        }

        // Find matching options with RELATED = 1
        if (property.optionsMap && filterValues.length > 0) {
          const matchingValues = property.optionsMap
            .filter((option: any) =>

              filterValues.includes(option.VALUE)
            )
            .map((option: any) => option.VALUE);

          // Only add to selectedOptions if we found matches
          if (matchingValues.length > 0) {
            selectedOptions[columnName] = matchingValues;
          }
        }
      }
    });
    return { selectedOptions };
  }

  getOptions(options: any) {
    const opt = cloneDeep(options);
    const filterValueLength = opt.findIndex((dataValue: string) => {
      return all.includes(dataValue);
    });
    if (filterValueLength !== -1) {
      opt.splice(filterValueLength, 1);
    }
    // if (opt.includes('All')) {
    //   opt.splice(opt.indexOf('All'), 1);
    // }
    return opt;
  }

  checkDefaultFilter() {
    let isDefault = true;
    this.autoSwitch = false;
    this.filterKeys.forEach((element: any, index: number) => {
      if (
        (element.value?.length <= 1 && (element.path != chartConstants.TIME_PERIOD ? element.value.toString() : this.selectedTimePeriods.toString()) != this.filterPanel.properties[index]?.default) || this.selectedTimePeriods?.length > 1 || (element.path != chartConstants.TIME_PERIOD && element.value?.length > 1)
      ) {
        isDefault = false;
        this.autoSwitch = true;
      }
    });
    if (this.manualRecentEnable) {
      isDefault = this.recentToggleValue;
    }
    this.selectedPeriod = {
      id: !isDefault ? 'All' : 'Latest-Readings',
      isSelected: !isDefault ? false : true,
      label: !isDefault ? 'ALL' : 'Recent',
      unit: !isDefault ? null : chartConstants.RECENT_LABEL,
      value: ''
    };
    this.deactivateRecent(isDefault);
  }

  validateSelectedOptionsAgainstUpdatedFilters() {
  if (!this.filterKeys?.length || !this.filterPanel?.properties?.length) return;

  this.filterKeys.forEach((filterKey: any, index: number) => {
    // Skip validation for the currently active filter to prevent dropdown from closing
    if (this.activeFilterIndex === index && this.filterPanel?.related_filters_viewName) {
      return;
    }

    const correspondingProperty = this.filterPanel.properties[index];
    
    if (correspondingProperty?.options) {
      // Get current available options
      const availableOptions = correspondingProperty.options;
      
      // Filter out selected values that no longer exist in available options
      const validSelectedValues = filterKey.value.filter((selectedValue: string) => 
        availableOptions.includes(selectedValue)
    );
      
      // If some selected values are no longer valid
      if (validSelectedValues.length !== filterKey.value.length) {
        
        // Update with valid options only, or set to default if none are valid
        if (validSelectedValues.length > 0) {
          filterKey.value = validSelectedValues;
        } else {
          // Set to default or first available option
          if (correspondingProperty.type !== 'multiselect') {
            filterKey.value = [correspondingProperty.default || availableOptions[0]];
          }
        }
        
      }
    }
  });
}

  setSelectedTimePeriod(opt: any) {
    this.selectedTimePeriods = opt;
  }

  changeRecent(event: boolean) {
    this.selectedPeriod = {
      id: !event ? 'All' : 'Latest-Readings',
      isSelected: !event ? false : true,
      label: !event ? 'ALL' : 'Recent',
      unit: !event ? null : chartConstants.RECENT_LABEL,
      value: ''
    };
    this.manualRecentEnable = true;
    this.recentToggleValue = event;
    if (this.chartType == 'pie') {
      this.createPieChartData();
    } else if (this.chartType == 'circular') {
      this.createCircularBarChart();
    } else {
      this.filterData();
    }
  }

  downloadCustomXl() {
    this.isOpen = true;
    this.filterModal.createElement();
  }

  closeFilterModel(_event: any) {
    this.isOpen = false;
    this.filterModal.removeModal();
  }

  // for dashboard filter //
  getDashboardPieFilter() {
    const filterWithoutTimePeriod = this.filterKeys.filter(
      (x: { path: string }) => x.path != chartConstants.TIME_PERIOD
    );
    if (filterWithoutTimePeriod?.length > 0) {
      if (
        filterWithoutTimePeriod.every(
          (x: { path: string; value: string | any[] }) => x.value?.length == 1
        )
      ) {
        this.pieFilterIndex = this.filterPanel?.properties[0].path == chartConstants.TIME_PERIOD ? 1 : 0;
        this.filterKeys[this.pieFilterIndex].value = this.filterPanel?.properties[this.pieFilterIndex].options;
      }
      if (this.filterKeys?.length > 0) {
        const filterBy: any = {};
        this.filterKeys.forEach(
          (element: { value: any; path: string | number }) => {
            if (
              element.path == chartConstants.TIME_PERIOD && element.value?.length == 1 && element.value[0] == 'All'
            ) {
              element.value = this.timePeriodOptions;
            }
            filterBy[
              element.path == chartConstants.TIME_PERIOD ? chartConstants.YEAR_PERIOD : element.path
            ] = element.value.map((x: string) => x.toUpperCase().replace(/\s/g, '')
            ) && element.value?.length == 1 ? element.value.toString().toUpperCase().replace(/\s/g, '') : element.path == chartConstants.TIME_PERIOD ? this.getOptions(element.value) : element.value.map((x: string) => x.toUpperCase().replace(/\s/g, '')
            );
          }
        );
        this.filterChanged.emit(filterBy);
      }
    }
  }

  // for forcast chart //
  getForcastData() {
    this.isYearly = true;
    if (this.chartData?.timeUnit?.includes(chartConstants.MONTHLY) || this.chartData.showQuarterlyIntervals == 'true' || this.chartData.showQuarterlyIntervals == true) {
      this.isYearly = false;
    }
    if (this.seriesMeta?.length > 0) {
      this.initialFilterData = [];
      this.foracstTooltip = [];
      this.tableData = [];
      const multiSeriesOptIndex = this.filterKeys.findIndex((x: { checkbox: boolean; }) => x.checkbox);
      const editableSerieData = cloneDeep(this.seriesMeta);
      const multiseriesData = this.filterPanel?.properties?.length ? this._filterService.filterForcastwithOpts(editableSerieData, this.filterKeys, true) : this.seriesMeta;
      multiseriesData.forEach((element: any) => {
        this.initialFilterData.push(...this._filterService.filterIndicatorForcast(element, this.selectedPeriod));
      });
      const validationLength = this.filterPanel?.properties?.length ? (this.filterKeys[multiSeriesOptIndex].value?.length * 2) : this.seriesMeta.length;
      this.tableData = this.setForcastTableData();
      this.seriesData = this.analyticalService.creatingLineChartData(this.initialFilterData, this.indicatorId, this.cntType, this.chartData?.actualValue, this.isYearly);
      if (this.initialFilterData?.length == validationLength) {
        this.initialFilterData.forEach((chart: { data: any; }) => {
          if (chart.data?.length > 0) {
            const toolTipData: any = [];
            chart.data.forEach((element: Record<string, number | string>) => {
              const toolTip: Record<string, number | string> = {
                MonthLabel: element['CHANGE_QQ'],
                YearLabel: element['CHANGE_YY']
              };
              if (this.chartData?.actualValue) {
                toolTip[this.chartData?.actualValue.label] = element[this.chartData?.actualValue.path];
              }
              toolTipData.push(toolTip);
            });
            this.foracstTooltip.push(toolTipData);
          }
        });
      }
      this.xAxisLabelType = chartConstants.xAxisDateTime;
      if (this.filterPanel?.properties?.length) {
        this.isVisble = false;
        this.change = true;
      }
      if (this.isDashboardCard) {
        this.seriesData[0].spacing = this._dashboardService.gettingSpaceValues(this.cntType, this.indicatorId);
        this.seriesData[0].legendPositions = this._dashboardService.getLegendPositions(this.cntType, this.indicatorId),
          this.seriesData[0].xAxisPositions = this._dashboardService.getXaxisPositions(this.cntType, this.indicatorId);
        this.seriesData[0].isDatalabel = this._dashboardService.getChartSettings(this.cntType, this.indicatorId, chartConstants.DATALABEL);
        this.seriesData[0].isPrecise = this._dashboardService.getChartSettings(this.cntType, this.indicatorId, chartConstants.PRESICE_VALUE);
        this._dashboardService.setSeriesLength(this.cntType, this.indicatorId, this.chartData);
      }
      if ((this.chartData.showQuarterlyIntervals == 'true' || this.chartData?.showQuarterlyIntervals == true) && this.selectedPeriod.id != 'All') {
        this.isVisble = false;
        this.xAxisLabelType = chartConstants.xAxisCatogory;
        this.createQuarterForcastData();
      }
    }
  }

  createQuarterForcastData() {
    this.chartCatogory = [];
    if (this.seriesData?.length > 0) {
      this.seriesData.forEach((element: any) => {
        // if (index <= 2) {
        this.chartCatogory.push(...this.convertUtcToDate(element.data));
        // }
        element.data = element.data.map((arr: string | any[]) => arr.slice(1));
      });
    }
    const groupedChartData = groupBy(this.seriesData, (a: any) => a.identifier);
    for (const key in groupedChartData) {
      if (Object.hasOwn(groupedChartData, key)) {
        // eslint-disable-next-line no-loop-func
        groupedChartData[key].forEach((elementValues: any) => {
          if (elementValues.type == 'arearange' || elementValues.name?.includes('-forecast') || elementValues.name?.includes('-تكهن')) {
            elementValues.pointStart = groupedChartData[key][0].data.length - 1;
          }
        });
        this.change = true;
      }
    }
    this.chartCatogory = [...new Set(this.chartCatogory)];
  }

  convertUtcToDate(data: any) {
    const dates: any = [];
    let date: any;
    if (data?.length > 0) {
      for (let index = 0; index < data.length; index++) {
        const convertedDate = this.datePipe.transform(data[index][0], 'yyyy-MM-dd');
        date = this.convertDateToQuarter(convertedDate);
        dates.push(date);
      }
    }
    return dates;
  }


  setForcastTableData() {
    let dynamicObject: any = {};
    let indexData: any = [];
    this.tableData = [];
    if (this.initialFilterData?.length > 0) {
      this.initialFilterData.forEach((element: any) => {
        dynamicObject = {};
        indexData = [];
        element.data?.forEach((value: Record<string, any>) => {
          if (this.chartData.tableFields?.length > 0) {
            this.chartData.tableFields.forEach((cell: { label: any; path: any; }) => {
              if (value[cell.path]) {
                dynamicObject[cell.label] = value[cell.path];
              }
            });
          }
          if (this.chartData.type == 'coi' && chartConstants.POPULATION_DOMAIN.includes(this.chartData.domain)) {
            dynamicObject['Citizenship'] = element.label;
          }
          indexData.push(cloneDeep(dynamicObject));
        });
        this.tableData.push(indexData);
      });
    }
    return this.tableData;
  }

  ngOnDestroy(): void {
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime);
    }
    this.subs.unsubscribe();
  }
}
