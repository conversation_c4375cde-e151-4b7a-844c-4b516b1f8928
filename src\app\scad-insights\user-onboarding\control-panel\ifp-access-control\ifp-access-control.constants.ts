import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';

export const accessLevels = [
  {
    label: 'Open',
    value: 'open',
    color: ifpColors.greenDark
  },
  {
    label: 'Confidential',
    value: 'confidential',
    color: ifpColors.orange
  },
  {
    label: 'Sensitive',
    value: 'sensitive',
    color: ifpColors.redNormal
  }
  // {
  //   label: 'Secret',
  //   value: 'secret',
  //   color: ifpColors.red
  // }
];

export const accessTabs = [
  {
    name: 'Invite',
    key: 'invite',
    badge: false
  },
  {
    name: 'New Requests',
    key: 'new',
    badge: true,
    count: 0
  },
  {
    name: 'Existing Users',
    key: 'existing',
    badge: true,
    count: 0
  },
  {
    name: 'Pending',
    key: 'pending',
    badge: true,
    count: 0
  },
  {
    name: 'Deleted Users',
    key: 'deleted',
    badge: false
  }
];

export const accessTabsConfidential = [
  {
    name: 'Invite',
    key: 'invite',
    badge: false
  },
  {
    name: 'Existing Users',
    key: 'existing',
    badge: true,
    count: 0
  },
  {
    name: 'Deleted Users',
    key: 'deleted',
    badge: false
  }
];

export const accessTabsPE = [
  {
    name: 'Invite',
    key: 'invite',
    badge: false
  },
  {
    name: 'New Requests',
    key: 'new',
    badge: true,
    count: 0
  },
  {
    name: 'Existing Users',
    key: 'existing',
    badge: true,
    count: 0
  },
  {
    name: 'Deleted',
    key: 'deleted',
    badge: false
  }
  // {
  //   name: 'Pending',
  //   key: 'pending',
  //   badge: true
  // }
];

export const accessTabsDg = [
  {
    name: 'New Requests',
    key: 'new',
    badge: true,
    count: 0
  },
  {
    name: 'Existing Users',
    key: 'existing',
    badge: true,
    count: 0
  },
  {
    name: 'Pending',
    key: 'pending',
    badge: true,
    count: 0
  }
];

export const accessControlFilters = [
  {
    label: 'Latest',
    value: 'latest'
  },
  {
    label: 'Name',
    value: 'name'
  },
  {
    label: 'Department',
    value: 'department'
  }
];

export const apiParams = {
  page: 1,
  limit: 10,
  // sortBy: 'requestDate',
  // order: 'desc',
  entity: '',
  search: '',
  userType: '',
  role: ''
};

export const role = {
  pePrimary: 'PRIMARY_PE_USER',
  peSecondary: 'SECONDARY_PE_USER',
  suPrimary: 'PRIMARY_SUPERUSER',
  suSecondary: 'SECONDARY_SUPERUSER',
  dg: 'DG',
  normalUser: 'USER',
  underSecretary: 'UNDER_SECRETARY',
  approver: 'VISUALIZATION_APPROVER'
};

export const roleType = {
  'DG': 'Director General/Under Secretary',
  'PRODUCT_ENGAGEMENT': 'SCAD',
  'SUPERUSER': 'Superuser'
};

export const roleList = {
  [role.pePrimary]: 'Product Engagement User 1',
  [role.peSecondary]: 'Product Engagement User 2',
  [role.suPrimary]: 'Primary Superuser',
  [role.suSecondary]: 'Secondary Superuser',
  [role.dg]: 'Director General',
  dgOrUnderSecretary: 'Director General/Under Secretary',
  [role.normalUser]: 'User',
  [role.underSecretary]: 'Under Secretary'
};

export const controlPanelAccess = [role.pePrimary, role.suPrimary, role.suSecondary, role.dg];

export const dataClassification = {
  open: 'Open',
  confidential: 'Confidential',
  sensitive: 'Sensitive',
  secret: 'Secret'
};

export const dgStatus = {
  invitePending: 'INVITE_PENDING',
  inviteSent: 'INVITE_SENT',
  available: 'REGISTERED'
};

export const superUserRoles = [role.suPrimary, role.suSecondary];
export const peUserRoles = [role.pePrimary, role.peSecondary];
export const dgRoles = [role.dg, role.underSecretary];

export const inviteStatus = {
  registration_pending: {
    key: 'registration_pending',
    value: 'Registration Pending'
  },
  awaiting_entraid: {
    key: 'awaiting_entraid',
    value: 'Awaiting EntraID Acceptance'
  },
  PENDING: {
    key: 'PENDING',
    value: 'PENDING'
  }
};
