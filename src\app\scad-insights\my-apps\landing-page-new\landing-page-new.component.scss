@use '../../../../assets/ifp-styles/abstracts' as *;
.if-myapp-landing {
  &__drag-drop {
    overflow: hidden;
    display: block;
    width: 100%;
  }
  &__tab-auto {
    width: 50%;
  }
  &__tab-custom-auto {
    width: 100%;
  }
&__section-button {
  margin: $spacer-2 $spacer-2 $spacer-3;
  padding: $spacer-3;
  border-radius: 6px;
  border: 1px solid $ifp-color-secondary-blue;
  color:$ifp-color-secondary-blue ;
  text-align: center;
}
&__sub-section-button {
  margin: $spacer-3 $spacer-3 $spacer-0;
  padding: $spacer-3;
  width: calc(100% -  $spacer-5);
  border-radius: 6px;
  border: solid $ifp-color-grey-7 1px;
  background-color: $ifp-color-grey-4 ;
  color:$ifp-color-black;
  text-align: center;
}
&__section-icons {
  margin: $spacer-0 $spacer-2;
}


  &__head {
    display: flex;
    padding: $spacer-3 $spacer-3 $spacer-3;
    //margin: (-$spacer-3) (-$spacer-3) ($spacer-0);\
    border-radius:  10px 10px 0 0;
    background-color: $ifp-color-primary-blue;
  }
  &__head-placeholder{
    display: flex;
  }
  &__section {
    //display: flex;
   // flex-wrap: wrap;
    min-height: 100px;
   // margin:  $spacer-0 (-$spacer-8) $spacer-0;
  }
  &__section-card {
    background-color: $ifp-color-white;
    //border-radius: 10px;
  //  margin:  $spacer-4 $spacer-8 $spacer-0;
    padding: $spacer-3   $spacer-3  $spacer-0;
   // border: solid $ifp-color-grey-7 1px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  &__wh-new {
    height: 100%;
   }
  &__cards-head{
    display: flex;
  }

  &__cards-title{
    font-size: $ifp-fs-5;
    font-weight:$fw-bold ;
    color: $ifp-color-primary-blue;
    .ifp-icon {
      font-size: inherit;
      margin-right: $spacer-1;
      color: $ifp-color-blue-hover;
      position: relative;
      top: 1px;
    }
  }
  &__title {
    margin-left: $spacer-3;
    font-size: $ifp-fs-8;
    font-weight:$fw-bold ;
    color: $ifp-color-white;
    &--edit {
      margin-left: $spacer-0;
    }
  }
  &__sideBar {

    display: block;
    width: 25%;
  }

  &__rect {
    cursor: pointer;
    display: inline-block;
    height: 20px;
    border: 2px solid $ifp-color-black;
    border-radius: 5px;
    transition: 0.3s;

    &--small {
      margin: $spacer-1 $spacer-1;
      width: 13px;
      min-width: 13px;
    }
    &--large {
      margin: $spacer-1 $spacer-1;
      width: 26px;
      min-width: 26px;
    }

    &:hover {
      border-color: $ifp-color-secondary-blue;
    }
    &--white {
      border: 2px solid $ifp-color-white;
      &:hover {
        background-color: $ifp-color-secondary-blue;
      }
    }
  }
&__plus {
  font-size: $ifp-fs-6;
  margin: $spacer-0 $spacer-2;
  cursor: pointer;
  transition: 0.3s;
  &:hover {
    color: $ifp-color-secondary-blue;
  }
}
  &__filter {
    display: flex;
    margin-bottom: $spacer-2;
    align-items: center;
    z-index: 1;
    position: relative;
    &--main {
     // margin: $spacer-2 (-$spacer-2);
    }
  }
  &__placeholder {
    background-color: $ifp-color-white;
    border-radius: 10px;
    margin:  $spacer-4 $spacer-8 $spacer-0;
    padding: 16px;
    border: solid $ifp-color-grey-7 1px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  &__heading-left {
    margin-left: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    &--white {
      color: $ifp-color-white;
    }
  }

  &__cross{
    margin: $spacer-0 $spacer-1;
    font-size: $ifp-fs-6;
    cursor: pointer;
    transition: 0.3s;
    &:hover {
      color: $ifp-color-secondary-blue;
    }
  }

  &__dropdown,&__button {
    margin: $spacer-0 $spacer-2;
  }
  &__button {
    margin-left: auto;

  }
  &__wrapper {
    overflow: hidden;
    border: solid $ifp-color-grey-7 1px;
    border-radius: 10px;
    padding: $spacer-0  $spacer-0 $spacer-3;
    background-color: $ifp-color-section-white;
    position: relative;
    margin-bottom: $spacer-4 ;
    margin:  $spacer-2;
    &--minimize{
      width: unset;
      height: unset;
    &::after, &::before  {
      content: none;
    }
      border-radius: 10px;
      border: solid $ifp-color-grey-7 1px;
      overflow: hidden;
      width: calc(25% - ($spacer-3));
      .if-myapp-landing{

        &__wh-new, &__analytic, &__drag-drop{
          display: none;
        }
        &__section {
          display: block;
          margin: $spacer-0 $spacer-0 $spacer-0;
        }
        &__section-card {
          margin:  $spacer-2 $spacer-0;
         // box-shadow: 0px 0px 6px #00000029;
        }
        &__cards {
          margin-bottom: $spacer-2;
        }


      }

    }
  }
  &__inner-wrapper {
    width: 100%;

    &--editable {
      width: 75%;
    }
  }
  &__outer-wrapper {
    display: flex;
  }
  &__main {
    margin-top: $spacer-3;
    //background-color: $ifp-color-white;
    min-height: 500px;

  }
  &__st{
    display: flex;
    align-items: center;
    margin-top: 100px ;
    justify-content: center;
    margin-bottom: 100px;
  }
  &__st-card {
    width: 100%;
    max-width: 800px;
    cursor: pointer;
  }
  &__wh-new ,&__analytic {
    display: flex;
    flex-wrap: wrap;
   // margin: $spacer-2  (-$spacer-1) $spacer-0;
    background-color: $ifp-color-section-white;
    border-radius: 10px;
  }
  &__wh-card {
    margin: $spacer-2 $spacer-1 $spacer-0;
  }
  &__analytic-card {
    margin: $spacer-2;
    width: calc(20% - 8px);
  }
  &__fill{
    border-color: $ifp-color-secondary-blue;
    cursor: default;
    &--header {
      border-color:$ifp-color-white;
      background-color: $ifp-color-white;
    }

    &--disable {
      cursor: default;
      border: 2px solid $ifp-color-grey-5;
      background-color: $ifp-color-grey-5;
    }
  }
  &__geo-spatial {
    display: block;
    width: 100%;
  }

}
:host::ng-deep{
  .if-myapp-landing {
    &__dropdown {
      .ifp-dropdown {
        background-color: $ifp-color-section-white;
      }
    }
  }
  .if-myapp-landing__wrapper--minimize {
    .ifp-card {
      margin:  $spacer-2 $spacer-0;
      box-shadow: 0px 0px 6px #00000029;
    }

  }
  .if-myapp-landing__cards{
    .ifp-card {
      // padding: $spacer-3 $spacer-2;
      background-color: $ifp-color-white;
    }
  }

  .if-myapp-landing__wh-new {
    .ifp-whats-new-card {
      max-width: 200px;
    }

    .ifp-loading-card__block {
      margin: $spacer-0 $spacer-0;
      width: 200px;
    }
  }
  .ifp-active-card {
    .ifp-whats-new-card {
      max-width: 100%;
    }
  }
  .if-myapp-landing__analytic {

    .ifp-loading-card__block {
      margin: $spacer-0 $spacer-0;
    }
  }

}
.ifp-icon-handler {

  &__icon {
    fill: $ifp-color-black;
    cursor: move;
    margin: $spacer-0 $spacer-1;
    &:hover{
      fill: $ifp-color-link;
    }
    &--white {
      fill: $ifp-color-white;
    }
    &--disable {
      fill: $ifp-color-grey-7;
      cursor: not-allowed;
      display: none;
    }
  }


}
:host-context([dir="rtl"]) {
  .if-myapp-landing {
    &__cards-title{
      .ifp-icon {
        margin-left: $spacer-1;
        margin-right: $spacer-0;
      }
    }
    &__title {
      margin-right: $spacer-3;
      margin-left: $spacer-0;
      &--edit {
        margin-right: $spacer-0;
      }
    }
    &__heading-left {
      margin-right: auto;
      margin-left: $spacer-0;
    }
    &__button {
      margin-right: auto;
      margin-left: $spacer-2;
    }
  }
}

@include desktop-sm {
  .if-myapp-landing {
    &__analytic-card {
      width: calc(25% - 8px);
    }
  }
}




:host::ng-deep {
  .if-myapp-landing__edit-button {
    em {
       font-size: $ifp-fs-6;
    }
  }
}
