import { cloneDeep } from 'lodash';
import {
  Component,
  computed,
  ElementRef,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  Signal,
  signal,
  viewChild,
} from '@angular/core';
import {
  ActivatedRoute,
  Router,
} from '@angular/router';
import { IfpBreadcrumbsComponent } from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { TranslateModule } from '@ngx-translate/core';
import { IfpButtonComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import {
  buttonClass,
  buttonIconPosition,
} from '../../scad-insights/core/constants/button.constants';
import { IfpModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { DxpPopupComponent } from '../dxp-popup/dxp-popup.component';
import { IfpModalService } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';
import { DxpDatasetCardComponent } from '../dxp-dataset-card/dxp-dataset-card.component';
import { IfpDropdownComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { IfpInfoComponent } from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-heading-with-info/ifp-info.component';
import { DxpLabelDragComponent } from '../widgets/dxp-label-drag/dxp-label-drag.component';
import { CommonModule, DatePipe, NgClass } from '@angular/common';
import { FormControl, FormsModule,   FormArray,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
  AbstractControl } from '@angular/forms';
import { DxpVisualizationPreviewComponent } from '../dxp-visualization-preview/dxp-visualization-preview.component';
import { DxpValidationPopUpComponent } from '../dxp-validation-pop-up/dxp-validation-pop-up.component';
import { DxpUserConfigComponent } from '../dxp-user-config/dxp-user-config.component';
import { DxpIntegrationApiService } from 'src/app/scad-insights/core/services/dxp-integration-api.service';
import { SubSink } from 'subsink';
import { dxpApi, statusContDxp } from '../dxp.constants';
import { USER_ROLES } from '../dxp-constants-text';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import {  SelectedProductWithoutId } from '../widgets/dxp-accordian/dxp-accordian.component';
import {
  ConvertedChartData,
  DxpPlotData,
  ProductDetail,
  DxpDetail,
  DxpColumnList,
  DxpCurrentLegend,
  SourceFiltersDxp,
  DxpAxisVariable,

  ConditionDxp,
  DxpSelectedLegendOption,
  ResponseForSaveKpi,
} from '../dxp.interface';

import { DragDropModule } from '@angular/cdk/drag-drop';
import { IfpCardLoaderComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { DxpKpiCustomFilter } from '../dxp-visualization-wizard-toolbar/dxp-visualization-filter-form/dxp-visualization-filter-form.component';
import { UserDetail } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-user-tag-group/ifp-user-tag-group.component';
import {
  dxpChartTypes,
  sourceWithoutValue,
} from 'src/app/dashboard-builder/organism/ifp-chart-toolbar/ifp-chart-toolbar.constants';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { Subject, Subscription, debounceTime } from 'rxjs';
import { IfpSingleDatePickerComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-single-date-picker/ifp-single-date-picker.component';
import { SearchSuggestionDirective } from 'src/app/scad-insights/core/directives/sugession.directive';
import { IfpDataTableComponent } from 'src/app/ifp-analytics/organism/ifp-data-table/ifp-data-table.component';
import { IfpNumberOnlyDirective } from 'src/app/scad-insights/core/directives/ifp-number-only.directive';
import { DxpKpiPreviewTabList } from '../dxp-visualization-wizard-toolbar/dxp-visualization-wizard-toolbar.component';
import { dxpPreviewWizardTabMenu } from '../dxp-visualization-wizard-toolbar/dxp-visualization-wizard.constants';
import { PaginationComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/pagination/pagination.component';
import { DistinctValuesStore, sourceFilterDxpPayload } from '../store/dxp-distinct-value.store';
import { AdminService } from 'src/app/scad-insights/core/services/sla/admin.service';





@Component({
  selector: 'ifp-dxp-visualization-wizard',
  imports: [
    IfpBreadcrumbsComponent,
    TranslateModule,
    ReactiveFormsModule,
    CommonModule,
    FormsModule,
    IfpButtonComponent,
    IfpModalComponent,
    DxpPopupComponent,
    DxpDatasetCardComponent,
    IfpDropdownComponent,
    IfpInfoComponent,
    DxpLabelDragComponent,
    NgClass,
    DxpVisualizationPreviewComponent,
    DxpValidationPopUpComponent,
    DxpUserConfigComponent,
    DragDropModule,
    IfpCardLoaderComponent,
    IfpTooltipDirective,
    IfpSingleDatePickerComponent,
    DatePipe,
    SearchSuggestionDirective,
    IfpDataTableComponent,
    IfpNumberOnlyDirective,
    PaginationComponent],
  providers: [DistinctValuesStore],
  templateUrl: './dxp-visualization-wizard.component.html',
  styleUrl: './dxp-visualization-wizard.component.scss',
})
export class DxpVisualizationWizardComponent implements OnInit, OnDestroy {
  public sectionRight = viewChild<ElementRef>('sectionRight');
  public modal = viewChild<IfpModalComponent>('modal');
  public modalPopUp = viewChild<IfpModalComponent>('modalPopUp');
  public modalTable = viewChild<IfpModalComponent>('modalTable');
  public userView = signal(false);
  public sourceSampleData = signal<{ key: string; title: string; value: any; type: string; }[][]>([]);
  public sourceDataHead = signal<string[]>([]);
  public tableLoader = signal(true);
  public pageLoader: boolean = false; // Page-level loader for edit prefill
  public filterChange = signal(false);
  public modalService = inject(IfpModalService);
  private readonly _dxpIntegrationApiService = inject(DxpIntegrationApiService);
  private readonly _toasterService = inject(ToasterService);
  private readonly _router = inject(Router);
  private readonly _route = inject(ActivatedRoute);
  private readonly _subs: SubSink = new SubSink();
  public pageData = signal([
    { title: 'Home', route: '/home' },
    { title: 'Visualization Wizard', route: '' },
  ]);

  public statusDxp = statusContDxp;

  public chartLegend = signal<DxpCurrentLegend []>([]);
  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public productdetailList: ProductDetail[] = [];
  public selectedProduct: SelectedProductWithoutId = {
    title: '',
  };

  public limit: number = 10;
  public offsetPage: number = 1;
  public totalCount: number = 0;
  public page: number = 1;
  readonly storeDistinctValue = inject(DistinctValuesStore);
  public dataset = signal<DxpColumnList[]>([]);
  public fullColumnsData = signal<DxpColumnList[]>([]);
  public legendColumn = signal<DxpColumnList[]>([]);
  public selectedTabItem =  signal<DxpKpiPreviewTabList>(dxpPreviewWizardTabMenu[0]);
  public selectedColumn = signal<any>(null);
  public comparators = signal([]);
  public productListLoader: boolean = true;
  public tableSubscription?: Subscription;
  public legendSelectedValues = signal<DxpSelectedLegendOption[]>([]);
  public legendValueOptions = signal<DxpSelectedLegendOption[]>([]);
  public legendCurrentSelected = signal<DxpCurrentLegend | undefined>(undefined);
  public datasetLoading = signal(false);
  public graphDatasetLoading = signal(false);
  public chartName = signal<string>('line');
  public closeSideDataset = signal(true);
  public sharedUserListSelected = signal<UserDetail[]>([]);
  public currentFilterClick = signal<{event:string; index: number}>({event: '', index: 0});
  public currentFilterClickValues = {
    apply: 'apply',
    remove: 'remove'
  };

  // current Default values
  public toolBarLegend = signal<DxpCurrentLegend[]>([]);
  // chart variables
  // public legendValueOptions = signal<DxpLegend[]>([]);

  public kpiCardFilters = signal<DxpKpiCustomFilter[]>([]);
  public cardFilters = signal<DxpKpiCustomFilter[]>([]);

  // Add signals for X and Y variables with aggregation data
  public xVariable = signal<DxpAxisVariable[]>([]);
  public yVariable = signal<DxpAxisVariable[]>([]);
  public error = signal(false);

  // Add axis label variables for two-way binding
  public xAxisLabel: string = '';
  public yAxisLabel: string = '';

  public dataTypesList = {
    numeric: ['int', 'float', 'double', 'long'],
    character: ['str', 'string', 'char'],
    date: ['date'],
  };

  public filterForm!: FormGroup;
  public axisForm: FormGroup;
  public graphData: ConvertedChartData | undefined;
  public userConfigData = signal<any>(null);
  public selectedFilterIndex: number = -1;
  private filterColumnChangeSub: any = null;
  public selectedIcon = signal('ifp-icon-hand-hammer');
  public enableFirstFilter = signal(false);
  public enableSecondFilter = signal(false);
  // New properties for the updated filter UI
  public isFilterSectionExpanded: boolean = true;
  public logicalOperator: 'AND' | 'OR' = 'AND';
  public appliedFilters = signal<SourceFiltersDxp[]>([]);
  public appliedLogicalOperator: 'AND' | 'OR' = 'AND';

  // Edit mode properties
  public isEditMode: boolean = false;
  public editObjectId: string | null = null;
  public editData: DxpDetail | null = null;
  storedUserConfigIds!: string[];

  // User role for conditional UI (e.g., hide Send for Approval for EXPLORER)
  public currentUserRole =   inject(AdminService).generalizedRole;
  public userRoles = USER_ROLES;

  // Signal for update popup description
  public updateDec = signal('');

  // Signal for update popup title
  public updateTitle = signal('');
  public chartTypes = signal(cloneDeep(dxpChartTypes));
  // Signal for update popup icon
  public updateIcon = signal('');
  public buttonLabel = signal('');
  public buttonSecondLabel = signal('');
  public image = signal(true);
  public enableCloseButton = signal(true);
  public searchSubject = new  Subject<{ searchText: string; column: string; int: number; }>();
  public searchTeam = signal('');

  public sourceWithoutValue: Record<string, string> = sourceWithoutValue;
  public valueFilterOne: Signal<FormControl<string>> = computed(
    () => this.filters.at(1).get('value') as FormControl<string>
  );

  public valueFilterZero: Signal<FormControl<string>> = computed(
    () => this.filters.at(0).get('value') as FormControl<string>
  );

  // enable double button in popup
  public enableDoubleButton = signal(false);
  public currentStatus = signal('');
  public successBackground = signal(false);
  public cardTitle = signal('');
  public cardDescription = signal('');
  public formSubscription!: Subscription;
  public querySubscription!: Subscription;
  constructor(private fb: FormBuilder) {
    this.filterForm = this.fb.group({
      filters: this.fb.array([this.createFilterGroup()]),
    });
    this.axisForm = this.fb.group({
      xAxisLabel: ['', Validators.required],
      xAxisVariable: this.fb.group({
        name: [''],
        data_type: [''],
        selectedAggregation: [''],
      }),
      yAxisLabel: ['', Validators.required],
      yAxisVariables: this.fb.group({
        name: [''],
        data_type: [''],
        selectedAggregation: [''],
      }), // For multiple Y
      unit: ['', [Validators.pattern(/^[A-Za-z\s]*$/)]],
    });

    // Subscribe to axis form changes to update the axis labels
    this.formSubscription =   this.axisForm.valueChanges.subscribe((values) => {
      this.xAxisLabel = values.xAxisLabel || '';
      this.yAxisLabel = values.yAxisLabel || '';
    });
    this.checkChangeInTheFilter();
  }

  ngOnInit(): void {
    // Check for edit mode (objectId in query params)
    this.chartTypes().forEach((chart) =>chart.selected = chart.key === 'line');
    this._subs.add(
      this._route.queryParamMap.subscribe((params) => {
        const objectId = params.get('objectId');
        if (objectId) {
          this.isEditMode = true;
          this.editObjectId = objectId;
          this.initializeEditMode(objectId);
        }
      })
    );
    this._subs.add(
      this.searchSubject.pipe(debounceTime(800)).subscribe((search: {searchText: string,  column: string, int: number}) => {
        this. callSourceApi(search.searchText, search.column);
      } ));

    // Check if the current URL is exactly '/dxp/visualization-wizard/publish'

    this._subs.add(
      this._route.url.subscribe((segments) => {
        const last = segments[segments.length - 1]?.path;
        const isPublish =          last === 'publish' ||          (this._route.snapshot.routeConfig?.path?.includes('publish') ??            false);
        const objectId = this._route.snapshot.queryParamMap.get('objectId');
        if (objectId) {
          this.isEditMode = true;
          this.editObjectId = objectId;
        }
        if (this._router.url.includes('publish') && this.isEditMode) {
          this.userView.set(true);
        } else {
          this.userView.set(!!isPublish);
          if (!!isPublish && this._router.url.includes('publish')) {
            this.goBack();
          }
        }

        this.setBreadcrumb();
      })
    );

    this.getAssetList();
  }

  setBreadcrumb() {
    this.pageData.set([
      { title: 'Home', route: '/home' },
      { title: 'Government Affairs', route: '/dxp' },
      {
        title: this.userView() ? 'Publishing Panel'  : this.isEditMode? 'Edit Visualization': 'Visualization Wizard',
        route: '',
      },
    ]);
  }

  ResetToolbar() {
    this.storeDistinctValue.resetAllData();
    this.toolBarLegend.set([]);
    this.kpiCardFilters.set([]);
    this.cardFilters.set([]);
    this.legendValueOptions.set([]);
    this.setUserConfig();
    if (this.currentFilterClickValues.apply === this.currentFilterClick().event){
      this.applyFilters();
    } else if (this.currentFilterClickValues.remove === this.currentFilterClick().event){
      this.removeAppliedFilter(this.currentFilterClick().index);
    }

    this.onSubmitClick(true);
  }






  get filters(): FormArray {
    return this.filterForm.get('filters') as FormArray;
  }

  createFilterGroup(): FormGroup {

    return this.fb.group({
      column: ['', Validators.required],
      comparator: ['', Validators.required],
      value: ['', Validators.required],
    });
  }

  editFilterGroup(value: DxpColumnList, currentValue: ConditionDxp): FormGroup {
    const comparatorValue = value.available_comparators?.find(
      (comparators) => currentValue.comparator === comparators.value
    );
    this.appliedFilters().push({
      column: value,
      comparator: comparatorValue!,
      value: (currentValue.value ?? '') as string,
      data_type: currentValue.data_type,
      inferred_dtb_dt_format: value.inferred_dtb_dt_format
    });
    return this.fb.group({
      column: [value, Validators.required],
      comparator: [comparatorValue, Validators.required],
      value: [currentValue.value ?? '', Validators.required],
    });
  }

  onDropdownSelect(index: number, controlName: string, value: any) {
    this.filters.at(index).get(controlName)?.setValue(value);
  }

  resetFilterForm(): void {
    const filter = this.filters.at(0);

    filter.patchValue({
      column: '',
      comparator: '',
      value: '',
    });
  }

  deleteOutput() {
    this.limit = 10;
    this.offsetPage = 1;
    this.totalCount = 0;
    this.page = 1;
    this.formSubscription.unsubscribe();
    this.filterForm?.reset();
    this.fullColumnsData.set([]);
    this.appliedFilters.set([]);
    this.selectedTabItem.set(dxpPreviewWizardTabMenu[0]);
    this.xAxisLabel = '';
    this.yAxisLabel = '';
    this.xVariable.set([]);
    this.yVariable.set([]);
    this.clearFilters(false);
    this.toolBarLegend.set([]);
    this.cardFilters.set([]);
    this.legendValueOptions.set([]);
    this.sharedUserListSelected.set([]);
    this.legendSelectedValues.set([]);
    this.chartLegend.set([]);
    this.chartName.set('line');
    this.cardTitle.set('');
    this.cardDescription.set('');
    this.selectedIcon.set('ifp-icon-hand-hammer');
    this.chartTypes().forEach((c) => (c.selected = c.key === 'line'));
    this.graphData = undefined;
    this.kpiCardFilters.set([]);
    this.axisForm = this.fb.group({
      xAxisLabel: ['', Validators.required],
      xAxisVariable: this.fb.group({
        name: [''],
        data_type: [''],
        selectedAggregation: [''],
      }),
      yAxisLabel: ['', Validators.required],
      yAxisVariables: this.fb.group({
        name: [''],
        data_type: [''],
        selectedAggregation: [''],
      }), // For multiple Y
      unit: ['', [Validators.pattern(/^[A-Za-z\s]*$/)]],
    });
    this.filterForm.updateValueAndValidity({ emitEvent: true });
    this.formSubscription =   this.axisForm.valueChanges.subscribe((values) => {
      this.xAxisLabel = values.xAxisLabel || '';
      this.yAxisLabel = values.yAxisLabel || '';
    });
    this.checkChangeInTheFilter();
    this.setUpSourceFilter();
    sourceFilterDxpPayload.set(undefined);
  }

  clearFilters(checkValidation = true): void {
    // Keep the existing controls to avoid breaking dropdown bindings
    // 1) Remove any second row
    if ((this.toolBarLegend().length >0 || this.kpiCardFilters().length >0) &&  checkValidation){
      this.createSourcePopup('clearSource');
      return;
    }
    if (this.filters.length > 1) {
      this.filters.removeAt(1);
    }
    const first = this.filters.at(0);
    if (first) {
      first.patchValue({
        column: '',
        comparator: '',
        value: '',
      });
      first.markAsPristine();
      first.markAsUntouched();
      first.updateValueAndValidity({ emitEvent: true });
    }

    // 3) Reset UI/edit state
    this.selectedFilterIndex = -1;
    if (this.filterColumnChangeSub) {
      this.filterColumnChangeSub.unsubscribe();
      this.filterColumnChangeSub = null;
    }

    this.appliedFilters.set([]);
    this.logicalOperator = 'AND';
    this.appliedLogicalOperator = 'AND';
    this.setUpSourceFilter();
    // 5) Trigger form validity recalculation
    this.filterForm.updateValueAndValidity({ emitEvent: true });
  }

  isFilterValid(filterOrIndex: number | AbstractControl): boolean {
    let filter: AbstractControl;

    if (typeof filterOrIndex === 'number') {
      filter = this.filters.at(filterOrIndex);
    } else {
      filter = filterOrIndex;
    }

    if (!filter) {
      return false;
    }

    const column = filter.get('column')?.value;
    const comparator = filter.get('comparator')?.value;
    const value = filter.get('value')?.value;

    return (
      column &&      comparator &&      ((value && value.toString().trim() !== '') ||        this.sourceWithoutValue[comparator?.value])
    );
  }

  canAddMoreFilters(): boolean {
    // Limit to a maximum of 2 rows in the UI
    return this.filters.length < 2;
  }

  /**
   * Initialize edit mode by loading existing data for the given object ID
   * @param objectId - The object ID to load data for
   */
  initializeEditMode(objectId: string) {
    this.pageLoader = true;
    this.loadExistingData(objectId);
  }

  /**
   * Load existing data for edit mode
   * @param objectId - The object ID to load data for
   */
  private loadExistingData(objectId: string) {
    this._subs.add(
      this._dxpIntegrationApiService.getMethodRequest(dxpApi.getDetails(objectId)).subscribe({
        next: (data: DxpDetail) => {
          this.editData = data; // Store the edit data
          // Capture role for conditional UI (e.g., hide Send for Approval for EXPLORER)
          this.populateEditData(data);
          this.appliedFilters.set([]);
        },
        error: (error) => {
          this._toasterService.error(
            error?.error?.message || 'Failed to load existing data'
          );
        },
      })
    );
  }

  setValueInFilter(value: string, int : number) {
    const first = this.filters.at(int);
    this.filters.at(int).patchValue({
      value: value
    });
    first.markAsPristine();
    first.markAsUntouched();
    first.updateValueAndValidity({ emitEvent: true });
  }

  suggestionListClicked(event: HTMLElement | undefined, element: HTMLDivElement, index: number) {
    const clickedInside = event && element?.contains(event);
    if (!clickedInside) {
      if (index === 0) {
        this.enableFirstFilter.set(false);
      } else {
        this.enableSecondFilter.set(false);
      }
    }
  }

  callSourceSuggestion(event: Event, column: string, int: number) {
    const searchTerm = (event.target as HTMLInputElement).value;
    this.searchSubject.next({searchText: searchTerm, column: column, int: int});

  }

  callSourceApi(searchTerm: string, column: string) {
    this.searchTeam.set(searchTerm);
    this.storeDistinctValue.loadByQuery({ columnName: column, search: searchTerm, product: this.selectedProduct });
  }

  /**
   * Populate form data with existing values for edit mode
   * @param data - The existing data to populate
   */
  private populateEditData(data: DxpDetail) {
    // Set selected product

    if (data.sourceProductId && data.sourceAssetId) {
      this.selectedProduct = {
        sourceProductId: data.sourceProductId,
        sourceAssetId: data.sourceAssetId,
        title: data.product.displayName || '',
        subTitle: data.product.displayName,
        cardType: 'dxp',
        cardTitle: data.product.displayName,
      };
      // Load columns data for the selected product
      this.getFullColumnsData(true);
    }

    this.cardDescription.set(data.subTitle);
    this.cardTitle.set(data.title);
    // set Legend data
    if (data.legendPanel && data.legendPanel.length > 0) {

      const currentLegends = data.legendPanel.map((legend, index) => {

        return {
          name: legend?.column ?? '',
          data_type: legend?.data_type ?? '',
          index: index,
          selectedValues: legend?.selected_values?.map((val) => ({ id: val, value: val,  checked: true })) ?? [],
          options: legend?.options?.map((opt) => ({ id: opt, value: opt })) ?? [],
          checked: legend?.default ?? false,
          default: legend?.default ?? false,
        };
      });


      this.toolBarLegend.set([...currentLegends]);
      // this.legendValueOptions.set([...optionLegends]);
    }

    // Prefill KPI filters from root-level filterPanel
    if (data.filterPanel && data.filterPanel.length > 0) {
      const mappedFilters = data.filterPanel.map((filter) => {
        const options = Array.isArray(filter.options) ? filter.options : [];
        const defaultValue = Array.isArray(filter.default)          ? filter.default.map((val: string) => ({ id: val, value: val }))          : [{
          id: filter.default ?? '',
          value: filter.default ?? '',
        }];
        const filterOptions = options.map((opt: string) => ({
          id: opt,
          value: opt,
        }));
        const selectedValueArray =  Array.isArray(filter.default) ? filter?.default?.map((val: string) => val): filter.default ? [filter.default] : [];

        return {
          inferred_dtb_dt_format: filter.inferred_dtb_dt_format,
          column: filter.column,
          filterLabel: filter.label || filter.column,
          data_type: filter.data_type,
          defaultValueOnly: Array.isArray(filter.default) ? filter.default : [filter.default ?? ''],
          filterOptionsString: options,
          selectedValueArray,
          defaultValue,
          filterOptions,
        };
      });

      // Store for submit payload construction
      this.kpiCardFilters.set([...mappedFilters]);
      this.cardFilters.set([...mappedFilters]);
    }

    // Prefill default chart type selection in toolbar and preview
    const apiChartType = (
      data.visualizationConfig?.chart_configuration?.default_chart_type || ''
    ).toLowerCase();
    if (apiChartType) {
      this.chartName.set(apiChartType);
      this.chartTypes().forEach((c) => (c.selected = c.key === apiChartType));
    }
    // Prefill/plot the graph immediately using saved series data (edit mode)
    if (data.series && data.series.series && data.series.series.length > 0) {
      const savedXAxisLabel =          data.visualizationConfig?.chart_configuration?.x_axis?.label || '';
      const savedYAxisLabel =          data.visualizationConfig?.chart_configuration?.y_axis?.label || '';

      // Keep local axis label state in sync so Preview shows labels
      this.xAxisLabel = savedXAxisLabel;
      this.yAxisLabel = savedYAxisLabel;

      // Convert details API series to ConvertedChartData expected by Preview
      this.graphData = {
        category: data.series.xAxis?.categories ?? [],
        series: data.series.series || [],
        xAxisLabel:
            savedXAxisLabel +            (data?.visualizationConfig?.chart_configuration?.unit &&            data?.visualizationConfig?.chart_configuration?.unit !== ''              ? ` (${data?.visualizationConfig?.chart_configuration?.unit})`              : ''),
        yAxisLabel: savedYAxisLabel,
        unit: data?.visualizationConfig?.chart_configuration?.unit ?? ''
      };

      // Ensure any loading spinner is off for prefilled render
      this.graphDatasetLoading.set(false);
    }
    const xAxisConfig =        data?.visualizationConfig?.chart_configuration?.x_axis?.axis;
    const yAxisConfig =        data?.visualizationConfig?.chart_configuration?.y_axis?.axis;
    this.selectedIcon.set(data.icon ?? '');
    this.axisForm = this.fb.group({
      xAxisLabel: [this.xAxisLabel, Validators.required],
      xAxisVariable: this.fb.group({
        name: xAxisConfig?.column ?? '',
        data_type: xAxisConfig?.data_type ?? '',
        selectedAggregation: xAxisConfig?.aggregator ?? '',
      }),
      yAxisLabel: [this.yAxisLabel, Validators.required],
      yAxisVariables: this.fb.group({
        name: yAxisConfig?.column ?? '',
        data_type: yAxisConfig?.data_type ?? '',
        selectedAggregation: yAxisConfig?.aggregator ?? '',
      }), // For multiple Y
      unit: [
        data?.visualizationConfig?.chart_configuration?.unit,
        [Validators.pattern(/^[A-Za-z\s]*$/)],
      ],
    });
    this.logicalOperator =       ( data?.visualizationConfig?.source_filter?.groups[0]?.operator?.toUpperCase() ?? 'AND') as
          | 'AND'
          | 'OR';
    this.appliedLogicalOperator =        ( data?.visualizationConfig?.source_filter?.groups[0]?.operator?.toUpperCase() ?? 'AND') as
          | 'AND'
          | 'OR';
    this.setUserConfig();
    this.checkChangeInTheFilter();
  }

  getAssetList(searchKey: string = '') {
    this.productListLoader = true;
    this._subs.add(
      this._dxpIntegrationApiService
        .getMethodRequest(dxpApi.product, { search: searchKey })
        .subscribe({
          next: (resp) => {
            this.productdetailList = resp;
            this.productListLoader = false;
          },
          error: (error) => {
            this._toasterService.error(error?.error?.message);
            this.productListLoader = false;
          },
        })
    );
  }

  onSearchAsset(key: string) {
    this.getAssetList(key);
  }

  ngAfterViewInit(): void {
    // Don't show popup in edit mode
    if (!this.isEditMode) {
      this.createKpi();
    }
  }

  openPopup() {
    // Switch to /visualization-wizard/publish path (child route)

    // this.modalPopUp()?.createElement();
    if (this.cardTitle() ==='' || this.cardTitle().length > 120 || this.cardDescription().length >  400) {
      if (!this.graphData){
        this.onSubmitClick();
      }
      this.error.set(true);
      this.sectionRight()?.nativeElement.scrollTo({
        top: this.sectionRight()?.nativeElement.scrollHeight,
        behavior: 'smooth'
      });
      if (this.cardTitle() ===''){
        this._toasterService.error('Please enter title');
      } else if (this.cardTitle().length > 120){
        this._toasterService.error('Title should not exceed 120 characters');
      } else if (this.cardDescription().length >  400){
        this._toasterService.error('Description should not exceed 400 characters');
      }
      return;
    }
    this.error.set(false);
    this.cardFilters.update((data) => {
      const mappedValue: DxpKpiCustomFilter[] = data.map((filter) => ({
        ...filter,
        value: filter.defaultValueOnly ?? [],
      }));
      return mappedValue;
    });
    // this.legendValueOptionsChart.set([...this.legendValueOptions()]);
    // this.legendSelectedValues.set([...this.selectedLegendValues()]);
    this.kpiCardFilters.set([...this.cardFilters()]);

    this.pageLoader = true;
    this.onSubmitClick()?.then(() => {
      if (this.editData) {
        this._router.navigate(['publish'], {
          queryParams: { objectId: this.editObjectId },
          relativeTo: this._route,
        });
      } else {
        this._router.navigate(['publish'], { relativeTo: this._route });
      }
      this.setUserConfig();
      this.userView.set(true);
      this.pageLoader = false;
      this.setBreadcrumb();
    });
  }

  goBack() {
    this.userView.set(false);
    if (this.editData) {
      this._router.navigate(['/dxp/visualization-wizard'], {
        queryParams: { objectId: this.editObjectId },
      });
    } else {
      this._router.navigate(['/dxp/visualization-wizard']);
    }

    this.setBreadcrumb();
  }

  setUserConfig() {
    const editUserAccess =   this.isEditMode &&      this.editData &&      Array.isArray(this.editData.userAccess)        ? this.editData.userAccess        : [];
    this.chartLegend.set([...this.toolBarLegend()]);
    this.legendCurrentSelected.set(this.chartLegend()?.find(element =>  element.default) ?? this.chartLegend()?.[0]);
    this.legendValueOptions.set([...(this.legendCurrentSelected()?.options ?? [])]);
    this.legendSelectedValues.set([...(this.legendCurrentSelected()?.selectedValues ?? [])]);
    this.userConfigData.set({
      onAppliedFilter: this.cardFilters() || [],
      legends: this.chartLegend() || [],
      defaultLegend: this.legendCurrentSelected() || undefined,
      legendValueOptions: this.legendValueOptions() || [],
      legendSelectedValues: this.legendSelectedValues() || [],
      graphData: this.graphData,
      title: this.cardTitle() ?? '',
      description: this.cardDescription() ?? '',
      chartName: this.chartName() || 'line', // Pass the selected chart type
      xAxisLabel: this.xAxisLabel, // Pass X-axis label
      yAxisLabel: this.yAxisLabel, // Pass Y-axis label
      // Pass user access when editing for prefill
      userAccess: editUserAccess,
    });
  }

  closePopup() {
    this.modalPopUp()?.removeModal();
    this.modalService.removeAllModal();
  }

  createKpi() {
    this.modal()?.createElement();
  }

  closeModal() {
    if (this.currentStatus() === statusContDxp.send){
      this._router.navigate(['/dxp']);
    }
    this.modalPopUp()?.removeModal();
    this.modal()?.removeModal();
    this.modalService.removeAllModal();
  }

  getFullColumnsData(editCall = false) {
    this.datasetLoading.set(true);
    if (!this.selectedProduct.sourceProductId || !this.selectedProduct.sourceAssetId) {
      this.datasetLoading.set(false);
      return;
    }
    this._subs.add(
      this._dxpIntegrationApiService
        .getMethodRequest(
          `${dxpApi.column(this.selectedProduct.sourceProductId)}${
            this.selectedProduct.sourceAssetId
          }/columns`
        )
        .subscribe({
          next: (data: DxpColumnList[]) => {
            this.fullColumnsData.set(data);
            this.legendColumn.set(
              data.filter((column) => column.data_type === 'string')
            );
            if (data && data.length > 0) {
              if ( this.editData?.visualizationConfig?.source_filter?.groups
                ?.at(0)
                ?.conditions?.length ) {
                this.filterForm = this.fb.group({
                  filters: this.fb.array([]),
                });
              }
              const processedData = data.map((item: DxpColumnList) => {
                const convertedData = {
                  ...item,
                  icon: this.getDataTypeIcon(item.data_type),
                };
                if (editCall) {
                  this.addXYVariableAndSourceFilterForEdit(convertedData);
                } else if (!this.filters.value?.length) {
                  this.filterForm = this.fb.group({
                    filters: this.fb.array([this.createFilterGroup()]),
                  });
                }
                return convertedData;
              });
              this.dataset.set(processedData);

              // Set default values for existing filters
              if (!editCall) {
                this.setDefaultValuesForExistingFilters();
              } else {
                this.setUpSourceFilter();
              }
              this.checkChangeInTheFilter();
              this.datasetLoading.set(false);
              this.pageLoader = false;
            }
          },
          error: (error) => {
            this._toasterService.error(error?.error?.message);
            this.datasetLoading.set(false);
            this.pageLoader = false;
          },
        })
    );
  }

  checkChangeInTheFilter() {
    this.filterForm.valueChanges.subscribe(() => {
      this.filterChange.set(true);
    });
  }

  addXYVariableAndSourceFilterForEdit(convertedData: DxpColumnList) {
    if (
      convertedData.name ===this.editData?.visualizationConfig?.chart_configuration?.x_axis?.axis?.column
    ) {
      this.xVariable.set([
        {
          ...convertedData,
          aggregations:
            convertedData.possible_aggregations?.x_aggregations ?? [],
          selectedAggregation:
            this.editData?.visualizationConfig?.chart_configuration.x_axis.axis
              .aggregator,
        },
      ]);
    }
    if (
      convertedData.name ===this.editData?.visualizationConfig?.chart_configuration?.y_axis?.axis?.column
    ) {
      this.yVariable.set([
        {
          ...convertedData,
          aggregations:
            convertedData.possible_aggregations?.y_aggregations ?? [],
          selectedAggregation:
            this.editData?.visualizationConfig?.chart_configuration.y_axis.axis
              .aggregator,
        },
      ]);
    }


    this.editData?.visualizationConfig?.source_filter?.groups
      ?.at(0)
      ?.conditions?.forEach((sourceFilter) => {
        if (sourceFilter.column === convertedData.name) {
          this.filters.push(this.editFilterGroup(convertedData, sourceFilter));
        }
      });
  }

  setDefaultValuesForExistingFilters() {
    const columnsData = this.fullColumnsData();
    if (columnsData.length > 0) {
      // Update existing filters with default values if they don't have column selected
      for (let i = 0; i < this.filters.length; i++) {
        const filter = this.filters.at(i);
        filter.get('column')?.setValue('');
        filter.get('comparator')?.setValue('');
      }
    }
  }

  getDataTypeIcon(dataType: string) {
    const type = dataType.toLowerCase();
    if (this.dataTypesList.numeric.includes(type)) {
      return 'ifp-icon-number-type';
    } else if (this.dataTypesList.character.includes(type)) {
      return 'ifp-icon-char-type';
    }
    return 'ifp-icon-calender';

  }

  onSelectedProduct(selectedProduct: SelectedProductWithoutId) {
    this.selectedProduct = selectedProduct;
    this.getFullColumnsData();
    this.closeModal();
    // this.checkProductSelection();
  }



  onColumnSelected(column: any) {
    this.selectedColumn.set(column);
    this.comparators.set(column?.available_comparators || []);
  }

  onColumnSelectedForFilter(column: any, filterIndex: number) {
    this.storeDistinctValue.loadByQuery({ columnName: column?.name, product: this.selectedProduct });
    const filter = this.filters.at(filterIndex);
    filter.get('column')?.setValue(column);
    // Reset comparator when column changes
    const defaultComparator =      column?.available_comparators && column.available_comparators.length > 0        ? column.available_comparators[0]        : '';
    filter.get('comparator')?.setValue(defaultComparator);
    filter.get('value')?.setValue(undefined);
    // If user is editing (after badges applied), keep the badges in sync with form edits
    const currentApplied = this.appliedFilters();
    if (currentApplied.length > filterIndex) {
      const updated = [...currentApplied];
      updated[filterIndex] = {
        ...updated[filterIndex],
        column,
        comparator: defaultComparator,
        data_type:
          typeof column === 'object' && column !== null ? column.data_type : '',
      };
      this.appliedFilters.set(updated);
      this.setUpSourceFilter();
    }
  }

  getComparatorsForColumn(column: any) {
    if (!column) {
      return [];
    }
    return column.available_comparators || [];
  }

  removeXAxisVariable() {
    this.xVariable.set([]);
    this.axisForm.get('xAxisVariable')?.patchValue({
      name: '',
      data_type: '',
      selectedAggregation: '',
    });
  }

  removeYAxisVariable() {
    this.yVariable.set([]);
    this.axisForm.get('yAxisVariables')?.patchValue({
      name: '',
      data_type: '',
      selectedAggregation: '',
    });
  }


  // Handle drag and drop for X variable
  onXVariableDrop(event: any) {
    if (event.previousContainer !== event.container) {
      const draggedItem = event.previousContainer.data[event.previousIndex];
      const defaultAggregation =        draggedItem.possible_aggregations?.x_aggregations &&        draggedItem.possible_aggregations.x_aggregations.length > 0          ? draggedItem.possible_aggregations.x_aggregations[0]          : '';
      const itemWithAggregations = {
        ...draggedItem,
        aggregations: draggedItem.possible_aggregations?.x_aggregations || [],
        selectedAggregation: defaultAggregation,
      };
      this.xVariable.set([itemWithAggregations]);
      // Update axisForm xAxisVariable
      this.axisForm.get('xAxisVariable')?.patchValue({
        name: itemWithAggregations.name,
        data_type: itemWithAggregations.data_type,
        selectedAggregation: defaultAggregation,
      });
    }
  }

  // Handle drag and drop for Y variable
  onYVariableDrop(event: any) {
    if (event.previousContainer !== event.container) {
      const draggedItem = event.previousContainer.data[event.previousIndex];
      const defaultAggregation =        draggedItem.possible_aggregations?.y_aggregations &&        draggedItem.possible_aggregations.y_aggregations.length > 0          ? draggedItem.possible_aggregations.y_aggregations[0]          : '';
      const itemWithAggregations = {
        ...draggedItem,
        aggregations: draggedItem.possible_aggregations?.y_aggregations || [],
        selectedAggregation: defaultAggregation,
      };
      // Only allow one Y variable: replace any existing item
      this.yVariable.set([itemWithAggregations]);
      // Update axisForm yAxisVariables FormArray

      this.axisForm.get('yAxisVariables')?.patchValue({
        name: itemWithAggregations.name,
        data_type: itemWithAggregations.data_type,
        selectedAggregation: itemWithAggregations.selectedAggregation,
      });
    }
  }

  // Remove item from X variable
  removeXVariable() {
    this.xVariable.set([]);
  }

  // Remove item from Y variable
  removeYVariable(index: number) {
    this.yVariable.update((current) => {
      const newArray = [...current];
      newArray.splice(index, 1);
      return newArray;
    });
  }

  // Handle aggregation selection for X variable
  onXVariableAggregationSelected(aggregation: string) {
    // Update the form control for X variable aggregation
    const xAxisVariable = this.axisForm.get('xAxisVariable');
    if (xAxisVariable) {
      xAxisVariable.get('selectedAggregation')?.setValue(aggregation);
    }
    // Also update the signal for UI sync if needed
    this.xVariable.update((current) => {
      if (current.length > 0) {
        const updatedItem = { ...current[0], selectedAggregation: aggregation };
        return [updatedItem];
      }
      return current;
    });
  }

  // Handle aggregation selection for Y variable
  onYVariableAggregationSelected(aggregation: string) {
    // Update the form control for Y variable aggregation
    const yAxisVariables = this.axisForm.get('yAxisVariables') as FormGroup;
    if (yAxisVariables) {
      yAxisVariables.get('selectedAggregation')?.setValue(aggregation);
    }
    // Also update the signal for UI sync if needed
    this.yVariable.update((current) => {
      if (current.length > 0) {
        const updatedItem = { ...current[0], selectedAggregation: aggregation };
        return [updatedItem];
      }
      return current;
    });
  }


  // Method to handle user config changes from user config component
  onUserConfigChanged(updatedConfig: any) {
    this.userConfigData.set(updatedConfig);
  }

  onSubmitClick(preview = false) {
    this.querySubscription?.unsubscribe();
    this.graphDatasetLoading.set(true);
    // Get form values
    const axisFormValue = this.axisForm.value;

    // Build X axis from form
    const xAxisVariable = axisFormValue.xAxisVariable;
    const x_axis :{
    label: string;
    axis?: {
        column: string;
        data_type: string;
        aggregator: string;
    } | null;
}= {
  label: axisFormValue.xAxisLabel,
  axis:
        xAxisVariable && xAxisVariable.name          ? {
          column: xAxisVariable.name,
          data_type: xAxisVariable.data_type,
          aggregator: xAxisVariable.selectedAggregation || '',
        }          : null,
};

    if (x_axis.axis === null) {
      delete x_axis.axis;
    }




    // Build Y axis from form
    const yAxisVariables = axisFormValue.yAxisVariables;

    const y_axis:{
    label: string;
    axis?: {
      column: string;
        data_type: string;
        aggregator: string;
    } | null;}= {
      label: axisFormValue.yAxisLabel,
      axis:
        yAxisVariables && yAxisVariables.name          ? {
          column: yAxisVariables.name,
          data_type: yAxisVariables.data_type,
          aggregator: yAxisVariables.selectedAggregation || '',
        }          : null,
    };

    if (y_axis.axis === null) {
      delete y_axis.axis;
    }
    let currentLegend: { column: string; data_type: string; selected_values: string[]; default: boolean } | null= null;
    // For save/persist, always store toolbar default legend
    if (preview && this.legendCurrentSelected()) {
      currentLegend = {
        column: this.legendCurrentSelected()?.name ?? '',
        data_type: this.legendCurrentSelected()?.data_type || 'string',
        selected_values: this.legendSelectedValues().map(value => value.value) || [],
        default: this.legendCurrentSelected()?.default || false,
      };

    } else {
      this.toolBarLegend().forEach((legend) => {
        currentLegend = {
          column: legend.name,
          data_type: legend.data_type || 'string',
          selected_values: legend.selectedValues?.map(value => value.value) || [],
          default: legend.default || false,
        };

      });
    }

    const currentFilter = preview ? this.cardFilters() : this.kpiCardFilters();
    const kpiFiltersArray: ConditionDxp[] = [];
    currentFilter?.forEach(data => {
      if (data.selectedValueArray && data.selectedValueArray.length > 0){
        kpiFiltersArray.push(
          {
            column: data.column,
            comparator: data.selectedValueArray?.length == 1 ? 'eq': 'in',
            value: data.selectedValueArray?.length == 1 ? data.selectedValueArray[0] : data.selectedValueArray,
            data_type: data.data_type,
            inferred_dtb_dt_format: data?.inferred_dtb_dt_format
          }
        );
      }
    });


    const payload = {
      filters: {
        groups: [
          {
            conditions: kpiFiltersArray,
            operator: 'and',
          },
        ],
        global_operator: 'and',
      },
      x_axis,
      y_axis,
      legend: currentLegend,
      // chart_type: this.chartName(),
      // "chart_type": "line",
    };
    // --- API call logic ---
    if (sourceFilterDxpPayload() &&sourceFilterDxpPayload()?.groups?.[0]) {
      payload.filters.groups.unshift(sourceFilterDxpPayload()!.groups[0]);
    }
    const productId = this.selectedProduct.sourceProductId;
    const assetId = this.selectedProduct.sourceAssetId;
    if (!productId || !assetId) {
      return;
    }
    const apiUrl = dxpApi.query(productId, assetId);
    this.graphData = {
      category: [],
      series: [],
      xAxisLabel: '',
      yAxisLabel: '',
      unit: ''
    };
    // Return a promise for the API call
    return new Promise<DxpPlotData>((resolve) => {

      this.querySubscription = this._dxpIntegrationApiService.postMethodRequest(apiUrl, payload).subscribe({
        next: (response: DxpPlotData) => {
          this.graphData = this.transformChartData(response);
          this.graphDatasetLoading.set(false);
          return resolve(response);
        },
        error: (error) => {
          this.graphDatasetLoading.set(false);
          return resolve(error);
        },
      });
    });
  }

  onPageChange(event: number) {
    this.offsetPage = event + 1;
    this.page = (event / this.limit) + 1;
    this.tableView(this.page, this.limit);
  }

  limitChanged(event: number) {
    this.offsetPage = 1;
    this.page = 1;
    this.limit = event;
    this.tableView(this.page, this.limit);
  }

  tableOpen() {
    this.offsetPage = 1;
    this.page = 1;
    this.totalCount = 0;
    this.modalTable()?.createElement();
  }

  tableView(page = 1, limit = 10) {
    this.sourceSampleData.set([]);
    const params= {
      page: page,
      limit: limit
    };
    this.tableLoader.set(true);
    const productId = this.selectedProduct.sourceProductId;
    const assetId = this.selectedProduct.sourceAssetId;
    if (!productId || !assetId) {
      return;
    }
    const apiUrl = `${dxpApi.query(productId, assetId)}?disposition=INLINE&format=JSON_ARRAY&${this._dxpIntegrationApiService.getParams(params)}`;
    const payload = {
      filters: sourceFilterDxpPayload()
      // "chart_type": "line",
    };
    // Return a promise for the API call
    this.tableSubscription?.unsubscribe();

    this.tableSubscription =  this._dxpIntegrationApiService.postMethodRequest(apiUrl, payload).subscribe({
      next: (data: {data : Record<string, string>[], total_count: number}) => {
        this.totalCount = data.total_count || 0;
        if (Array.isArray(data.data) && data.data.length) {
          this.sourceSampleData.set(
            data.data.map(element => Object.keys(element).map(key => ({
              key,
              title: key,
              value: element[key],
              type: 'default'
            }))
            )
          );
          this.sourceDataHead.set(Object.keys(data.data[0]));
        }
        this.tableLoader.set(false);
      },
      error: () => {
        this.tableLoader.set(false);
      },
    });


  }

  closeTableModal() {
    this.modalTable()?.removeModal();
    this.modalService.removeAllModal();
  }

  transformChartData(apiData: DxpPlotData): ConvertedChartData {
    const category: string[] = apiData.series.xAxis.categories;
    const series = apiData?.series?.series;
    const xAxisLabel = apiData.xAxis;
    const yAxisLabel = apiData.yAxis;
    return { category, series, xAxisLabel, yAxisLabel, unit: this.axisForm.get('unit')?.value ?? ''  };
  }

  // Getter for xAxisVariable FormGroup
  get xAxisVariableControl(): FormGroup {
    return this.axisForm.get('xAxisVariable') as FormGroup;
  }

  // Getter for yAxisVariables FormArray
  get yAxisVariablesControl(): FormGroup {
    return this.axisForm.get('yAxisVariables') as FormGroup;
  }



  submitForApproval(mode: 'draft' | 'save' | 'send' = statusContDxp.save as 'save') {

    if (this.cardTitle() ==='' || this.cardTitle().length > 120 || this.cardDescription().length >  400) {

      this.error.set(true);
      this.sectionRight()?.nativeElement.scrollTo({
        top: this.sectionRight()?.nativeElement.scrollHeight,
        behavior: 'smooth'
      });
      if (this.cardTitle() ===''){
        this._toasterService.error('Please enter title');
      } else if (this.cardTitle().length > 120){
        this._toasterService.error('Title should not exceed 120 characters');
      } else if (this.cardDescription().length >  400){
        this._toasterService.error('Description should not exceed 400 characters');
      }
      return;
    }
    const axisFormValue = this.axisForm.value;
    const xAxisVariable = axisFormValue.xAxisVariable;
    const yAxisVariables = axisFormValue.yAxisVariables;


    const payload: {
      title: string;
      subTitle: string;
      icon: string;
      sourceAssetId: string;
      sourceProductId: string;
      visualizationConfig: {
        source_filter: {
          groups: {
            conditions: {
              column: any;
              comparator: any;
              data_type: any;
              value: any;
            }[];
            operator: string;
          }[];
          global_operator: string;
        } | undefined;
        chart_configuration: {
          default_chart_type: string;
          unit: any;
          x_axis?: {
            label: any;
            axis: {
              column: any;
              data_type: any;
              aggregator: any;
            } | null;
          };
          y_axis?: {
            label: any;
            axis: {
              column: any;
              data_type: any;
              aggregator: any;
            } | null;
          };
          filterPanel: {
            column: string;
            label: string;
            default: string[];
            data_type: string | undefined;
          }[];
          legendPanel: object[];
        };
      };
      userIds: (string | undefined)[];
    } = {
      // Prefer live title/description from the Preview (wizard view). Fallback to User Config input (publish view).
      title: (this.cardTitle() ?? '').trim(),
      subTitle: (this.cardDescription() ?? '').trim(),
      icon: this.selectedIcon(),
      sourceAssetId: this.selectedProduct.sourceAssetId ?? '',
      sourceProductId: this.selectedProduct.sourceProductId ?? '',
      visualizationConfig: {
        source_filter: sourceFilterDxpPayload(),
        chart_configuration: {
          default_chart_type: this.chartName(),
          unit: axisFormValue.unit || '',
          filterPanel: [
            ...(this.cardFilters() || []).map((f) => ({
              column: f.column,
              label: f.filterLabel || f.column,
              comparator: 'eq',
              default: f.defaultValueOnly,
              data_type: f.data_type || undefined,
              inferred_dtb_dt_format: f?.inferred_dtb_dt_format
            })),
          ],
          legendPanel: (() => {
            const toolbarDefaultLegend: { column: string; data_type: string; selected_values: string[]; default: boolean }[] = [];
            // For save/persist, always store toolbar default legend
            this.toolBarLegend().forEach((legend) => {
              toolbarDefaultLegend.push({
                column: legend.name,
                data_type: legend.data_type || 'string',
                selected_values: legend.selectedValues?.map(value => value.value) || [],
                default: legend.default || false,
              });

            });
            return toolbarDefaultLegend;
          })(),
        },
      },
      userIds: this.sharedUserListSelected()?.map((user) => user.id) || [],
    };
    if (yAxisVariables && yAxisVariables.name) {
      payload.visualizationConfig.chart_configuration['y_axis'] = {
        label: axisFormValue.yAxisLabel,
        axis:
          yAxisVariables && yAxisVariables.name            ? {
            column: yAxisVariables.name,
            data_type: yAxisVariables.data_type,
            aggregator: yAxisVariables.selectedAggregation || '',
          }            : null,
      };
    }
    if (xAxisVariable && xAxisVariable.name) {
      payload.visualizationConfig.chart_configuration['x_axis'] = {
        label: axisFormValue.xAxisLabel,
        axis:
          xAxisVariable && xAxisVariable.name            ? {
            column: xAxisVariable.name,
            data_type: xAxisVariable.data_type,
            aggregator: xAxisVariable.selectedAggregation || '',
          }            : null,
      };
    }
    // Build URL with query params
    const params: string[] = [];
    if (mode === statusContDxp.draft) {
      params.push('is_draft=true');
    } else if (mode === statusContDxp.send) {
      params.push('send_for_approval=true');
    }

    // In edit mode, use path /entity-kpi/:objectId and append any params AFTER it
    let endpoint =      this.isEditMode && this.editObjectId        ? dxpApi.entityKpiChart(this.editObjectId)        : dxpApi.listEntityKpi;
    if (params.length) {
      endpoint = `${endpoint}?${params.join('&')}`;
    }
    // Choose method: PUT in edit mode, POST otherwise
    const request$ = this.isEditMode      ? this._dxpIntegrationApiService.putMethodRequest(endpoint, payload)      : this._dxpIntegrationApiService.postMethodRequest(endpoint, payload);

    this._subs.add(
      request$.subscribe({
        next: (value: ResponseForSaveKpi) => {
          const msg =            mode === statusContDxp.draft              ? this.isEditMode                ? 'Draft updated successfully'                : 'KPI Saved as Draft!'              : mode === statusContDxp.send              ? 'Submitted for approval successfully'              : this.isEditMode              ? 'Visualization updated successfully'              : 'Visualization saved successfully';
          this._toasterService.success(msg);
          this.createUpdatePop(mode);
          if (value?.objectId){
            this.isEditMode = true;
            this.editObjectId = value.objectId;
          }
        },
        error: (error) => {
          this._toasterService.error(
            error?.error?.message || 'Failed to submit Entity KPI'
          );
        },
      })
    );
  }

  onCancelFilterEdit() {
    this.selectedFilterIndex = -1;
  }

  // New methods for the updated filter UI
  toggleFilterSection(): void {
    this.isFilterSectionExpanded = !this.isFilterSectionExpanded;
  }

  addSecondFilter(): void {
    if (this.filters.length < 2) {
      this.filters.push(this.createFilterGroup());
    }
  }

  removeSecondFilter(emit = false): void {
    if (this.filters.length > 1) {
      // Remove the second row from the form
      this.filters.removeAt(1);

      // If we were editing the second filter, exit edit mode
      if (this.selectedFilterIndex === 1) {
        this.selectedFilterIndex = -1;
      }
      // Recalculate validity to update button states
      this.filterForm.updateValueAndValidity({ emitEvent: true });
    }
    this.filterChange.set(emit);
  }

  setLogicalOperator(operator: 'AND' | 'OR'): void {
    this.logicalOperator = operator;
  }

  applyFilters(): void {
    this.currentFilterClick.set({event: this.currentFilterClickValues.apply, index: 0});
    if (this.toolBarLegend().length >0 || this.kpiCardFilters().length >0){
      this.createSourcePopup();
      return;
    }
    // Validate: if two rows are present, both must be fully filled
    if (this.filters.length === 2) {
      if (!this.isFilterValid(0) || !this.isFilterValid(1)) {
        return; // do nothing until both are complete
      }
    } else if (!this.isFilterValid(0)) {
      return;

    }

    const filtersToApply: any[] = [];

    // Read current rows in order and keep values for in-place edit
    const firstFilter = this.filters.at(0);
    const firstColumn = firstFilter.get('column')?.value;
    filtersToApply.push({
      column: firstColumn,
      comparator: firstFilter.get('comparator')?.value,
      value: firstFilter.get('value')?.value,
      data_type:
        typeof firstColumn === 'object' && firstColumn !== null          ? firstColumn.data_type          : '',
    });

    if (this.filters.length > 1) {
      const secondFilter = this.filters.at(1);
      const secondColumn = secondFilter.get('column')?.value;
      filtersToApply.push({
        column: secondColumn,
        comparator: secondFilter.get('comparator')?.value,
        value: secondFilter.get('value')?.value,
        data_type:
          typeof secondColumn === 'object' && secondColumn !== null            ? secondColumn.data_type            : '',
      });
    }

    // Check if both filters have the same column, comparator, and value
    if (filtersToApply.length === 2) {
      const [filter1, filter2] = filtersToApply;
      const sameColumn = filter1.column?.name === filter2.column?.name ||                         filter1.column === filter2.column;
      const sameComparator = filter1.comparator?.value === filter2.comparator?.value ||                             filter1.comparator === filter2.comparator;
      const sameValue = filter1.value?.toString().toLowerCase() === filter2.value?.toString().toLowerCase();

      if (sameColumn && sameComparator && sameValue) {
        this._toasterService.warning('The same filter has been applied.');
      }
    }




    // Apply filters and store operator; do not clear the form values
    this.appliedFilters.set(filtersToApply);
    this.appliedLogicalOperator = this.logicalOperator;
    this.setUpSourceFilter();
    this.filterChange.set(false);
  }

  setUpSourceFilter() {
    const sourceFiltersArray = (this.appliedFilters() || []).map(
      (f: SourceFiltersDxp) => ({
        column:
          typeof f.column === 'object' && f.column !== null ? f.column.name : f.column,
        comparator:
          typeof f.comparator === 'object' && f.comparator !== null ? f.comparator.value || f.comparator.display_name : f.comparator,
        data_type: f.data_type,
        value: f.value,
        inferred_dtb_dt_format: f.column.inferred_dtb_dt_format
      })
    );
    const sourceOperator = sourceFiltersArray.length > 1 ? (
      this.appliedLogicalOperator || this.logicalOperator || 'and'
    ).toLowerCase() : 'and';
    if (sourceFiltersArray && sourceFiltersArray.length) {
      sourceFilterDxpPayload.set({
        groups: [
          {
            conditions: sourceFiltersArray,
            operator: sourceOperator,
          },
        ],
        global_operator: 'and',
      });
    } else {
      sourceFilterDxpPayload.set(undefined);
    }
  }

  removeAppliedFilter(index: number): void {
    this.currentFilterClick.set({event: this.currentFilterClickValues.remove, index: index});
    if (this.toolBarLegend().length >0 || this.kpiCardFilters().length >0){
      this.createSourcePopup();
      return;
    }
    // Remove from applied list
    this.appliedFilters.update((filters) => filters.filter((_, i) => i !== index));

    // Also reflect the removal in the filter form above
    if (index === 1 && this.filters.length > 1) {
      // Removing second filter: drop the second row entirely
      this.removeSecondFilter();
      this.setUpSourceFilter();
      return;
    }

    if (index === 0) {
      this.filters.at(0).patchValue({
        column: this.filters.at(1).value?.column || '',
        comparator: this.filters.at(1).value?.comparator || '',
        value: this.filters.at(1).value?.value || '',
      });
      this.filters.removeAt(1);
    }
    this.setUpSourceFilter();
    this.filterChange.set(false);
  }

  // Enforce alphabetic-only input in Unit field at typing time
  onUnitInput(event: Event) {
    const input = event.target as HTMLInputElement;
    // Allow English and Arabic letters and spaces only
    const cleaned = input.value.replace(/[^\p{Script=Arabic}A-Za-z\s]/gu, '');
    if (cleaned !== input.value) {
      input.value = cleaned;
      this.axisForm.get('unit')?.setValue(cleaned, { emitEvent: false });
    }
  }

  createSourcePopup(currentStatus = 'changeSource'){
    this.image.set(false);
    this.updateTitle.set('Change Source Data Filter');
    this.updateIcon.set('ifp-icon ifp-icon-exclamation ');
    this.updateDec.set(
      'Changing the source data filter will reset any configured dynamic filters and legends for this card. Do you want to continue?'
    );
    this.buttonLabel.set('Proceed');
    this.buttonSecondLabel.set('Cancel');
    this.enableDoubleButton.set(true);
    this.successBackground.set(false);
    this.currentStatus.set(currentStatus);
    this.modalPopUp()?.createElement();
  }

  createUpdatePop(status: string) {
    this.image.set(false);
    this.enableDoubleButton.set(false);
    this.currentStatus.set(status);
    this.enableCloseButton.set(true);
    this.successBackground.set(true);
    if (status === statusContDxp.cancel) {
      this.successBackground.set(false);
      this.updateDec.set(
        'Proceeding ahead will cause you to lose all changes.'
      );
      this.updateTitle.set('Are you sure you want to cancel?');
      this.updateIcon.set('ifp-icon ifp-icon-exclamation ');
      this.buttonLabel.set('Yes');
      this.buttonSecondLabel.set('No');
      this.enableDoubleButton.set(true);
      this.modalPopUp()?.createElement();
    } else if (status === statusContDxp.draft) {
      this.updateDec.set(
        'Your KPI has been saved as a draft. You can view it under the Drafts section of the My KPIs tab.'
      );
      this.updateTitle.set('KPI Saved as Draft!');
      this.updateIcon.set('ifp-icon ifp-icon-document-tick');
      this.buttonLabel.set('View in Drafts');

      this.modalPopUp()?.createElement();
    } else if (status === statusContDxp.save) {
      this.updateDec.set(
        'The KPI you created have been saved successfully. You can view under the Completed section of the My KPIs tab.'
      );
      this.updateTitle.set('KPI Saved!');
      this.updateIcon.set('../../../assets/tick-animation.gif');
      this.buttonLabel.set(
        !this.editData?.approvalRequest?.approvalActions?.revert?.visible          ? 'View in My KPI'          : 'View in Reverted Tab'
      );
      this.image.set(true);
      this.modalPopUp()?.createElement();
    } else if (status === statusContDxp.send) {
      this.updateDec.set(
        'Your visualization has been submitted for approval and will be published once approved by the Visualization Approver. You can track its status under the \'Pending\' tab.'
      );
      this.updateTitle.set('Submitted Successfully');
      this.updateIcon.set('ifp-icon ifp-icon-long-time');
      this.buttonLabel.set('Create New KPI');
      this.enableDoubleButton.set(true);
      this.buttonSecondLabel.set('Go To Pending Tab');
      this.modalPopUp()?.createElement();
    }
  }

  popupPrimaryBtnClick() {
    if (this.currentStatus() === statusContDxp.cancel) {
      this.routeToDxp();
    } else if (this.currentStatus() === statusContDxp.draft) {
      this.routeToDxp(statusContDxp.draft);
    } else if (this.currentStatus() === statusContDxp.save) {
      this.routeToDxp(statusContDxp.save);
    } else if (this.currentStatus() === statusContDxp.send) {
      this.isEditMode = false;
      this.userView.set(false);
      this._router.navigate(['/dxp/visualization-wizard']);
      this.dataset.set([]);
      this.pageData.set([
        { title: 'Home', route: '/home' },
        { title: 'Government Affairs', route: '/dxp' },
        { title: 'Visualization Wizard', route: '' },
      ]);
      this.modalPopUp()?.removeModal();
      this.isEditMode = false;
      this.editObjectId = null;
      this.editData = null;
      this.deleteOutput();

      this.createKpi();
    } else if (this.currentStatus() === 'changeSource') {
      this.ResetToolbar();
      this.closePopup();
    } else if (this.currentStatus() === 'clearSource') {
      this.ResetToolbar();
      this.clearFilters(false);
      this.closePopup();
    }
    this.currentStatus.set('');
  }

  popupSecondaryBtnClick() {
    if (this.currentStatus() === statusContDxp.save) {
      this.routeToDxp();
    } else if (this.currentStatus() === statusContDxp.send) {
      this.routeToDxp(statusContDxp.send);
    } else {
      this.closeModal();
    }
    this.currentStatus.set('');
  }

  routeToDxp(status?: string) {
    if (status === statusContDxp.draft) {
      this._router.navigate(['/dxp'], {
        queryParams: { tab: 'my-kpis-draft', sideMenu: 'myKpis' },
      });
    } else if (status === statusContDxp.save) {
      if (this.editData?.approvalRequest?.approvalActions?.revert?.visible) {
        this._router.navigate(['/dxp'], {
          queryParams: { tab: 'reverted', sideMenu: 'approvalStatus' },
        });
      } else {
        this._router.navigate(['/dxp'], {
          queryParams: { tab: 'my-kpis-complete', sideMenu: 'myKpis' },
        });
      }
    } else if (status === statusContDxp.send) {
      this._router.navigate(['/dxp'], {
        queryParams: { tab: 'pending', sideMenu: 'approvalStatus' },
      });
    } else {
      this._router.navigate(['/dxp']);
    }
  }

  ngOnDestroy(): void {
    this.formSubscription?.unsubscribe();
    this._subs.unsubscribe();
    this.closeModal();
    this.closePopup();
    this.closeTableModal();
    this.tableSubscription?.unsubscribe();
    sourceFilterDxpPayload.set(undefined);
    this.querySubscription?.unsubscribe();
    if (this.filterColumnChangeSub) {
      this.filterColumnChangeSub.unsubscribe();
    }
  }
}
