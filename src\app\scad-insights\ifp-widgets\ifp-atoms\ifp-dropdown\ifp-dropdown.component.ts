import { CommonModule, DOCUMENT } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, HostListener, Inject, Input, OnChanges, OnDestroy, OnInit, Output, Renderer2, ViewChild, SimpleChanges, input, viewChild } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { OutsideClickDirective } from 'src/app/scad-insights/core/directives/outsideClick.directive';
import { IfpButtonComponent } from '../ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpCheckboxComponent } from '../ifp-checkbox/ifp-checkbox.component';
import { cloneDeep } from 'lodash';
import { ControlValueAccessor, FormControl, NG_VALUE_ACCESSOR, ReactiveFormsModule } from '@angular/forms';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IfpCheckBoxComponent } from '../ifp-check-box/ifp-check-box.component';
import { slideDownDropDownAnimations } from 'src/app/scad-insights/animation/slideDown.animation';
import { CustomLetter } from '../../../core/pipes/firstLetter.pipe';
import { PopService } from 'src/app/scad-insights/core/services/popperService/popper.service';


@Component({
  selector: 'app-ifp-dropdown',
  templateUrl: './ifp-dropdown.component.html',
  styleUrls: ['./ifp-dropdown.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule, TranslateModule, OutsideClickDirective, IfpButtonComponent, IfpCheckboxComponent, ReactiveFormsModule,
    IfpTooltipDirective, IfpCheckBoxComponent, CustomLetter],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: IfpDropdownComponent,
      multi: true
    }
  ],
  animations: [
    slideDownDropDownAnimations
  ]
})
export class IfpDropdownComponent implements OnInit, OnDestroy, OnChanges, ControlValueAccessor {
  @Input() isInline: boolean = false;
  @Input() dropDownItems: any[] = [];
  @Input() isMulti = false;
  @Input() placeHolder = '';
  @Input() key!: string;
  @Input() isDownload: boolean = false;
  @Input() outputKey!: string;
  @Input() title!: string;
  @Input() showTitle: boolean = false;
  @Input() multiDefaultSelect: boolean = false;
  @Input() singleDefaultSelect: boolean = true;
  @Input() pointerPositions: any = {};
  @Input() selectedValues: any[] = [];
  @Input() isDownloadDropdownHide: boolean = false;
  @Input() selectedValue!: string | any;
  @Input() output: 'full' | 'value' = 'full';
  @Input() limit: number | null = null;
  @Input() isCheckedClear: boolean = false;
  @Input() checkBoxKey = '';
  @Input() filterLabel!: string;
  @Input() isExtense: boolean = false;
  @Input() boarderBottom = false;
  @Input() searchEnable = false;
  @Input() selectAllBox = false;
  @Input() selectAll = false;
  @Input() iconEnable = false;
  @Input() uniqCheck = false;
  @Input() leftEnable = false;
  @Input() iconClass = '';
  @Input() path: string = '';
  @Input() isBehaviourAutoSwitch = false;
  @Input() analyticClasses: string = '';
  @Input() isEmitEventAll: boolean = false;
  @Input() formDisable = false;
  @Input() disableSingleValue = false;
  @Input() triggerSelectAllEvent: boolean = false;
  @Input() disableDropdown: boolean = false;
  @Input() chartType: string = 'line';
  @Input() detectChange!: boolean;
  @Input() minLimit!: number;
  @Input() required!: boolean;
  @Input() isAppendBody: boolean = false;
  @Input() subMenu: any[] = [];
  @Input() disableOption:string = '';
  @Input() disableTranslation: boolean = false;
  @Input() defaultValue ?:string;
  @Output() dropDownItemClicked: EventEmitter<any> = new EventEmitter<string>;
  @Output() dropDownItemClickedWithIndex: EventEmitter<any> = new EventEmitter<string>;
  @Output() dropDownItemMultiClicked: EventEmitter<any[]> = new EventEmitter<any[]>;
  @Output() dropDownMultiEvent: EventEmitter<any> = new EventEmitter<any>;
  @Output() selectedEventData = new EventEmitter<boolean>;
  @ViewChild('downloadDrop', { static: false }) downloadDrop!: ElementRef;
  // @ViewChild('dropTitle', { static: false }) dropTitle!: ElementRef;
  @ViewChild('dropdown', { static: false }) dropdown!: ElementRef;
  @ViewChild('dropdownList', { static: false }) dropdownList!: ElementRef;
  public dropTitle = viewChild<ElementRef>('dropTitle');
  public dropdownListInner = viewChild<ElementRef>('dropdownListInner');
  public childElement!: HTMLElement | undefined;
  public dropdownActive: boolean = false;
  public buttonClass = buttonClass;
  public search = new FormControl();
  public selectedOutputValue: any = [];
  public searchResult: any[] = [];
  public isChangeCount: number = 0;
  public singleKey: any;
  public dropDownTop: number = 0;
  public dropDownLeft: number = 0;
  public setOutput = input(false);
  public bodyBind = input<boolean>(false);
  public autoCalculatePosition = input<boolean>(false);
  public enableSingleValueSelection = input<boolean>(true);
  public instance!:{destroy : () => object, update: () => object, forceUpdate: () => object};
  public zIndex = input<number>(3);
  public disableTitleTranslation = input<boolean>(false);

  constructor(private _renderer: Renderer2, @Inject(DOCUMENT) private _document: Document, private _cdr: ChangeDetectorRef, private _elementRef: ElementRef, private _tran: TranslateService) {
    this.removeDropDown();
  }



  onItemClicked(event: any, index: number) {
    // Add small delay to ensure proper event handling
    setTimeout(() => {

      this.selectedValue = this.output === 'full' ? event : (this.outputKey ? event[this.outputKey] : event);
      if (!this.isMulti) {
        this.dropdownActive = !this.dropdownActive;
        if (!this.dropdownActive) {
          this.search.setValue('');
        }
      }
      if (this.childElement) {
        this.removeDropDown();
      }
      if (this.isBehaviourAutoSwitch) {
        event = this.selectedValues.toString();
      }
      this.dropDownItemClicked.emit(event);
      this.dropDownItemClickedWithIndex.emit({index: index, item: event});
      if (this.bodyBind() && !this.isMulti) {
        this.removeDropdownList();
      }
      this._cdr.detectChanges();
    }, 10);
  }


  onChange = (_quantity: any) => { };

  onTouched = () => { };

  touched = false;

  disabled = false;



  writeValue(selectedValue: any) {
    this.selectedValues = [];
    this.selectedOutputValue = selectedValue || (this.isMulti ? [] : null);
    if (this.isMulti && selectedValue?.length) {
      selectedValue.forEach((element: any) => {
        const index = this.dropDownItems.findIndex(data => this.output === 'full' ? data === element : data[this.outputKey] === element);
        this.selectedValues.push(this.dropDownItems[index]);
      });
    } else {
      this.selectedValue = selectedValue;
    }
    this._cdr.detectChanges();
  }

  registerOnChange(onChange: any) {
    this.onChange = onChange;
  }

  registerOnTouched(onTouched: any) {
    this.onTouched = onTouched;
  }

  markAsTouched() {
    if (!this.touched) {
      this.onTouched();
      this.touched = true;
    }
  }


  emitEvent(event: string) {
    if (this.isDownload) {
      this.dropDownItemClicked.emit(event);
    }
  }

  onItemMultiClicked(event: any) {
    if ((this.outputKey && this.selectedOutputValue?.length) ? this.selectedOutputValue?.includes(event[this.outputKey]) : this.selectedValues?.includes(event)) {
      if (this.minLimit && this.minLimit === this.selectedValues.length) {
        return;
      }
      const index = this.selectedValues.findIndex((dataValue: any) => this.outputKey ? event[this.outputKey] === dataValue[this.outputKey] : dataValue === event);
      if (index > -1) {
        this.selectedValues = cloneDeep(this.selectedValues);
        this.selectedValues.splice(index, 1);
      }
    } else {
      if (this.limit && this.limit <= this.selectedValues.length) {
        return;
      }
      if (this.isMulti) {
        this.selectedValues.push(event);
      } else {
        this.selectedValues = [];
        this.selectedValues.push(event);
      }
    }
    if (this.selectedValues?.length <= 0 && this.multiDefaultSelect) {
      if (this.defaultValue) {
        this.selectedValues.push(this.defaultValue);
      } else  {
        this.selectedValues.push(this.dropDownItems?.[0]);
      }

    }
    this.selectedOutputValue = this.selectedValues.map(data => this.output === 'full' ? data : data[this.outputKey]);
    this.onChange(this.isMulti ? this.selectedOutputValue : event);
    this.onTouched();
    this.dropDownItemMultiClicked.emit(this.isMulti ? this.selectedOutputValue : event);
    this.dropDownMultiEvent.emit(this.isMulti ? { value: this.selectedOutputValue, event: event, selectedValues: this.selectedValues } : { value: event });
    this._cdr.detectChanges();
  }

  remove() {
    // this.dropDownTop = this.dropTitle.nativeElement.getBoundingClientRect().y+35;
    // this.dropDownLeft = this.dropTitle.nativeElement.getBoundingClientRect().x;
    this.search = new FormControl();
    if (this.searchEnable) {
      this.searchResult = cloneDeep(this.dropDownItems);
    }
  }


  selectAllEvent(event: boolean) {
    this.selectAll = event;
    this.selectedValues = [];
    this.selectedOutputValue = [];
    if (this.selectAll) {
      this.selectedValues = cloneDeep(this.searchResult);
      this.selectedValues = this.selectedValues.filter(x => !x.disabled);
      this.searchResult.forEach((element: any) => {
        if (!element.disabled) {
          this.selectedOutputValue.push(this.output === 'full' ? element : element[this.outputKey]);
        }
      });
    } else if (this.defaultValue && this.selectedOutputValue?.length === 0) {
      this.selectedValues = [this.defaultValue];
      this.selectedOutputValue = [this.defaultValue];
    }
    this.onChange(this.selectedOutputValue);
    this.onTouched();
    this.selectedEventData.emit(event);
    if (this.isEmitEventAll) {
      this.dropDownItemMultiClicked.emit(this.selectedValues);
    }
    this._cdr.detectChanges();
  }


  @HostListener('window:scroll', ['$event'])
  scrollHandler() {
    if (this.bodyBind()) {
      this.removeDropdownList();
      return;
    }
    this.removeDropDown();
  }


  @HostListener('window:pointerup', ['$event.target'])
  onOutsideClick(target: HTMLElement) {
    if (this.childElement && !this.childElement?.contains(target) && (this.isDownload || !this.bodyBind())) {
      if (!this.childElement?.contains(target)) {
        this._renderer.removeChild(this._document.body, this.childElement);
        this.childElement = undefined;
      }
    } else if (this.bodyBind() && this.dropdownActive && this.dropdownList && !this.dropdownList?.nativeElement.contains(target) && !this.dropTitle()?.nativeElement.contains(target)) {
      this.removeDropdownList();
      this.dropdownActive = false;
    }
    if (!this._elementRef.nativeElement.contains(target) && this.isDownload) {
      this.dropdownActive = false;
    }
  }

  ngOnInit() {
    this.setDropDown();
    this.searchResult = cloneDeep(this.dropDownItems);
  }

  ngOnChanges(simpleChanges: SimpleChanges): void {
    this.setDropDown();
    if (simpleChanges['dropDownItems']) {
      this.searchResult = cloneDeep(this.dropDownItems);
    }

    // Keep internal selectedOutputValue in sync when parent passes selectedValues
    if (simpleChanges['selectedValues']) {
      if (this.isMulti) {
        // For multi-select, store the primitive output values to drive checkbox checks
        this.selectedOutputValue = (this.selectedValues || []).map((data: any) => this.output === 'full' ? data : (this.outputKey ? data?.[this.outputKey] : data)
        );
      } else {
        // For single-select, store the primitive value or object based on output
        const first = (this.selectedValues && this.selectedValues.length) ? this.selectedValues[0] : undefined;
        this.selectedOutputValue = this.output === 'full' ? first : (first ? (this.outputKey ? first[this.outputKey] : first) : null);
      }
    }

    if (this.formDisable) {
      this.selectedOutputValue = this.selectedValues?.map((data) => {
        return this.searchResult.filter(value => JSON.stringify(value) == JSON.stringify(data))[0];
      }) || [];
    }

    if (this.isExtense) {
      this.selectedValues = cloneDeep(this.selectedValues);
    }
    if (this.isCheckedClear) {
      this.selectedOutputValue = [];
    }
    this.isChangeCount += 1;
    if (this.isBehaviourAutoSwitch && this.isChangeCount == 1) {
      this.setMultiSingleCheckboxDefualtValue();
    }
    if (this.triggerSelectAllEvent) {
      this.selectAllEvent(true);
    }
  }



  setDropDown() {
    if (!this.isBehaviourAutoSwitch) {
      if (this.singleDefaultSelect && !this.selectedValue) {
        this.selectedValue = this.dropDownItems?.[0];
      }
      if (this.multiDefaultSelect) {
        this.selectedValues = [];
        this.selectedOutputValue = this.output === 'full' ? this.dropDownItems?.[0] : this.dropDownItems?.[0][this.outputKey];
        this.selectedValues.push(this.dropDownItems?.[0]);
      }

      if (this.setOutput()) {
        this.selectedOutputValue = this.output === 'full' ? this.dropDownItems?.[0] : this.dropDownItems?.[0][this.outputKey];
      }
    }
  }


  checkDisable(index: number) {
    return this.dropDownItems[index]?.disabled;
  }



  ngOnDestroy(): void {
    this.removeDropDown();
    this.removeDropdownList();
  }

  removeDropDown() {
    if (this.childElement) {
      this._renderer.removeChild(this._document.body, this.childElement);
      this.childElement = undefined;
    }
  }

  // open download dropdown ;
  openDownload(event: any) {

    if (this.childElement) {
      this._renderer.removeChild(this._document.body, this.downloadDrop.nativeElement);
      this.childElement = undefined;
      return;
    }
    const clickedElement = event.target as HTMLElement;
    const boundingClientRect = clickedElement.getBoundingClientRect();
    this._renderer.setStyle(this.downloadDrop.nativeElement, 'top', `${boundingClientRect.top + 10}px`);
    if (this._tran.currentLang === 'en') {
      this._renderer.setStyle(this.downloadDrop.nativeElement, 'left', `${boundingClientRect.left - 82}px`);
    } else {
      this._renderer.setStyle(this.downloadDrop.nativeElement, 'left', `${boundingClientRect.left}px`);
    }

    this.childElement = this.downloadDrop.nativeElement;
    this._renderer.appendChild(this._document.body, this.downloadDrop.nativeElement);
    this._renderer.addClass(this.downloadDrop.nativeElement, 'active');
  }

  onSearch() {
    const searchResult = this.search.value.trim();

    const searchArray = this.dropDownItems.filter((prod) => {
      return (this.key ? prod[this.key] : prod).toLowerCase().includes(searchResult.toLowerCase());
    });

    this.searchResult = searchArray;
  }

  checkSelected(data: any) {
    if (!this.selectedOutputValue) {
      return false;
    }
    if (!Array.isArray(this.selectedOutputValue)) {
      this.selectedOutputValue = [this.selectedOutputValue];
    }
    // return this.selectedOutputValue.find((x: string) => x == data);
    return this.selectedOutputValue.includes(this.outputKey ? data?.[this.outputKey] : data);
  }

  setMultiSingleCheckboxDefualtValue() {
    if (this.singleDefaultSelect) {
      this.selectedValue = this.selectedValue ?? this.dropDownItems?.[0];
    }
    if (this.multiDefaultSelect) {
      this.selectedValues = [];
      this.selectedOutputValue = this.output === 'full' ? this.dropDownItems?.[0] : this.dropDownItems?.[0][this.outputKey];
      this.selectedValues.push(this.dropDownItems?.[0]);
    }
  }


  checkSubMenu(opt: string) {
    if (this.subMenu?.length <= 0) {
      return false;
    }
    return this.subMenu?.find(x => x.VALUE == opt) || this.subMenu?.find(x => x.VALUE_AR == opt);
  }

  toggleDropdown(event: any) {
    event.stopPropagation();
    // const isActive = !this.dropdownActive;
    this.dropdownActive = !this.dropdownActive;
    if (this.bodyBind()) {
      if (this.dropdownActive) {
        setTimeout(() => {
          this.setDropdownPosition();
        }, 200);
      } else {
        this.removeDropdownList();
      }
      return;
    }
    if (this.isAppendBody && this.dropdownActive) {
      setTimeout(() => {
        this.openDropdown(event);
      }, 200);
    } else {
      this.removeDropDown();
    }
  }

  openDropdown(event: any) {
    if (this.childElement) {
      this._renderer.removeChild(this._document.body, this.dropdownList.nativeElement);
      this.childElement = undefined;
      return;
    }
    const clickedElement = event.target as HTMLElement;
    const parent = clickedElement.parentElement;
    const parentWidth = parent?.offsetWidth;
    if (parent) {
      const boundingClientRect = parent?.getBoundingClientRect();
      this._renderer.setStyle(this.dropdownList?.nativeElement, 'width', `${parentWidth}px`);
      this._renderer.setStyle(this.dropdownList?.nativeElement, 'top', `${boundingClientRect.top + 24}px`);
      // if (this._translate.currentLang === 'en') {
      this._renderer.setStyle(this.dropdownList?.nativeElement, 'left', `${boundingClientRect.left}px`);
      // } else {
      //   this._renderer.setStyle(this.downloadDrop.nativeElement, 'left', `${boundingClientRect.left}px`);
      // }
    }
    this.childElement = this.dropdownList?.nativeElement;
    this._renderer.appendChild(this._document.body, this.dropdownList?.nativeElement);
  }

  outsideClick(event: any) {
    const dropdownElement = this.dropdown.nativeElement as Node;
    if (!dropdownElement?.contains(event?.target as Node)) {
      this.dropdownActive = false;
      if (this.isAppendBody) {
        this.removeDropDown();
      }
      if (this.bodyBind()) {
        this.removeDropdownList();
      }
    }
  }

  removeDropdownList() {
    if (this.instance) {
      this.instance?.destroy();
    }
    if (this.dropdownList?.nativeElement) {
      const container = document.fullscreenElement ?? document.body;
      this._renderer.removeChild(container, this.dropdownList?.nativeElement);
    }
    this.dropdownActive = false;
  }

  setDropdownPosition() {
    this._renderer.setStyle(this.dropdownList?.nativeElement, 'max-width', `${this.dropTitle()?.nativeElement.offsetWidth}px`);
    const container = document.fullscreenElement ?? document.body;
    this._renderer.appendChild(container, this.dropdownList?.nativeElement);
    if (this.instance) {
      this.instance?.destroy();
    }


    // Check if there's enough space below the dropdown
    const dropdownRect = this.dropTitle()?.nativeElement.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const spaceBelow = viewportHeight - dropdownRect.bottom;
    // If space below is less than 300px, position the dropdown above
    const placement = spaceBelow < 300 && this.autoCalculatePosition() ? 'top' : 'bottom';
    if (placement === 'top') {
      this._renderer.setStyle(this.dropdownListInner()?.nativeElement, 'transform-origin', 'bottom');
    }
    this.instance = new PopService().createPopper(this.dropTitle()?.nativeElement, this.dropdownList?.nativeElement, {
      placement: placement,
      modifiers: [
        {
          name: 'offset',
          options: {
            offset: [0, 5]
          }
        },
      ]
    });
  }

}
