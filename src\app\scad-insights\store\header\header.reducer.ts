import { createReducer, on } from '@ngrx/store';
import { setQuery, searchSuccess, getHeader, headerSuccess, getUserPermission, getUserPermissionSuccess } from './header.action';
import { HeaderState, PermissionList, searchResponseState } from './header.state';



const initialState: searchResponseState = {
  query: '',
  result: {
    contentTypes: [{
      contentType: 'no-data',
      id: '',
      imgSrc: '',
      isSelected: false,
      items: [],
      machineName: '',
      title: '',
      categories: ''
    }]
  },
  numberOfResults: 0
};

const initailHeader: HeaderState = {
  accessibility_icon: '',
  accessibility_light_icon: '',
  dark_theme_light_icon: '',
  dark_theme_dark_icon: '',
  language: [],
  language_icon: '',
  language_light_icon: '',
  light_theme_dark_icon: '',
  light_theme_light_icon: '',
  languages: [],
  navigation_menu: [
    {
      menu_label: '',
      menu_link: '',
      show_dropdown: ''
    }
  ],
  notification_icon: '',
  notification_light_icon: '',
  search_button_label: '',
  search_icon: '',
  search_light_icon: '',
  search_placeholder: '',
  show_accessibility: false,
  show_language: false,
  show_notifications: false,
  show_themes: false,
  site_logo: '',
  site_logo_light: '',
  site_slogan: '',
  site_slogan_light: '',
  sub_title: '',
  title: ''
};

const initialPermission: PermissionList = {
  feature_name: '',
  config: {
    title: '',
    light_link: '',
    dark_link: '',
    isClip: false
  }
}

export const searchReducer = createReducer(
  initialState,
  on(setQuery, (state, { query }) => ({ ...state, query })),
  on(searchSuccess, (state, { result }) => {
    return ({ ...state, ...result });
  })
);

export const getHaeder = createReducer(
  initailHeader,
  on(getHeader, (state, { }) => ({ ...state })),
  on(headerSuccess, (state, { result }) => {
    return ({ ...state, ...result });
  })
);

export const getPermissionList = createReducer(
  initialPermission,
  on(getUserPermission, (state, { }) => ({ ...state })),
  on(getUserPermissionSuccess, (state,  result ) => {
    return ({ ...state, ...result });
  })
);
