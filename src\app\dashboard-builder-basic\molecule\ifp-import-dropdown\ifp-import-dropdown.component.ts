import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'ifp-import-dropdown',
    imports: [
        CommonModule,
        TranslateModule
    ],
    templateUrl: './ifp-import-dropdown.component.html',
    styleUrl: './ifp-import-dropdown.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class IfpImportDropdownComponent {

  @Input() showImportTypes: boolean = false;
  @Output() openImportType: EventEmitter<string> = new EventEmitter<string>();



  openImportIndicators(type: string) {
    this.openImportType.next(type);
  }
}
