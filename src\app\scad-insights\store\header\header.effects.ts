/* eslint-disable indent */
import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { catchError, map, mergeMap, switchMap } from 'rxjs/operators';
import { getHeader, getUserPermission, getUserPermissionSuccess, headerSuccess, searchSuccess, setQuery } from './header.action';
import { initialHaeder, initialSearchState, searchResponseState } from './header.state';
import { of } from 'rxjs';
import { HeaderService } from 'src/app/scad-insights/core/services/header/header.service';


@Injectable()
export class SearchEffects {

  constructor(
    private actions$: Actions,
    // eslint-disable-next-line @typescript-eslint/no-shadow, no-shadow
    private HeaderService: HeaderService
  ) { }

  search$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(setQuery),
      switchMap(({ query }) => this.HeaderService.getSearchList(query).pipe(
        map((result: searchResponseState) => {
          this.HeaderService.isApiLoading = false;
          return searchSuccess({ result: result, error: '', status: true });
        }),
        catchError(error => {
          this.HeaderService.isApiLoading = false;
          return of(searchSuccess({ result: initialSearchState, error: error, status: false }));
        } // Dispatch error action
        )
      )
      )
    );
  });

  header$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getHeader),
      mergeMap(() => this.HeaderService.getHeaderData().pipe(
        switchMap((result) => {
          // result = this.checkPermissions(result);
          return this.HeaderService.getHeaderNavigationData().pipe(
            map(next => {
              result.navigation_menu = next;
              return headerSuccess({ result: result, error: '', status: true });
            }),
            catchError(error => {
              return of(headerSuccess({ result: result, error: error, status: false }));
            })
          );
        }),
        catchError(error => of(headerSuccess({ result: initialHaeder, error: error, status: false })) // Dispatch error action
        ))
      )
    );
  });

  permission$ = createEffect(() => {
    return this.actions$.pipe(
      ofType(getUserPermission),
      mergeMap(() => this.HeaderService.getPermissionList().pipe(
        map((result) => {
          return getUserPermissionSuccess({ result: result, error: '', status: true });
        }),
        catchError(error => of(getUserPermissionSuccess({ result: initialHaeder, error: error, status: false })) // Dispatch error action
        ))
      )
    );
  }
  );




  checkPermissions(result: { navigation_menu: { menu_title: string }[] }) {
    const userData = localStorage.getItem('userData');
    const groupIds = userData ? JSON.parse(userData).idTokenClaims.groups : [];
    // groupIds.push('d0fb7c2a-65a5-4ae3-8175-d68315f9797d');
    const isPowerBiAccess = this.HeaderService.powerBiApprovedGroupIds.some((id: string) => groupIds.includes(id));
    result.navigation_menu = isPowerBiAccess ? result.navigation_menu : result.navigation_menu.filter((x: { menu_title: string; }) => x.menu_title !== 'GOVERNMENT AFFAIRS');
    return result;
  }

}
