import { roleList } from './../control-panel/ifp-access-control/ifp-access-control.constants';
import { Component, ChangeDetectionStrategy, Input, Output, EventEmitter, ChangeDetectorRef, ElementRef, ViewChild, Renderer2, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, Inject } from '@angular/core';
import { accessLevels, dataClassification } from '../control-panel/ifp-access-control/ifp-access-control.constants';
import { ClassificationDetail } from '../user-onboarding.interface';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DOCUMENT, NgClass } from '@angular/common';
import { IfpCheckBoxComponent } from '../../ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { OutsideClickDirective } from '../../core/directives/outsideClick.directive';

@Component({
  selector: 'ifp-access-level-dropdown',
  imports: [TranslateModule, NgClass, IfpCheckBoxComponent, OutsideClickDirective],
  templateUrl: './ifp-access-level-dropdown.component.html',
  styleUrl: './ifp-access-level-dropdown.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: IfpAccessLevelDropdownComponent,
      multi: true
    }
  ]
})

export class IfpAccessLevelDropdownComponent implements OnDestroy {
  @ViewChild('dropdown', { static: false }) dropdown!: ElementRef;
  @ViewChild('dropdownList', { static: false }) dropdownList!: ElementRef;

  @Output() dropDownItemClicked = new EventEmitter();
  @Input() dropDownItems: ClassificationDetail[] = accessLevels;
  @Input() viewOnly: boolean = false;
  @Input() isInline: boolean = false;
  @Input() selectedItem: any;
  @Input() selectedValue: string = '';
  @Input() isAppendBody: boolean = false;
  @Input() ischeckBoxShow: boolean = false;
  @Input()
  set clearValue(val: boolean) {
    if (val) {
      this.selectedItem = false;
    }
  }
  public isExpanded: boolean = false;
  public dataClassification = dataClassification;
  public childElement!: HTMLElement | undefined;
  public roleList = roleList;

  onChange = (_quantity: any) => { };
  onTouched = () => { };
  touched = false;
  disabled = false;

  constructor(private _cdr: ChangeDetectorRef, private _renderer: Renderer2, @Inject(DOCUMENT) private _document: Document, private _elementRef: ElementRef, private _translate: TranslateService) {
    this.removeDropDown();
  }

  @HostListener('window:pointerup', ['$event.target'])
  onOutsideClick(target: HTMLElement) {
    if (this.childElement) {
      if (!this.childElement?.contains(target)) {
        this._renderer.removeChild(this._document.body, this.childElement);
        this.childElement = undefined;
      }
    }
    if (!this._elementRef.nativeElement.contains(target)) {
      this.isExpanded = false;
    }
  }

  @HostListener('window:scroll', ['$event'])
  scrollHandler() {
    this.removeDropDown();
  }
  ngOnint() {
    this.selectedItem = this.ischeckBoxShow ? this.dropDownItems[1] : "";
  }
  collapse(event: any) {
    let foundMatch = false;
    this.dropDownItems.forEach((item: ClassificationDetail) => {
      if (!foundMatch) {
        item.isSelected = true;
      } else {
        item.isSelected = false;
      }
      if (item.value === this.selectedValue) {
        foundMatch = true;
      }
    });
    this.isExpanded = !this.isExpanded;
    if (this.isAppendBody && this.isExpanded) {
      this.openDownload(event);
    } else {
      this.removeDropDown();
    }
  }

  onSelect(event: any, index: number) {
    if (this.dropDownItems[index].value.toLowerCase() == dataClassification.open.toLowerCase()) {
      return;
    }
    this.dropDownItems[index].isSelected = event;
    this.assignValue(index, event);
    this.selectedItem = this.dropDownItems[this.dropDownItems.filter(x => x.isSelected).length - 1];
    this.dropDownItemClicked.emit(this.selectedItem);
    this.isExpanded = false;
  }
  ondropdownSelect(event: any, index: number) {
    this.selectedItem = this.dropDownItems[index];
    this.dropDownItemClicked.emit(this.selectedItem);
    this.isExpanded = false;
  }

  writeValue(selectedValue: ClassificationDetail) {
    if (selectedValue) {
      this.selectedItem = selectedValue;
      const index = this.dropDownItems.findIndex(x => x.value == this.selectedItem.value);
      this.assignValue(index, false);
      this._cdr.detectChanges();
    }
  }

  assignValue(index: number, event: boolean) {
    this.dropDownItems.forEach((element: ClassificationDetail, i: number) => {
      element.isSelected = (i < index && !element.isSelected) || (i > index && element.isSelected) ? event : element.isSelected;
    });
  }

  registerOnChange(onChange: any) {
    this.onChange = onChange;
  }

  registerOnTouched(onTouched: any) {
    this.onTouched = onTouched;
  }

  markAsTouched() {
    if (!this.touched) {
      this.onTouched();
      this.touched = true;
    }
  }

  outsideClick(event: any) {
    const dropdownElement = this.dropdown.nativeElement as Node;
    if (!dropdownElement?.contains(event?.target as Node)) {
      this.isExpanded = false;
      if (this.isAppendBody) {
        this.removeDropDown();
      }
    }
  }

  removeDropDown() {
    if (this.childElement) {
      this._renderer.removeChild(this._document.body, this.childElement);
      this.childElement = undefined;
    }
  }

  openDownload(event: any) {
    if (this.childElement) {
      this._renderer.removeChild(this._document.body, this.dropdownList.nativeElement);
      this.childElement = undefined;
      return;
    }
    const clickedElement = event.target as HTMLElement;
    const parent = clickedElement.parentElement;
    const parentWidth = this.dropdown.nativeElement.offsetWidth;
    if (parent) {
      const boundingClientRect = parent?.getBoundingClientRect();
      this._renderer.setStyle(this.dropdownList.nativeElement, 'width', `${parentWidth}px`);
      this._renderer.setStyle(this.dropdownList.nativeElement, 'top', `${boundingClientRect.top + 24}px`);
      // if (this._translate.currentLang === 'en') {
      this._renderer.setStyle(this.dropdownList.nativeElement, 'left', `${boundingClientRect.left}px`);
      // } else {
      //   this._renderer.setStyle(this.downloadDrop.nativeElement, 'left', `${boundingClientRect.left}px`);
      // }
    }
    this.childElement = this.dropdownList.nativeElement;
    this._renderer.appendChild(this._document.body, this.dropdownList.nativeElement);
  }

  ngOnDestroy(): void {
    this.removeDropDown();
  }

}
