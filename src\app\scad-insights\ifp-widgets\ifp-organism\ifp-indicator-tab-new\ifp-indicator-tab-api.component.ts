
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnChanges, Output, EventEmitter, On<PERSON><PERSON><PERSON>, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IfpWhatsNewCardComponent } from '../../ifp-molecules/ifp-whats-new-card/ifp-whats-new-card.component';
import { IfpAnalysisCardComponent } from '../ifp-analysis-card/ifp-analysis-card.component';
import { classifications, domainTabType } from 'src/app/scad-insights/core/constants/domain.constants';
import { IfpTabComponent } from '../../ifp-molecules/ifp-tab/ifp-tab.component';
import { IfpHorizontalTabComponent } from '../../ifp-molecules/ifp-horizontal-tab/ifp-horizontal-tab.component';
import { IndicatorCardFilterAndSearchComponent } from '../../ifp-molecules/indicator-card-filter-and-search/indicator-card-filter-and-search.component';

import { cloneDeep } from 'lodash';
import { PaginationComponent } from '../../ifp-molecules/pagination/pagination.component';
import { IfpNoDataComponent } from '../../ifp-molecules/ifp-no-data/ifp-no-data.component';
import { Store } from '@ngrx/store';
import { IfpReportCardComponent } from '../../ifp-molecules/ifp-report-card/ifp-report-card.component';
import { IfpScreenerComponent } from '../ifp-screener/ifp-screener.component';
import { cardSequenceAnimation } from 'src/app/scad-insights/animation/card-flip.animation';
import { ActivatedRoute, Router } from '@angular/router';
import { DomainsService } from 'src/app/scad-insights/core/services/domains/domains.service';
import { loadDomainDetailsNewNode } from 'src/app/scad-insights/domains/store/domain-details-api/domain-details-api.action';
import { selectCurrentNode } from 'src/app/scad-insights/domains/store/domain-details-api/domain-details-api.selector';
import { tap, Subject, debounceTime, takeUntil } from 'rxjs';
import { getStatisticsInsights } from 'src/app/scad-insights/store/statistics-insights/statistics-insights-list.action';
import { IfpSpinnerComponent } from '../../ifp-molecules/ifp-spinner/ifp-spinner.component';
import { IfpGlobalService } from 'src/app/scad-insights/core/services/ifp-global.service';

@Component({
  selector: 'ifp-indicator-tab-api',
  imports: [CommonModule,
    IfpAnalysisCardComponent,
    IfpTabComponent,
    IfpWhatsNewCardComponent,
    IfpHorizontalTabComponent,
    IndicatorCardFilterAndSearchComponent,
    PaginationComponent,
    IfpNoDataComponent,
    IfpReportCardComponent,
    IfpScreenerComponent,
    IfpSpinnerComponent
  ],
  templateUrl: './ifp-indicator-tab-api.component.html',
  styleUrls: ['./ifp-indicator-tab-api.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    cardSequenceAnimation('ifp-indicator-tab__indicator-card')
  ]
})
export class IfpIndicatorTabApiComponent implements OnChanges, OnDestroy {
  @Input() tab: any[] = [];
  @Input() id!: string | null;
  @Input() filter: any[] = [];
  @Input() themeListSingles = '';
  @Input() productSelect!: any;
  @Input() selectedDomainSingles = '';
  @Input() addMyApps: boolean = false;
  @Input() isCompare: boolean = false;
  @Input() isCompared: boolean = true;
  @Input() tabIndex = 0;
  @Input() productSelectIndex: number | null = null;
  @Input() analyticClass: string = '';
  @Input() domain!: string;
  @Input() domainDropdownOptions: any[] = [];
  @Input() selectedDropdownOption: any = null;
  @Output() apply = new EventEmitter();
  @Output() myApps = new EventEmitter();
  @Output() dropdownChange = new EventEmitter();
  @Output() dateRangeChange = new EventEmitter();
  public category: any = '';

  public categoryList: any = [];
  public filterOutput: any = [];
  public types = domainTabType;
  public classification = classifications;
  public themeListSingle: any = {};
  public selectedDomainSingle: any = {};
  public enableCompare: any = [];
  public themeList: any = [];
  public currentTab: any = {};
  public onSearch = '';
  public offsetPage = 1;
  public search = '';
  public offset: number = 0;
  public limit: number = 10;
  public size: number = 0;
  public selectedTab = 0;
  public searchTimer: any;
  public loader = true;
  public screenerSettings: any = {};
  public total = 0;
  public dissapparingProductIndex!: number;
  public entity_id!: string;
  public currentDropdownSelection: any = null;
  public currentDateRange: { startDate: Date | null; endDate: Date | null } | null = null;
  public selectedView: string = 'list';

  // Debouncing and state management for dropdown changes
  private dropdownChangeSubject = new Subject<any>();
  private dateRangeChangeSubject = new Subject<{ startDate: Date | null; endDate: Date | null }>();
  private destroy$ = new Subject<void>();
  private isDropdownChanging = false;
  public selectedSort: string = 'date_desc';


  // Hies  variables//
  public _globalService = inject(IfpGlobalService);


  public nodeValues = this.store.select(selectCurrentNode).pipe(tap((data) => {
    // Handle error states
    if (data?.status === 'error') {
      this.loader = false;
      this.total = 0;
      this._cdr.detectChanges();
      return;
    }

    this.loader = false;
    this.total = data?.total_count || 0;
    this._cdr.detectChanges();

    if (data?.results?.length > 0) {
      const id = data.results.map((value: { id: string }) => value.id);
      if (this.currentTab.key !== classifications.analyticalApps && this.currentTab.key !== classifications.reports) {
        this.store.dispatch(getStatisticsInsights({ id: id, name: this.currentTab.key }));
      }
    }
  }));

  public screenerEnable = false;
  constructor(private _cdr: ChangeDetectorRef, private store: Store, private _route: Router, private domainService: DomainsService, private _actRoute: ActivatedRoute) {
    this.domainService.clickDomain$.subscribe(resp => {
      if (resp == 'reset') {
        this.enableCompare = [];
        this.productSelectIndex = null;
        this.productSelect = '';
        this.offset = 0;
        this.offsetPage = 1;
      }
    });

    // Set up debounced dropdown change handling
    this.dropdownChangeSubject
      .pipe(
        debounceTime(150), // Reduced to 150ms for better responsiveness
        takeUntil(this.destroy$)
      )
      .subscribe(selectedOption => {
        this.performDropdownChange(selectedOption);
      });

    // Set up debounced date range change handling
    this.dateRangeChangeSubject
      .pipe(
        debounceTime(150), // Reduced to 150ms for better responsiveness
        takeUntil(this.destroy$)
      )
      .subscribe(dateRange => {
        this.performDateRangeChange(dateRange);
      });

    this.entity_id = '';
    this._actRoute.queryParams.subscribe(val => {
      if (val['entity_id']) {
        this.entity_id = val['entity_id'];
      }
    });

  }

  myappsEvent(event: any) {
    this.myApps.emit(event);
  }


  tabClick(event: { event: any, index: number }) {
    this.loader = true;
    this.resetAll();
    this._cdr.detectChanges();
    this.currentTab = event.event;
    this._cdr.detectChanges();
    this.tabIndex = event.index;
    this.setFilterData(event.event.id);
    this.screenerEnable = false;
    this.changeDomain(this.filterOutput[0]);
    if (this.currentTab.key === classifications.analyticalApps || this.currentTab.key === classifications.reports) {
      this.isCompare = false;
    } else {
      this.isCompare = true;
    }

    this.callNode();
    this._cdr.detectChanges();
  }

  resetAll() {
    this.themeListSingle = '';
    this.selectedDomainSingle = '';
    this.category = '';
    this.productSelect = '';
    this.reset();
  }

  setFilterData(id: number) {
    this.filterOutput = this.filter[+id].data ? [...this.filter[+id].data] : [];
    this._cdr.detectChanges();
  }



  onPageChange(event: any) {
    this.offsetPage = event + 1;
    this.offset = (event / this.limit) + 1;
    this.callNode();

  }

  limitChanged(event: any) {
    this.offsetPage = 1;
    this.offset = 1;
    this.limit = event;
  }

  reset() {
    this.offsetPage = 1;
    this.offset = 1;
    this.productSelect = '';
    this.productSelectIndex = -1;

  }

  selectTab(event: { item: any; index: number }) {
    this.offsetPage = 1;
    this.offset = 1;
    if (this.dissapparingProductIndex != null && this.dissapparingProductIndex == event.index) {
      this.productSelectIndex = -1;
      this.productSelect = undefined;
    } else {
      this.productSelect = event.item;
      this.productSelectIndex = cloneDeep(event.index);
      this.dissapparingProductIndex = cloneDeep(event.index);
    }
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
    this.searchTimer = setTimeout(() => {
      this.callNode();
    }, 300);
    this.apply.emit(
      {
        tabName: this.currentTab.name,
        key: this.currentTab.key,
        subDomain: this.selectedDomainSingle,
        subTheme: this.themeListSingle,
        index: this.tabIndex,
        category: this.category,
        product: { product: this.productSelect, index: this.productSelectIndex }
      }
    );
  }

  selectThemeAndDomain(event: any) {
    this.selectedDomainSingle = event;
    this.themeList = [];
    if (event?.subthemes) {
      this.themeList = event?.subthemes;
      if (this.themeList?.length === 1) {
        this.themeListSingle = this.themeList[0];
      }
    }
  }

  changeDomain(event: any) {
    this.resetAll();
    if (this.currentTab.key === classifications.analyticalApps) {
      this.category = event;
    } else {
      this.selectThemeAndDomain(event);
    }
    this.checkScreener();
    this.apply.emit(
      {
        tabName: this.currentTab.name,
        key: this.currentTab.key,
        subDomain: this.selectedDomainSingle,
        subTheme: this.themeListSingle,
        index: this.tabIndex,
        category: this.category
      }
    );
    this._cdr.detectChanges();
    this.callNode();
  }

  checkScreener() {
    if (this.selectedDomainSingle && this.selectedDomainSingle !== '') {
      this.screenerEnable = this.selectedDomainSingle.showScreener;
      if (this.screenerEnable) {
        this.screenerSettings = this.selectedDomainSingle.screenerConfiguration ? { ...this.selectedDomainSingle.screenerConfiguration } : {};
        if (!this.screenerSettings?.domain) {
          this.screenerSettings['domain'] = this.domain;
        }
      }
    }
    if (this.themeListSingle && this.themeListSingle !== '' && this.currentTab.key === this.classification.officialStatistics) {
      this.screenerEnable = this.themeListSingle.showScreener;
      if (this.screenerEnable) {
        this.screenerSettings = this.themeListSingle.screenerConfiguration ? { ...this.themeListSingle.screenerConfiguration } : {};
        if (!this.screenerSettings?.domain) {
          this.screenerSettings['domain'] = this.domain;
        }
      }
    }
  }

  sortCliked(event: any) {
    this.selectedSort = event.value;
    this.offset = 0;
    this.offsetPage = 1;
    this.callNode();
  }

  setFilter() {
    try {
      const data: any = {};

      // Ensure currentTab exists before accessing its properties
      if (!this.currentTab) {
        return data;
      }

      data['classification'] = this.currentTab?.id;

      if (this.themeListSingle && this.themeListSingle !== '') {
        data['subtheme'] = this.themeListSingle?.id;
      }
      if (this.selectedDomainSingle && this.selectedDomainSingle !== '') {
        data['subdomain'] = this.selectedDomainSingle?.id;
      }
      if (this.productSelect && this.productSelect !== '') {
        data['product'] = this.productSelect?.pid;
      }

      data['page'] = this.currentTab.key === this.classification.analyticalApps || this.currentTab.key === this.classification.reports ? 1 : this.offset;
      data['limit'] = this.currentTab.key === this.classification.analyticalApps || this.currentTab.key === this.classification.reports ? 9999 : this.limit;

      if (this.search && this.search !== '') {
        data['search'] = this.search;
      }
      if (this.category && this.category !== '' && this.category?.id !== 'All') {
        data['category'] = this.category.id;
      }
      if (this.entity_id && this.entity_id !== '') {
        data['entity_id'] = this.entity_id;
      }

      // Add smart_publisher parameter if Publication is selected AND current tab is Reports
      // Ensure currentDropdownSelection is properly set
      if (this.currentDropdownSelection && this.currentTab?.key === this.classification.reports) {
        data['publication_type'] = this.currentDropdownSelection?.value ?? this.currentDropdownSelection;
      }

      if (this.selectedSort && this.currentTab?.key === this.classification.reports) {
        data['sort_by'] = this.selectedSort;
      }

      // Add date range parameters if dates are selected
      if (this.currentDateRange?.startDate) {
        data['from_date'] = this.formatDate(this.currentDateRange.startDate);
      }
      if (this.currentDateRange?.endDate) {
        data['to_date'] = this.formatDate(this.currentDateRange.endDate);
      }

      return data;
    } catch (error) {
      return {};
    }
  }

  private formatDate(date: Date): string {
    // Use local date formatting to avoid timezone conversion issues
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`; // YYYY-MM-DD format
  }

  onDropdownChange(selectedOption: any) {
    if (this.isDropdownChanging) {
      return; // Prevent multiple simultaneous changes
    }

    this.isDropdownChanging = true;
    this.loader = true;
    this._cdr.detectChanges();

    // Use debounced subject instead of immediate call
    this.dropdownChangeSubject.next(selectedOption);
  }

  onDateRangeChange(dateRange: { startDate: Date | null; endDate: Date | null }) {
    this.loader = true;
    this._cdr.detectChanges();

    // Use debounced subject instead of immediate call
    this.dateRangeChangeSubject.next(dateRange);
  }

  // Debounced dropdown change handler
  private performDropdownChange(selectedOption: any): void {
    try {
      this.currentDropdownSelection = selectedOption;
      this.dropdownChange.emit(selectedOption);

      // Reset pagination when filter changes
      this.offset = 0;
      this.offsetPage = 1;

      // Ensure state is updated before API call
      this._cdr.detectChanges();

      // Reduced delay for better responsiveness
      setTimeout(() => {
        this.callNode();
        this.isDropdownChanging = false;
      }, 25);
    } catch (error) {
      this.loader = false;
      this.isDropdownChanging = false;
      this._cdr.detectChanges();
    }
  }

  // Debounced date range change handler
  private performDateRangeChange(dateRange: { startDate: Date | null; endDate: Date | null }): void {
    try {
      this.currentDateRange = dateRange;
      this.dateRangeChange.emit(dateRange);

      // Reset pagination when filter changes
      this.offset = 0;
      this.offsetPage = 1;

      // Ensure state is updated before API call
      this._cdr.detectChanges();

      // Reduced delay for better responsiveness
      setTimeout(() => {
        this.callNode();
      }, 25);
    } catch (error) {
      this.loader = false;
      this._cdr.detectChanges();
    }
  }

  changeTheme(event: any) {
    this.reset();
    this.themeListSingle = event;
    if (this.themeListSingle && this.themeListSingle !== '' && this.currentTab.key === this.classification.officialStatistics) {
      this.screenerEnable = this.themeListSingle.showScreener;
    }
    if (this.screenerEnable) {
      this.screenerSettings = this.themeListSingle.screenerConfiguration;
    }
    this.apply.emit(
      {
        tabName: this.currentTab.name,
        key: this.currentTab.key,
        subDomain: this.selectedDomainSingle,
        subTheme: this.themeListSingle,
        index: this.tabIndex,
        category: this.category
      }
    );
    this.callNode();
  }


  ngOnChanges() {
    if (this.tab[this.tabIndex].key === classifications.analyticalApps) {
      this.category = this.selectedDomainSingles;
      this.selectedDomainSingle = '';
      this.themeListSingle = '';
    } else {
      this.category = '';
      this.selectedDomainSingle = this.selectedDomainSingles;
      this.themeListSingle = this.themeListSingles ? this.themeListSingles : '';
      this.selectThemeAndDomain(this.selectedDomainSingles);
    }

    // Initialize dropdown selection if provided, or set default to Publication
    if (this.selectedDropdownOption) {
      this.currentDropdownSelection = this.selectedDropdownOption;
    } else if (this.domainDropdownOptions && this.domainDropdownOptions.length > 0) {
      // Default to Publication if no selection is provided
      this.currentDropdownSelection = this.domainDropdownOptions.find(option => option.value === 'publication') || this.domainDropdownOptions[0];
    }

    // Trigger change detection to ensure UI updates
    this._cdr.detectChanges();

    this.selectedTab = this.tabIndex;
    this.currentTab = this.tab[this.tabIndex];
    this.checkScreener();
    this.setFilterData(this.currentTab.id);
    if (this.currentTab.key === classifications.analyticalApps || this.currentTab.key === classifications.reports) {
      this.isCompare = false;
    } else {
      this.isCompare = true;
    }
    this.callNode();
    this._cdr.detectChanges();
  }

  searchResult(event: string) {
    this.offset = 0;
    this.search = event;
    this.callNode();
  }

  callNode() {
    try {
      this.loader = true;
      this._cdr.detectChanges();

      // Ensure we have valid data before making the API call
      if (!this.id) {
        this.loader = false;
        this._cdr.detectChanges();
        return;
      }

      const filter = this.setFilter();
      this.store.dispatch(loadDomainDetailsNewNode({ id: this.id, filter }));
    } catch (error) {
      this.loader = false;
      this._cdr.detectChanges();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();

    // Clear any pending timers
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }


  emitCompare(event: any) {
    if (event.flag) {
      const data = {
        id: event.id,
        contentType: event.contentType,
        viewName: event.viewName
      };
      this.enableCompare.push(data);
    }
    if (!event.flag) {
      const index = this.enableCompare.findIndex((x: any) => x.id == event.id);
      this.enableCompare.splice(index, 1);
    }
    this.enableCompare = cloneDeep(this.enableCompare);
  }

  emitScreenerCompare(event: any) {
    if (event.event) {
      const data = {
        id: event.id,
        contentType: event.contentType,
        viewName: event.viewName
      };
      this.enableCompare.push(data);
    }
    if (!event.event) {
      const index = this.enableCompare.findIndex((x: any) => x.id == event.id);
      this.enableCompare.splice(index, 1);
    }
    // this.enableCompare = cloneDeep(this.enableCompare);
  }

  // emitScreenser(event:any){
  //   this.compareChecked.emit(event);
  // }


  compareButton(event: any) {
    this._route.navigate(['compare-chart'], { queryParams: { compareIds: JSON.stringify(this.enableCompare), prevUrl: this._route.url }, skipLocationChange: true });
  }

  checkLength() {
    return this.enableCompare.length > 1;
  }

  onToggleView(event: string) {
    this.selectedView = event;
  }

}

