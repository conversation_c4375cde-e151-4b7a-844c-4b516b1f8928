import { Ng<PERSON><PERSON>, NgStyle } from '@angular/common';
import { AfterViewInit, Component, ElementRef, input, Input,  InputSignal,  OnChanges,  OnDestroy,  Renderer2,  ViewChild } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { ShortNumberPipe } from 'src/app/scad-insights/core/pipes/shortNumber.pipe';

@Component({
    selector: 'ifp-progress-circle',
    imports: [TranslateModule, ShortNumberPipe, NgClass, IfpTooltipDirective, NgStyle],
    templateUrl: './progress-circle.component.html',
    styleUrl: './progress-circle.component.scss'
})
export class ProgressCircleComponent implements AfterViewInit, OnDestroy, OnChanges {

  @ViewChild('circleProgress') circleProgress!:ElementRef;
  @ViewChild('circleProgressLeft') circleProgressLeft!:ElementRef;
  @ViewChild('circleProgressRight') circleProgressRight!:ElementRef;
  @ViewChild('tooltip') tooltip!:ElementRef;
  @ViewChild('tooltipSecond') tooltipSecond!:ElementRef;
  @Input() value = 0;
  @Input() total = 0;
  @Input() inactiveValue = 0;
  @Input() activeKey = 'Active Users';
  @Input() inActiveKey = 'Inactive Users';
  @Input() tooltipEnable = true;
  @Input() classCircle = '';
  @Input() radius = 15.9155;
  @Input() icon!: string;
  @Input() progressColor = '#0082fc';
  public secondaryColor: InputSignal<string> = input('#D1D5DA');
  public type: InputSignal<string> = input('circle');
  public size: number = 34;
  public activeNote: string = 'Users who have logged into the platform within the last 6 months are considered as active';
  public inActiveNote: string = 'Users who haven\'t logged in during the last 6 months are considered inactive';
  public listener!: () => void;
  public listener1!: () => void;
  public listener2!: () => void;
  public listener3!: () => void;
  public listener4!: () => void;
  public listener5!: () => void;


  constructor(public _render2: Renderer2) {

  }

  ngAfterViewInit(){
    this._render2.setAttribute(this.circleProgressLeft.nativeElement, 'r', this.radius.toString());
    this._render2.setAttribute(this.circleProgressRight.nativeElement, 'r', this.radius.toString());
    this.initProgressBar();
  }

  ngOnChanges(): void {
    if (this.circleProgress) {
      this.size = +(this.radius * 2).toFixed(0) + 2;
      this.initProgressBar();
    }

  }



  initProgressBar() {

    const percentageComplete = (this.value / this.total);
    const strokeDashOffsetValue = this.type() === 'circle' ? 100 - (percentageComplete * 100) : (100 - (percentageComplete * 100)/2);
    const progressBar = this.circleProgressLeft.nativeElement;
    this._render2.setStyle(progressBar, 'stroke-dashoffset', strokeDashOffsetValue);
    if (this.tooltipEnable) {
      this.listener4 = this._render2.listen(this.circleProgressLeft.nativeElement, 'mousemove', (event)=> {
        const x = event.pageX;
        const y = event.pageY;
        const currentRect = this.circleProgress.nativeElement.getBoundingClientRect();
        const tooltipRect = this.tooltip.nativeElement.getBoundingClientRect();

        if ((currentRect.x +( currentRect.width/2 )) < x) {
          this._render2.setStyle(this.tooltip.nativeElement, 'top', `${y}px`);
          this._render2.setStyle(this.tooltip.nativeElement, 'left', `${x + 20}px`);
        } else {
          this._render2.setStyle(this.tooltip.nativeElement, 'top', `${y}px`);
          this._render2.setStyle(this.tooltip.nativeElement, 'left', `${x - tooltipRect.width - 20}px`);
        }

      });
      this.listener5 = this._render2.listen(this.circleProgressRight.nativeElement, 'mousemove', (event)=> {
        const x = event.pageX;
        const y = event.pageY;

        const currentRect = this.circleProgress.nativeElement.getBoundingClientRect();
        const tooltipRect = this.tooltipSecond.nativeElement.getBoundingClientRect();

        if ((currentRect.x +( currentRect.width/2 )) < x) {
          this._render2.setStyle(this.tooltipSecond.nativeElement, 'top', `${y}px`);
          this._render2.setStyle(this.tooltipSecond.nativeElement, 'left', `${x + 20}px`);
        } else {
          this._render2.setStyle(this.tooltipSecond.nativeElement, 'top', `${y}px`);
          this._render2.setStyle(this.tooltipSecond.nativeElement, 'left', `${x - tooltipRect.width - 20}px`);
        }
      });
      this.listener = this._render2.listen(this.circleProgressLeft.nativeElement, 'mouseover', ()=> {
        this.setupTooltip(this.tooltip);
      });
      this.listener1 = this._render2.listen(this.circleProgressLeft.nativeElement, 'mouseout', ()=> {

        this.removeTooltip(this.tooltip);
      });
      this.listener2 = this._render2.listen(this.circleProgressRight.nativeElement, 'mouseover', ()=> {
        this.setupTooltip(this.tooltipSecond);
      });
      this.listener3 = this._render2.listen(this.circleProgressRight.nativeElement, 'mouseout', ()=> {
        this.removeTooltip(this.tooltipSecond);
      });
    }

  }

  setupTooltip(element:ElementRef) {
    this._render2.setStyle(element.nativeElement, 'display', 'inline-block');
    this._render2.appendChild(document.body, element.nativeElement);
  }

  removeTooltip(element:ElementRef) {
    this._render2.removeChild(document.body, element.nativeElement);
    this._render2.setStyle(element.nativeElement, 'display', 'none');

  }



  percentageToDegrees(value: number) {
    return (360 * value)/100;
  }

  ngOnDestroy(): void {
    if (this.tooltipEnable) {
      this.listener();
      this.listener1();
      this.listener2();
      this.listener3();
      this.listener4();
      this.listener5();
    }
  }

}
