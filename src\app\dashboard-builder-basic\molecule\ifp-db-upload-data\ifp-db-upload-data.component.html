<!-- <app-ifp-db-file-uploader [allowedExtensions]="allowedExtensions"></app-ifp-db-file-uploader> -->
@for (axis of axisOptions; let i= $index; track axis) {
<!-- <app-ifp-dropdown class="ifp-db-coordinates" [title]="axis.title" [placeHolder]="'Select'" [showTitle]="true"
  [dropDownItems]="axis.options" (dropDownItemClicked)="onSelectXaxis($event, axis.key)" [key]="axis.optKey"
   [isMulti]="axis.multiSelect" (dropDownItemMultiClicked)="onSelectYaxis($event, axis.key)"
   [selectedValue]="selectedXaxis" [selectedValues]="selectedYaxis" ></app-ifp-dropdown> -->


   <app-ifp-db-dropdown class="ifp-db-coordinates" [options]="axis.options" [title]="axis.title"
   [isMultiSelect]="axis.multiSelect" (multiSelected)="onSelectYaxis($event, axis.key)"
   [key]="'name'" [multipleSelectedItems]="axis.multiSelect ? selectedYaxis: []" (singleSelected)="onSelectXaxis($event, axis.key)"
   [selectedSingleItem]="selectedXaxis"></app-ifp-db-dropdown>
}
