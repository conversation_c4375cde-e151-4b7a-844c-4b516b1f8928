@use "../../../../assets/ifp-styles/abstracts/index" as *;

// Existing styles in IFP start
.ifp-container {
  &--sm {
    max-width: 750px;
    width: 85%;
  }
}

// Existing styles in IFP end

.ifp-tools-page {
  text-align: center;
  padding: $spacer-6 $spacer-0;
  position: relative;
  min-height: calc(100vh - 242px);
  &__beta {
    position: absolute;
    top: $spacer-4;
    right: $spacer-4;
  }
  &__bg {
    position: absolute;
    width: 100%;
    height: 100%;
    object-position: top;
    top: 0;
    left: 0;
  }
  &__heading {
    font-size: $ifp-fs-13;
    font-weight: $fw-bold;
    margin-bottom: $spacer-3;
  }
  &__desc {
    color: $ifp-color-grey-9;
    font-size: $ifp-fs-4;
    line-height: 1.4;
  }
  &__card-wrapper {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    flex-wrap: wrap;
    margin: (-$spacer-6) auto $spacer-0;
    max-width: 80%;
    min-height: 450px;
  }
  &__text-sec {
    margin: $spacer-0 auto;
    max-width: 400px;
  }
  &__card-title {
    font-size: $ifp-fs-8;
    font-weight: $fw-bold;
  }
  &__card-desc {
    font-size: $ifp-fs-4;
    line-height: 1.5;
    margin-top: $spacer-2;
    color: $ifp-color-grey-2;
    max-height: 77px;
    min-height: 77px;
    transition: 0.3s;
    @include lineLimit(3);
  }
  &__card-btn-sec {
    opacity: 0;
    overflow: hidden;
    transition: 0.8s;
    transform: translateY(100%);
    height: 32px;
  }
  &__card-logo {
    margin: $spacer-0 auto $spacer-4;
    width: auto;
    height: 95px;
  }
  &__header {
    margin-bottom: $spacer-6 * 2;
    position: relative;
    z-index: 1;
  }
  &__card-inner {
    width: calc(33.33% - 2%);
    min-width: calc(33.33% - 6%);
    min-height: 400px;
  }
  &__card {
    // opacity: 0;
    border: 6px solid $ifp-color-white;
    border-radius: 40px;
    background-color: $ifp-color-white;
    padding: $spacer-5 $spacer-4 $spacer-0;

    margin: $spacer-2 1%;
    transition: 0.8s;
    box-shadow: inset 0 39px 44px -20px rgba(203, 212, 209, 0.2), 0 104px 95px -50px rgba(98, 110, 106, 0.16);
    overflow: hidden;
    transform: scale(0.93);
    position: relative;
    // &:first-child {
    //   animation: fade-in 0.7s linear forwards;
    // }
    // &:nth-child(2) {
    //   animation: fade-in 0.7s 0.4s linear forwards;
    // }
    // &:last-child {
    //   animation: fade-in 0.7s 0.8s linear forwards;
    // }
    &:hover {
      transform: translateY(0) scale(1);
      .ifp-tools-page {
        &__card-btn-sec {
          opacity: 1;
          overflow: visible;
          transform: translateY(0);
          margin: $spacer-4 $spacer-0 $spacer-3;
          height: auto;
        }
        &__card-desc {
          max-height: 500px;
          overflow: visible;
          display: block;
        }
      }
    }
    .ifp-tools-page__card-btn-sec ::ng-deep ifp-button .ifp-btn {
      margin-bottom: $spacer-2;
    }
  }
  &__btn {
    padding: $spacer-2;
  }
}

:host-context([dir="rtl"]) {
  .ifp-tools-page__beta {
    right: auto;
    left: $spacer-4;
  }
}

@include desktop-sm {
  .ifp-container--sm{
    max-width: 100%;
    width: 100%;
  }
  .ifp-tools-page {
    &__bg {
      display: none;
    }
    &__card-wrapper {
      flex-wrap: wrap;
      max-width: 100%;
      min-height: 0;
      padding: $spacer-0 $spacer-5;
    }
    &__card {
      &:nth-child(2) {
        margin: $spacer-3;
      }
    }
    &__card-inner {
      min-height: 0;
    }
    &__card-btn-sec {
      opacity: 1;
      transform: none;
      margin: $spacer-3 $spacer-0;
      height: auto;
    }
    &__card-inner {
      width: 50%;
    }
  }
}
.ifp-beta-icon {
  width: 50px;
}

@include mobile {
  .ifp-tools-page {
    padding: $spacer-4 $spacer-0;
    &__heading {
      font-size: $ifp-fs-9;
    }
    &__card-wrapper {
      margin-top: $spacer-4;
      padding: $spacer-0 $spacer-3;
    }
    &__card {
      width: 100%;
      margin: $spacer-3 $spacer-0;
      padding: $spacer-4 $spacer-3 $spacer-0;
      &:nth-child(2) {
        margin: $spacer-3 $spacer-0;
      }
    }
    &__card-logo {
      max-width: 150px;
    }
    &__card-btn-sec {
      margin-bottom: $spacer-4;
    }
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
