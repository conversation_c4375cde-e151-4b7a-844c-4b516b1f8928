@if (!isDashboardCard) {
  <div class="ifp-node" #fullScreen>
    <div class="ifp-node__card-left">
      <div class="ifp-node__left-actions" id="chartCard">
        <div class="ifp-node__group-one">
          <h2 class="ifp-node__tiltle">{{(title | quotRemove)| translate}}</h2>
        </div>

        <app-ifp-tab [tabData]="chartDropDownData" [transparent]="true" [showIcon]="false" (selectedTabEvent)="chartChange($event)" [selectedTab]="title" [selectionType]="'name'" class="ifp-node__switch-tab"></app-ifp-tab>

        <div class="ifp-node__filters">
          <!-- <app-ifp-dropdown [boarderBottom]="true" [isMulti]="false"
            class="ifp-node__dropdown ifp-node__filter-item ifp-node__dropdown--main"
            *ngIf="chartDropDownData?.length > 1" [dropDownItems]="chartDropDownData"
            (dropDownItemMultiClicked)="chartChange($event)" [singleDefaultSelect]="true"></app-ifp-dropdown> -->

          <ng-container *ngFor="let filters of filterPanel?.properties; let i = index">
            @if(filters?.level === 0 &&  !filters.isHide) {
            <app-ifp-dropdown *ngIf="!filters.isDisabled && !isShowDropDown"
              [isMulti]="(checkValue() && filters.type !== 'radio') || checkIndex() === i || filters.path === chartConstants.TIME_PERIOD || filterPanel?.related_filters_viewName"
              class="ifp-search-filter__dropdown ifp-node__filter-item" [limit]="optionsLimit"
              [placeHolder]="filters.label" [dropDownItems]="filters.options"
              (dropDownItemMultiClicked)="applyFilter($event,filters.path, i)"
              [multiDefaultSelect]="(checkValue() && filters.type !== 'radio') || checkIndex() === i || filters.path === chartConstants.TIME_PERIOD || filterPanel?.related_filters_viewName"
              [filterLabel]="filters?.label" [placeHolder]="filters.label" [showTitle]="true"
              [analyticClasses]="analyticsClasses.chartFilterApplay"
              [singleDefaultSelect]="checkValue()  || checkIndex() === i || filters.path === chartConstants.TIME_PERIOD"
              [isBehaviourAutoSwitch]="true"
              [triggerSelectAllEvent]="i === pieFilterIndex && this.chartType === 'pie'  ?  true: false"
              [chartType]="chartType" [path]="filters.path" [selectedValues]="filterKeys[i]?.value "
              [selectedValue]="filterKeys[i]?.value.toString()" [searchEnable]="true"
              [selectAllBox]="(checkValue() && filters.type !== 'radio') || checkIndex() === i || filters.path === chartConstants.TIME_PERIOD || filterPanel?.related_filters_viewName"
              [isEmitEventAll]="true"></app-ifp-dropdown>
            }
          </ng-container>
        </div>

        @if(filterPanel?.properties?.length > 0) {
        <div class="ifp-node__filters">
          <ng-container *ngFor="let filters of filterPanel?.properties; let i = index">
            @if(filters?.level !== 0 && !filters.isHide) {
            <app-ifp-dropdown *ngIf="!filters.isDisabled && !isShowDropDown"
              [isMulti]="(checkValue() && filters.type !== 'radio') || checkIndex() === i || filters.path === chartConstants.TIME_PERIOD || filterPanel?.related_filters_viewName"
              class="ifp-search-filter__dropdown" [limit]="optionsLimit" [placeHolder]="filters.label"
              [dropDownItems]="filters.options" ngClass="ifp-node__filter-item"
              [filterLabel]="filters?.label" [placeHolder]="filters.label" [showTitle]="true"
              [analyticClasses]="analyticsClasses.chartFilterApplay"
              [isBehaviourAutoSwitch]="true"
              [triggerSelectAllEvent]="i === pieFilterIndex && this.chartType === 'pie'  ?  true: false"
              (dropDownItemMultiClicked)="applyFilter($event,filters.path, i)"
              [chartType]="chartType" [path]="filters.path" [disableSingleValue]="true"
              [selectedValues]="filterKeys[i]?.value "
              [selectedValue]="filterKeys[i]?.value.toString() " [searchEnable]="true" [formDisable]="true"
              [selectAllBox]="(((checkValue() && filters.type !== 'radio') || checkIndex() === i) && filters.path !== chartConstants.TIME_PERIOD) || filterPanel?.related_filters_viewName"
               [isEmitEventAll]="true" [selectAll]="filterKeys[i]?.value?.length == filters.options?.length"
               [subMenu]="filters.subFilter" [disableOption]="filters.defualtDisable ? filters.default : ''"></app-ifp-dropdown>
            }
          </ng-container>
          <app-ifp-dropdown *ngIf="chartType === 'pie' && !isCustomFilter" [showTitle]="true" [isMulti]="false"
            class="ifp-search-filter__dropdown" [limit]="optionsLimit" [placeHolder]="'Period Selection' | translate"
            [dropDownItems]="customTimePeriodOptions" ngClass="ifp-node__filter-item"
            (dropDownItemMultiClicked)="changePeriod($event);" [multiDefaultSelect]="true"
            [isBehaviourAutoSwitch]="true"></app-ifp-dropdown>
        </div>
        }

        <div class="ifp-node__rating" *ngIf="chartValues?.length > 0">
          <!-- <ifp-rating [title]="formatTitle(rating)"
            [value]="rating.value !== 'rating.value'? rating.value : 0 |number: getFormat(rating.valueFormat)"
            *ngFor="let rating of chartValues" [subValueColor]="rating.value < 0 ? '#FF001E' : '#1c9452'"
            [subValue]="(rating.percentage)" class="ifp-node__value"></ifp-rating> -->
          <ifp-rating [title]="formatTitle(rating)" [value]="rating.value |  customNumber:rating.valueFormat"
            *ngFor="let rating of chartValues" [subValueColor]="rating.value < 0 ? (rating.invertColor ? '#1c9452' :'#FF001E') : (rating.invertColor ?'#FF001E' :'#1c9452')"
            [subValue]="(rating.percentage)" class="ifp-node__value"
            [arrowType]="rating.value < 0 ? (rating.invertArrows ? 'up' : 'down') :  (rating.invertArrows ? 'down' : 'up')"></ifp-rating>

        </div>
        <div class="ifp-node__chart">
          <ng-container *ngIf="!isLoader; else chartLoad">
            <div>
              <app-ifp-analytic-line-chart
                *ngIf="isVisble && (chartType === 'line' || chartType === 'column' || chartType === 'bar')"
                [chartData]="seriesData" [isRangeSelect]="isRangeSelector" #chartComponent [yaxisLabel]="yaxisLabel"
                (rangeValueUpdated)="valueRangeUpdated($event)" [isRender]="isRender"
                (zoomOutDisabled)="disableZoomout($event)" [xAxisLabelType]="xAxisLabelType"
                [xAxisCatogory]="chartCatogory" [hideSuffix]="hideSuffix" [isCFD]="filterPanel?.isCFD"
                [isCoi]="chartData.type  === 'coi' "></app-ifp-analytic-line-chart>

              <app-ifp-circular-bar-chart #chartComponent [chartData]="circularChartData" [chartCategory]="chartCatogory"
                *ngIf="chartType === 'circular' && isVisble" [chartFilterOgData]="foracstTooltip"
                [yAxisLabelName]="yaxisLabel" [isDatalabel]="isDatalabel"></app-ifp-circular-bar-chart>
              <app-ifp-pie-chart *ngIf="isVisble && chartType === 'pie'" [pieChartSeries]="pieChartSeriesData"
                #chartComponent [chartCategory]="chartCatogory" [selectedPeriod]="pieSelectedPeriod"
                [yAxisLabelName]="yaxisLabel"></app-ifp-pie-chart>
            </div>
            <div *ngIf="chartType === 'table'" class="ifp-node__table">
              <app-ifp-table [tableData]="tableData" [isPrint]="true"></app-ifp-table>

            </div>
            <div class="ifp-analysis__selectors">
              <div class="ifp-analysis__range-selector" *ngIf="chartType === 'line'">
                <div class="ifp-analysis__range-selector-label">
                  {{'Range Selector' | translate}}
                </div>
                <app-ifp-toggle-button [analyticClass]="analyticsClasses.chartRangeSelector"
                  (toggleChange)="getRangeSelector($event)"></app-ifp-toggle-button>
              </div>
              <app-ifp-month-selector
                *ngIf="periodFilter && periodFilter.options?.length > 0  && isMonthSelector && chartType !== 'pie'"
                [filter]="periodFilter?.options" class="ifp-node__month-selector"
                (filterSelected)="applyPeriodFilter($event)" [enable]="recentEnable"
                (onToggle)="changeRecent($event)"></app-ifp-month-selector>
            </div>
            <p class="ifp-scenario__content">{{description}}</p>


            <div *ngIf="filterPanel?.isCFD">
              <br>
              <p class="ifp-scenario__content" style="font-style: italic;">
                {{ 'CFDMDisclaimer' | translate }}
              </p>
            </div>

            <div class="ifp-node__disclaimer"
              *ngIf="domain && domain === (chartConstants.LABOUR_FORCE_EN || domain === chartConstants.LABOUR_FORCE_AR) && disclaimerDetails?.length">
              <h4 class="ifp-node__disclaimer-title">{{'Disclaimer' | translate}}</h4>
              <p class="ifp-node__desc" *ngFor="let disclaimer of disclaimerDetails">{{disclaimer}}</p>
            </div>


          </ng-container>
          <div class="ifp-node__txt-icon" *ngIf="publishDate">
            <ifp-icon-text [icon]="'ifp-icon-calender'" [text]="publishDate" [key]="'Updated date'"></ifp-icon-text>
            <ifp-icon-text class="ifp-node__source" [icon]="'ifp-icon-text'" *ngIf="source" [text]="source"
              [key]="'Source'"></ifp-icon-text>
          </div>
          @if (security) {
            <ifp-tag class="ifp-node__data-tag" [isBoxView]="true" [background]="'transparent'" [tagName]="security.name" [color]="security.color ?? ''" [infoHead]="('Data classification' | translate) + ': ' + security.name" [info]="security.description ?? ''"></ifp-tag>
          }
          @if (seriesData?.length) {
            <ifp-chart-insight [indicatorId]="indicatorId" [subNodeId]="subNodeId" [nodeTitle]="title" class="ifp-node__insights"></ifp-chart-insight>
          }
          <ng-template #chartLoad>
            <app-ifp-card-loader class="ifp-loader" [type]="'chart'"></app-ifp-card-loader>
          </ng-template>
        </div>
      </div>
      <div class="ifp-chart-toolbar" [ngClass]="{'ifp-chart-toolbar--disabled' : seriesData?.length <=0 }">
        <div class="ifp-chart-toolbar__tool-item"
          [ngClass]="{'ifp-chart-toolbar__tool-item--active': toolbarAction === 'notification'}">
          <ifp-button [tooltipValue]="'Notification updates' | translate" class="ifp-whats-new-card__btn"
            [buttonColor]="toolbarAction === 'notification' ? 'blue' :'black'" [buttonClass]="buttonClass.icon"
            [iconClass]="'ifp-icon-notification'" (ifpClick)="toolbarAction = 'notification'"></ifp-button>
        </div>
        <div class="ifp-chart-toolbar__tool-item">
          <ifp-button [tooltipValue]="(!myAppsStatus ? 'Add to my bookmarks' : 'Remove from my bookmarks' ) | translate"
            class="ifp-whats-new-card__btn" [buttonColor]="myAppsStatus ? 'blue' :'black' " [event]="myAppsStatus "
            [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-plus-square'" (ifpClick)="addDataMyApps($event)"
            [analyticClass]="!myAppsStatus ? analyticsClasses.addToMyApps : analyticsClasses.removeMyApps"></ifp-button>
        </div>

        <div class="ifp-chart-toolbar__tool-item"
          [ngClass]="{'ifp-chart-toolbar__tool-item--active': toolbarAction === 'settings'}">
          <!-- <img src="../../../../assets/images/settings-icon.svg" (click)="toolbarAction = 'settings'" width="22"> -->
          <ifp-button [tooltipValue]="'Settings' | translate" class="ifp-whats-new-card__btn"
            [buttonColor]="toolbarAction === 'settings' && seriesData?.length ? 'blue' :'black' "
            [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-settings'"
            (ifpClick)="toolbarAction = 'settings'"></ifp-button>
        </div>

        <div class="ifp-chart-toolbar__tool-item">
          <!-- <img src="../../../../assets/images/save.svg" (click)="print()" width="22"> -->
          <ifp-button [tooltipValue]="'Print' | translate" class="ifp-whats-new-card__btn" [buttonColor]="'black'"
            [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-print'" (ifpClick)="print()"></ifp-button>
        </div>

        <div class="ifp-chart-toolbar__tool-item"
          [ngClass]="{'ifp-chart-toolbar__tool-item--active': toolbarAction === 'comment'}">
          <ifp-button [tooltipValue]="'My Comments' | translate" class="ifp-whats-new-card__btn"
            [buttonColor]="toolbarAction === 'comment' ? 'blue' :'black' " [buttonClass]="buttonClass.icon"
            [iconClass]="'ifp-icon-comment'" (ifpClick)="toolbarAction = 'comment'"></ifp-button>
        </div>
        <div class="ifp-chart-toolbar__tool-item">
          <ifp-button [tooltipValue]="'Full Screen' | translate" class="ifp-whats-new-card__btn" [buttonColor]="'black'"
            [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-fullscreen'" (ifpClick)="fullscreen()"></ifp-button>
        </div>

        <div class="ifp-chart-toolbar__tool-item" *ngIf="chartType === 'line' || chartType === 'column'">
          <ifp-button [tooltipValue]="'Zoom in' | translate" class="ifp-whats-new-card__btn" [buttonColor]="'black'"
            [buttonClass]="buttonClass.icon" [iconClass]="'ifp-icon-zoom-in'" (ifpClick)="zoom('in')"></ifp-button>
        </div>
        <div class="ifp-chart-toolbar__tool-item" *ngIf="chartType === 'line' || chartType === 'column'">
          <ifp-button [tooltipValue]="'Zoom out' | translate" class="ifp-whats-new-card__btn" [buttonColor]="'black'"
            [buttonClass]="(!isZoomoutDisabled ? buttonClass.disabled : '') +' '+ buttonClass.icon"
            [iconClass]="'ifp-icon-zoom-out'" (ifpClick)="zoom('out')"></ifp-button>
        </div>
      </div>
    </div>
    <div class="ifp-node__card-right" *ngIf="seriesData?.length">
      <div class="ifp-chart-toolbar__action-box">
        <h4 class="ifp-chart-toolbar__right-title">{{'Download' | translate}}</h4>
        <div class="ifp-chart-toolbar__download-wrapper" [ngClass]="{'disabled': !tncState}">
          <div class="ifp-chart-toolbar__download-item" (click)="download('pdf')">
            <img src="../../../../assets/images/pdf-icon.svg" class="ifp-chart-toolbar__download-icon">
          </div>
          <div class="ifp-chart-toolbar__download-item" (click)="download('XL')">
            <img src="../../../../assets/images/xls-icon.svg" class="ifp-chart-toolbar__download-icon">
          </div>
          <div class="ifp-chart-toolbar__download-item" (click)="download('png')">
            <img src="../../../../assets/images/img-icon.svg" class="ifp-chart-toolbar__download-icon">
          </div>
          <div class="ifp-chart-toolbar__download-item" (click)="downloadCustomXl()">
            <img src="../../../../../assets/images/filter-excel-green.svg" class="ifp-chart-toolbar__download-icon">
          </div>
          <!-- <div class="ifp-chart-toolbar__download-item">
            <img src="../../../../assets/images/ppt-icon.svg" class="ifp-chart-toolbar__download-icon">
          </div> -->
        </div>
        <div class="ifp-chart-toolbar__terms">
          <app-ifp-check-box class="ifp-chart-toolbar__tnc-checkbox" [hideLabel]="true" [checkedData]="tncState"
            (checked)="termsResponse($event)" [disabled]="tncState"></app-ifp-check-box>
          <p class="ifp-chart-toolbar__tnc-text">{{'I accept the ' | translate}} <span class="ifp-link"
              (click)="showTnC()">{{'terms and conditions ' | translate}}</span> {{'for downloading the documents' |
            translate}}</p>
        </div>
      </div>
      <div class="ifp-chart-toolbar__action-box">
        <ng-container *ngIf="toolbarAction === 'settings'">
          <h4 class="ifp-chart-toolbar__right-title ifp-chart-toolbar__right-title--bold">{{'Settings' | translate}}</h4>
          <div class="ifp-chart-toolbar__switch-icons">
            <app-ifp-tab [tabData]="tabData" [isSmall]="true" (selectedTabEvent)="changeChart($event.event.name)"
              [selectedTab]="chartType" [selectionType]="'name'" class="ifp-chart-toolbar__switch-view"></app-ifp-tab>
          </div>

          <ng-container
            *ngIf="chartType === 'line' || chartType === 'column' || chartType === 'bar' || chartType === 'circular' || chartType === 'pie'">
            <div class="ifp-chart-toolbar__checkbox-outer">
              <input type="checkbox" checked="true" id="tooltip" class="ifp-chart-toolbar__checkbox"
                (change)="changeTooltip($event)">
              <label for="tooltip" class="ifp-chart-toolbar__checkbox-label">{{'Tooltip' | translate}}</label>
            </div>
            <div class="ifp-chart-toolbar__checkbox-outer" *ngIf="chartType !== 'pie'">
              <input type="checkbox" id="dataLabels" (change)="changeDataLabel($event)" checked="true"
                class="ifp-chart-toolbar__checkbox">
              <label for="dataLabels" class="ifp-chart-toolbar__checkbox-label">{{'Data Labels' | translate}}</label>
            </div>
            <div class="ifp-chart-toolbar__checkbox-outer"
              *ngIf="chartType === 'line' || chartType === 'column' || chartType === 'bar' || chartType === 'circular'">
              <input type="checkbox" id="priceValue" [checked]="isPreciseValue" (change)="changePreciseLabel($event)"
                class="ifp-chart-toolbar__checkbox">
              <label for="priceValue" class="ifp-chart-toolbar__checkbox-label">{{'Precise Value' | translate}}</label>
            </div>
          </ng-container>
        </ng-container>
        <ng-container *ngIf="toolbarAction === 'comment'">
          <h4 class="ifp-chart-toolbar__right-title ifp-chart-toolbar__right-title--bold">{{'My Comments' | translate}}
          </h4>
          <p class="ifp-chart-toolbar__right-desc">{{'Comments are only visible to you' | translate}}</p>
          <app-ifp-comments [insightData]="insightData$ | async" (addComment)="addInsight($event)"
            (editComment)="updateInsight($event)" (deleteComment)="deleteInsight($event)"></app-ifp-comments>
        </ng-container>
        <ng-container *ngIf="toolbarAction === 'notification'">
          <!-- <h4 class="ifp-chart-toolbar__right-title ifp-chart-toolbar__right-title--bold">{{'Notifications' | translate}}</h4>
          <p class="ifp-chart-toolbar__right-desc">Manage your notifications settings</p>
          <div class="ifp-chart-toolbar__switch-sec">
            <app-ifp-toggle-button (toggleChange)="addNotification($event)" class="ifp-chart-toolbar__switch" [enable]="isNotificationEnabled"></app-ifp-toggle-button>
            <p class="ifp-chart-toolbar__label">Enable notification</p>
          </div>
          <div class="ifp-chart-toolbar__switch-sec">
            <app-ifp-toggle-button (toggleChange)="setEmailNotifStatus($event)" class="ifp-chart-toolbar__switch" [enable]="isEmailEnabled"></app-ifp-toggle-button>
            <p class="ifp-chart-toolbar__label">Receive notifications via e-mail</p>
          </div> -->
          <app-ifp-notification-settings [id]="indicatorId" [contentType]="'analytical-apps'"
            [appType]="appType"></app-ifp-notification-settings>
        </ng-container>
      </div>
    </div>
  </div>
}


<app-ifp-modal #modalSla [modalClass]="'ifp-modal__pdf-table'">
  <div class="ifp-scenario__download-wrapper">
    <div class="lds-spinner">
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
    </div>

    <app-ifp-pdf-template id="downloadPrint">
      <div class="ifp-node__left-actions" #printCard>
        <div class="ifp-node__group-one">
          <h2 class="ifp-node__tiltle">{{title | translate}}</h2>
        </div>
        <div class="ifp-node__filters" *ngIf="filterPanel?.properties?.length > 0">
          <app-ifp-dropdown *ngFor="let filters of filterPanel?.properties; let i=index"
            [isMulti]="(checkValue() && filters.type !== 'radio') || checkIndex() === i || filters.path === chartConstants.TIME_PERIOD || filterPanel?.related_filters_viewName"
            class="ifp-search-filter__dropdown" [limit]="optionsLimit" [placeHolder]="filters.label"
            [dropDownItems]="filters.options" ngClass="ifp-node__filter-item"
            (dropDownItemMultiClicked)="applyFilter($event,filters.path, i)" [multiDefaultSelect]="true"
            [filterLabel]="filters?.label" [placeHolder]="filters.label" [showTitle]="true"
            [isDownloadDropdownHide]="true"></app-ifp-dropdown>
        </div>
        <div class="ifp-node__rating" *ngIf="chartValues?.length > 0">
          <ifp-rating [title]="formatTitle(rating)" [value]="rating.value |  customNumber:rating.valueFormat"
            *ngFor="let rating of chartValues" [subValueColor]="rating.value < 0 ? (rating.invertColor ? '#1c9452' :'#FF001E') : (rating.invertColor ?'#FF001E' :'#1c9452')"
            [subValue]="(rating.percentage)" class="ifp-node__value"
            [arrowType]="rating.value < 0 ? (rating.invertArrows ? 'up' : 'down') :  (rating.invertArrows ? 'down' : 'up')"></ifp-rating>
        </div>
        <div class="ifp-node__chart">
          <!-- <ng-container *ngIf="!loaderChart; else chartLoad"> -->
            @if (chartType !== 'table') {
              <div class="ifp-node__chart-template">
                <app-ifp-analytic-line-chart
                *ngIf="isVisble && (chartType === 'line' || chartType === 'column' || chartType === 'bar')"
                [chartData]="seriesData" [isRangeSelect]="isRangeSelector" #chartComponent [yaxisLabel]="yaxisLabel"
                (rangeValueUpdated)="valueRangeUpdated($event)" [isRender]="isRender"
                (zoomOutDisabled)="disableZoomout($event)" [xAxisLabelType]="xAxisLabelType"
                [xAxisCatogory]="chartCatogory" [hideSuffix]="hideSuffix" [isCFD]="filterPanel?.isCFD"
                [isCoi]="chartData.type  === 'coi' " [isDatalabel]="isDatalabel" [preciseValue]="isPreciseValue"></app-ifp-analytic-line-chart>
                <app-ifp-circular-bar-chart #chartComponent [chartData]="circularChartData"
                  [chartCategory]="chartCatogory" *ngIf="chartType === 'circular' && isVisble"
                  [chartFilterOgData]="foracstTooltip" [yAxisLabelName]="yaxisLabel"
                  [isDatalabel]="isDatalabel"></app-ifp-circular-bar-chart>
                <app-ifp-pie-chart *ngIf="isVisble && chartType === 'pie'" [pieChartSeries]="pieChartSeriesData"
                  #chartComponent [chartCategory]="chartCatogory" [selectedPeriod]="pieSelectedPeriod"
                  [yAxisLabelName]="yaxisLabel"></app-ifp-pie-chart>
              </div>
            }


            <div *ngIf="chartType === 'table'" class="ifp-node__table">
              <app-ifp-table [tableData]="tableData"></app-ifp-table>

            </div>
            <div>

            </div>
            <app-ifp-month-selector *ngIf="periodFilter && periodFilter[0]?.options?.length > 0 && isMonthSelector"
              [filter]="periodFilter[0]?.options" class="ifp-node__month-selector"
              (filterSelected)="applyPeriodFilter($event)"></app-ifp-month-selector>
            <p class="ifp-scenario__content">{{description}}</p>
          <!-- </ng-container>
          <ng-template #chartLoad>
            <app-ifp-card-loader class="ifp-loader" [type]="'chart'"></app-ifp-card-loader>
          </ng-template> -->
        </div>
      </div>




    </app-ifp-pdf-template>
  </div>
</app-ifp-modal>

<app-ifp-modal #tncModal [modalClass]="'ifp-modal__template-certificate'">
  <app-ifp-tnc-modal (termsResponse)="termsResponse($event)" [isAccepted]="tncState"></app-ifp-tnc-modal>
</app-ifp-modal>


@if (isDashboardCard) {
  <div class="ifp-node__chart">
    <ng-container *ngIf="!isLoader; else chartLoad">
      <div>
        <app-ifp-analytic-line-chart
          *ngIf="isVisble && (chartType === 'line' || chartType === 'column' || chartType === 'bar')"
          [chartData]="seriesData" [isRangeSelect]="isRangeSelector" #chartComponent [yaxisLabel]="yaxisLabel"
          (rangeValueUpdated)="valueRangeUpdated($event)" [isRender]="isRender"
          (zoomOutDisabled)="disableZoomout($event)" [xAxisLabelType]="xAxisLabelType"
          [xAxisCatogory]="chartCatogory" [height]="300" [isDashboardCard]="true"></app-ifp-analytic-line-chart>
        <app-ifp-circular-bar-chart #chartComponent [chartData]="circularChartData" [chartCategory]="chartCatogory"
          *ngIf="chartType === 'circular' && isVisble" [chartFilterOgData]="foracstTooltip"
          [yAxisLabelName]="yaxisLabel" [height]="300" [isDashboardCard]="true"
          [isDatalabel]="isDatalabel"></app-ifp-circular-bar-chart>
        <app-ifp-pie-chart *ngIf="isVisble &&  (chartType === 'pie' || chartType == 'doughnut')" [pieChartSeries]="pieChartSeriesData"
          #chartComponent [chartCategory]="chartCatogory" [selectedPeriod]="pieSelectedPeriod"
          [yAxisLabelName]="yaxisLabel" [height]="300" [isDonut]="chartType == 'doughnut' ? true : false" [isDashboardCard]="true"></app-ifp-pie-chart>
      </div>
      <p class="ifp-scenario__content">{{description}}</p>
    </ng-container>
    <div *ngIf="chartType === 'table'" class="ifp-node__table">
      <app-ifp-table [tableData]="tableData" [isPrint]="true"></app-ifp-table>

    </div>
    <!-- <div class="ifp-node__txt-icon" *ngIf="publishDate">
      <ifp-icon-text [icon]="'ifp-icon-calender'" [text]="publishDate" [key]="'Updated date'"></ifp-icon-text>
      <ifp-icon-text class="ifp-node__source" [icon]="'ifp-icon-text'" *ngIf="source" [text]="source"
        [key]="'Source'"></ifp-icon-text>
    </div> -->

    <ng-template #chartLoad>
      <app-ifp-card-loader class="ifp-loader" [type]="'chart'"></app-ifp-card-loader>
    </ng-template>
  </div>
}

<app-ifp-modal #filterModal [modalClass]="'ifp-modal__template-certificate'">
  <ifp-ifp-custom-xl-filter [isOpen]="isOpen" [chartFilter]="chartDropDownData" [isApi]="true" [filters]="filterPanel" class="ifp-modal__template-certificate" (dismissModel)="closeFilterModel($event)"
  [allChartsData]="allChartsData" [tableFields]="tableLabels"></ifp-ifp-custom-xl-filter>
</app-ifp-modal>

