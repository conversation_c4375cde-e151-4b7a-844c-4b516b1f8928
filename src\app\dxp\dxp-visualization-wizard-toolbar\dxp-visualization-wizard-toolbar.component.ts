import { cloneDeep } from 'lodash';
import { iconLibrary } from './../../dashboard-builder/molecule/ifp-db-icon-library/ifp-db-icon-library.constants';

import { buttonClass } from './../../scad-insights/core/constants/button.constants';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, NgClass } from '@angular/common';
import { Component, input, model, output, OnInit, OnDestroy, signal, inject, effect } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpButtonComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { IfpNoDataComponent } from '../../scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { DxpKpiCustomFilter, DxpKpiCustomFilterColumnList, DxpVisualizationFilterFormComponent } from './dxp-visualization-filter-form/dxp-visualization-filter-form.component';
import { DxpVisualizationFilterCardComponent } from './dxp-visualization-filter-card/dxp-visualization-filter-card.component';
import { IfpIconSelectorComponent } from '../../dashboard-builder/atom/ifp-icon-selector/ifp-icon-selector.component';
import { DbToolbarIcon } from 'src/app/dashboard-builder/organism/ifp-chart-toolbar/ifp-chart-toolbar.interface';
import { IfpAccordionComponent } from 'src/app/dashboard-builder/molecule/ifp-accordion/ifp-accordion.component';
import { dxpPreviewWizardTabMenu } from './dxp-visualization-wizard.constants';
import { FormsModule } from '@angular/forms';
import { SelectedProductWithoutId } from '../widgets/dxp-accordian/dxp-accordian.component';
import { IfpDbDropdownComponent } from 'src/app/dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.component';
import { IfpCheckBoxComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
import { Subscription } from 'rxjs';
import { DxpColumnList, DxpCurrentLegend } from '../dxp.interface';
import { DistinctValuesStore } from '../store/dxp-distinct-value.store';
@Component({
  selector: 'ifp-dxp-visualization-wizard-toolbar',
  imports: [TranslateModule, NgClass, IfpButtonComponent, IfpNoDataComponent, DxpVisualizationFilterFormComponent, DxpVisualizationFilterCardComponent, IfpIconSelectorComponent, IfpAccordionComponent, FormsModule, IfpDbDropdownComponent, IfpCheckBoxComponent, JsonPipe],
  templateUrl: './dxp-visualization-wizard-toolbar.component.html',
  styleUrl: './dxp-visualization-wizard-toolbar.component.scss'
})
export class DxpVisualizationWizardToolbarComponent implements OnInit, OnDestroy {
  public filterSettingsChange = output<DxpKpiCustomFilter[]>();
  public requestGraphData = output<void>();
  public defaultLegendChangeEvent = output<any>();
  public applyDefaultLegend = output<void>();

  public cardTitle = model('');
  public cardDescription = model('');
  public selectedLegendComponent = model<DxpCurrentLegend[]>([]);
  public selectedFromChart = model<DxpCurrentLegend[]>([]);
  public selectedIcon = model('ifp-icon-bar-arrow');
  public chartName =  model<string>('line');
  public selectedTabItem =  model<DxpKpiPreviewTabList>(dxpPreviewWizardTabMenu[0]);
  public tabList = input<DxpKpiPreviewTabList[]>(dxpPreviewWizardTabMenu);
  public columnList = input<DxpKpiCustomFilterColumnList[]>([]);
  public legendValuesLoadingToolbar = signal(false);
  public selectedProductDetails = input<SelectedProductWithoutId>();
  public defaultLegendComponent = signal<DxpCurrentLegend | undefined>(undefined);
  public chartTypes =  model<{
    title: string;
    key: string;
    icon: string;
    disabled: boolean;
    selected: boolean;
    value: string;
}[]>([]);

  public isAddFilter = model(true);
  public addedFilters = model<DxpKpiCustomFilter[]>([]);
  public editIndex: number = -1;
  public editingFilter: DxpKpiCustomFilter | null = null;
  public legendSubscription!: Subscription;
  public buttonClass = buttonClass;
  public indexMapping:Record<number, string> = {};
  readonly storeDistinctValue = inject(DistinctValuesStore);

  // for dummy datause
  public legendOptions = [
    { id: 'legend1', value: 'Legend 1' },
    { id: 'legend2', value: 'Legend 2' }
    // need to add columns data
  ];

  public selectedLegend: string | null = null;

  public legendCard = iconLibrary;
  constructor() {
    effect(() => {
      let loader = false;
      const resp = this.storeDistinctValue.getAllData();
      console.log('resp in effect', resp);
      this.selectedLegendComponent.update(legends => {
        legends.forEach(legend => {
          if ((!legend.options?.length )&& resp[`${legend.name+this.selectedProductDetails()?.sourceProductId+this.selectedProductDetails()?.sourceAssetId}source`]) {
            legend['options'] = resp[`${legend.name+this.selectedProductDetails()?.sourceProductId+this.selectedProductDetails()?.sourceAssetId}source`].data?.map((v: string, index: number) => ({ id: v, value: v, checked: index < 10 }));
            legend['selectedValues'] =legend['options']?.slice(0, 10);
          }
          if (!resp[`${legend.name+this.selectedProductDetails()?.sourceProductId+this.selectedProductDetails()?.sourceAssetId}source`]?.loader) {
            loader=false;
          }
        });
        this.legendValuesLoadingToolbar.set(loader);
        return [...legends];
      });
    });
  }

  // Selected values under the default legend dropdown (no API call until Apply)

  ngOnInit(): void {

    this.selectedTabItem.set(this.tabList()[0]);
  }

  removeSelected(index: number) {
    this.selectedLegendComponent.update(selected => {
      selected.splice(index, 1);
      return [...selected];
    });

    this.onLegendMultiSelected( this.selectedLegendComponent());
  }

  onLegendMultiSelected(selected: DxpColumnList[]) {

    this.selectedLegendComponent.set(cloneDeep(selected));
    if (this.selectedLegendComponent().length > 0) {
      if (
        !this.defaultLegendComponent() ||        (this.defaultLegendComponent() && !this.selectedLegendComponent().includes(this.defaultLegendComponent() as DxpCurrentLegend))
      ) {
        this.selectedLegendComponent()[0]['default'] = true;
        this.defaultLegendComponent.set(this.selectedLegendComponent()[0]);
        this.defaultLegendChangeEvent.emit(this.defaultLegendComponent());
      }
      // Notify preview/KPI about the current default legend

    } else {
      this.defaultLegendComponent.set(undefined);
    }
  }

  onDefaultLegendChange(selectedLegend: DxpCurrentLegend) {
    this.selectedLegendComponent.update(selected => {
      return selected.map(item => {
        item['default'] = false;
        return item;
      });
    });

    selectedLegend['default'] = true;
    // this.defaultLegendComponent.set(selectedLegend);
    // if (!this.selectedLegendComponent().includes(selectedLegend)) {
    //   this.selectedLegendComponent.set([selectedLegend]);
    // }
    // this.defaultLegendChangeEvent.emit(this.defaultLegendComponent());
  }

  // Track value selection for the default legend (no API here)
  onLegendValuesSelected(values: { id: string, value: string, checked: boolean }[], selectedLegend: DxpCurrentLegend) {
    values.forEach(v => v['checked'] = true);
    selectedLegend['selectedValues'] = values;
    // this.selectedLegendValues.set( values);
  }

  onSelectLegend(event: DxpColumnList) {
    this.legendValuesLoadingToolbar.set(true);
    this.storeDistinctValue.loadByQuery({ columnName: event.name,  product: this.selectedProductDetails(), sourceFilterEnabled: true});
  }

  // Apply button handler: emit selections and trigger graph API
  onApplyLegends() {
    this.selectedFromChart.set([...this.selectedLegendComponent()]);
    this.applyDefaultLegend.emit();
  }


  onSelectTabItem(index: number) {
    this.selectedTabItem.set(this.tabList()[index]);
  }

  addNewFilter(item: DxpKpiCustomFilter) {
    // If editing, replace existing; otherwise add new
    if (this.editIndex >= 0) {
      const updated = [...this.addedFilters()];
      updated[this.editIndex] = item;
      this.addedFilters.set(updated);
      this.editIndex = -1;
      this.editingFilter = null;
    } else {
      this.addedFilters.set([...this.addedFilters(), item]);
    }
    this.isAddFilter.set(false);
    this.filterSettingsChange.emit(this.addedFilters());
  }

  onRemoveCard(index: number) {
    this.addedFilters.set(this.addedFilters().filter((_, i) => i !== index));
    this.filterSettingsChange.emit(this.addedFilters());
  }

  onEditCard(index: number) {
    this.editIndex = index;
    this.editingFilter = this.addedFilters()[index];
    this.isAddFilter.set(true);
  }

  updateChartType(chartType: DbToolbarIcon) {
    this.chartTypes().forEach(c => c.selected = (c.key === chartType.key));
    this.chartName.set(chartType.key);
  }

  expandAccordian(_event: boolean, content: Content) {
    // Find the clicked content item
    const clickedContent = this.selectedTabItem().content.find(x => x.title === content.title);
    if (clickedContent) {
      // Toggle the clicked item's expand state
      clickedContent.isExpand = !clickedContent.isExpand;

      // Close all other accordions when opening one (accordion behavior)
      if (clickedContent.isExpand) {
        this.selectedTabItem().content.forEach(x => {
          if (x.title !== content.title) {
            x.isExpand = false;
          }
        });
      }
    }
  }

  ngOnDestroy(): void {
    this.legendSubscription?.unsubscribe();
  }
}
export interface DxpKpiChartSettings {
  chartType: string;
}

export interface DxpKpiPreviewTabList {
  key: string;
  title: string;
  description: string;
  icon: string;
  disabled: boolean;
  content: Content[];
}

interface Content {
  title: string;
  key: string;
  isExpand?: boolean;
}
