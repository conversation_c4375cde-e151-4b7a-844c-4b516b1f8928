import { NgModule, inject } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { slaGuardService } from './scad-insights/core/guard/sla.guard';
import { settingGuardService } from './scad-insights/core/guard/settings.guard';
import { IsAdminService } from './scad-insights/core/guard/isAdmin.guard';
import { NdaGuardService } from './scad-insights/core/guard/nda.guard';


const routes: Routes = [
  {
    path: '',
    redirectTo: 'home',
    pathMatch: 'full'
  },
  { path: '', loadChildren: () => import('src/app/layout/blank/blank.module').then((m: typeof import('src/app/layout/blank/blank.module')) => m.BlankModule) },
  {
    path: '', loadChildren: () => import('src/app/layout/ifp-pages/ifp-pages.module').then((m: typeof import('src/app/layout/ifp-pages/ifp-pages.module')) => m.IfpPagesModule),
    resolve: [() => {
      return inject(slaGuardService).resolve();
    // }, () => {
    //   return inject(TermsGuardService).resolve();
    // }, () => {
    }, () => {
      return inject(settingGuardService).resolve();
    }, () => {
      return inject(IsAdminService).resolve();
    },
    () => {
      return inject(NdaGuardService).resolve();
    }
    ]
  },
  {
    path: '', loadChildren: () => import('src/app/bayaan-store/ifp-store.module').then((m: typeof import('src/app/bayaan-store/ifp-store.module')) => m.IfpStoreModule),
    resolve: [() => {
      return inject(slaGuardService).resolve();
    // }, () => {
    //   return inject(TermsGuardService).resolve();
    // }, () => {
    }, () => {
      return inject(settingGuardService).resolve();
    }, () => {
      return inject(IsAdminService).resolve();
    },
    () => {
      return inject(NdaGuardService).resolve();
    }
    ]
  },
  {
    path: '', loadChildren: () => import('src/app/ifp-analytics/ifp-analytics.module').then((m: typeof import('src/app/ifp-analytics/ifp-analytics.module')) => m.IfpAnalyticsModule),
    resolve: [() => {
      return inject(slaGuardService).resolve();
    // }, () => {
    //   return inject(TermsGuardService).resolve();
    // }, () => {
    }, () => {
      return inject(settingGuardService).resolve();
    }, () => {
      return inject(IsAdminService).resolve();
    },
    () => {
      return inject(NdaGuardService).resolve();
    }
    ]
  },
  {
    path: '**',
    redirectTo: '/404'
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { scrollPositionRestoration: 'enabled' })],
  exports: [RouterModule]
})
export class AppRoutingModule { }
