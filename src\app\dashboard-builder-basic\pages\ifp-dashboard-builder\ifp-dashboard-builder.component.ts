import { contentType, contentTypeDashboard } from 'src/app/scad-insights/core/constants/contentType.constants';
import {  DOCUMENT, Location, NgClass, NgIf, NgStyle } from '@angular/common';
import { ChangeDetectorRef, Component, ElementRef, HostListener, Inject, Input, OnChanges, OnDestroy, OnInit, Renderer2, SimpleChanges, ViewChild, WritableSignal, signal } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { buttonClass, buttonColor, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { IfpModalComponent } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpImportIndicatorsComponent } from '../../organism/ifp-import-indicators/ifp-import-indicators.component';
import { IfpDbFileUploaderComponent } from '../../molecule/ifp-db-file-uploader/ifp-db-file-uploader.component';
import { fileFormats } from '../../molecule/ifp-db-file-uploader/ifp-db-file-uploader.constants';
import { IfpDbCardComponent } from '../../molecule/ifp-db-card/ifp-db-card.component';
import { IfpChartToolbarComponent } from '../../organism/ifp-chart-toolbar/ifp-chart-toolbar.component';
import { SubSink } from 'subsink/dist/subsink';
import { Store } from '@ngrx/store';
import { selectIndicatorGetById } from 'src/app/scad-insights/store/incidicator-list/incidicator-list.selector';
import { cloneDeep } from 'lodash';
import { chartConstants } from 'src/app/scad-insights/core/constants/chart.constants';
import {
  CompactType,
  DisplayGrid,
  GridsterComponent,
  GridsterConfig,
  GridsterItem,
  GridsterModule,
  GridType
} from 'angular-gridster2';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';
import { Subject, debounceTime } from 'rxjs';
import html2canvas from 'html2canvas';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { getIndicator } from 'src/app/scad-insights/store/incidicator-list/incidicator-list.action';
import { PagesService } from 'src/app/scad-insights/core/services/pages/pages.service';
import { IfpImportDropdownComponent } from '../../molecule/ifp-import-dropdown/ifp-import-dropdown.component';
import { getStatisticsInsights } from 'src/app/scad-insights/store/statistics-insights/statistics-insights-list.action';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { Title } from '@angular/platform-browser';
import { dashboardConstants } from 'src/app/scad-insights/core/constants/dashboard.constants';
import { axisDropDowns, multiDimentionData, singleDimentionData } from '../../organism/ifp-chart-toolbar/ifp-chart-toolbar.constants';
import { AxisDropDown } from '../../organism/ifp-chart-toolbar/ifp-chart-toolbar.interface';
import { ScUploadModelComponent } from '../../molecule/sc-upload-model/sc-upload-model.component';
import { CustomCardService } from 'src/app/scad-insights/core/services/create-dashboard/custom-card.service';
import { loadAnalyticalSuccess } from 'src/app/scad-insights/home/<USER>/Analytical apps/analyticalApps.action';
import { title } from 'src/app/scad-insights/core/constants/header.constants';
import { prepsApiEndpoints } from 'src/app/ifp-analytics/data-prep/ifp-data-prep/constants/if-preps.constants';
import { IfpPrepService } from 'src/app/ifp-analytics/data-prep/ifp-data-prep/ifp-prep-service';
import { UsageDashboardLogService } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard.constants';


@Component({
    selector: 'app-ifp-dashboard-builder',
    templateUrl: './ifp-dashboard-builder.component.html',
    styleUrls: ['./ifp-dashboard-builder.component.scss'],
    imports: [TranslateModule, RouterLink, IfpButtonComponent, IfpModalComponent, IfpImportIndicatorsComponent, NgClass, IfpDbFileUploaderComponent, IfpDbCardComponent, NgIf, IfpChartToolbarComponent,
    GridsterModule, NgStyle, IfpImportDropdownComponent, IfpTooltipDirective, IfpSpinnerComponent, ScUploadModelComponent],
    providers: [IfpPrepService]
})
export class IfpDashboardBuilderComponent implements OnInit, OnDestroy, OnChanges {

  @HostListener('window:pointerup', ['$event.target'])
  onOutsideClick(target: HTMLElement) {
    if (target.classList.contains('fixed')) {
      this.selectedId = '';
      this.isToolbarExpanded = false;
    }
    if (!target.classList.contains('ifp-db__header-title')) {
      this.isTitleUpdate = false;
      if (this._dashboardService.dashboardProperties.title) {
        this.isWhiteSpaces = this._dashboardService.dashboardProperties.title.trim() === '';
      } else {
        this.isWhiteSpaces = false;
      }
    }
  }

  @Input() dashboardId!: string;
  @Input() selectTab!: string;
  @Input() mode: 'preview' | 'detail' | 'edit' = 'edit';
  @Input() isAiDashboard: boolean = false;

  @ViewChild('importInidcators') importInidcators!: IfpModalComponent;
  @ViewChild('loaderModal') loaderModal!: IfpModalComponent;
  @ViewChild('uploadData') uploadData!: IfpModalComponent;
  @ViewChild('dbHeader', { static: true }) dbHeader!: ElementRef;
  @ViewChild('playGround', { static: false }) playGround!: GridsterComponent;
  @ViewChild('sideBar') sideBar!: ElementRef;
  @ViewChild('downloadPrint') downloadPrint!: ElementRef;
  @ViewChild('toolbar') toolbar!: ElementRef;



  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public importType: string = 'browse';
  public showImportTypes: WritableSignal<boolean> = signal(false);
  public logoFormat: string[] = fileFormats.logoFormats;
  public selectedCards: any = [];
  public selectedId!: string;
  public subs = new SubSink();
  public selectedAllCardData: any = [];
  public selectedCardData: any = [];
  private visulaizationId!: number;
  public isPieDisabled: boolean = false;
  public chartConstants = chartConstants;
  public selectedChartType!: string;
  public contentType!: string;
  public isToolbarExpanded: boolean = false;
  options!: GridsterConfig;
  dashboard!: GridsterItem[];
  public isAddIndicator: boolean = false;
  public isToolbar: boolean = true;
  public isFilterPanel: boolean = false;
  public deletedCards: any = [];
  public height: number = 260;
  public isSticky: boolean = false;
  public itemComponent: any;
  public dashboardConstants = dashboardConstants;
  public updateDashboardTitleSubject = new Subject<string>();
  public allDropDownData: { name: string }[] = [];
  public addIndicatorPos: any = { id: 1, cols: 3, rows: 6, y: 0, x: 0, resizeEnabled: false, key: 'add_indicator' };
  public message: string = this._translate.instant('Would you like to proceed and save the current dashboard?');
  public editMessage: string = this._translate.instant('Would you like to continue editing?');
  public detailMessage: string = this._translate.instant('Are you sure you want to go back?');
  public buttonColor = buttonColor;
  public gridsterHeight: number = 500;
  public isEdit: boolean = false;
  public isEnableButton: boolean = false;
  public dataType!: string;
  public isImportDropdown: boolean = false;
  public isPreviousMode!: 'preview' | 'detail' | 'edit';
  public isTitleLengthOver: boolean = false;
  public isDragging = false;
  private offsetX = 0;
  private offsetY = 0;
  public isLeftCut: boolean = false;
  public isWhiteSpaces: boolean = false;
  public isTextareaExpanded: boolean = false;
  public isDragged: boolean = false;
  public sendData!: { key: string, value: string[] | string };
  private selectedToolbarPosition: string = 'right';
  public isTitleUpdate: boolean = false;
  public isSelectedTool!: string;
  private dragging = false;
  private startX = 0;
  private startWidth = 0;
  public toolbarWidth: number = 0;
  public uploadedFile!: any;
  public csvData: any = [];
  public prepAnchor: any;
  public cutomCardDataSourceType!: string;
  public prepId: string = '';
  private sessionId!: string;

  // dashboard detail variables //

  @HostListener('window:scroll', ['$event'])
  onScroll() {
    if (this.sideBar) {
      if (window.matchMedia('(min-width: 1023.98px)').matches) {
        const body = document.getElementsByTagName('body')[0];
        const currentScroll = window.scrollY;
        this.isSticky = false;
        if ((currentScroll > this.height + 50) && (body.offsetHeight > 500)) {
          this.isSticky = true;
        }
      }
    }
  }


  constructor(public location: Location, private store: Store, public _dashboardService: DashboardService, private _renderer: Renderer2,
    private router: Router, private _toaster: ToasterService, private route: ActivatedRoute, private _pageService: PagesService,
    private _translate: TranslateService, @Inject(DOCUMENT) private document: Document, private _themeService: ThemeService, private _cdr: ChangeDetectorRef,
    private _titleService: Title, private _elementRef: ElementRef, private _customCardService: CustomCardService, private _preService: IfpPrepService,
    private _customService: CustomCardService, public themeService: ThemeService, private log: UsageDashboardLogService) {
    this.isEdit = false;
    this.dashboardId = '';
    this._dashboardService.dashboardProperties.title = '';
    this._dashboardService.dashboardProperties.logo = '';
    if (localStorage.getItem(this._dashboardService.selectedCards)) {
      this.selectedCards = localStorage.getItem(this._dashboardService.selectedCards);
      this.selectedCards = JSON.parse(this.selectedCards);
    }
    this.subs.add(this.updateDashboardTitleSubject
      .pipe(debounceTime(800))
      .subscribe((value) => {
        const title: any = value;
        this._dashboardService.dashboardProperties.title = title;
      }));
    this.route.queryParams.subscribe(val => {
      if (val?.['id']) {
        this.dashboardId = val?.['id'];
      }
      if (val?.['tab']) {
        this.selectTab = val?.['tab'];
      }
      if (val?.['sendData']) {
        this.sendData = JSON.parse(val?.['sendData']);
      }
      if (val?.['mode']) {
        this.isEdit = val?.['mode'];
      }
      if (val['prepId']) {
        this.prepId = val['prepId'];
        this.prepAnchor = val['anchor'];
        this.uploadFileData();
      }
      if (this.dashboardId) {
        this.mode = this.isEdit ? 'edit' : 'detail';
        this.getDashboradDetail();
      }
    });
  }






  ngOnInit(): void {
    this.setGridsterOption();
    if (!this.isAiDashboard) {
      this._titleService.setTitle(`${title.bayaan} | Dashboards`);
    }
    this._dashboardService.chartSettings = [];
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }
    this.sessionId = this.log.createUUid;
    this.log.logStart(this.sessionId, logType.dashboard, this.log.currentTime );
  }

  ngOnChanges(_changes: SimpleChanges): void {
    if (_changes['dashboardId']) {
      this.getDashboradDetail();
    }
  }

  setGridsterOption() {
    this.options = {
      gridType: GridType.Fixed,
      compactType: CompactType.None,
      margin: 10,
      outerMargin: false,
      outerMarginTop: null,
      outerMarginRight: null,
      outerMarginBottom: null,
      outerMarginLeft: null,
      useTransformPositioning: false,
      mobileBreakpoint: 200,
      minCols: 12,
      maxCols: 12,
      minRows: 11,
      maxRows: 100,
      maxItemCols: 12,
      minItemCols: 1,
      maxItemRows: 100,
      minItemRows: 1,
      maxItemArea: 1000,
      minItemArea: 1,
      defaultItemCols: 1,
      defaultItemRows: 1,
      fixedColWidth: ((this.dbHeader.nativeElement.offsetWidth / 12) - 9.5),
      fixedRowHeight: 40,
      keepFixedHeightInMobile: false,
      keepFixedWidthInMobile: false,
      scrollSensitivity: 0,
      scrollSpeed: 20,
      enableEmptyCellClick: true,
      enableEmptyCellContextMenu: false,
      enableEmptyCellDrop: false,
      enableEmptyCellDrag: false,
      emptyCellDragMaxCols: 12,
      emptyCellDragMaxRows: 12,
      ignoreMarginInRow: false,
      draggable: {
        enabled: this.mode === 'edit' ? true : false
      },
      resizable: {
        enabled: this.mode === 'edit' ? true : false
      },
      swap: true,
      pushItems: true,
      disablePushOnDrag: true,
      disableScrollVertical: true,
      disableScrollHorizontal: true,
      disablePushOnResize: false,
      pushDirections: { north: true, east: true, south: true, west: true },
      pushResizeItems: false,
      displayGrid: DisplayGrid.None,
      disableWindowResize: true,
      disableWarnings: false,
      scrollToNewItems: false,
      itemChangeCallback: this.onItemChange.bind(this),
      itemResizeCallback: this.onItemResize.bind(this)
    };
  }


  onItemChange(item: GridsterItem, itemComponent: any): void {
    if (itemComponent?.gridster?.grid?.length > 0) {
      this.itemComponent = itemComponent;
      this.calculateHeight(100);
    }
  }

  onItemResize(item: any, _itemComponent: any): void {
    if (!this.selectedCards) {
      this.selectedCards = {
        experimental_statistics: [],
        analytical_apps: [],
        official_statistics: [],
        compare_statistics: [],
        add_indicator: [],
        custom_card: []
      };
    }
    if (this.selectedCards[item.key]?.length > 0 && this.selectedCards[item.key]?.find((x: { id: any; }) => x.id == item.id)) {
      const index = this.selectedCards[item.key]?.findIndex((x: { id: any; }) => x.id == item.id);
      this.selectedCards[item.key][index].cols = item.cols;
      this.selectedCards[item.key][index].rows = item.rows;
    } else {
      this.selectedCards[item.key]?.push(item);
    }
    if (_itemComponent.height > 500) {
      this.calculateHeight(80);
    }
  }

  preview(flag: 'preview' | 'detail' | 'edit') {
    // Preview Dashboard
    this._dashboardService.dashboardProperties.title = this._dashboardService?.dashboardProperties?.title ? this._dashboardService?.dashboardProperties?.title : 'Untitled';
    if (this.isPreviousMode != this.mode) {
      this.isPreviousMode = this.mode;
    }
    this.mode = flag;
    this.selectedId = '';
    this.calculateHeight(200);
    setTimeout(() => {
      this.setGridsterOption();
    }, 300);
  }



  openImportIndicators(type: string) {
    this.isAddIndicator = true;
    this.isToolbarExpanded = false;
    this.importType = type;
    if (!this.selectedCards) {
      this.selectedCards = {
        experimental_statistics: [],
        analytical_apps: [],
        official_statistics: [],
        compare_statistics: [],
        add_indicator: [],
        custom_card: []
      };
    }
    if (this.itemComponent?.gridster?.grid?.length > 0) {
      this.itemComponent?.gridster?.grid.forEach((element: { key: string | number; }) => {
        if (element?.key) {
          this.selectedCards[element.key].push(element);
        }
      });
    }
    if (type != 'upload') {
      this.isImportDropdown = true;
      this.importInidcators.createElement();
    } else {
      this.uploadData.createElement();
    }
  }

  toggleDropDown() {
    this.showImportTypes.set(this.showImportTypes() ? false : true);
  }



  generateRandomNumber(): number {
    return Math.floor((1000 + Math.random()) * 9000);
  }

  closeImport() {
    this.isAddIndicator = false;
    this.isImportDropdown = false;
    this.importInidcators.removeModal();
  }

  addAllIndicators(indicatorId: any) {
    if (!this.selectedCards || this.selectedCards?.length <= 0) {
      this.selectedCards = cloneDeep(indicatorId);
      this._dashboardService.chartSettings = cloneDeep(indicatorId);
    } else {
      Object.keys(indicatorId).forEach(key => {
        const selectedCardsKey = this.selectedCards[key] || [];
        const chartSettingsKey = this._dashboardService.chartSettings[key] || [];

        if (indicatorId[key]?.length > 0) {
          indicatorId[key].forEach((element: { id: any; }) => {
            if (!selectedCardsKey.some((x: { id: any; }) => x.id === element.id)) {
              selectedCardsKey.push(element);
              chartSettingsKey.push(element);
            }
          });
          if (!this.selectedCards[key]) {
            this.selectedCards[key] = selectedCardsKey;
            this._dashboardService.chartSettings[key] = chartSettingsKey;
          }
        }
      });
    }
    Object.keys(this.selectedCards).forEach(key => {
      if (this.selectedCards[key]?.length <= 0) {
        delete this.selectedCards[key];
      }
      if (this.selectedCards[key]?.length > 0) {
        const indicatorIdsSet = new Set(indicatorId[key].map((x: { id: any; }) => x.id));
        this.selectedCards[key] = this.selectedCards[key].filter((element: { id: any; }) => indicatorIdsSet.has(element.id));
      }
    });
    if (!this.isAiDashboard) {
      this.calculateHeight(150);
    }
    this.isAddIndicator = false;
    this.isImportDropdown = false;
    this.checkCardLength();
  }

  getObjectKeys(obj: any): string[] {
    return Object.keys(obj);
  }

  selectCard(id: string, card: any) {
    if (this.mode != 'edit') {
      return;
    }
    this.isFilterPanel = false;
    // this.isToolbarExpanded = true;
    this.selectedId = id;
    this.isPieDisabled = true;
    if (card?.key == dashboardConstants.customCard) {
      this.selectedCardData = card;
      this.customCardConfiguration();
      return;
    }
    this.subs.add(
      this.store.select(selectIndicatorGetById(this.selectedId))
        .pipe()
        .subscribe((data) => {
          if (data.body) {
            this.selectedCardData = cloneDeep(data.body);
            this.dataType = this.selectedCardData.type;
            if (this.selectedCardData?.type == this.chartConstants.INSIGHT_DISCOVERY) {
              this.selectedAllCardData = this.selectedCardData;
              const visulaId = this._dashboardService.chartSettings[chartConstants.ANALYTICAL_APPS]?.find((x: { id: string; }) => x.id == this.selectedId)?.selectedVisualId;
              this.visulaizationId = visulaId ? visulaId : this.selectedAllCardData.default_visualisation;
              this.selectedCardData = this.selectedAllCardData?.visualizations?.find((x: { id: number; }) => x.id == this.visulaizationId);
              this.allDropDownData = [];
              this.selectedAllCardData.visualizations.forEach((element: { component_title: string; }) => {
                const names = {
                  name: element.component_title
                };
                this.allDropDownData.push(names);
              });
            }
            if (this.selectedCardData?.filterPanel?.properties?.length > 0) {
              this.selectedCardData?.filterPanel?.properties.forEach((element: any) => element.filterOptions = this.setOptions(element.options));
              this.isPieDisabled = false;
            }
            if (this.selectedCardData.content_classification != chartConstants.COMPARE_STATISTICS_CNT) {
              this.contentType = this.selectedCardData.content_classification_key ? this.selectedCardData.content_classification_key : contentTypeDashboard['analytical-apps'];
              if (this.selectedCardData.content_classification_key == contentTypeDashboard['analytical-apps'] && this.selectedCardData?.type == 'coi') {
                this.contentType = contentTypeDashboard['analytical-apps'];
              }
            } else {
              this.contentType = chartConstants.COMPARE_STATISTICS;
            }
          }
          this._dashboardService.setIndicatorTitles(this.selectedCardData.component_title, this.contentType, this.selectedId);
        })
    );
  }

  changeChartType(_event: any) {
    //
  }

  checkLength() {
    let isShow = true;
    for (const key in this.selectedCards) {
      if (this.selectedCards[key].length > 0) {
        isShow = false;
      }
    }
    return isShow;
  }

  checkObjectLength() {
    return Object.keys(this.selectedCards).length;
  }

  updateTitle(title: string, key: string) {
    if (!this.contentType) {
      this.contentType = this.selectedCardData.key;
    }
    this._dashboardService.updateIndicatorTitle(title, this.contentType, this.selectedId, key);
    this._dashboardService.titleUpdated.next(key);
  }

  checkCardsLength() {
    let toolbar = false;
    Object.keys(this.selectedCards).some(key => {
      const cards = this.selectedCards[key];
      if (cards?.length > 0) {
        if (key !== dashboardConstants.customCard) {
          toolbar = true;
        }
        if (!toolbar && key === dashboardConstants.customCard) {
          toolbar = !!cards[0].chartType;
        }
      }
    });
    return toolbar;
  }

  deletaCard(event: any) {
    setTimeout(() => {
      const index = this.selectedCards[event.cntType].findIndex((x: { id: any; }) => x.id == event.id);
      this.selectedCards[event.cntType].splice(index, 1);
      this._dashboardService.chartSettings[event.cntType].splice(index, 1);
      this._dashboardService.deleteSelectedCard.next({ id: event.id, cntType: event.cntType });
      this.checkCardLength();
      if (this.selectedId == event.id) {
        this.selectedId = '';
      }
    }, 300);
  }

  checkCardLength() {
    this.isEnableButton = Object.keys(this.selectedCards).some(key => {
      const cards = this.selectedCards[key];
      if (cards?.length > 0) {
        if (key !== dashboardConstants.customCard) {
          return true;
        }
        return !!cards[0].chartType;
      }
      return false;
    });

    if (!this.isEnableButton) {
      this.addIndicatorPos = { id: 1, cols: 3, rows: 6, y: 0, x: 0, resizeEnabled: false, key: 'add_indicator' };
    }
  }

  openFilterPanel(_event: boolean) {
    this.isToolbarExpanded = true;
    this.isFilterPanel = true;
    this.toolbarWidth = 480;
  }

  // closeFilterPanel(_event: boolean) {
  //   this.isFilterPanel = false;
  //   this.isToolbar = true;
  // }



  setOptions(options: string[]) {
    const filterOptions: { name: string }[] = [];
    if (options?.length > 0) {
      options.forEach(element => {
        const data = {
          name: element
        };
        filterOptions.push(data);
      });
    }
    return filterOptions;
  }

  updateDashboardTitle(event: any) {
    this.isTitleLengthOver = event.target.value?.length > 60 ? true : false;
    this.isWhiteSpaces = event.target.value.trim() === '';
    this.updateDashboardTitleSubject.next(event.target.value);
  }


  uploadLogo(event: any) {
    this._dashboardService.dashboardProperties.logo = event.url.changingThisBreaksApplicationSecurity;
    this._dashboardService.dashboardProperties.file = event.file[0];
  }

  deleteLogo(_event: any) {
    this._dashboardService.dashboardProperties.logo = '';
    this._dashboardService.dashboardProperties.file = '';
  }

  saveDashboard() {
    if (this.isTitleLengthOver) {
      this._toaster.warning('Maximum title limit reached! The maximum limit for this action is 60.');
      return;
    }
    if (this.isWhiteSpaces) {
      this._toaster.warning('Please remove any unnecessary whitespace.');
      return;
    }
    this.selectedId = '';
    this.preview('preview');

    this.loaderModal.createElement();
    // use opposite theme
    this._renderer.removeClass(document.body, (this._themeService.defaultTheme == 'light' ? 'ifp-light-theme' : 'ifp-dark-theme'));
    this._renderer.addClass(document.body, (this._themeService.defaultTheme == 'light' ? 'ifp-dark-theme' : 'ifp-light-theme'));
    this.canvasRender(this.downloadPrint.nativeElement).then((result) => {
      const darkImage = result;
      this.canvasRender(this.downloadPrint.nativeElement).then((resultNew) => {
        const lightImage = resultNew;
        const data = {
          name: this._dashboardService.dashboardProperties.title ? this._dashboardService.dashboardProperties.title : 'Untitled',
          nodes: this.getClassificationNodes()
        };
        const logo = this._dashboardService.dashboardProperties.file;
        this._dashboardService.createDashboard(data, logo, this.convertBase64ToFile(this._themeService.defaultTheme == 'light' ? lightImage : darkImage, 'thumbnail_light'), this.convertBase64ToFile(this._themeService.defaultTheme == 'light' ? darkImage : lightImage, 'thumbnail_dark'),
          this.isEdit, this.dashboardId).subscribe(_resp => {
          this.loaderModal.removeModal();
          this.router.navigate(['store/dashboards-basic']);
          localStorage.removeItem(this._dashboardService.selectedCards);
          this._toaster.success(`Dashboard ${this.isEdit ? ' Updated' : ' Created'} Successfully`);
          this._dashboardService.dashboardProperties.title = '';
          this._dashboardService.dashboardProperties.logo = '';
        });
      });
    }
    );
    this._renderer.removeClass(document.body, (this._themeService.defaultTheme == 'light' ? 'ifp-dark-theme' : 'ifp-light-theme'));
    this._renderer.addClass(document.body, (this._themeService.defaultTheme == 'light' ? 'ifp-light-theme' : 'ifp-dark-theme'));
  }





  canvasRender(element: HTMLElement) {
    return new Promise((resolve: (value: string) => void) => html2canvas(element, {allowTaint: true, useCORS: true}).then(canvas => {
      resolve(canvas.toDataURL('image/png'));
    }));
  }


  getClassificationNodes() {
    const dynamicObject: any = {};
    const chartSettings = this._dashboardService.chartSettings;
    const keys = Object.keys(chartSettings);
    keys.forEach(key => {
      let keyData: any = [];
      if (chartSettings[key]?.length >= 0) {
        keyData = [];
        chartSettings[key].forEach((element: {
          id: any; cols: number, rows: number, x: number, y: number, type: string, title?: string, key: string,
          Yaxis: AxisDropDown[], Xaxis: AxisDropDown, data: any, screener: boolean, chartType: string;
        }) => {
          const index = this.selectedCards[key]?.findIndex((x: { id: any; }) => x.id == element.id);
          if (index >= 0) {
            element.cols = this.selectedCards[key][index].cols;
            element.rows = this.selectedCards[key][index].rows;
            element.x = this.selectedCards[key][index].x;
            element.y = this.selectedCards[key][index].y;
            element.type = this.selectedCards[key][index].type;
            element.screener = this.selectedCards[key][index].screener;
            element.key = this.selectedCards[key][index].key;
            element.Xaxis = this.selectedCards[key][index].Xaxis;
            element.Yaxis = this.selectedCards[key][index].Yaxis;
            element.id = element.id.toString();
          }
          if (element.key == dashboardConstants.customCard) {
            delete element.data;
          }
          const data = {
            id: element.id,
            properties: element
          };
          if (element.rows && element.cols) {
            keyData.push(data);
          }
        });
      }
      if (keyData?.length > 0) {
        dynamicObject[key] = keyData;
      }
    });
    return dynamicObject;
  }

  calculateHeight(addValue: number) {
    const cards: any[] = [];
    Object.keys(this.selectedCards).forEach((key) => cards.push(...this.selectedCards[key]));
    setTimeout(() => {
      const maxY = cards.reduce((max: number, item: { y: number; }) => Math.max(max, item.y), Number.NEGATIVE_INFINITY);
      const maxRow = cards.reduce((max: number, item: { rows: number; }) => Math.max(max, item.rows), Number.NEGATIVE_INFINITY);
      this.gridsterHeight = ((40 * (maxY + maxRow)) + (maxRow * addValue));
    }, 300);
  }

  convertBase64ToFile(base64Data: any, filename: string) {
    const base64String = base64Data.split(',')[1]; // Assuming the base64Data format is "data:image/png;base64,<actual_base64_data>"
    const byteCharacters = atob(base64String);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: 'image/png' });
    return new File([blob], filename, { type: 'image/png' });
  }


  // dashboard detail start //

  getDashboradDetail() {
    this._dashboardService.getDashboardData(this.dashboardId ?? '', this.selectTab).subscribe(resp => {
      this.setGridsterOption();
      this._dashboardService.dashboardProperties.title = resp.name;
      if (resp.logo && resp.logo != null) {
        this._dashboardService.dashboardProperties.logo = `data:${resp.logo?.type};base64,${resp?.logo?.content.replace(/"/g, '')}`;
      }
      this.setSelectedCardsData(resp.nodes);
      this.checkCardLength();
    });
  }

  setSelectedCardsData(nodes: any) {
    this.selectedCards = {
      experimental_statistics: [],
      analytical_apps: [],
      official_statistics: [],
      compare_statistics: [],
      add_indicator: [],
      custom_card: []
    };
    this._dashboardService.chartSettings = {
      experimental_statistics: [],
      analytical_apps: [],
      official_statistics: [],
      compare_statistics: [],
      custom_card: []
    };
    const ids: any = [];
    Object.keys(nodes).forEach((key) => {
      if (nodes[key]?.length > 0) {
        nodes[key].forEach((element: {
          nodeRequest: any; id: string; properties: any
        }) => {
          if ((key === 'analytical_apps' || key === 'experimental_statistics') && element.properties?.type != 'Livability Dashboard') {
            const dispatchPayload: any = {
              id: element.id,
              contentType: key === 'analytical_apps' ? (element.properties.type == 'coi' || element.properties.type == 'scad' ? contentType['scad_official_indicator'] : key) : 'innovative-insights'
            };
            if (key === 'experimental_statistics') {
              dispatchPayload.visa = true;
            }
            if (key === 'analytical_apps' && element.properties?.selectedFilter) {
              this.callInsightDiscoveryFilter(element.properties);
            }
            this.store.dispatch(getIndicator(dispatchPayload));
          }
          if (key === 'official_statistics') {
            const dispatchPayload: any = {
              id: element.id,
              contentType: element.properties.screener ? contentType['official-insights'] : contentType['scad_official_indicator']
            };
            ids.push(element.id);
            this.store.dispatch(getIndicator(dispatchPayload));
          }
          if (element.properties?.type == 'Livability Dashboard') {
            this._dashboardService.getLiveabilityData(element.id);
          }

          if (key === 'compare_statistics') {
            this._dashboardService.getCompareIndicatorData(element.id, element.properties.title);
          }


          const data = {
            id: element.id,
            x: element.properties.x,
            y: element.properties.y,
            cols: element.properties.cols,
            rows: element.properties.rows,
            type: element.properties.type,
            requestNode: element.nodeRequest,
            screener: element.properties.screener,
            key: element.properties.key,
            Xaxis: element.properties.Xaxis,
            Yaxis: element.properties.Yaxis,
            chartType: element.properties.chartType
          };
          this.selectedCards[key].push(data);
          this._dashboardService.chartSettings[key].push(element.properties);
        });
      }
    });
    this.store.dispatch(getStatisticsInsights({ id: ids, name: contentTypeDashboard['statistics-insights'] }));
    Object.keys(this.selectedCards).forEach(key => {
      if (this.selectedCards[key]?.length <= 0) {
        delete this.selectedCards[key];
      }
    });
    if (!this.isAiDashboard) {
      this.calculateHeight(40);
    }
  }


  callInsightDiscoveryFilter(filters: any) {
    this._pageService.getFilterCall(filters.filterPayload).subscribe(dataValue => {
      let updatedResponse: any = [];
      this.store.select(selectIndicatorGetById(filters.id.toString())).subscribe(resp => {
        updatedResponse = [];
        updatedResponse = cloneDeep(resp);
        if (updatedResponse.body && updatedResponse.body.visualizations?.length > 0) {
          const dataIndex = updatedResponse.body.visualizations.findIndex((x: { id: number; }) => x.id == filters.selectedVisualId);
          const metaIndex = updatedResponse.body.visualizations[dataIndex].indicatorVisualizations.visualizationsMeta.findIndex((y: { id: any; }) => y.id == updatedResponse.body.visualizations[dataIndex].indicatorVisualizations.visualizationDefault);
          updatedResponse.body.visualizations[dataIndex].indicatorVisualizations.visualizationsMeta[metaIndex].seriesMeta[0].data = dataValue[0].data;
          const id = filters.id;
          const initialization = dataValue.type !== 'insights-discovery' ? updatedResponse.body : updatedResponse.body.visualizations?.[0];
          const postData = { data: { ...updatedResponse.body, id }, status: { status: true, errorMessage: '', loader: false }, initialData: initialization, isRender: true };
          this.store.dispatch(loadAnalyticalSuccess({ data: postData }));
          // setTimeout(() => {
          this._dashboardService.settingsChanged.next({ type: contentType['analytical-apps'], id: filters.id, tools: this._dashboardService.cardFilter });
          // }, 3000);
        }
      });

    });
  }


  // dashboard detail end //


  exportPDF() {
    this.selectedId = '';
    if (this.mode != 'detail' && this.mode != 'preview') {
      this.preview('preview');
    }
    this.loaderModal.createElement();
    setTimeout(() => {
      this._dashboardService.downloadPdf(this.downloadPrint.nativeElement, this._dashboardService.dashboardProperties.title ?? 'Untitled 1');
      this.loaderModal.removeModal();
    }, 500);
  }


  onMouseDown(event: MouseEvent) {
    if (this.isDragged) {
      this.isDragging = true;
      this.offsetX = event.clientX;
      this.offsetY = event.clientY;
    }
  }

  onMouseUp() {
    this.isDragging = false;
  }

  onMouseMove(event: MouseEvent) {
    if (this.isDragging) {
      this.isToolbarExpanded = true;
      const dx = event.clientX - this.offsetX;
      const dy = event.clientY - this.offsetY;
      const toolbar = this.toolbar.nativeElement as HTMLElement;
      const left = toolbar.offsetLeft > (this.document.documentElement.clientWidth - 480) ? (this.document.documentElement.clientWidth - 480) : toolbar.offsetLeft;
      toolbar.style.left = `${(left > 0 ? left : 0) + dx}px`;
      toolbar.style.top = `${toolbar.offsetTop + dy}px`;
      this.offsetX = event.clientX;
      this.offsetY = event.clientY;
      if (toolbar.offsetLeft > (this.document.documentElement.clientWidth / 2)) {
        this.changeToolbarRight();
      } else {
        this.changeToolbarToLeft();
      }
    }
  }



  expandOrCollapseToolbar() {
    const isRight = this.toolbar.nativeElement.classList.contains('ifp-db__toolbar--right');
    const defaultLang = this._themeService.defaultLang;
    this.toolbar.nativeElement.style.left = isRight ? (defaultLang == 'en' ? 'auto' : 0) : (defaultLang == 'en' ? 0 : 'auto');
    this.toolbar.nativeElement.style.right = isRight ? (defaultLang == 'en' ? 0 : 'auto') : (defaultLang == 'en' ? 'auto' : 0);
    this.isToolbarExpanded = !this.isToolbarExpanded;
    if (this.isToolbarExpanded) {
      this.toolbarWidth = this.isSelectedTool == 'data' ? (window.innerWidth - ((window.innerWidth / 2) + 100)) : 480;
    }
    if (this.isToolbarExpanded && (!this.selectedId || this.selectedId == '')) {
      this.selectCard(this.selectedCards[Object.keys(this.selectedCards)[0]][0].id, this.selectedCards[Object.keys(this.selectedCards)[0]][0]);
    }
  }

  closeExpandModel(_event: boolean) {
    const isRight = this.toolbar.nativeElement.classList.contains('ifp-db__toolbar--right');
    const defaultLang = this._themeService.defaultLang;
    this.toolbar.nativeElement.style.left = isRight ? (defaultLang == 'en' ? 'auto' : 0) : (defaultLang == 'en' ? 0 : 'auto');
    this.toolbar.nativeElement.style.right = isRight ? (defaultLang == 'en' ? 0 : 'auto') : (defaultLang == 'en' ? 'auto' : 0);
    // this._renderer.setStyle(this._elementRef.nativeElement.querySelector('.resizable'), 'width', `${0}px`);
    // this._renderer.setStyle(this._elementRef.nativeElement.querySelector('.resizable'), 'max-width', `${0}px`);
    this.isToolbarExpanded = false;
  }

  openTextArea(event: boolean) {
    this.isToolbarExpanded = event;
    this.isTextareaExpanded = event;
  }


  // toolbar position change  //
  changeToolbarPosition(event: string) {
    this.selectedToolbarPosition = event;
    if (this.selectedToolbarPosition == 'drag') {
      this.isDragged = false;
    }
    this.isDragged = false;
    if (event == 'left') {
      this.changeToolbarToLeft();
      this.toolbar.nativeElement.style.left = 0;
      this.toolbar.nativeElement.style.right = 'auto';
    } else if (event == 'right') {
      this.changeToolbarRight();
      this.toolbar.nativeElement.style.left = 'auto';
      this.toolbar.nativeElement.style.right = 0;
    } else {
      this.isDragged = true;
    }
  }

  pinDashboard(_event: boolean) {
    if (this.selectedToolbarPosition == 'drag') {
      this.isDragged = !this.isDragged;
    }
  }

  changeToolbarToLeft() {
    this.toolbar.nativeElement.classList.add('ifp-db__toolbar--left');
    this.toolbar.nativeElement.classList.remove('ifp-db__toolbar--right');
    this.isLeftCut = true;
  }

  changeToolbarRight() {
    this.toolbar.nativeElement.classList.remove('ifp-db__toolbar--left');
    this.toolbar.nativeElement.classList.add('ifp-db__toolbar--right');
    this.isLeftCut = false;
  }

  goBack() {
    if (this.location?.back) {
      this.location.back();
    } else {
      this.router.navigate(['/home']);
    }
  }

  // ** for customcard //
  updateCustomChart(event: string, cardIndex: number) {
    if (!this.dashboardId) {
      const singleDimentionChartType: string[] = ['line', 'column', 'bar', 'table', 'circular'];
      this.selectedCards[dashboardConstants.customCard][cardIndex].chartType = event;
      const chartData = this.cutomCardDataSourceType == 'Upload Data' ? this.csvData : (singleDimentionChartType.includes(event) ? singleDimentionData : multiDimentionData);
      this.selectedCards[dashboardConstants.customCard][cardIndex].data = chartData;
      const cardId = this.selectedCards[dashboardConstants.customCard][cardIndex].id;
      const customCardIndex = this._dashboardService.chartSettings['custom_card'].findIndex((x: { id: any; }) => x.id == cardId);
      if (customCardIndex >= 0) {
        this._dashboardService.chartSettings['custom_card'][customCardIndex].chartType = event;
        this._dashboardService.chartSettings['custom_card'][customCardIndex].data = chartData;
      }
    }
    this.selectedCardData = this.selectedCards[dashboardConstants.customCard][cardIndex];
    this.selectedCardData.isOpen = true;
    if (this.mode != 'detail' && this.mode != 'preview') {
      this.selectedId = this.selectedCards[dashboardConstants.customCard][cardIndex].id;
    }
    this.isToolbarExpanded = true;
    this.isSelectedTool = 'data';
    this.setToolBarWidth();
    this.checkCardLength();
  }

  onMouseResizeDown(event: MouseEvent) {
    if ((event.target as HTMLElement).classList.contains('handle') && !this.isDragged && this.isSelectedTool == 'data') {
      this.dragging = true;
      this.startX = event.clientX;
      this.startWidth = this._elementRef.nativeElement.querySelector('.resizable').offsetWidth;
      this._renderer.setStyle(document.body, 'cursor', 'ew-resize');
      this._renderer.setStyle(document.body, 'user-select', 'none');
    }
  }

  onResizeMouseMove(event: MouseEvent) {
    if (!this.dragging) {
      return;
    }
    const newWidth = this.startWidth + (this.selectedToolbarPosition == 'right' ? (this.startX - event.clientX) : (event.clientX - this.startX));
    this._renderer.setStyle(this._elementRef.nativeElement.querySelector('.resizable'), 'width', `${newWidth}px`);
    this._renderer.setStyle(this._elementRef.nativeElement.querySelector('.resizable'), 'max-width', `${newWidth}px`);
  }

  onResizeMouseUp(event: MouseEvent) {
    if (this.dragging) {
      this.dragging = false;
      this._renderer.setStyle(document.body, 'cursor', 'default');
      this._renderer.setStyle(document.body, 'user-select', 'auto');
    }
  }

  selectTool(event: string) {
    this.isSelectedTool = event;
    this.setToolBarWidth();
  }

  setToolBarWidth() {
    this.toolbarWidth = this.isSelectedTool == 'data' ? (window.innerWidth - ((window.innerWidth / 2) + 100)) : 480;
  }

  customCardConfiguration() {
    const multiDimentionTypes: string[] = ['pie', 'doughnut'];
    const customCardIndex = this._dashboardService.chartSettings[dashboardConstants.customCard].findIndex((x: { id: any; }) => x.id == this.selectedId);
    if (multiDimentionTypes.includes(this._dashboardService.chartSettings[dashboardConstants.customCard][customCardIndex].chartType)) {
      this.isPieDisabled = false;
    }
  }

  // **for upload/add data functions //;
  closeUploadModel() {
    this.uploadData.removeModal();
  }

  uploadOrAddData(event: { data?: any, type: string, result: any, objectId: string, fileName: string }) {
    this.cutomCardDataSourceType = event.type;
    if (!this.selectedCards.custom_card) {
      this.selectedCards['custom_card'] = [];
      this._dashboardService.chartSettings['custom_card'] = [];
    }
    const customeCard = {
      id: this.generateRandomNumber(), cols: 3, rows: 11, y: 0, x: 0, key: dashboardConstants.customCard, type: '',
      Xaxis: axisDropDowns[0].options[0], Yaxis: [axisDropDowns[0].options[1]], dataObjectId: event.objectId,
      fileName: event.fileName, chartType: 'column'
    };
    this.selectedCards['custom_card'].push(customeCard);
    this._dashboardService.chartSettings['custom_card'].push(customeCard);
    this.checkCardLength();
    setTimeout(() => {
      this.showImportTypes.set(false);
    }, 100);
    this.uploadedFile = undefined;
    if (this.cutomCardDataSourceType == 'Upload Data') {
      this.uploadedFile = event.data;
      this.csvData = event.result;
      this.selectedId = customeCard.id.toString();
      this._dashboardService.setUploadCardTitle(event?.data?.file?.[0]?.name ?? 'Custom card', this.selectedId);
    }
    this.uploadData.removeModal();
    const cardIndex = this.selectedCards[dashboardConstants.customCard].findIndex((x: { id: number; }) => x.id == customeCard.id);
    this.updateCustomChart('column', cardIndex);
  }


  getData(index: number) {
    return this._dashboardService.chartSettings[dashboardConstants.customCard][index].data;
  }


  ngOnDestroy(): void {
    localStorage.setItem(this._dashboardService.selectedCards, JSON.stringify(this.selectedCards));
    if (this.sessionId) {
      this.log.logEnds(this.sessionId, this.log.currentTime );
    }

  }

  uploadFileData() {
    this._preService.getMethodRequest(`${prepsApiEndpoints.getNode + this.prepId + prepsApiEndpoints.workflowPreview}`, { limit: 200, offset: 0, page: 1, anchor: this.prepAnchor }).subscribe({
      next: next => {
        this._customService.uploadCustomData(next.records).subscribe({
          next: data => {
            const event = {
              data: {},
              type: 'Upload Data',
              fileName: '',
              result: data.records,
              objectId: data.dataId
            };
            this.uploadOrAddData(event);
          },
          error: error => {
            this._toaster.error(error.message);
            return;
          }
        });
      },
      error: error => {
        this._toaster.error(error.message);
      }
    });
  }



}



