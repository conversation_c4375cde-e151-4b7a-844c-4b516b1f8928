<div class="ifp-del-users">
  <h2 class="ifp-del-users__title">{{'Deleted Users' | translate}}</h2>
  <table class="ifp-del-users__table">
    <tr class="ifp-del-users__table-row ifp-del-users__table-row--head">
      <th class="ifp-del-users__table-col">{{'Username'}}</th>
      <th class="ifp-del-users__table-col">{{'User email'}}</th>
      <th class="ifp-del-users__table-col">{{'User role'}}</th>
      <th class="ifp-del-users__table-col">{{'Entity name'}}</th>
      <th class="ifp-del-users__table-col">{{'Deleted by'}}</th>
      <th class="ifp-del-users__table-col">{{'Deleted on'}}</th>
      <th class="ifp-del-users__table-col">{{'Deleted by role'}}</th>
    </tr>
    @for (item of tableData; track $index) {
      <tr class="ifp-del-users__table-row">
        <td class="ifp-del-users__table-col">{{item.userName}}</td>
        <td class="ifp-del-users__table-col">{{item.userEmail}}</td>
        <td class="ifp-del-users__table-col">{{item.userRole}}</td>
        <td class="ifp-del-users__table-col">{{item.entityName}}</td>
        <td class="ifp-del-users__table-col">{{item.deletedBy}}</td>
        <td class="ifp-del-users__table-col">{{item.deletedAt | date:dateFormat }}</td>
        <td class="ifp-del-users__table-col">{{item.deletedByRole}}</td>
      </tr>
    }
  </table>
</div>
