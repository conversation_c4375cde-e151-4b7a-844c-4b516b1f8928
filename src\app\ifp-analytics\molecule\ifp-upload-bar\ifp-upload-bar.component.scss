@use "../../../../assets/ifp-styles/abstracts/index" as *;
.ifp-upload-box {
    background-color: $ifp-color-grey-bg;
    border-radius: 7px;
    overflow: hidden;
    &__sec-wrapper{
      display: flex;
      align-items: center;
      padding: $spacer-3 $spacer-3( $spacer-3 -  $spacer-1 ) $spacer-3 ;
    }
    &__svg-wrapper {
      margin-inline-end: $spacer-2;
    }
    &__data-wrapper {
      display: flex;

    }
    &__cross{
      margin-inline-start: auto;
      cursor: pointer;
    }
    &__bar {
      height:$spacer-1;
      background-color: $ifp-progress-green;
    }
    &__text-wrapper {
      margin-inline-start:$spacer-3;
      text-align: start;
    }
    &__text {
      color: $ifp-color-primary-grey;
      font-size: $ifp-fs-4;
      overflow: hidden;
      text-overflow: ellipsis;
      text-wrap: nowrap;
    }
    &__text-wrapper{
      max-width: calc(100% - 66px);
    }
    &__size {
      font-size: $ifp-fs-3;
      color: $ifp-color-grey-14;
    }
    &__error {
      background-color: $ifp-color-red;
    }

    &__error-light {
      background-color: $ifp-color-pale-red;
    }
}
:host-context(.ifp-dark-theme) {
  .ifp-upload-box {
    &__text {
      color: $ifp-color-white-global;
    }
    &__error-light {
      .ifp-upload-box{
        &__text {
          color: $ifp-color-white-global;
        }
        &__cross{
          color: $ifp-color-white-global;
        }
      }
    }
  }
}
