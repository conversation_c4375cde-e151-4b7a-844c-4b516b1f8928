import { Component, EventEmitter, Output, Input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { buttonClass, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { AbstractControl, FormControl, FormGroup, ReactiveFormsModule, ValidationErrors, Validators } from '@angular/forms';
import { ShareAppsData } from './ifp-share-modal.interface';
import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';

@Component({
    selector: 'app-ifp-share-modal',
    templateUrl: './ifp-share-modal.component.html',
    styleUrls: ['./ifp-share-modal.component.scss'],
    imports: [TranslateModule, ReactiveFormsModule]
})
export class IfpShareModalComponent {

  @Input() sharedAppsCount: number = 0;
  @Input() modelHead: string = 'Share Apps';
  @Input() shareItem: string = 'Apps';
  @Input() isHideTitleColumn: boolean = false;
  @Output() closeShareModal = new EventEmitter();
  @Output() submitShare: EventEmitter<ShareAppsData> = new EventEmitter<ShareAppsData>();

  public buttonIconPosition = buttonIconPosition;
  public buttonClass = buttonClass;
  public isValid: boolean = true;
  public receipientList: string[] = [];
  public shareForm!: FormGroup;
  public isSubmit: boolean = false;
  public currentUser!: string;
  constructor(private _msalService: IFPMsalService) {
    this.currentUser = this._msalService.getLoginData?.account?.username;
    this.shareForm = new FormGroup({
      email: new FormControl('', [Validators.email, this.userValidation.bind(this)]),
      name: new FormControl('', [Validators.maxLength(100)]),
      comment: new FormControl('', [Validators.maxLength(300)])
    });
  }


  userValidation(control: AbstractControl): ValidationErrors | null {
    if (control.value && control.value.toLowerCase() === this.currentUser.toLowerCase()) {
      return { notCurrentUserEmail: true };
    }
    return null;
  }

  closeModal() {
    this.shareForm.setValue({
      email: '',
      name: '',
      comment: ''
    });
    this.isValid = false;
    this.receipientList = [];
    this.closeShareModal.emit();
  }

  onEmailEntered() {
    if (!this.shareForm.controls['email'].invalid) {
      const email = this.shareForm.controls['email'].value;
      this.shareForm.patchValue({
        email: ''
      });
      if (this.receipientList.includes(email)) {
        return;
      }
      if (email) {
        this.receipientList.push(email);
        this.isValid = true;
      }
    } else {
      this.isValid = false;
    }
    // On enter press
  }

  removeUser(index: number) {
    this.receipientList.splice(index, 1);
    this.isValid = this.receipientList.length !== 0;
  }

  // shareClicked(event: any, type: string = 'share') {
  //   if (type === 'send') {
  //     this.onSubmit();
  //   }
  // }

  onSubmit() {
    if (this.isHideTitleColumn) {
      this.shareForm.controls['name'].clearValidators();
      this.shareForm.controls['name'].updateValueAndValidity();
    }
    this.isSubmit = true;
    if (this.shareForm.valid && (this.receipientList.length || (this.shareForm.controls['email'].value !== ''))) {
      if (this.shareForm.controls['email'].value !== '') {
        this.receipientList.push(this.shareForm.controls['email'].value);
      }
      const data = {
        shareList: this.receipientList,
        name: this.shareForm.controls['name'].value,
        comment: this.shareForm.controls['comment'].value,
        shareNodes: []
      };
      this.isSubmit = false;
      this.submitShare.emit(data);
      this.closeModal();
    } else {
      this.isValid = false;
    }
  }
}
