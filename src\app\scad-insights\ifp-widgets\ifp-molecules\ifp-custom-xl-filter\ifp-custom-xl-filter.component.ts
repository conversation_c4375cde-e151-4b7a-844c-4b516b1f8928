import { AfterViewChecked, ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, input, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { IfpDropdownComponent } from '../../ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { IfpButtonComponent } from '../../ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { all, chartConstants, checkAllArray } from 'src/app/scad-insights/core/constants/chart.constants';
import { cloneDeep } from 'lodash';
import { PagesService } from 'src/app/scad-insights/core/services/pages/pages.service';
import { DownloadService } from 'src/app/scad-insights/core/services/download.service';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { FilterService } from 'src/app/scad-insights/core/services/filter/filter.service';
import { Security } from 'src/app/scad-insights/core/interface/indicator.interface';

@Component({
    selector: 'ifp-ifp-custom-xl-filter',
    templateUrl: './ifp-custom-xl-filter.component.html',
    styleUrl: './ifp-custom-xl-filter.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        IfpDropdownComponent,
        IfpButtonComponent,
        TranslateModule
    ]
})
export class IfpCustomXlFilterComponent implements OnChanges, AfterViewChecked {

  public security = input<Security>();

  @Input() filters: any = [];
  @Input() chartFilter: any = [];
  @Input() allChartsData: any = [];
  @Input() isApi: boolean = false;
  @Input() isOpen: boolean = false;
  @Input() timeUnit!: string;
  @Input() tableFields: any = [];
  @Output() dismissModel = new EventEmitter();

  public buttonClass = buttonClass;

  public selectedFilter: any = [];
  public visulaisationData: any;
  public timePeriodOptions: any = [];
  public isDropdown: boolean = false;
  public isChange: boolean = false;
  public isMainDropdown: boolean = false;

  constructor(private _pageService: PagesService, private _downloadService: DownloadService, private _toasterService: ToasterService, private _cdr: ChangeDetectorRef,
    private _themeService: ThemeService, private _translateService: TranslateService, private _filterService: FilterService) {
  }



  ngOnChanges(_changes: SimpleChanges): void {
    this.setSelectedFilter();
    if (this.allChartsData && this.chartFilter?.length > 0) {
      this.visulaisationData = this.allChartsData?.visualizations?.find((x: { component_title: number; }) => x.component_title == this.chartFilter[0].name);
      this.filters = this.visulaisationData.filterPanel;
    }
    this.isDropdown = true;
    this.isMainDropdown = true;
  }

  setSelectedFilter() {
    this.selectedFilter = cloneDeep(this.filters?.properties);
    if (this.selectedFilter?.length > 0) {
      this.selectedFilter.forEach((element: { value: any[]; default: any; options: any[], path: string }) => {
        element.value = [];
        element.value.push(this._themeService.defaultLang == 'en' ? element.default : element.default);
        if (element.path == chartConstants.TIME_PERIOD) {
          this.timePeriodOptions = element.options;
        }
      });
    }
  }

  chartChange(event: any) {
    this.visulaisationData = this.allChartsData?.visualizations?.find((x: { component_title: number; }) => x.component_title == event.name);
    this.filters = this.visulaisationData.filterPanel;
    this.setSelectedFilter();
    this.isChange = true;
    this.isDropdown = false;
  }

  applyFilter(event: any, index: number) {
    this.selectedFilter[index].value = event;
  }


  closeModel() {
    this.isChange = true;
    this.isDropdown = false;
    this.isMainDropdown = false;
    this.dismissModel.emit(true);
  }



  download() {
    if (!this.isApi) {
      this.customFilter();
      return;
    }
    const filterBy: any = {};
    this.selectedFilter.forEach((element: { value: any; path: string | number; }) => {
      if (element.path == chartConstants.TIME_PERIOD) {
        element.value = element.value.map((item: string) => item);
      }
      if (element.path == chartConstants.TIME_PERIOD && checkAllArray(element.value)) {
        element.value = this.timePeriodOptions;
      }

       // execlude (Category, Gender, Salary, Risk, DaysDelay,) if it's CFDM
       if(this.filters.isCFD) {
        if(element.path != chartConstants.CATEGORY_PATH && 
          element.path != chartConstants.AGE_PATH &&
          element.path != chartConstants.DAYS_DELAY_PATH && 
          element.path != chartConstants.GENDER_PATH ) {
            filterBy[element.path == chartConstants.TIME_PERIOD ? chartConstants.YEAR_PERIOD : element.path] = element.value.map((x: string) => x) && element.value?.length == 1 ? element.value.toString() : (element.path == chartConstants.TIME_PERIOD ? this.getOptions(element.value) : element.value.map((x: string) => x));
          }
      } else {
        filterBy[element.path == chartConstants.TIME_PERIOD ? chartConstants.YEAR_PERIOD : element.path] = element.value.map((x: string) => x) && element.value?.length == 1 ? element.value.toString() : (element.path == chartConstants.TIME_PERIOD ? this.getOptions(element.value) : element.value.map((x: string) => x));
      }
      
    });
    const data = {
      meta: [
        {
          dbColumn: this.visulaisationData.indicatorVisualizations?.visualizationsMeta[0].dbColumn,
          dbIndicatorId: this.visulaisationData.indicatorVisualizations?.visualizationsMeta[0].seriesMeta[0].dbIndicatorId,
          viewName: this.visulaisationData.indicatorVisualizations?.visualizationsMeta[0].viewName,
          filterBy: filterBy
        }
      ]
    };
    this._pageService.getFilterCall(data.meta).subscribe(resp => {
      if (resp[0].data.length > 1000000) {
        this._toasterService.info('Maximum records reached (1M). Please include more filters, and refine queries for optimal performance.');
        return;
      }
      const downloadData: any = [];
      if (resp[0].data?.length > 0) {
        downloadData.push(...resp[0].data);
      }
      this._downloadService.exportToExcel(this.setTableData(downloadData), this.visulaisationData.component_title.replace('/', ' '), this.security()?.name, this.security()?.description);
      this.closeModel();
    });
  }


  getOptions(options: any) {
    const opt = cloneDeep(options);
    if (checkAllArray(opt)) {
      const index = opt.findIndex((x: string) => all.includes(x));
      opt.splice(index, 1);
    }
    return opt;
  }


  ngAfterViewChecked(): void {
    if (this.isChange) {
      this.isDropdown = true;
      this.isChange = false;
      this.isMainDropdown = true;
      this._cdr.detectChanges();
    }
  }


  customFilter() {
    const data: any = this.allChartsData?.indicatorVisualizations?.visualizationsMeta[0]?.seriesMeta[0]?.data;
    const downloadData: any = [];
    if (data?.length > 0) {
      data.forEach((element: any) => {
        let isContainData = true;
        if (this.selectedFilter?.length > 0) {
          this.selectedFilter.forEach((filter: any) => {
            filter.value = filter.value.map((item: string) => item.toLowerCase());
            if (!filter.value.includes(element[filter.path].toLowerCase())) {
              isContainData = false;
            }
          });
          if (isContainData) {
            downloadData.push(element);
          }
        }
      });
    }
    this._downloadService.exportToExcel(this.setTableData(downloadData), this.allChartsData.component_title.replace('/', ' '), this.security()?.name, this.security()?.description);
    this.closeModel();
  }


  setTableData(data: any) {
    let dynamicObject: any = {};
    const tableData: any[] = [];
    if (data?.length > 0) {
      data.forEach((value: any) => {
        dynamicObject = {};
        if (this.tableFields?.length > 0) {
          this.tableFields.forEach((cell: { label: any; path: any; }) => {
            if (!this.timeUnit) {
              this.timeUnit = 'Yearly';
            }
            if (value[cell.path] !== undefined) {
              let key = cell.label;
              if (cell.label === this._translateService.instant('DATE')) {
                key = this.timeUnit === 'Yearly' ? this._translateService.instant('Year') : (this.timeUnit === 'Quarterly' ? this._translateService.instant('Quarter') : 'Date');
              }
              dynamicObject[key] = cell.path === 'OBS_DT' && (this.timeUnit === 'Yearly' || this.timeUnit === 'y') ? value['YEAR'] : (cell.path === 'OBS_DT' && this.timeUnit === 'Quarterly' ? this._filterService.convertDateToQuarter(value[cell.path], 'table') : value[cell.path]);
            }
          });
        }
        tableData.push(cloneDeep(dynamicObject));
      });
    }
    return tableData;
  }


}
