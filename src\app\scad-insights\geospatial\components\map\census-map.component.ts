import { After<PERSON>iewInit, Component, On<PERSON><PERSON>roy, OnInit, HostListener, Input, Output, ViewChild, ElementRef, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

import WebMap from '@arcgis/core/WebMap.js';
import MapView from '@arcgis/core/views/MapView.js';
import { SubSink } from 'subsink';
import { finalize } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { MapDataService } from '../../services/map-data-management.service';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { DownloadService } from 'src/app/scad-insights/core/services/download.service';
import { BasemapGalleryComponent } from '../map-widgets/basemap-gallery/basemap-gallery.component';
import { MapZoomComponent } from '../map-widgets/map-zoom/map-zoom.component';
import { MeasurementsComponent } from '../map-widgets/measurements/measurements.component';
import { MapSearchComponent } from '../map-widgets/map-search/map-search.component';
import { PoiComponent } from '../map-widgets/poi/poi.component';
import { CommonService } from 'src/app/scad-insights/geospatial/services/common.service';
import { ChartDataManagementService } from '../../services/charts-data-management.service';
import Expand from '@arcgis/core/widgets/Expand';
import FeatureLayer from '@arcgis/core/layers/FeatureLayer';
import { geoMapKeys } from '../../constants/geospatial.contants';
import { TranslateModule } from '@ngx-translate/core';


@Component({
  selector: 'ifp-census-map',
  standalone: true,
  imports: [
    BasemapGalleryComponent,
    MapZoomComponent,
    MeasurementsComponent,
    MapSearchComponent,
    PoiComponent,
    CommonModule,
    TranslateModule
  ],
  templateUrl: './census-map.component.html',
  styleUrl: './census-map.component.scss'
})
export class CensusMapComponent implements OnInit, AfterViewInit, OnDestroy {

  @ViewChild('selectionLevel') selectionLevel!: ElementRef;
  @ViewChild('mapType') mapType!: ElementRef;
  @ViewChild('PoiComponent') PoiComponent!: ElementRef;
  @Input() filtersData: any = [];
  @Input() isPreviewOpen: boolean = false;
  @Input() isCustomizeOpen: boolean = false;

  @Output() filterChange = new EventEmitter();

  @Output() regionChange = new EventEmitter();
  @Output() regionChangeFromMap = new EventEmitter();

  @Output() districtChange = new EventEmitter();
  @Output() districtChangeFromMap = new EventEmitter();

  @Output() communityChange = new EventEmitter();
  @Output() communityChangeFromMap = new EventEmitter();

  filterObject: any = geoMapKeys.defaultQueryParams;
  subsink: SubSink = new SubSink();

  selectedFeature: any = null;
  view: any;
  poiFeatures: any = [];

  public layerName: string = '';
  public language: string = 'en';
  public isOpen: boolean = false;
  public isMapTypeOpen: boolean = false;
  public selectionLevels: any = geoMapKeys.selectionLevels;
  public currentSelectionLevel: any = this.selectionLevels[0];
  public mapLayers: any = [];
  public currentMapType: any = {};
  public mapLocations: any = [];
  public defaultLocation = geoMapKeys.allRegions;
  public selectedDomain: string = geoMapKeys.domains[0].SELECT_EN;
  public chartData: any = [];
  public heatmapLayer!: any;
  public selectionLevelLabel: any = geoMapKeys.selectionLevelLabel;
  public mapTypeLabel: any = geoMapKeys.mapTypeLabel;
  public userAccessPermission: any = {};
  public loading: boolean = false;
  private subscription = new Subscription();
  private hiesDataTimeout: any = null;
  private mapId: string = '';
  public theme: string = 'light';

  constructor(
    private gisSharedService: MapDataService,
    public themeService: ThemeService,
    private commonService: CommonService,
    private chartDataService: ChartDataManagementService,
    private downloadService: DownloadService,
  ) { }

  ngOnInit(): void {
    this.subsink.add(
      this.themeService.defaultLang$.subscribe((lang: string) => {
        this.language = lang;
      }),
      this.themeService.defaultTheme$.subscribe((val: any) => {
        this.theme = val;
      })
    );
    this.commonService._isMapUpdated$.subscribe((_isMapUpdated: any) => {
      this.mapLocations = _isMapUpdated;
    });
    this.gisSharedService._geoMapAccessPermission$.subscribe((userAccessPermission: any) => {
      this.userAccessPermission = userAccessPermission;

      // remove the community from selections level if user dont have the permission
      this.selectionLevels = this.selectionLevels.filter((level: any) => {
        if (level.SELECT_CODE === 3) {
          return this.userAccessPermission.communityAccess === true;
        }
        return true;
      });

      this.chartDataService.currentDomain$.subscribe((domainId: number) => {
        if(domainId == 1){
          if(this.userAccessPermission.communityAccess){
            this.mapId = geoMapKeys.popMapKeyCommunity;
          }else {
            this.mapId = geoMapKeys.popMapKeyDistrict;
          }
        }else if(domainId == 2){
          if(this.userAccessPermission.communityAccess){
            this.mapId = geoMapKeys.laborforceMapKeyCommunity;
          }else {
            this.mapId = geoMapKeys.laborforceMapKeyDistrict;
          }
        }else if(domainId == 3){
          if(this.userAccessPermission.communityAccess){
            this.mapId = geoMapKeys.realestateMapKeyCommunity;
          }else {
            this.mapId = geoMapKeys.realestateMapKeyDistrict;
          }
        }
        this.gisSharedService.setIsMapLoadingCompleted(false);
        this.initMap();

        // set the current domain map layers 
        this.mapLayers = geoMapKeys.mapLayers;
        this.mapLayers = this.mapLayers.filter((layer: any) => layer.DOMAIN_ID == domainId);

        let currentSelectedLayer = this.gisSharedService.getCurrentDomainLayer();
        if(Object.keys(currentSelectedLayer).length > 0){
          currentSelectedLayer = this.mapLayers.find((layer: any) => layer.DOMAIN_ID == domainId && layer.SELECT_CODE == currentSelectedLayer.SELECT_CODE)
          this.currentMapType = currentSelectedLayer;
        }else {
           this.currentMapType = this.mapLayers[0];
        }
        this.gisSharedService.setCurrentDomainLayer(this.currentMapType);
      });
    });
  }

  ngAfterViewInit(): void {
    this.subsink.add(
      this.gisSharedService._isAuthenticated$.subscribe((auth: boolean) => {
        if (auth) {
          setTimeout(() => {
            this.initMap();
          }, 1000);
        }
      })
    );
  }

  ngOnDestroy(): void {
    this.subsink.unsubscribe();
  }

  initMap(): void {
    const webMap = new WebMap({
      portalItem: {
        id: this.mapId,
      }
    });
    this.view = new MapView({
      map: webMap,
      container: 'viewDivCensus',
      // center: [54.210943, 24.226687],
      // @ts-expect-error
      animate: true,
      zoom: 8,
      ui: {
        components: ['']
      }
    });

    // disable the map popup 
    this.view.popup.autoOpenEnabled = false;

    this.gisSharedService.setView(this.view);

    webMap.when((webmap: any) => {

       // Wait for all layers to load
      const layerPromises = webmap.layers.map((layer: any) => layer.when());

      Promise.all(layerPromises).then(() => {
        this.gisSharedService.setIsMapLoadingCompleted(true);
      });


      // Hide all layers defined in mapTypes array
      this.view?.map?.layers.forEach((layer: any) => {
        console.log(layer.title)
      if(layer.title !== geoMapKeys.mapRegionsTitle && 
        layer.title !== geoMapKeys.mapDistrictsTitle && 
        layer.title !== geoMapKeys.mapCommunitiesTitle){
          if (layer) {
            layer.visible = false;
          }
        }
      })
      // Show only the selected layer
      const filteredLayer = this.view.map.layers.find((layer: any) => layer.title === this.currentMapType.LAYER_NAME);
      if (filteredLayer) {
        this?.view?.map?.add(filteredLayer);
        filteredLayer.visible = true;
      }
    

      // Check for tables
      if (webmap.tables && webmap.tables.length > 0) {
        webmap.tables.forEach((table: __esri.Layer) => {
          if (table.title === 'POI') {
            this.processTable(table);
          }
        });
      } else {
        console.log('No tables found in the web map');
      }

      // Create main container
      const customLayerDiv = document.createElement('div');
      customLayerDiv.id = 'customLayersContainer';
      customLayerDiv.className = 'layer-switcher';

      // Create two separate sections for different layer groups
      const adminSection = document.createElement('div');
      adminSection.className = 'layer-group';
      const adminTitle = document.createElement('h3');
      adminTitle.textContent = this.language == 'en' ? 'Administrative Boundaries' : 'الحدود الإدارية';
      adminSection.appendChild(adminTitle);

      const visualSection = document.createElement('div');
      visualSection.className = 'layer-group';
      const visualTitle = document.createElement('h3');
      visualTitle.textContent = this.language == 'en' ? 'Population Distribution' : 'توزيع السكان';
      visualSection.appendChild(visualTitle);

      // Get all layers
      const layers = webmap.layers.toArray();

      // Process regular layers (admin boundaries)
      layers.forEach((layer: any) => {
        if (layer.type !== 'group' && layer.title !== 'DOT_DENSITY') {
          const layerItem = this.createRadioButton(
            layer,
            'adminRadio',
            () => {
              // Only change visibility of the clicked admin layer
              layer.visible = !layer.visible;
              // Hide other admin layers
              layers.forEach((otherLayer: any) => {
                if (otherLayer !== layer && !otherLayer.title.includes('DotDensity') && !otherLayer.title.includes('Heatmap')) {
                  otherLayer.visible = false;
                }
              });
            },
            this.language
          );
          adminSection.appendChild(layerItem);
        }
      });


      // Handle DOT_DENSITY and heatmap layers
      const dotDensityLayer = layers.find((layer: any) => layer.title === 'DOT_DENSITY');
      if (dotDensityLayer) {
        const visualLayersConfig = [
          {
            id: 'population-dots',
            title: this.language == 'en' ? 'Dot Density' : 'كثافة النقاط',
            handler: () => {
              // First hide heatmap if it exists
              const existingHeatmap = this.view.map.findLayerById('population-heatmap');
              if (existingHeatmap) {
                existingHeatmap.visible = false;
              }

              // Create or show dot density layer
              const dotDensityVis = this.view.map.findLayerById('population-dotdensity');
              if (!dotDensityVis) {
                this.gisSharedService.createDotdensityFeatureLayerSeaRemoved(
                  dotDensityLayer.parsedUrl.path,
                  this.view,
                  'population',
                  'Population Distribution',
                  true
                );
              } else {
                dotDensityVis.visible = true;
              }
            }
          },
          {
            id: 'heatmap',
            title: this.language == 'en' ? 'Heatmap' : 'خريطة حرارية',
            handler: () => {
              // First hide dot density if it exists
              const existingDotDensity = this.view.map.findLayerById('population-dotdensity');
              if (existingDotDensity) {
                existingDotDensity.visible = false;
              }

              // Create or show heatmap layer
              const heatmapVis = this.view.map.findLayerById('population-heatmap');
              if (!heatmapVis) {
                this.createHeatmapLayer(dotDensityLayer.parsedUrl.path, true);
              } else {
                heatmapVis.visible = true;
              }
            }
          }
        ];

        visualLayersConfig.forEach(config => {
          const visualItem = this.createRadioButton(
            { id: config.id, title: config.title, visible: false },
            'visualRadio',
            config.handler,
            this.language
          );
          visualSection.appendChild(visualItem);
        });
      }

      // Add sections to main container
      customLayerDiv.appendChild(adminSection);
      customLayerDiv.appendChild(visualSection);

      // Add styling
      const style = document.createElement('style');
      style.textContent = `
        .layer-switcher {
          padding: 10px;
        }
        .layer-group {
          margin-bottom: 20px;
        }
        .layer-group h3 {
          margin-bottom: 10px;
          font-size: 16px;
          font-weight: bold;
          color: #323232;
        }
        .layer-item {
          margin-bottom: 8px;
          display: flex;
          align-items: center;
        }
        .layer-item input[type="radio"] {
          margin-right: 8px;
        }
        .layer-item label {
          cursor: pointer;
        }
      `;
      document.head.appendChild(style);

      // Create and add the Expand widget
      const layerSwitcher = new Expand({
        view: this.view,
        content: customLayerDiv,
        expandIconClass: 'esri-icon-feature-layer',
        group: 'top-right',
        expandTooltip: this.language == 'en' ? 'Layer Controls' : 'التحكم في الطبقات',
        collapseTooltip: this.language == 'en' ? 'Collapse' : 'تصغير'
      } as __esri.ExpandProperties);

      // this.view.ui.add(layerSwitcher, 'top-right');

      // Initialize layers with outFields
      webmap.layers.items.forEach((layer: any) => {
        layer.outFields = ['*'];
      });
    });


    this.view.watch('zoom', (newZoom: number) => {
      this.updateLayerVisibility(newZoom);
    });


    this.view.on('click', (event: any) => {

      this.view.hitTest(event).then((response: any) => {
        if (response.results.length) {
          response.results.map((result: any) => {
            const graphic = result.graphic;
            const layer = graphic.layer;
            const layerName = layer.title;
            if (this.selectedFeature && this.selectedFeature === graphic) {
              this.clearSelectedFeature();
            } else {

              if (layerName == geoMapKeys.mapCommunitiesTitle) {
                const attr = graphic.attributes;

                this.highlightFeature(graphic);
                this.selectedFeature = graphic;

                let region_code = Number(attr.scad_r_id);

                this.filterObject.COMMUNITY_CODE = [attr.scad_sc_id];
                this.filterObject.REGION_CODE = [region_code];
                this.filterObject.DISTRICT_CODE = [attr.scad_sd_id];

                const obj = [{
                  'REGION_CODE': region_code,
                  'DISTRICT_CODE': attr.scad_sd_id,
                  'COMMUNITY_CODE': attr.scad_sc_id,
                  'COMMUNITY_AR': attr.sc_name_en,
                  'COMMUNITY_EN': attr.sc_name_ar
                }];
                this.communityChangeFromMap.emit(obj);
                this.filterChange.emit(this.filterObject);

                this.mapLocations = [attr.sc_name_en];

              }

              else if (layerName == geoMapKeys.mapDistrictsTitle && this.currentSelectionLevel.SELECT_EN == 'District') {
                this.highlightFeature(graphic);
                this.selectedFeature = graphic;
                const attr = graphic.attributes;

                this.filterObject.REGION_CODE = [attr.region_id]
                this.filterObject.DISTRICT_CODE = [attr.district_code];
                // remove communities codes
                this.filterObject.COMMUNITY_CODE = [];

                const obj = [{
                  'REGION_CODE': attr.region_id,
                  'DISTRICT_CODE': attr.district_code,
                  'DISTRICT_AR': attr.district_a,
                  'DISTRICT_EN': attr.district_e
                }];
                this.districtChangeFromMap.emit(obj);
                this.filterChange.emit(this.filterObject);

                this.mapLocations = [attr.district_e];

                if (this.hiesDataTimeout) {
                  clearTimeout(this.hiesDataTimeout);
                }
        
                // get Hies data 
                // this.hiesDataTimeout = setTimeout(() => {
                //   this.getHiesData(graphic, {
                //     'REGION_CODE': attr.region_id,
                //     'DISTRICT_CODE': attr.district_code,
                //     'DISTRICT_AR': attr.district_a,
                //     'DISTRICT_EN': attr.district_e
                //   });
                // }, 300);

              }

              else if (layerName == geoMapKeys.mapRegionsTitle && this.currentSelectionLevel.SELECT_EN == 'Region') {
                this.highlightFeature(graphic);
                this.selectedFeature = graphic;
                const attr = graphic.attributes;

                this.filterObject.REGION_CODE = [attr.region_id];
                // remove districts and communities codes
                this.filterObject.DISTRICT_CODE = [];
                this.filterObject.COMMUNITY_CODE = [];

                const obj = [{
                  'REGION_CODE': attr.region_id,
                  'REGION_AR': attr.r_name_ar,
                  'REGION_EN': attr.r_name_en
                }];

                this.regionChangeFromMap.emit(obj);
                this.filterChange.emit(this.filterObject);

                this.mapLocations = [attr.r_name_en];

                if (this.hiesDataTimeout) {
                  clearTimeout(this.hiesDataTimeout);
                }
        
                // get Hies data 
                // this.hiesDataTimeout = setTimeout(() => {
                //   this.getHiesData(graphic, {
                //     'REGION_CODE': attr.region_id,
                //     'REGION_AR': attr.r_name_ar,
                //     'REGION_EN': attr.r_name_en
                //   });
                // }, 300);

              }
            }
          });
        } else {
          this.clearSelectedFeature();
        }
      });
    });
  }

  highlightFeature(graphic: any) {
    const highlightSymbol = {
      type: 'simple-fill',
      color: 'rgba(127, 140, 170, 0.06)',
      outline: {
        color: 'rgb(86, 90, 99)',
        width: 2.5
      }
    };
    graphic.symbol = highlightSymbol;

    this.view.graphics.removeAll();
    this.view.graphics.add(graphic);
    //this.view.goTo(graphic.geometry.extent.expand(1));
  }


  private getHiesData(graphic: any, filterObject: any) {
    const sub = this.commonService.getHiesSummary(filterObject)
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe({
        next: (data) => {
           this.highlightFeatureWithHiesData(graphic, filterObject, data);
        },
        error: (error) => {
          console.error('Error loading hies data:', error);
        }
      });
    this.subscription.add(sub);
  }

  highlightFeatureWithHiesData(graphic: any, filterObject: any, data: any) {
    const highlightSymbol = {
      type: 'simple-fill',
      color: 'rgba(127, 140, 170, 0.06)',
      outline: {
        color: 'rgb(86, 90, 99)',
        width: 2.5
      }
    };

    graphic.symbol = highlightSymbol;
    this.view.graphics.removeAll();
    this.view.graphics.add(graphic);

    this.removeCustomPopup();

    setTimeout(() => {
      const popupContainer = document.createElement('div');
      popupContainer.className = 'custom-popup';

      popupContainer.innerHTML = `
        <div class="custom-popup-header">
          <div class="title">${filterObject.REGION_EN || filterObject.DISTRICT_EN} Statistics</div>
          <button class="close-btn" title="Close">&times;</button>
        </div>
        <div class="custom-popup-content">
          ${this.createCleanPopupContent(data, filterObject)}
        </div>
      `;

      document.body.appendChild(popupContainer);

      popupContainer.querySelector('.close-btn')?.addEventListener('click', () => {
        popupContainer.remove();
      });

      this.injectCustomPopupStyles();
    }, 100);

    // Apply clean styling
    this.applyCleanPopupStyling();
  }

  private removeCustomPopup() {
    const existing = document.querySelector('.custom-popup');
    if (existing) existing.remove();
  }

  private injectCustomPopupStyles() {
    if (document.getElementById('custom-popup-style')) return;

    const style = document.createElement('style');
    style.id = 'custom-popup-style';
    style.textContent = `
      .custom-popup {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(255, 255, 255, 0.15); 
        -webkit-backdrop-filter: blur(8px);
        backdrop-filter: blur(8px);
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        max-width: 480px;
        min-width: 360px;
        width: auto;
        z-index: 99999;
        overflow: hidden;
        opacity: 0;
        animation: fadeIn 0.25s ease-out forwards;
      }

      .custom-popup-header {
        padding: 14px 18px;
        font-weight: bold;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #ddd;
      }

      .custom-popup-header .title {
        font-size: 16px;
        color: #000;
      }

      .custom-popup-header .close-btn {
        background: none;
        border: none;
        font-size: 22px;
        font-weight: bold;
        cursor: pointer;
        color: #333;
        transition: transform 0.2s ease;
      }

      .custom-popup-header .close-btn:hover {
        transform: scale(1.2);
      }

      .custom-popup-content {
        padding: 20px;
        max-height: 80vh;
        overflow-y: auto;
      }

      @keyframes fadeIn {
        from { opacity: 0; transform: translate(-50%, -55%); }
        to { opacity: 1; transform: translate(-50%, -50%); }
      }

      /* Optional: Hide any overlapping Esri popup if still shown */
      .esri-popup {
        display: none !important;
      }
    `;
    document.head.appendChild(style);
  }


  private createCleanPopupContent(data: any, filterObject: any): string {

    const formatCurrency = (amount: number) => {
      if(amount == 0) return 'No data available';
        return new Intl.NumberFormat('en-AE', {
            style: 'currency',
            currency: 'AED',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount || 0);
    };

    // Check if district-level data exists for non-citizens
    const hasDistrictIncomeNonCitizen = data.DISTRICT_AVG_HOUSEHOLD_INCOME_NON_CITIZIN && data.DISTRICT_AVG_HOUSEHOLD_INCOME_NON_CITIZIN > 0;
    const hasDistrictExpenditureNonCitizen = data.DISTRICT_AVG_HOUSEHOLD_EXPENDITURE_NON_CITIZIN && data.DISTRICT_AVG_HOUSEHOLD_EXPENDITURE_NON_CITIZIN > 0;
    
    const isDistrictSelected = filterObject.DISTRICT_EN;
    
    // Determine which non-citizen data to use (district if available, otherwise region)
    const getNonCitizenIncomeData = () => {
        if (hasDistrictIncomeNonCitizen) {
            return data.DISTRICT_AVG_HOUSEHOLD_INCOME_NON_CITIZIN;
        }
        return data.REGION_AVG_HOUSEHOLD_INCOME_NON_CITIZIN;
    };

    const getNonCitizenExpenditureData = () => {
        if (hasDistrictExpenditureNonCitizen) {
            return data.DISTRICT_AVG_HOUSEHOLD_EXPENDITURE_NON_CITIZIN;
        }
        return data.REGION_AVG_HOUSEHOLD_EXPENDITURE_NON_CITIZIN;
    };

    // Check if we have non-citizen data available (either district or region)
    const hasNonCitizenIncomeData = hasDistrictIncomeNonCitizen || (data.REGION_AVG_HOUSEHOLD_INCOME_NON_CITIZIN && data.REGION_AVG_HOUSEHOLD_INCOME_NON_CITIZIN > 0);
    const hasNonCitizenExpenditureData = hasDistrictExpenditureNonCitizen || (data.REGION_AVG_HOUSEHOLD_EXPENDITURE_NON_CITIZIN && data.REGION_AVG_HOUSEHOLD_EXPENDITURE_NON_CITIZIN > 0);
    
    // Determine grid class based on available data
    const getGridClass = (hasDistrictData: boolean) => {
        return hasDistrictData ? 'stats-grid-two-col' : 'stats-grid';
    };

    // Get label for non-citizen data (indicates if it's region-level)
    const getNonCitizenLabel = () => {
        return isDistrictSelected ? 'Non Emirati (Region Level)' : 'Non Emirati';
    };

    // Get overall averages (district if available, otherwise region)
    const getOverallIncomeData = () => {
        if (isDistrictSelected) {
          return data.DISTRICT_AVG_HOUSEHOLD_INCOME;
        }
        return data.REGION_AVG_HOUSEHOLD_INCOME;
    };

    const getOverallExpenditureData = () => {
        if (isDistrictSelected) {
            return data.DISTRICT_AVG_HOUSEHOLD_EXPENDITURE;
        }
        return data.REGION_AVG_HOUSEHOLD_EXPENDITURE;
    };

    // Get citizen data (district if available, otherwise region)
    const getCitizenIncomeData = () => {
        if (isDistrictSelected) {
            return data.DISTRICT_AVG_HOUSEHOLD_INCOME_CITIZIN;
        }
        return data.REGION_AVG_HOUSEHOLD_INCOME_CITIZIN;
    };

    const getCitizenExpenditureData = () => {
        if (isDistrictSelected) {
            return data.DISTRICT_AVG_HOUSEHOLD_EXPENDITURE_CITIZIN;
        }
        return data.REGION_AVG_HOUSEHOLD_EXPENDITURE_CITIZIN;
    };


    return `
      <div class="clean-popup">
        <div class="popup-section">
          <div class="section-title">
              <h4>Annual Average Household Income</h4>
          </div>
          <div class="${getGridClass(isDistrictSelected)}">
              ${!isDistrictSelected ? `
              <div class="stat-card">
                  <div class="stat-label">Overall Average</div>
                  <div class="stat-value">${formatCurrency(getOverallIncomeData())}</div>
              </div>
              ` : ''}
              <div class="stat-card">
                  <div class="stat-label">Emirati</div>
                  <div class="stat-value">${formatCurrency(getCitizenIncomeData())}</div>
              </div>
              ${hasNonCitizenIncomeData ? `
              <div class="stat-card">
                  <div class="stat-label">${getNonCitizenLabel()}</div>
                  <div class="stat-value">${formatCurrency(getNonCitizenIncomeData())}</div>
              </div>
              ` : ''}
          </div>
      </div>

      <div class="popup-section">
          <div class="section-title">
              <h4>Annual Average Household Expenditure</h4>
          </div>
          <div class="${getGridClass(isDistrictSelected)}">
              ${!isDistrictSelected ? `
              <div class="stat-card">
                  <div class="stat-label">Overall Average</div>
                  <div class="stat-value">${formatCurrency(getOverallExpenditureData())}</div>
              </div>
              ` : ''}
              <div class="stat-card">
                  <div class="stat-label">Emirati</div>
                  <div class="stat-value">${formatCurrency(getCitizenExpenditureData())}</div>
              </div>
              ${hasNonCitizenExpenditureData ? `
              <div class="stat-card">
                  <div class="stat-label">${getNonCitizenLabel()}</div>
                  <div class="stat-value">${formatCurrency(getNonCitizenExpenditureData())}</div>
              </div>
              ` : ''}
          </div>
      </div>
    </div>
  `;
  }


  private applyCleanPopupStyling() {
      if (!document.getElementById('clean-popup-styles')) {
          const style = document.createElement('style');
          style.id = 'clean-popup-styles';
          style.textContent = `
              /* Main popup container - remove white background and ensure proper sizing */
              .esri-popup__main-container {
                background: transparent !important;
                backdrop-filter: blur(12px) saturate(180%) !important;
                -webkit-backdrop-filter: blur(12px) saturate(180%) !important;
                border: 1px solid rgba(255, 255, 255, 0.2) !important;
                border-radius: 16px !important;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
                max-width: 480px !important;
                width: auto !important;
                min-width: 450px !important;
                max-height: 90vh !important;
                overflow: visible !important;
              }

              /* Header styling */
              .esri-popup__header {
                color: #000000 !important;
                border-radius: 16px 16px 0 0 !important;
                padding: 16px 20px !important;
                border: none !important;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
              }

              .esri-popup__header-container--button:hover{
                background-color: transparent !important;
              }

              .esri-popup__header-title {
                font-weight: 600 !important;
                font-size: 16px !important;
                color: #000000 !important;
                margin: 0 !important;
              }

              /* Content area */
              .esri-popup__content {
                padding: 0 !important;
                background: transparent !important;
                border-radius: 0 0 16px 16px !important;
                max-height: none !important;
                overflow: visible !important;
              }

              /* Hide navigation and scrollbars */
              .esri-popup__navigation {
                display: none !important;
              }

              .esri-popup__scroller {
                overflow: visible !important;
                max-height: none !important;
              }

              /* Popup buttons */
              .esri-popup__button {
                border: 1px solid rgba(255, 255, 255, 0.3) !important;
                color: #000000 !important;
                transition: all 0.2s ease !important;
              }

              .esri-popup__button:hover {
                background: rgba(255, 255, 255, 0.2) !important;
                transform: scale(1.05) !important;
              }

              /* Custom popup content */
              .clean-popup {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                background: transparent !important;
                border-radius: 0 0 16px 16px;
                overflow: visible;
                width: 100%;
              }

              .popup-section {
                padding: 20px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.6);
              }

              .popup-section:last-of-type {
                border-bottom: none;
              }

              .section-title {
                display: flex;
                align-items: center;
                margin-bottom: 16px;
              }

              .section-title h4 {
                margin: 0;
                color: #000000;
                font-size: 16px;
                font-weight: 600;
                letter-spacing: -0.01em;
              }

              .stats-grid {
                display: grid;
                grid-template-columns: repeat(3, minmax(120px, 1fr));
                gap: 12px;
              }

              .stats-grid-two-col {
                display: grid;
                grid-template-columns: repeat(2, minmax(150px, 1fr));
                gap: 15px;
              }

              .stats-grid-one-col {
                display: grid;
                grid-template-columns: repeat(1, minmax(150px, 1fr));
                gap: 15px;
              }

              .stat-card {
                background: transparent !important;
                backdrop-filter: blur(8px);
                border: 1px solid rgba(255, 255, 255, 0.25);
                border-radius: 12px;
                padding: 14px;
                transition: all 0.2s ease;
                position: relative;
                overflow: hidden;
                min-width: 120px;
                flex: 1;
              }

              .stat-card.primary {
                background: rgba(255, 255, 255, 0.6) !important;
                backdrop-filter: blur(12px);
                border: 1px solid rgba(255, 255, 255, 0.35);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
              }

              .stat-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
                background: rgba(255, 255, 255, 0.6) !important;
              }

              .stat-card.primary:hover {
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
                background: rgba(255, 255, 255, 0.6) !important;
              }

              .stat-label {
                font-size: 11px;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                margin-bottom: 8px;
                color: rgba(0, 0, 0, 0.65);
                line-height: 1.2;
              }

              .stat-value {
                font-size: 14px;
                font-weight: 700;
                line-height: 1.1;
                color: #000000;
                white-space: nowrap;
              }

              .popup-insights {
                background: rgba(255, 255, 255, 0.6);
                backdrop-filter: blur(8px);
                padding: 16px 20px;
                border-top: 1px solid rgba(255, 255, 255, 0.6);
                border-radius: 0 0 16px 16px;
              }

              .insight-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
              }

              .insight-label {
                font-size: 13px;
                font-weight: 500;
                color: rgba(0, 0, 0, 0.7);
              }

              .insight-value {
                font-size: 14px;
                font-weight: 600;
                padding: 6px 10px;
                border-radius: 8px;
                background: rgba(255, 255, 255, 0.6);
                backdrop-filter: blur(5px);
                border: 1px solid rgba(255, 255, 255, 0.25);
              }

              /* Mobile responsive */
              @media (max-width: 450px) {
                .esri-popup__main-container {
                  min-width: 380px !important;
                  max-width: 420px !important;
                }

                .stats-grid,
                .stats-grid-one-col,
                .stats-grid-two-col {
                  grid-template-columns: 1fr;
                }

                .stat-card {
                  padding: 16px;
                  min-width: auto;
                }

                .stat-value {
                  font-size: 16px;
                }

                .popup-section {
                  padding: 16px;
                }
              }

              /* Ensure all white backgrounds are removed */
              .esri-widget,
              .esri-popup,
              .esri-popup__main-container,
              .esri-popup__content,
              .esri-popup__scroller,
              .esri-popup__content > div,
              .clean-popup,

              .esri-popup__main-container,
              .esri-popup__content,
              .esri-popup__scroller,
              .clean-popup {
                scrollbar-width: none !important;
                -ms-overflow-style: none !important;
              }

              .esri-popup__main-container::-webkit-scrollbar,
              .esri-popup__content::-webkit-scrollbar,
              .esri-popup__scroller::-webkit-scrollbar,
              .clean-popup::-webkit-scrollbar {
                display: none !important;
              }
          `;
          document.head.appendChild(style);
      }
  }

  clearSelectedFeature() {
    if (this.selectedFeature) {
      this.selectedFeature.symbol = {
        type: 'simple-fill',
        color: 'rgba(0, 0, 0, 0)',
        outline: {
          color: 'rgba(58, 71, 81, 1)',
          width: 0.53
        }
      };
      this.selectedFeature = null;
    }
  }

  // Function to process the table
  processTable(table: __esri.Layer) {
    if (table.type === 'feature') {
      const featureTable = table as __esri.FeatureLayer;
      featureTable.queryFeatures().then((result) => {
        this.poiFeatures = result.features
          .filter(feature => feature.attributes.category == 'POI')
          .sort((a, b) => a.attributes.parameter.localeCompare(b.attributes.parameter))
          .map(feature => ({
            name: feature.attributes.desc_en,
            name_ar: feature.attributes.desc_ar,
            url: feature.attributes.value,
            icon: feature.attributes.icon
          }));
        this.gisSharedService.setCensusPoiData(this.poiFeatures);

      });
    }
  }


  filterChanged(filter: any) {
    this.filterChange.emit(filter);
  }

  locationChanged(location: string) {
    this.mapLocations = [location];
  }

  districtChanged(districts: any) {
    this.districtChange.emit(districts);
  }

  private async createHeatmapLayer(url: string, show: boolean) {
    const heatmapRenderer = {
      type: "heatmap",
      field: "population",
      colorStops: [
        { color: "rgba(255, 255, 255, 0)", ratio: 0 },
        { color: "rgba(0, 180, 240, 0.50)", ratio: 0.4 },
        { color: "rgba(0, 51, 255, 0.40)", ratio: 0.5 },
        { color: "rgba(255, 0, 0, 0.50)", ratio: 0.6 },
        { color: "rgba(255, 0, 0, 0.40)", ratio: 0.85 },
        { color: "rgba(255, 140, 0, 0.50)", ratio: 0.9 },
        { color: "rgba(245, 225, 0, 0.40)", ratio: 0.95 }
      ],
      maxPixelIntensity: 50,
      minPixelIntensity: 50, 
      radius: 20
    };

    const layer = new FeatureLayer({
      id: geoMapKeys.mapHeatDefaultRealData,
      url,
      title: this.language === "en" ? "Population Heatmap" : "خريطة السكان الحرارية",
      visible: show,
      renderer: heatmapRenderer as any,
      popupEnabled: false
    });

    // const sumPopulation = new StatisticDefinition ({
    //   onStatisticField: "population",
    //   outStatisticFieldName: "TotalPop",
    //   statisticType: "sum"
    // });

    const query = layer.createQuery();
    query.where = "1=1";
    query.returnGeometry = true;
    query.outFields = ["*"];
    // query.outStatistics = [sumPopulation];
    // query.groupByFieldsForStatistics = ["scad_sc_id"];

    layer.queryFeatures(query).then((result) => {
      result.features.forEach((f, i) => {
        const { scad_sc_id, TotalPop } = f.attributes;
        //console.log(`Feature #${i}, scad_sc_id=${scad_sc_id}, TotalPop=${TotalPop}`);
      });
    })
      .catch((error) => {
        console.error("Query error:", error);
      });

    return layer;
  }



  private createRadioButton(layer: any, groupName: string, onChange: () => void, language: string) {
    const button = document.createElement('input');
    button.type = 'radio';
    button.name = groupName;
    button.id = layer.id;
    button.checked = layer.visible;

    button.addEventListener('change', () => {
      if (button.checked) {
        onChange();
      }
    });

    const label = document.createElement('label');
    label.setAttribute('for', layer.id);
    label.innerHTML = layer.title || layer.id;

    const layerItem = document.createElement('div');
    layerItem.className = 'layer-item';
    layerItem.appendChild(button);
    layerItem.appendChild(label);

    return layerItem;
  }


  getOriginalDownloadData() {
    return this.chartDataService.getOriginalData();
  }

  getFilteredDownloadData() {
    return this.chartDataService.getFilteredData();
  }

  download(type: string) {
    this.chartData = this.getFilteredDownloadData();
    if (Object.keys(this.chartData).length == 0) { 
      this.chartData = this.getOriginalDownloadData();
    }
    if (this.chartData?.summary?.length > 0) {
      if (type == 'XL') {
        const downloadData: any = [];
        downloadData.push(...this.chartData?.summary);
        this.downloadService.exportToExcel(
          downloadData,
          'Geo Spatial Data'
        );
      }
    }
  }

  toggleDropdown() {
    this.isOpen = !this.isOpen;
  }

  toggleMapTypeDropdown() {
    this.isMapTypeOpen = !this.isMapTypeOpen;
  }
  

  toggleSelection(level: any): void {
    this.currentSelectionLevel = level;
    if (level.SELECT_EN == geoMapKeys.district) {
      this.gisSharedService.layerSelection(geoMapKeys.mapDistrictsTitle);
    } else if (level.SELECT_EN == geoMapKeys.region) {
      this.gisSharedService.layerSelection(geoMapKeys.mapRegionsTitle);
    } else if (level.SELECT_EN == geoMapKeys.community) {
      this.gisSharedService.layerSelection(geoMapKeys.mapCommunitiesTitle);
    }
    this.isOpen = !this.isOpen;
  }

  toggleMapLayer(selectedLayer: any) {
    // Hide all layers defined in mapTypes array
    this.view?.map?.layers.forEach((layer: any) => {
      if(layer.title !== geoMapKeys.mapRegionsTitle && 
        layer.title !== geoMapKeys.mapDistrictsTitle && 
        layer.title !== geoMapKeys.mapCommunitiesTitle){
          if (layer) {
            layer.visible = false;
          }
        }
    })
    // Show only the selected layer
    const filteredLayer = this.view.map.layers.find((layer: any) => layer.title === selectedLayer.LAYER_NAME);
    if (filteredLayer) {
      filteredLayer.visible = true;
    }
    this.isMapTypeOpen = !this.isMapTypeOpen;
    this.currentMapType = selectedLayer;
    this.gisSharedService.setCurrentDomainLayer(this.currentMapType);
  }


  @HostListener('document:click', ['$event'])
  onClickOutside(event: Event): void {
    if (!this.selectionLevel?.nativeElement.contains(event.target)) {
      this.isOpen = false;
    }
    if (!this.mapType?.nativeElement.contains(event.target)) {
      this.isMapTypeOpen = false;
    }
  }

  private updateLayerVisibility(zoomLevel: number): void {
    if (zoomLevel <= 9) {
      this.currentSelectionLevel = this.selectionLevels[0];
    } else if (zoomLevel > 9 && zoomLevel <= 11) {
      this.currentSelectionLevel = this.selectionLevels[1];
    } else if (zoomLevel > 11 && this.userAccessPermission.communityAccess) {
      this.currentSelectionLevel = this.selectionLevels[2];
    }
  }

  resetSelectionLevel(event: any) {
    this.gisSharedService.MapToInitialLevel("fromIcon");
    this.currentSelectionLevel = this.selectionLevels[0];
  }


}

