<div class="ifp-gen-ai-chat-chip"
  [ngClass]="{'ifp-gen-ai-chat-chip--prompt': chat().role === genAiRole.user ,'ifp-gen-ai-chat-chip--ai': chat().role === genAiRole.ai }">
  <div class="ifp-gen-ai-chat-chip__wrapper">
    @if (aiPrompt()) {
    <em class="ifp-icon ifp-icon-gen-ai ifp-gen-ai-chat-chip__icon"></em>
    }
    @if(chat().role === genAiRole.ai) {
    <div class="ifp-gen-ai-chat-chip__ai-wrapper">
      <img src="../../../../assets/images/gen-ai-image/ai-icon.png" class="ifp-gen-ai-chat-chip__ai-image" width="43px"
        height="43px">
   <div class="ifp-gen-ai-chat-chip__reason-wrapper">
    @if ((chat().data?.reasoning && chat().data?.reasoning?.trim() !== '') ) {
      <div class="ifp-gen-ai-chat-chip__reason-detail" [ngClass]="{'ifp-gen-ai-chat-chip__reason-detail--open': reasoningOpen()}">
        <div class="ifp-gen-ai-chat-chip__reason-header" (click)="reasoningOpen.set(!reasoningOpen())">
          <p class="ifp-gen-ai-chat-chip__reason-title"><em class="ifp-icon ifp-icon-bulb ifp-gen-ai-chat-chip__bulb"></em> {{'Reason' | translate}}</p>
          @if(reasoningOpen()) {
      <em class="ifp-icon ifp-icon-up-arrow ifp-gen-ai-chat-chip__reason-toggle-arrow" ></em>
          } @else {
      <em class="ifp-icon ifp-icon-down-arrow ifp-gen-ai-chat-chip__reason-toggle-arrow" ></em>
          }

        </div>
        @if(reasoningOpen()) {
        <ifp-mark-down [data]="chat().data?.reasoning" class="ifp-gen-ai-chat-chip__matrics-markdown ifp-gen-ai-chat-chip__reason-desc" [ngClass]="{'class': reasoningOpen()}"></ifp-mark-down>
        }
      </div>
      }

      <div class="ifp-gen-ai-chat-chip__response-wrapper">
        @if (chat().data?.tab_response && chat().data?.tab_response?.length) {
            <ifp-tab-badge [genAi]="true" [selectedValue]="currentTab()" [data]="tabs()" (selectedEvent)="currentTab.set($event.key ?? '');currentTabValue.set($event.node ?? '');"></ifp-tab-badge>
        }

      <div  [ngClass]="{'ifp-gen-ai-chat-chip__animation-wrapper':chat().loader}">
        <ifp-mark-down class="ifp-gen-ai-chat-chip__matrics-markdown"
          [data]="currentTabValue() && currentTabValue() !== ''? currentTabValue() : (chat().data?.content ?? '')"></ifp-mark-down>
          @if(chat().loader) {
        <span class="ifp-gen-ai-chat-chip__animaton-node"></span>
        <span class="ifp-gen-ai-chat-chip__animaton-node"></span>
        <span class="ifp-gen-ai-chat-chip__animaton-node"></span>
          }
      </div>

           @if (!chat().stream) {
             @if(map()) {
                   <ifp-gen-ai-map-chip class="ifp-gen-ai-chat-chip__map" (click)="mapClick.emit(chat().data?.map_config)"></ifp-gen-ai-map-chip>
             }
    <ifp-gen-ai-screen-source-pill  [ngClass]="{'ifp-gen-ai-chat-chip__source-pills--active': sourceKeys().length > 0}" [sourceKeys]="sourceKeys()" [message]="chat()" (sourceClick)="sourceClick.emit($event)"
            class="ifp-gen-ai-chat-chip__source-pills"
            ></ifp-gen-ai-screen-source-pill>
        <ifp-gen-ai-indicator-pills  [ngClass]="{'ifp-gen-ai-chat-chip__indicator--active': sourceKeys().length  === 0}"  class="ifp-gen-ai-chat-chip__indicator"
          [message]="chat()"></ifp-gen-ai-indicator-pills>
           }


          @if(( chat().data?.custom_charts &&  chat().data?.custom_charts?.length ) || (chat().data?.viz_data && chat().data?.viz_data?.length)) {
          <div class="ifp-gen-ai-chat-chip__chart-wrapper">
          @for (chartDetails of chat().data?.custom_charts; track chartDetails; let index= $index;) {
          <ifp-ai-chart-card class="ifp-gen-ai-chat-chip__chart"
            [ngClass]="{'ifp-gen-ai-chat-chip__chart--odd': (index+1) % 2 != 0}" [isCustom]="true"
            [customChartData]="chartDetails"></ifp-ai-chart-card>
          }
          @for (chartDetails of chat().data?.viz_data; track chartDetails; let index= $index;) {

          <ifp-ai-chart-card class="ifp-gen-ai-chat-chip__chart" [id]="chartDetails.node_id"
            [ngClass]="{'ifp-gen-ai-chat-chip__chart--odd': (index+1) % 2 != 0}" [isCustom]="false"
            [cntType]="chartDetails.content_type"></ifp-ai-chart-card>

          }
           </div>
          }


        @if (actionEnable()&&  !chat().loader)  {
        <div class="ifp-gen-ai-chat-chip__chart-actions" #action>
          <em class="ifp-gen-ai-chat-chip__action-icon ifp-icon-refresh" (click)="regenerate.emit(chat())"
            [appIfpTooltip]="'Regenerate' | translate" [zIndex]="1210"></em>
          <em class="ifp-gen-ai-chat-chip__action-icon ifp-icon-copy" [appIfpTooltip]="'Copy Text' | translate"
            (click)="toaster('Text copied to clipboard')" [zIndex]="1210"
            [cdkCopyToClipboard]="chat().data?.content ?? ''"></em>

          <em class=" ifp-gen-ai-chat-chip__action-icon ifp-icon ifp-icon-like ifp-gen-ai-chat-chip__fd-hand-up"
            [appIfpTooltip]="'Like' | translate" [zIndex]="1210" (click)="postChatFeedback('like')"
            [ngClass]="{'ifp-gen-ai-chat-chip__action-icon--active':liked()}"></em>

          <div class="ifp-gen-ai-chat-chip__fd-text-outer" (click)="postFeedbackOpen()" #outsideRef>
            <em class=" ifp-gen-ai-chat-chip__action-icon ifp-icon ifp-icon-dislike ifp-gen-ai-chat-chip__fd-hand-down"
              [appIfpTooltip]="'DisLike' | translate" [zIndex]="1210"
              [ngClass]="{'ifp-gen-ai-chat-chip__action-icon--active': liked()===false}" ></em>

          </div>
          @if(feedbackPopUp()) {
            <div #dislikeRef appAppendToBody [marginTop]="30" appOutsideClick [top]="messagePosition().top"   [position]="'right'"  [left]="messagePosition().left"  [parentWidth]="messagePosition().parentWidth" (outsideClick)="outsideClick($event)"  class="ifp-gen-ai-chat-chip__feedback-wrapper">
                      <ifp-gen-ai-like-feedback (submit)="postChatFeedback('', $event);feedbackPopUp.set(false);"  (close)="feedbackPopUp.set(false);"></ifp-gen-ai-like-feedback>
            </div>

          }

        </div>
        }


      </div>



</div>

    </div>

    } @else {
    {{chat().data?.content}}
    }
  </div>
  @if(chat().role === genAiRole.ai && actionEnable()) {

  @for (item of chat().data?.Smart_Suggestion_list; track $index) {
  <div class="ifp-gen-ai-chat-chip__suggestion-wrapper">
    <div>
      <div class="ifp-gen-ai-chat-chip__suggestion">
        <em class="ifp-icon ifp-icon-bulb ifp-gen-ai-chat-chip__suggestion-icon"></em>
        {{item.suggestion}}
      </div>
    </div>
    <div>
      <div class="ifp-gen-ai-chat-chip__relatedQuery" (click)="prompt.emit(item.Related_Query)">
        {{item.Related_Query}}
      </div>
    </div>
  </div>
  }
  }

</div>
