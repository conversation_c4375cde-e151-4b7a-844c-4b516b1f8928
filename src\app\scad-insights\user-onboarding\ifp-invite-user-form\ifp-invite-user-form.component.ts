import { dateFormat } from 'src/app/scad-insights/core/constants/date.constants';
import { SearchSuggestionDirective } from './../../core/directives/sugession.directive';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, HostListener, input, Input, OnChanges, OnDestroy, OnInit, output, Output, signal, SimpleChanges, ViewChild, WritableSignal } from '@angular/core';
import { Access, AccessList, ClassificationDetail, FilterItems, InviteList, SecondaryRoleList } from '../user-onboarding.interface';
import { accessLevels, dataClassification, dgRoles, dgStatus, inviteStatus, role, roleList, superUserRoles } from '../control-panel/ifp-access-control/ifp-access-control.constants';
import { buttonClass, buttonIconPosition } from '../../core/constants/button.constants';
import { IfpDropdownComponent } from '../../ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { FormArray, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IfpButtonComponent } from '../../ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { SubSink } from 'subsink';
import { IfpInfoComponent } from '../../ifp-widgets/ifp-molecules/ifp-heading-with-info/ifp-info.component';
import { NgClass } from '@angular/common';
import { ifpColors } from '../../core/constants/color.constants';
import { IfpAccessLevelDropdownComponent } from '../ifp-access-level-dropdown/ifp-access-level-dropdown.component';
import { IFPMsalService } from '../../core/services/IFP-msal.service';
import { ValidationService } from '../../core/services/validation/validation.service';
import { AdminService } from '../../core/services/sla/admin.service';
import { ToasterService } from '../../core/services/tooster/ToastrService.service';
import { IfpTooltipDirective } from '../../core/directives/ifp-tooltip.directive';
import { IfpNumberOnlyDirective } from '../../core/directives/ifp-number-only.directive';
import { IfpTabComponent } from '../../ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';
import { IfpNoDataComponent } from '../../ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { ApiService } from '../../core/services/api.service';
import { deleteInviteApi, productEngagement, resendInviteApi, superUser } from '../user-onboarding.constants';
import { commonApi } from '../../core/apiConstants/common-api.constants';
import { IfpModalComponent } from '../../ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpRemoveCardComponent } from '../../ifp-widgets/ifp-atoms/ifp-remove-card/ifp-remove-card.component';
import { IfpCheckBoxComponent } from '../../ifp-widgets/ifp-atoms/ifp-check-box/ifp-check-box.component';
@Component({
  selector: 'ifp-invite-user-form',
  imports: [IfpDropdownComponent, ReactiveFormsModule, TranslateModule, IfpButtonComponent, IfpInfoComponent, NgClass, IfpAccessLevelDropdownComponent, IfpTooltipDirective, IfpNumberOnlyDirective, IfpTabComponent, SearchSuggestionDirective, IfpNoDataComponent, IfpModalComponent, IfpRemoveCardComponent, IfpCheckBoxComponent],
  templateUrl: './ifp-invite-user-form.component.html',
  styleUrl: './ifp-invite-user-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class IfpInviteUserFormComponent implements OnInit, OnChanges, OnDestroy {

  @HostListener('focus', ['$event']) onFocus(formName: string, controlName: string) {
    if (formName === 'peForm') {
      // this.peForm.controls[controlName].setValue(true);
      if (controlName.includes('primary')) {
        this.peFormPrimary.controls[controlName].setValue(true);
      } else {
        this.peFormSecondary.controls[controlName].setValue(true);
      }
    } else {
      this.dgForm.controls[controlName].setValue(true);
    }
  }

  @HostListener('blur', ['$event']) onblur(formName: string, controlName: string) {
    setTimeout(() => {
      if (formName === 'peForm') {
        // this.peForm.controls[controlName].setValue(false);
        if (controlName.includes('primary')) {
          this.peFormPrimary.controls[controlName].setValue(true);
        } else {
          this.peFormSecondary.controls[controlName].setValue(true);
        }
      } else {
        this.dgForm.controls[controlName].setValue(false);
      }
    }, 300);
  }

  @ViewChild('inviteDgModal') inviteDgModal!: IfpModalComponent;
  @ViewChild('cancelInviteModal') cancelInviteModal!: IfpModalComponent;

  @Output() inviteSuperUser = new EventEmitter();
  @Output() inviteDg = new EventEmitter();
  @Output() inviteUser = new EventEmitter();
  public saveFormValue = output<Record<string, FormGroup>>();

  @Input() role!: string;
  @Input() entityList: FilterItems[] = [];
  @Input() dataClassificationList = accessLevels;
  @Input() accessLevels: AccessList[] = [];
  @Input() isSubmitted: boolean = false;
  @Input() exisitingUserList: string[] = [];

  // public isSaveData = model<boolean>(true);
  public inviteFormData = input<Record<string, FormGroup>>();
  public userRoleList = input<SecondaryRoleList[]>([]);

  public dgStatus: string = this._adminService.dgStatus();

  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public userRole = role;
  public roleList = roleList;
  public peForm!: FormGroup;
  public peFormPrimary!: FormGroup;
  public peFormSecondary!: FormGroup;
  public suForm!: FormGroup;
  public dgForm!: FormGroup;
  public invitesForm!: FormGroup;
  public subs: SubSink = new SubSink();
  public dataClassification = dataClassification;
  public jobLevelList: any = [];
  public departmentList: any = [];
  public entity!: FilterItems;
  public classificationInfo: string = 'After registration, the user can access all domains except the sensitive';
  public infoWidth: string = '290px';
  public selectedClassification!: ClassificationDetail;
  public defaultClassification: ClassificationDetail = this.dataClassificationList[1];
  public countryList: Record<string, CountryList> = {
    uae: {
      country: 'UAE',
      flagIcon: '../../../../assets/images/uae-flag-icon.png',
      code: 971
    }
  };

  public selectedCountry = this.countryList['uae'];
  public isReadOnly: boolean = false;
  // public isAllAccess: boolean = true;
  public domainsToDisplay: string[] = [];
  public viewOnly: boolean = true;
  public ifpColor = ifpColors;
  // public isDgAvailable: boolean = this._adminService.isDgAvailable();
  public isDgRequired: boolean = this._adminService.isDgRequired();
  public entityDomain: WritableSignal<string[]> = signal([]);
  public domainAccessList: string[] = [];
  // public exisitingList = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];
  public inviteTabs = [
    { key: 'newInvite', name: 'New Invite' },
    { key: 'sentInvites', name: 'Sent Invites' }
  ];

  public selectedTab: { name: string, key: string } = this.inviteTabs[0];
  public inviteList: InviteList[] = [];
  public dgStatusConst = dgStatus;
  public isInitialSelect: boolean = true;
  public allowedDomain: WritableSignal<string[]> = signal([]);
  public dateFormat = dateFormat;
  public enableInvite: boolean = true;
  public submittedUserType: string = '';
  public dgTitles: { key: string, value: string }[] = [
    {
      key: role.dg,
      value: roleList[role.dg]
    },
    {
      key: role.underSecretary,
      value: roleList[role.underSecretary]
    }
  ];

  public platforms = [
    {
      key: 'Web',
      value: 'web'
    },
    {
      key: 'Web and Mobile',
      value: 'all'
    }
  ];

  public dgType: { key: string, value: string } = this.dgTitles[0];
  public superUserRoles = superUserRoles;
  public editInviteIndex: number = -1;
  public previousInviteValue!: any;
  public previousSelectedIndex!: any;
  public platformType: { key: string; value: string } = this.platforms[0];
  public selectedToDeleteIndex: number = -1;
  public inviteStatus: Record<string, { key: string; value: string }> = inviteStatus;
  public clearDropdowns = false;
  isEmployeeTypeSelected = false;
  public isLoading: boolean = false;
  public primaryUserRoleList: any;
  public primaryUserDGList: any;
  constructor(private _validationService: ValidationService, private _formBuilder: FormBuilder, private _msalService: IFPMsalService, private _adminService: AdminService, private _toaster: ToasterService, private _apiService: ApiService, private _cdr: ChangeDetectorRef) { }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['isSubmitted']) {
      this.resetAllForms();
      this.isSubmitted = false;
    }
    if (changes['accessLevels']) {
      this.getDomains(this.defaultClassification.value);
      if (this.suForm && (this.suForm.value.classification === this.selectedClassification)) {
        this.suForm.controls['domains'].setValue(this.domainsToDisplay);
      }
    }
    if (changes['exisitingUserList']) {
      if (this.peForm?.value.entity) {
        this.setSuggestionList();
      }
    }
  }

  ngOnInit() {
    this.getJoblevels();
    this.getDepartments();
    // this.getUserRoles();
    this.getPrimaryUserRoles();
    this.getPrimaryUserRoles(true);
    if (this._adminService.userEntity?.domains?.length) {
      this.entityDomain.set(this._adminService.userEntity.domains);
    } else {
      const emailDomain = this._msalService.getLoginData.account.username.replace(/.*@/, '');
      this.entityDomain.set([emailDomain]);
    }
    this.initForm();
  }

  onClick(event: MouseEvent) {
    event.stopPropagation();
  }

  onSelectItem(event: boolean) {
    if (event){
      this.isEmployeeTypeSelected = event;
      this.suForm.get('employeeType')?.setValue(event ? true : false);
      this.suForm.get('employeeType')?.setErrors(null);
    } else {
      this.isEmployeeTypeSelected = event;
      this.suForm.get('employeeType')?.setValue(event ? true : false);
      this.suForm.get('employeeType')?.setErrors({ invalidEmplyeeTypeMsg: true });
    }

  }

  getJoblevels() {
    this.subs.add(
      this._apiService.getMethodRequest(superUser.jobLevels).subscribe((res: any) => {
        if (res) {
          this.jobLevelList = res;
        }
      })
    );
  }

  getDepartments() {
    this.subs.add(
      this._apiService.getMethodRequest(superUser.departments).subscribe((res: any) => {
        if (res) {
          this.departmentList = res;
        }
      })
    );
  }

  // getUserRoles() {
  //   this.isLoading = true;
  //   const params = { role_type: 'secondary_role'};
  //   this.subs.add(
  //     this._apiService.getMethodRequest(superUser.usersRole, params).subscribe({
  //       next: res => {
  //         if (res) {
  //           this.userRoleList = res;
  //         }
  //       },
  //       error: _error => {
  //         this.isLoading = false;
  //       }
  //     })
  //   );
  // }

  getPrimaryUserRoles(dg = false) {

    this.isLoading = true;
    let params;
    if (dg) {
      params = { role_type: 'primary_role', panel_type: 'dg' };
    } else if (this.role == role.pePrimary || this.role == role.peSecondary) {
      params = { role_type: 'primary_role', panel_type: 'pe' };
    } else {
      params = { role_type: 'primary_role', panel_type: 'super_user' };
    }
    this.subs.add(
      this._apiService.getMethodRequest(superUser.usersRole, params).subscribe({
        next: res => {
          if (res) {
            if (dg) {
              this.primaryUserDGList = res;
            } else {
              this.primaryUserRoleList = res;
            }

          }
        },
        error: _error => {
          this.isLoading = false;
        }
      })
    );
  }

  changeEmployeeType(empType:any){
    console.log(empType);
  }

  resetAllForms() {
    if (this.dgForm?.valid) {
      this.clearAddedInvite();
      this.resetDgForm();
      return;
    }
    if (this.peForm) {
      if (this.peFormPrimary.valid && this.submittedUserType === 'primary') {
        this.peFormPrimary.reset();
        this.peFormPrimary.controls['primaryEmailFocus'].setValue(false);
        this.peFormPrimary.controls['pePrimaryPlatform'].setValue(this.platforms[0]);
      }
      if (this.peFormSecondary.valid && this.submittedUserType === 'secondary') {
        this.peFormSecondary.reset();
        this.peFormSecondary.controls['secondaryEmailFocus'].setValue(false);
        this.peFormSecondary.controls['peSecondaryPlatform'].setValue(this.platforms[0]);
      }
    }
    if (this.suForm) {
      this.inviteUserList.clear();
      this.suForm.reset();
      this.suForm.get('classification')?.setValue(this.defaultClassification);
      this.suForm.get('jobLevel')?.setValue('');
      this.suForm.get('department')?.setValue('');
      this.suForm.get('employeeType')?.setValue('');
      this.isEmployeeTypeSelected = false;
      this.suForm.get('domains')?.setValue(this.domainsToDisplay);
      this.selectedClassification = this.defaultClassification;
    }
  }

  initForm() {
    this.getDomains(this.defaultClassification.value);
    if (this.role === role.pePrimary) {
      this.initPeForm();
    } else {
      this.dgForm = this._formBuilder.group({
        dgEmail: ['', [Validators.required, Validators.email, this._validationService.emailMultipleDomainValidator(this.entityDomain())]],
        dgEid: ['', [Validators.required, this._validationService.emiratesIdValidator]],
        dgEmailFocused: [false],
        // primaryDgRole: ['', [Validators.required]],
        secondaryDgRole: ['']
      });
      this.dgType = this.dgTitles[0];
      this.selectedClassification = this.dataClassificationList.find(item => item.value === this.dataClassification.confidential.toLowerCase()) ?? { label: 'Confidential', value: 'confidential', color: ifpColors.orange };
      this.initSuForm();
    }
    this.invitesForm = this._formBuilder.group({
      sentInvites: this._formBuilder.array([])
    });
  }

  initPeForm() {
    this.peFormPrimary = this.inviteFormData()?.['peFormPrimary'] ?? this._formBuilder.group({
      primarySuEmail: ['', [Validators.required, Validators.email]],
      primarySuMobile: ['', [Validators.required, this._validationService.phoneNumberValidator]],
      primarySuJobTitle: ['', [Validators.required]],
      primarySuJoblevel: ['', [Validators.required]],
      primarySuDepartment: ['', [Validators.required]],
      primaryEmailFocus: [false],
      pePrimaryPlatform: [this.platforms[0]],
      // primarySuRole: ['', [Validators.required]],
      secondarySuRole: ['']
    });

    this.peFormSecondary = this.inviteFormData()?.['peFormSecondary'] ?? this._formBuilder.group({
      secondarySuEmail: ['', [Validators.required, Validators.email]],
      secondarySuMobile: ['', [Validators.required, this._validationService.phoneNumberValidator]],
      secondarySuJobTitle: ['', [Validators.required]],
      secondarySuJoblevel: ['', [Validators.required]],
      secondarySuDepartment: ['', [Validators.required]],
      secondaryEmailFocus: [false],
      peSecondaryPlatform: [this.platforms[0]],
      // primarySuRole: ['', [Validators.required]],
      secondarySuRole: ['']
    });

    this.peForm = this.inviteFormData()?.['peForm'] ?? this._formBuilder.group({
      entity: ['', [Validators.required]],
      suggestionList: ['']
    });
  }

  initSuForm() {
    this.suForm = this.inviteFormData()?.['suForm'] ?? this._formBuilder.group({
      email: ['', [Validators.required, Validators.email, this._validationService.emailMultipleDomainValidator(this.entityDomain())]],
      emiratesId: ['', [Validators.required, this._validationService.emiratesIdValidator]],
      jobTitle: ['', Validators.required],
      jobLevel: ['', Validators.required],
      department: ['', Validators.required],
      classification: [this.defaultClassification, Validators.required],
      domains: [this.domainsToDisplay],
      justification: [''],
      userList: this._formBuilder.array([]),
      platform: this.platformType,
      employeeType: ['', Validators.required],
      // primarySuRole: ['', Validators.required],
      secondarySuRole: ['', Validators.required]
    });
  }

  selectEntity(entity: any) {
    this.entity = entity;
    this.allowedDomain.set(entity.DOMAINS);
    this.peForm.controls['entity'].setValue(entity);
    this.setSuggestionList();
    this.peFormPrimary.controls['primarySuEmail'].setValidators([Validators.required, Validators.email, this._validationService.emailMultipleDomainValidator(entity.DOMAINS)]);
    this.peFormSecondary.controls['secondarySuEmail'].setValidators([Validators.required, Validators.email, this._validationService.emailMultipleDomainValidator(entity.DOMAINS)]);
    this.peFormPrimary.controls['primarySuEmail'].updateValueAndValidity();
    this.peFormSecondary.controls['secondarySuEmail'].updateValueAndValidity();
  }

  selectDg(designation: any) {
    // this.dgForm.controls['dgJobTitle'].setValue(designation);
    this.dgType = designation;
  }

  selectPlatform(platform: any, index: number = -1, form: string = 'suForm') {
    if (index < 0) {
      if (this.superUserRoles.includes(this.role)) {
        this.platformType = platform;
        this.suForm.controls['platform'].setValue(platform);
      } else {
        this.peFormSecondary.controls['peSecondaryPlatform'].setValue(platform);
      }
      // } else if (form === 'pePrimary') {
      //   this.peFormPrimary.controls['pePrimaryPlatform'].setValue(platform);
      // }

      return;
    }
    this.inviteUserList.at(index).controls.userPlatform.setValue(platform);
  }

  get inviteUserList(): FormArray<FormGroup<UserListForm>> {
    if (this.suForm) {
      return this.suForm.get('userList') as FormArray<FormGroup<UserListForm>>;
    }
    return {} as FormArray<FormGroup<UserListForm>>;
  }

  get sentInviteList(): FormArray<FormGroup<SentInvitesForm>> {
    if (this.invitesForm) {
      return this.invitesForm.get('sentInvites') as FormArray<FormGroup<SentInvitesForm>>;
    }
    return {} as FormArray<FormGroup<SentInvitesForm>>;
  }

  selectprimarySuJoblevel(data:any){
    this.peFormPrimary.get('primarySuJoblevel')?.patchValue(data);
  }

  selectprimarySuDepartment(data:any){
    this.peFormPrimary.get('primarySuDepartment')?.patchValue(data);
  }

  // selectprimarySuRole(data:any){
  //   this.peFormPrimary.get('primarySuRole')?.patchValue(data);
  // }
  selectSecondarySuRole(data:any){
    this.peFormPrimary.get('secondarySuRole')?.patchValue(data);
  }

  // selectPSuRole(data:any){
  //   this.suForm.get('primarySuRole')?.patchValue(data);
  // }
  selectSSuRole(data:any){
    this.suForm.get('secondarySuRole')?.patchValue(data);
  }

  // selectPDgRole(data:any){
  //   this.dgForm.get('primaryDgRole')?.patchValue(data);
  // }
  selectSDgRole(data:any){
    this.dgForm.get('secondaryDgRole')?.patchValue(data);
  }

  addUser() {
    const isSensitive = this.suForm.value.classification.label === dataClassification.sensitive;
    if (isSensitive && this.isDgRequired && this.dgStatus !== this.dgStatusConst.available) {
      this.openDgModal();
      return;
    }
    if (!this.suForm.get('employeeType')?.value) {
      this.suForm.get('employeeType')?.setErrors({ invalidEmplyeeTypeMsg: true });
      return;
    }
    this.isEmployeeTypeSelected = false;
    this.suForm.get('employeeType')?.setErrors(null);

    // if (this.suForm.get('jobLevel')?.value.label === 'Select') {
    //   this.suForm.get('jobLevel')?.setErrors({ invalidJoblevel: true });
    //   return;
    // } else {
    //   this.suForm.get('jobLevel')?.setErrors(null);
    // }
    const emailCheck = this.inviteUserList.controls.some((control) => control.value.userEmail === this.suForm.value.email);
    const eidCheck = this.inviteUserList.controls.some((control) => control.value.userEmiratesId === this.suForm.value.emiratesId);
    if (!emailCheck && !eidCheck) {
      const classification = this.suForm.value.classification;
      this.inviteUserList.push(this._formBuilder.group({
        userEmail: [this.suForm.value.email, [Validators.required, Validators.email, this._validationService.emailMultipleDomainValidator(this.entityDomain())]],
        userEmiratesId: [this.suForm.value.emiratesId, [Validators.required, this._validationService.emiratesIdValidator]],
        userJobTitle: [this.suForm.value.jobTitle, Validators.required],
        userJobLevel: [this.suForm.value.jobLevel, Validators.required],
        userDepartment: [this.suForm.value.department, Validators.required],
        userClassification: [classification, Validators.required],
        userDomains: [this.suForm.value.domains],
        userJustification: [this.suForm.value.justification ?? ''],
        viewOnly: [true],
        userPlatform: [this.suForm.value.platform],
        userType: [this.suForm.value.employeeType],
        // primaryRole: [this.suForm.value.primarySuRole,],
        secondaryRole: [this.suForm.value.secondarySuRole],

      }));
      this.clearAddedInvite();
      return;
    }
    if (emailCheck) {
      this._toaster.error('User with the given e-mail already exists');
    }
    if (eidCheck) {
      this._toaster.error('User with the given Emirates ID already exists');
    }
  }

  clearAddedInvite() {

    this.suForm.controls['justification'].removeValidators(Validators.required);
    this.suForm.patchValue({
      email: '',
      emiratesId: '',
      jobTitle: '',
      jobLevel: '',
      department: '',
      employeeType: '',
      classification: this.defaultClassification,
      justification: '',
      domains: [],
      usersRole: ''
    });
    this.isEmployeeTypeSelected = false;
    this.clearDropdowns = true;
    this.suForm.markAsPristine();
    this.suForm.markAsUntouched();
    this.suForm.updateValueAndValidity();
  }

  selectDomains(domains: string[], index: number = -1) {
    if (index < 0) {
      this.suForm.controls['domains'].setValue(domains);
    }
  }

  selectJobList(datalevel: any, index: number = -1) {
    if (index < 0) {
      this.clearDropdowns = false;
      this.suForm.controls['jobLevel'].setValue(datalevel);
    } else {
      const formItem = this.inviteUserList.at(index);
      formItem.controls.userJobLevel.setValue(datalevel);
    }
  }

  selectDepartment(datalevel: any, index: number = -1) {
    if (index < 0) {
      this.clearDropdowns = false;
      this.suForm.controls['department'].setValue(datalevel);
    } else {
      const formItem = this.inviteUserList.at(index);
      formItem.controls.userDepartment.setValue(datalevel);
    }
  }

  selectDataClassification(datalevel: any, index: number = -1) {
    this.selectedClassification = datalevel;
    if (index < 0) {
      this.suForm.controls['classification'].setValue(datalevel);
      if (datalevel.value === dataClassification.sensitive.toLowerCase()) {
        this.getDomains(datalevel.value);
        this.suForm.controls['domains'].setValue(this.domainsToDisplay);
        this.suForm.controls['justification'].addValidators(Validators.required);
        this.suForm.controls['justification'].updateValueAndValidity();
      } else {
        this.suForm.controls['domains'].setValue(this.domainAccessList);
        this.suForm.controls['justification'].removeValidators(Validators.required);
        this.suForm.controls['justification'].updateValueAndValidity();
      }
    } else {
      const formItem = this.inviteUserList.at(index);
      formItem.controls.userClassification.setValue(datalevel);
      const userJustification = formItem.controls['userJustification'];
      if (datalevel.value === dataClassification.sensitive.toLowerCase()) {
        formItem.controls.userClassification.setValue(datalevel);
        this.getDomains(datalevel.value);
        formItem.controls.userDomains.setValue(this.domainsToDisplay);
        userJustification.addValidators(Validators.required);
        userJustification.updateValueAndValidity();
      } else {
        formItem.controls.userDomains.setValue(this.domainAccessList);
        userJustification.setValue('');
        userJustification.removeValidators(Validators.required);
        userJustification.updateValueAndValidity();
      }
    }
  }

  selectUserRole(datalevel: any, index: number = -1) {
    if (index < 0) {
      this.clearDropdowns = false;
      // this.suForm.controls['primaryRole'].setValue(datalevel)
      this.suForm.controls['secondaryRole'].setValue(datalevel);
    } else {
      const formItem = this.inviteUserList.at(index);
      // formItem.controls.primaryRole.setValue(datalevel);
      formItem.controls.secondaryRole.setValue(datalevel);
    }
  }

  //   selectPrimaryRole(datalevel: any, index: number = -1) {
  //   if (index < 0) {
  //     this.clearDropdowns = false;
  //     this.suForm.controls['primaryRole'].setValue(datalevel)
  //   } else {
  //     const formItem = this.inviteUserList.at(index);
  //     formItem.controls.primaryRole.setValue(datalevel);
  //   }
  // }
  getDomains(dataLevel: string) {
    this.domainAccessList = this.accessLevels.flatMap((accessLevel: AccessList) => accessLevel.access.filter((access: Access) => access.value === dataLevel && access.enabled).map(() => accessLevel.name));
    this.domainsToDisplay = JSON.parse(JSON.stringify(this.domainAccessList));
  }

  onInviteSuperUser(type: 'primary' | 'secondary') {
    if (this.peForm.valid) {
      const checkEmail = this.peFormPrimary.value.primarySuEmail === this.peFormSecondary.value.secondarySuEmail;
      const checkMobile = this.peFormPrimary.value.primarySuMobile === this.peFormSecondary.value.secondarySuMobile;
      if (checkEmail || checkMobile) {
        if (checkEmail) {
          this._toaster.error('The email addresses for both users are identical');
        }
        if (checkMobile) {
          this._toaster.error('The mobile number for both users are identical');
        }
      } else {
        const superUsers = this.getSuperUserData(type);
        const data = {
          entityId: this.peForm.value.entity.ID,
          superusers: superUsers
        };
        this.submittedUserType = type;
        this.inviteSuperUser.emit(data);
      }
    }
  }

  getSuperUserData(type: string) {
    const superUsers = [];
    if (this.peFormPrimary.valid && type === 'primary') {
      const primaryRoleObj = this.primaryUserRoleList.find((role: any) => role.name === this.userRole.suPrimary);
      const primaryRoleId = primaryRoleObj ? primaryRoleObj.object_id : null;
      superUsers.push({
        email: this.peFormPrimary.value.primarySuEmail,
        phone: this.selectedCountry.code + this.peFormPrimary.value.primarySuMobile.toString(),
        designation: this.peFormPrimary.value.primarySuJobTitle,
        jobLevel: this.peFormPrimary.value.primarySuJoblevel.value,
        department: this.peFormPrimary.value.primarySuDepartment.value,
        role: 'Primary Superuser',
        onBoardMobile: this.peFormPrimary.value.pePrimaryPlatform.value === 'all',
        primaryRole: primaryRoleId,
        secondaryRole: this.peFormPrimary.value.secondarySuRole.object_id
      });
    }
    if (this.peFormSecondary.valid && type === 'secondary') {
      const secondaryRoleObj = this.primaryUserRoleList.find((role: any) => role.name === this.userRole.suSecondary);
      const secondaryRoleId = secondaryRoleObj ? secondaryRoleObj.object_id : null;
      superUsers.push({
        email: this.peFormSecondary.value.secondarySuEmail,
        phone: this.selectedCountry.code + this.peFormSecondary.value.secondarySuMobile.toString(),
        designation: this.peFormSecondary.value.secondarySuJobTitle,
        jobLevel: this.peFormSecondary.value.secondarySuJoblevel.value,
        department: this.peFormSecondary.value.secondarySuDepartment.value,
        role: 'Secondary Superuser',
        onBoardMobile: this.peFormSecondary.value.peSecondaryPlatform.value === 'all',
        primaryRole: secondaryRoleId,
        secondaryRole: this.peFormSecondary.value.secondarySuRole.object_id
      });
    }
    return superUsers;
  }

  selectSecodarySuDepartment(data:any){
    this.peFormSecondary.get('secondarySuDepartment')?.patchValue(data);
  }

  selectsecondarySuJoblevel(data:any){
    this.peFormSecondary.get('secondarySuJoblevel')?.patchValue(data);
  }

  selectsecondarySuRole(data:any){
    this.peFormSecondary.get('secondarySuRole')?.patchValue(data);
  }

  // selectPrimarySuRole(data:any){
  //   this.peFormSecondary.get('primarySuRole')?.patchValue(data);
  // }
  onInviteDg() {
    if (this.dgForm.valid) {
      const primaryRoleObj = this.primaryUserDGList.find((role: any) => role.name === this.userRole.dg);
      const primaryRoleId = primaryRoleObj ? primaryRoleObj.object_id : null;
      const data = {
        type: 'dg',
        recepients: [
          {
            email: this.dgForm.value.dgEmail,
            eid: this.dgForm.value.dgEid,
            designation: this.dgType.value,
            primaryRole: primaryRoleId,
            secondaryRole: this.dgForm.value.secondaryDgRole.object_id
            // designation: this.dgForm.value.dgJobTitle.value
          }
        ]
      };
      this.inviteDg.emit(data);
    }
  }

  onInviteUser() {
    if (this.inviteUserList.controls.length > 0) {
      const primaryRoleObj = this.primaryUserRoleList.find((role: any) => role.name === this.userRole.normalUser);
      const primaryRoleId = primaryRoleObj ? primaryRoleObj.object_id : null;
      const recepients: InviteData[] = [];
      this.inviteUserList.controls.forEach((element: any) => {
        let data = {
          email: element.value.userEmail,
          eid: element.value.userEmiratesId,
          designation: element.value.userJobTitle,
          jobLevel: element.value.userJobLevel.value,
          department: element.value.userDepartment.value,
          isDirect: element.value.userType,
          access: this.getAccessList(element.value.userDomains, element.value.userClassification.label),
          onBoardMobile: element?.value?.userPlatform?.value === 'all',
          primaryRole: primaryRoleId,
          secondaryRole: element.value.secondaryRole.object_id
        };
        if (element.value?.userJustification !== '') {
          data = { ...data, ...{ justification: element.value.userJustification } };
        }
        recepients.push(data);
      });

      const data = {
        type: 'user',
        recepients: recepients
      };

      this.inviteUser.emit(data);
    }
  }

  getAccessList(domainList: string[], classification: string) {
    const access = this.domainAccessList.map((item: string) => {
      if (classification !== dataClassification.sensitive) {
        return { domain: item, classification: classification };
      } else if (classification === dataClassification.sensitive && domainList.includes(item)) {
        return { domain: item, classification: dataClassification.sensitive };
      }
      return { domain: item, classification: dataClassification.confidential };
    });
    return access;
  }

  removeUser(index: number) {
    this.inviteUserList.removeAt(index);
    this.enableInvite = this.inviteUserList.controls.length === 0;
    if (!this.enableInvite) {
      this.enableInvite = this.inviteUserList.controls.some(
        (item: FormGroup<UserListForm>) => item.value.viewOnly
      );
    }
  }

  removeDomain(domainIndex: number, controlIndex: number = -1) {
    if (controlIndex >= 0) {
      this.inviteUserList.controls[controlIndex].value.userDomains.splice(domainIndex, 1);
      if (this.inviteUserList.controls[controlIndex].value.userDomains.length <= 0) {
        const user = this.inviteUserList.at(controlIndex);
        user.controls.userClassification.setValue(this.defaultClassification);
        user.controls.userJustification.setValue('');
        user.controls.userJustification.removeValidators(Validators.required);
        user.controls.userJustification.updateValueAndValidity();
      }
    } else {
      this.suForm.value.domains.splice(domainIndex, 1);
      if (this.suForm.value.domains.length <= 0) {
        this.suForm.controls['classification'].setValue(this.defaultClassification);
        this.suForm.controls['domains'].setValue(this.domainsToDisplay);
        this.suForm.controls['justification'].setValue('');
        this.suForm.controls['justification'].removeValidators(Validators.required);
        this.suForm.controls['justification'].updateValueAndValidity();
      }
    }

  }

  editField(user: FormGroup<UserListForm>, isEditMode: boolean) {
    if (user.valid) {
      const isSensitive = user.value.userClassification.label === dataClassification.sensitive;
      if (isSensitive && this.isDgRequired && this.dgStatus !== this.dgStatusConst.available) {
        this.openDgModal();
        return;
      }
      this.enableInvite = !isEditMode;
      user.controls['viewOnly'].setValue(!isEditMode);
    }
  }

  domainRefresh(index: number = -1) {
    this.getDomains('sensitive');
    if (index >= 0) {
      this.inviteUserList.controls[index].controls.userDomains.setValue(this.domainsToDisplay);
    } else {
      this.suForm.controls['domains'].setValue(this.domainsToDisplay);
    }
  }

  onInviteTabChange(event: any) {
    this.selectedTab = event;
    if (this.role !== role.dg && event.key === 'sentInvites') {
      this.getInviteList();
    }
  }

  async resendInvite(user: InviteList, index: number) {
    await this.onResendInvitation(index).then(() => {
      if (dgRoles.includes(user.inviteeRole)) {
        this.getUpdatedRole();
      } else {
        return;
      }
    });
  }

  onResendInvitation(i: number): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const user = this.inviteList[i];
        if (this.editInviteIndex === i) {
          const userData = {...this.sentInviteList.at(i).value, jobLevel: this.sentInviteList.at(i).value.jobLevel.value, department: this.sentInviteList.at(i).value.department.value};
          const enpoint = `${resendInviteApi}${user.invitationId}`;
          let data;
          if (this.superUserRoles.includes(this.role)) {
            data = {
              userEmail: userData.inviteeEmail,
              designation: userData.designation,
              jobLevel: userData.jobLevel,
              department: userData.department
            };
          } else {
            data = {
              userEmail: userData.inviteeEmail,
              phone: `${this.selectedCountry.code}${userData.phoneNumber}`,
              designation: userData.designation,
              jobLevel: userData.jobLevel,
              department: userData.department
            };
          }
          this.subs.add(
            this._apiService.patchMethodRequest(enpoint, data).subscribe({
              next: () => {
                this.editInviteIndex = -1;
                this._toaster.success('The invitation has been updated successfully');
                this._cdr.detectChanges();
                resolve();
              },
              error: (error) => {
                this._toaster.error(error?.error?.message);
                resolve();
              }
            })
          );
        } else {
          const enpoint = `${resendInviteApi}${user.invitationId}/resend`;
          this.subs.add(
            this._apiService.postMethodRequest(enpoint).subscribe({
              next: (res) => {
                this._toaster.success(res?.message);
                resolve(res);
              },
              error: (error) => {
                this._toaster.error(error.error.message);
                resolve();
              }
            })
          );
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  getUpdatedRole() {
    this.subs.add(
      this._apiService.getMethodRequest(commonApi.role).subscribe({
        next: (res) => {
          this._adminService.dgStatus.set(res?.dg_status);
          this.dgStatus = res.dg_status;
        }
      })
    );
  }

  async deleteInvite() {
    const user = this.inviteList[this.selectedToDeleteIndex];
    await this.onDeleteInvitation(user, this.selectedToDeleteIndex).then(() => {
      if (dgRoles.includes(user.inviteeRole)) {
        this.getUpdatedRole();
      } else {
        return;
      }
    });
  }

  onDeleteInvitation(user: any, index: number) {
    return new Promise((resolve, reject) => {
      try {
        const enpoint = `${deleteInviteApi}${user.invitationId}`;
        this.subs.add(
          this._apiService.deleteWithoutQuery(enpoint).subscribe({
            next: (res) => {
              if (this.editInviteIndex === index) {
                this.editInviteIndex = -1;
              }
              this.inviteList.splice(index, 1);
              this.sentInviteList.removeAt(index);
              this.closeDeleteModal();
              this._cdr.detectChanges();
              this._toaster.success('Invitation cancelled successfully');
              resolve(res);
            },
            error: (error) => {
              this._toaster.error(error.error.message);
              reject(new Error(error.error.message));
            }
          })
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  setSuggestion(value: string, formName: string, controlName: string) {
    if (formName === 'peForm') {
      // this.peForm.controls[controlName].setValue(value);
      if (controlName.includes('primary')) {
        this.peFormPrimary.controls[controlName].setValue(value);
      } else {
        this.peFormSecondary.controls[controlName].setValue(value);
      }
    } else {
      this.dgForm.controls[controlName].setValue(value);
    }
  }

  getInviteList() {
    if (this.role === role.pePrimary) {
      this.subs.add(
        this._apiService.getMethodRequest(productEngagement.invitationsList).subscribe({
          next: (res: InviteList[]) => {
            this.sentInviteList.clear();
            this.inviteList = res;
            this.inviteList.forEach((item: InviteList) => {
              const entityDomain = item.entityDomain;
              this.sentInviteList.push(this._formBuilder.group({
                inviteeEmail: [item.inviteeEmail, [Validators.required, Validators.email, this._validationService.emailMultipleDomainValidator(entityDomain)]],
                role: item.inviteeRole,
                phoneNumber: [item.inviteePhone.slice(3), [Validators.required, this._validationService.phoneNumberValidator]],
                designation: [item.inviteeDesignation, Validators.required],
                jobLevel: [item.inviteeJobLevel, Validators.required],
                department: [item.inviteeDepartment, Validators.required],
                entity: item.entityName,
                status: this.inviteStatus[item.status].value,
                // primaryRole: [item.inviteePrimaryRole, Validators.required],
                secondaryRole: [item.inviteeSecondaryRole]
              }));
            });
            this._cdr.detectChanges();
          },
          error: (error) => {
            this._toaster.error(error.error.message);
          }
        })
      );
    } else {
      const params = {
        entityId: this._adminService.userEntity.id
      };
      this.subs.add(
        this._apiService.getMethodRequest(superUser.invitationsList, params).subscribe({
          next: (res: InviteList[]) => {
            this.sentInviteList.clear();
            this.inviteList = res;
            this.inviteList.forEach((item: InviteList) => {
              const entityDomain = item.inviteeEmail.replace(/.*@/, '');
              this.sentInviteList.push(this._formBuilder.group({
                inviteeEmail: [item.inviteeEmail, [Validators.required, Validators.email, this._validationService.emailDomainValidator(entityDomain)]],
                role: item.inviteeRole,
                phoneNumber: [{ value: item.inviteePhone, disabled: true }],
                jobLevel: [item.inviteeJobLevel, Validators.required],
                department: [item.inviteeDepartment, Validators.required],
                designation: [item.inviteeDesignation, Validators.required],
                entity: [{ value: item.entityName, disabled: true }],
                status: this.inviteStatus[item.status].value,
                // primaryRole: [item.inviteePrimaryRole],
                secondaryRole: [item.inviteeSecondaryRole]
              }));
            });
            this._cdr.detectChanges();
          },
          error: (error) => {
            this._toaster.error(error.error.message);
          }
        })
      );
    }
  }

  editInvite(index: number) {
    const item = this.sentInviteList.at(index);
    if (this.editInviteIndex >= 0) {
      this.cancelEditInvite(this.editInviteIndex);
    }
    this.editInviteIndex = index;
    this.previousInviteValue = item.value;
    const jobLevelval = this.jobLevelList.filter( (obj:any) => obj.value.toUpperCase() === item.value?.jobLevel?.toUpperCase())[0];
    const departmentval = this.departmentList.filter( (obj:any) => obj.label.toUpperCase() === item.value?.department?.toUpperCase())[0];
    // const primaryRoleval = this.primaryUserRoleList.filter( (obj:any) => obj.label.toUpperCase() === item.value?.primaryRole?.toUpperCase())[0];
    const secondaryRoleval = this.userRoleList().filter( (obj:any) => obj.label.toUpperCase() === item.value?.secondaryRole?.toUpperCase())[0];

    this.sentInviteList.at(index).get('jobLevel')?.setValue(jobLevelval);
    this.sentInviteList.at(index).get('department')?.setValue(departmentval);
    // this.sentInviteList.at(index).get('primaryRole')?.setValue(primaryRoleval);
    this.sentInviteList.at(index).get('secondaryRole')?.setValue(secondaryRoleval);
  }

  selectsentInvitJobLevel(data: any, index: number, formName?: string) {
    this.sentInviteList.at(index).get('jobLevel')?.setValue(data);
  }

  selectSentInvitDeptList(data: any, index: number, formName?: string) {
    this.sentInviteList.at(index).get('department')?.setValue(data);
    // console.log(data, index);
  }

  //   selectsentInvitPrimaryRole(data: any, index: number, formName?: string) {
  //   this.sentInviteList.at(index).get('primaryRole')?.setValue(data);
  // }
  selectsentInvitSecondaryRole(data: any, index: number, formName?: string) {
    this.sentInviteList.at(index).get('secondaryRole')?.setValue(data);
    // console.log(data, index);
  }

  cancelEditInvite(index: number) {
    if (this.previousInviteValue) {
      this.sentInviteList.at(index).setValue({ // Reset to previous value
        inviteeEmail: this.previousInviteValue.inviteeEmail,
        phoneNumber: this.previousInviteValue?.phoneNumber ?? null,
        designation: this.previousInviteValue.designation,
        jobLevel: this.previousInviteValue.jobLevel,
        department: this.previousInviteValue.department,
        role: this.previousInviteValue.role,
        entity: this.previousInviteValue?.entity ?? null,
        status: this.previousInviteValue.status,
        // primaryRole: this.previousInviteValue.primaryRole ?? null,
        secondaryRole: this.previousInviteValue.secondaryRole ?? null
      });
    }
    this.editInviteIndex = -1;
    this.previousInviteValue = {};
  }

  setSuggestionList() {
    const filteredList = this.exisitingUserList?.filter((email: string) => this.peForm.value.entity.DOMAINS.includes(email.split('@')[1]));
    this.peForm.controls['suggestionList'].setValue(filteredList?.length ? filteredList : []);
  }

  resetDgForm() {
    this.dgStatus = this._adminService.dgStatus();
    this.dgForm.reset();
    this.dgForm.controls['dgEmailFocused'].setValue(false);
    this.dgType = this.dgTitles[0];
    this.inviteDgModal.removeModal();
  }

  openDgModal() {
    this.inviteDgModal.createElement();
  }

  closeDgModal() {
    this.resetDgForm();
  }

  openCancelInviteModal(index: number) {
    this.selectedToDeleteIndex = index;
    this.cancelInviteModal.createElement();
  }

  closeDeleteModal() {
    this.selectedToDeleteIndex = -1;
    this.cancelInviteModal.removeModal();
  }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
    if (this.role === role.pePrimary) {
      const data = {
        peForm: this.peForm,
        peFormPrimary: this.peFormPrimary,
        peFormSecondary: this.peFormSecondary
      };
      this.saveFormValue.emit(data);
    } else {
      this.saveFormValue.emit({ suForm: this.suForm });
    }
  }
}

interface CountryList {
  country: string;
  flagIcon: string;
  code: number;
}

interface UserListForm {
  userEmail: FormControl;
  userEmiratesId: FormControl;
  userJobTitle: FormControl;
  userJobLevel: FormControl;
  userDepartment: FormControl;
  userClassification: FormControl;
  userDomains: FormControl;
  userJustification: FormControl;
  viewOnly: FormControl;
  userPlatform: FormControl;
  userType: FormControl;
  // primaryRole: FormControl;
  secondaryRole: FormControl;
}

interface InviteData {
  email: string;
  eid: string;
  designation: string;
  access: {
    domain: string;
    classification: string;
  }[]
}

interface SentInvitesForm {
  inviteeEmail: FormControl;
  phoneNumber: FormControl;
  designation: FormControl;
  role: FormControl;
  jobLevel: FormControl;
  department: FormControl;
  entity: FormControl;
  status: FormControl;
  // primaryRole: FormControl;
  secondaryRole: FormControl;
}
