
import { Injectable, WritableSignal, inject, signal } from '@angular/core';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { HttpClient } from '@angular/common/http';
import { Workbook } from 'exceljs';
import { Subject } from 'rxjs';
import { ThemeService } from './theme/theme.service';
import { TranslateService } from '@ngx-translate/core';



@Injectable({
  providedIn: 'root'
})
export class DownloadService {

  private readonly _translate = inject(TranslateService);

  public canvas: any;
  public createChartData$ = new Subject();
  public isPrinting: boolean = false;
  public mapBase64url: any;
  public selectedLang: any;
  public insightsLogo!: string;
  public statisticCenterLogo!: string;
  public printGeoSpatial: WritableSignal<boolean> = signal(false);
  constructor(private http: HttpClient, private _themeService: ThemeService) {
    this._themeService.defaultLang$.subscribe((lang: string) => {
      this.selectedLang = lang;
    });
  }

  // export exel ;
  async exportToExcel(_data: any[], _fileName: string, classification: string = 'Sensitive', description: string = 'Data is available utmost confidentiality.') {
    const logoUrl = this.insightsLogo;
    const endLogo = this.statisticCenterLogo;
    let endLogoAlphabet: any;
    let endLogoAlphaBetSecond: any;
    const asciiCodeOfA = 'A'.charCodeAt(0);
    const header = this.getTitles(_data[0]);
    if (header.length >= 0 && header.length < 26) {
      const aschicodeStartLetter = asciiCodeOfA + (header.length - 1);
      endLogoAlphabet = String.fromCharCode(aschicodeStartLetter);
      const aschiCodeSecondLetter = asciiCodeOfA + header.length;
      endLogoAlphaBetSecond = String.fromCharCode(aschiCodeSecondLetter);
    }
    const title = _fileName;
    _data = this.formatData(_data, header);
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet(_fileName);
    if (logoUrl) {
      try {
        const logoBase64 = await this.fetchImageAsBase64(logoUrl);
        const logoImage = workbook.addImage({
          base64: logoBase64,
          extension: 'png'
        });
        worksheet.addImage(logoImage, 'A2:A4');
      } catch {
        // catch issue
      }
    }
    if (endLogo) {
      try {
        const logoBase64End = await this.fetchImageAsBase64(endLogo);
        const logoImageEnd = workbook.addImage({
          base64: logoBase64End,
          extension: 'png'
        });
        worksheet.addImage(logoImageEnd, `${endLogoAlphabet}5:${endLogoAlphaBetSecond}7`);
      } catch {
        // catch issue
      }
    }
    const titleRow = worksheet.addRow([title]);
    titleRow.font = { name: 'Arial', family: 4, size: 14, bold: true };
    worksheet.addRow([]);
    worksheet.mergeCells('A1:Z8');
    worksheet.addRow([]);
    const headerRow = worksheet.addRow(header);
    worksheet.properties;
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFD6A461' },
        bgColor: { argb: '#FFFFFFFF' }
      };
      cell.font = { name: 'Arial', family: 4, size: 10, bold: true, color: { argb: 'FFFFFFFF' } };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
    });
    headerRow.height = 50;
    _data.forEach(d => {
      worksheet.addRow(d);
    }
    );
    if (header && header?.length > 0) {
      for (let index = 0; index < header.length; index++) {
        worksheet.getColumn(index + 1).width = 18;
      }
    }
    worksheet.addRow([]);
    const footerRow = worksheet.addRow([`${classification} Information: ${description}.`]);
    footerRow.getCell(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFCCFFE5' }
    };
    footerRow.getCell(1).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
    worksheet.mergeCells(`A${footerRow.number}:F${footerRow.number}`);
    workbook.xlsx.writeBuffer().then((data: any) => {
      const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      saveAs(blob, `${_fileName}.xlsx`);
    });

  }

  getTitles(labels: any) {
    return Object.keys(labels);
  }

  getValues(labels: any) {
    return Object.values(labels);
  }

  formatData(data: any, header: string[]) {
    const returnData: any = [];
    if (data?.length > 0) {
      data.forEach((element: any) => {
        const values: string[] = [];
        header.forEach((headerValue: string) => {
          values.push(element[headerValue]);

        });
        returnData.push(values);
      });
    }
    return returnData;
  }


  fetchImageAsBase64(url: string): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous'; // Set crossOrigin attribute
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        ctx?.drawImage(img, 0, 0);
        const base64String = canvas.toDataURL('image/png');
        resolve(base64String);
      };
      img.onerror = (error) => {
        reject(error);
      };
      img.src = url;
    });
  }

  saveExcelFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], { type: '.xlsx' });
    saveAs(data, `${fileName}.xlsx`);
  }


  // download Pdf ;
  downloadPdf(element: any, title: string, height: number = 0, width: number = 0, type: string = 'graph'): Promise<boolean> {
    const orientation = width >= height ? 'landscape' : 'portrait';
    return new Promise((resolve, _reject) => {
      html2canvas(element, {
        scale: type == 'table' ? 1 : 1.5,
        allowTaint: true, useCORS: true
      }).then((canvas) => {
        // Convert canvas to base64 image
        const image = canvas.toDataURL('image/png', 10.0);
        const pdf = new jsPDF({
          orientation: orientation, // or 'landscape'
          unit: 'pt',
          format: [height, width],
          compress: true
        });
        const imgWidth = width; // Adjust image width as needed
        const imgHeight = height; // Assuming image aspect ratio of 4:3, adjust as needed
        const xPos = 0;
        const yPos = 0;

        pdf.addImage(image, 'JPEG', xPos, yPos, imgWidth, imgHeight);
        pdf.save(`${title}.pdf`);
        resolve(true);
      });
    });
  }

  async cutImageIntoParts(element: HTMLElement): Promise<HTMLCanvasElement[]> {
    this.canvas = await html2canvas(element, { allowTaint: true, useCORS: true });
    const numCols = 1;
    const numRows = this.canvas.height / 1500;
    const width = this.canvas.width;
    const height = this.canvas.height - 100;
    const cellWidth = width / numCols;
    const cellHeight = height / numRows;
    const imageParts: HTMLCanvasElement[] = [];
    for (let row = 0; row < numRows; row++) {
      for (let col = 0; col < numCols; col++) {
        const partCanvas = document.createElement('canvas');
        partCanvas.width = cellWidth;
        partCanvas.height = cellHeight;
        const ctx = partCanvas.getContext('2d');
        if (ctx) {
          ctx.drawImage(
            this.canvas,
            col * cellWidth, row * cellHeight, cellWidth, cellHeight,
            0, 0, cellWidth, cellHeight
          );
          imageParts.push(partCanvas);
        }
      }
    }

    return imageParts;
  }

  downloadAsCSV(data: any, name?: string) {
    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(data);
    // Generate the workbook
    const workbook: XLSX.WorkBook = { Sheets: { 'data': worksheet }, SheetNames: ['data'] };
    // Convert the workbook to an Excel binary file
    const excelBuffer: any = XLSX.write(workbook, { bookType: 'csv', type: 'array' });
    // Create a Blob from the buffer
    const blob: Blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' });
    // Save the file using FileSaver.js
    saveAs(blob, name ? `${name}.csv` : 'data.csv');
  }

  // download png ;
  downloadElementAsPNG(element: any, fileName: string) {
    html2canvas(element, { allowTaint: true, useCORS: true }).then((canvas) => {
      // Convert canvas to base64 image
      const image = canvas.toDataURL('image/png');
      // Create a temporary link element
      const link = document.createElement('a');
      link.href = image;
      link.download = `${fileName}.png`;
      link.click();
    });
  }


  // convertImage ;
  getImageAsBase64(imageUrl: string): Promise<string> {
    return this.http
      .get(imageUrl, { responseType: 'blob' })
      .toPromise()
      .then((blob: any) => this.blobToBase64(blob));
  }

  private blobToBase64(blob: Blob): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        resolve(base64String);
      };
      reader.onerror = (error) => {
        reject(error);
      };
      reader.readAsDataURL(blob);
    });
  }


    public async downloadSingleObjectXl(_data: any, _fileName: string, classification: string = 'Sensitive', description: string = 'Data is available utmost confidentiality.'): Promise<void> {
    const logoUrl = this.insightsLogo;
    const endLogo = this.statisticCenterLogo;
    const header = this.selectedLang == 'en' ? ['Title', 'Value'] : ['عنوان', 'قيمة'];
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet(_fileName);
    if (logoUrl) {
      try {
        const logoBase64 = await this.fetchImageAsBase64(logoUrl);
        const logoImage = workbook.addImage({
          base64: logoBase64,
          extension: 'png'
        });
        worksheet.addImage(logoImage, 'A2:A4');
      } catch { // image error
      }
    }
    if (endLogo) {
      try {
        const logoBase64End = await this.fetchImageAsBase64(endLogo);
        const logoImageEnd = workbook.addImage({
          base64: logoBase64End,
          extension: 'png'
        });
        worksheet.addImage(logoImageEnd, 'C3:E4');
      } catch {
        // image error
      }
    }
    worksheet.addRow([]);
    worksheet.mergeCells('A1:Z5');
    worksheet.addRow([]);
    const headerRow = worksheet.addRow(header);
    headerRow.eachCell((cell) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFD6A461' },
        bgColor: { argb: '#FFFFFFFF' }
      };
      cell.font = { name: 'Arial', family: 4, size: 10, bold: true, color: { argb: 'FFFFFFFF' } };
      cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
    });
    headerRow.height = 50;
    if (header && header?.length > 0) {
      for (let index = 0; index < header.length; index++) {
        worksheet.getColumn(index + 1).width = 18;
      }
    }
    worksheet.addRow([]);
    for (const key in _data) {
      // eslint-disable-next-line no-prototype-builtins
      if (_data.hasOwnProperty(key)) {
        const dataRaw = worksheet.addRow([key, _data[key]]);
        dataRaw.eachCell((cell) => {
          cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
        });
      }
    }

    worksheet.getColumn(1).width = 20;
    worksheet.getColumn(2).width = 100;
    const footerRow = worksheet.addRow([`${classification} Information: ${description}.`]);
    // const footerRow = worksheet.addRow([this.selectedLang == 'en' ? 'Sensitive Information: Data is available utmost confidentiality.' : 'معلومات حساسة: البيانات متاحة بسرية تامة.']);
    footerRow.getCell(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFCCFFE5' }
    };
    footerRow.getCell(1).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
    worksheet.mergeCells(`A${footerRow.number}:F${footerRow.number}`);
    workbook.xlsx.writeBuffer().then((data: any) => {
      const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      saveAs(blob, `${_fileName}.xlsx`);
    });

  }
}
