import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateModule } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { IfpImgComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-img/ifp-img.component';
import { getFooter } from 'src/app/scad-insights/store/footer/footer.action';
import { selectFooterResponse } from 'src/app/scad-insights/store/footer/footer.selector';
import { SubSink } from 'subsink';

@Component({
  selector: 'ifp-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
  imports: [
    RouterLink,
    RouterLinkActive,
    TranslateModule,
    IfpImgComponent,
    AsyncPipe
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FooterComponent implements OnInit {
  public buttonClass = buttonClass;
  public subs: SubSink = new SubSink;
  public footerData$: Observable<any> = this._store.select(selectFooterResponse);
  public linkIconWidth: number = 16;
  public linkIconHeight: number = 16;
  public currentYear: number = new Date().getFullYear();
  constructor(private _store: Store) {}

  ngOnInit() {
    this._store.dispatch(getFooter());
    // this._store.select(selectFooterResponse).subscribe((data: any) => {
    // });
  }
}
