<div class="ifp-hies-census-filters  ifp-download-analysis">
  <div class="ifp-hies-census-filters__header">
    <h3 class="ifp-hies-census-filters__title">{{ 'Most Used Filters in Census' | translate }}</h3>
    <div class="ifp-hies-census-filters__download ifp-drop-download">
      <app-ifp-dropdown [isInline]="true" [dropDownItems]="[{label: 'Export'}]" [key]="'label'" [isDownload]="true"></app-ifp-dropdown>
    </div>
  </div>

  <div class="ifp-hies-census-filters__content">
    <div class="ifp-hies-census-filters__panel">
      <p class="ifp-hies-census-filters__panel-title">{{ 'By Region' | translate }}</p>
      <div class="ifp-hies-census-filters__table-scroll" [class.is-empty]="!regionData || regionData.length === 0">
        <table class="ifp-hies-census-filters__table">
          <thead>
            <tr class="ifp-download-analysis__list-header">
              <th class="ifp-hies-census-filters__col-filter">{{ 'Filter' | translate }}</th>
              <th class="ifp-hies-census-filters__col-uses">{{ 'Uses' | translate }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let row of regionData; trackBy: trackByIndex" class="ifp-hies-census-filters__row">
              <td class="ifp-hies-census-filters__cell">{{ row.label }}</td>
              <td class="ifp-hies-census-filters__cell ifp-hies-census-filters__cell--right">{{ row.value }}</td>
            </tr>
            <tr *ngIf="!regionData || regionData.length === 0">
              <td colspan="2"><app-ifp-no-data></app-ifp-no-data></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="ifp-hies-census-filters__panel">
      <p class="ifp-hies-census-filters__panel-title">{{ 'By Year' | translate }}</p>
      <div class="ifp-hies-census-filters__table-scroll" [class.is-empty]="!yearData || yearData.length === 0">
        <table class="ifp-hies-census-filters__table">
          <thead>
            <tr class="ifp-download-analysis__list-header">
              <th class="ifp-hies-census-filters__col-filter">{{ 'Filter' | translate }}</th>
              <th class="ifp-hies-census-filters__col-uses">{{ 'Uses' | translate }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let row of yearData; trackBy: trackByIndex" class="ifp-hies-census-filters__row">
              <td class="ifp-hies-census-filters__cell">{{ row.label }}</td>
              <td class="ifp-hies-census-filters__cell ifp-hies-census-filters__cell--right">{{ row.value }}</td>
            </tr>
            <tr *ngIf="!yearData || yearData.length === 0">
              <td colspan="2"><app-ifp-no-data></app-ifp-no-data></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
