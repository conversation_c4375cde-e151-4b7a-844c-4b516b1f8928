<div class="ifp-db" [ngClass]="{'ifp-db--preview': mode === 'preview', 'ifp-db--detail': mode === 'detail'}">
  @if (mode === 'edit') {
  <div class="ifp-db__page-header">
    <div class="ifp-container">
      <div class="ifp-db__header-wrapper">
        <div class="ifp-db__header-left">
          <a [routerLink]="['/store/dashboards-basic']" class="ifp-link ifp-db__header-back"
            [title]="'Back' | translate"><em class="ifp-icon ifp-icon-left-arrow"></em> {{'Back' | translate}}</a>
            <div class="ifp-beta-title">
              <p class="ifp-db__page-title">{{'My Dashboards' | translate}}</p>
              <!-- @if ((themeService.defaultLang$|async) === 'en') {
                <img src="../../../assets/images/beta-icon.svg" alt="BETA" class="ifp-beta-icon">
              } @else {
                <img src="../../../assets/images/beta-icon-arabic.svg" alt="BETA" class="ifp-beta-icon">
              } -->
            </div>
        </div>
        <div class="ifp-db__header-right">
          <div class="ifp-db__header-action-sec">
            <!-- <div class="ifp-db__header-action-btn" [title]="'Reset'| translate"><em
                  class="ifp-icon ifp-icon-reset"></em></div> -->
            <div class="ifp-db__header-action-btn" [title]="'Download'| translate" (click)="exportPDF()"
              [ngClass]="{'ifp-db__header-action-btn--disabled' : !isEnableButton}"><em
                class="ifp-icon ifp-icon-download-line"></em></div>
          </div>
          <div class="ifp-db__header-btn-sec">
            <ifp-button [label]="'Preview' | translate" (ifpClick)="preview('preview')"
              [buttonClass]="isEnableButton ? buttonClass.primary : buttonClass.disabled +' '+ buttonIconPosition.right"
              [iconClass]="'ifp-icon-eye'" class="ifp-db__header-btn"></ifp-button>
            <ifp-button [label]="isEdit && dashboardId ? 'Update': 'Save' | translate" (ifpClick)="saveDashboard()"
              [buttonClass]="isEnableButton ? buttonClass.primary : buttonClass.disabled +' '+ buttonIconPosition.right"
              [iconClass]="'ifp-icon-save'" class="ifp-db__header-btn"></ifp-button>
          </div>
        </div>
      </div>
    </div>
  </div>
  }
  <div class="ifp-container">
    @if (mode !== 'edit' && !isAiDashboard) {
    <div class="ifp-db__head">
      @if (mode === 'detail' || mode === 'preview') {
      <div>
        <span (click)="mode === 'detail' ? goBack(): preview(isPreviousMode)" class="ifp-link ifp-db__header-back"
          [title]="'Back' | translate"><em class="ifp-icon ifp-icon-left-arrow"></em> {{'Back' | translate}}</span>
        @if (sendData && sendData?.key) {
        <p class="ifp-db__header-back-label">{{sendData?.key}} : <span
            class="ifp-db__header-back-link">{{sendData?.value}}</span></p>
        }
      </div>
      }
      <!-- @if (mode === 'preview') {
      <div class="ifp-db__message">
        <p class="ifp-db__message-text">{{(!dashboardId ? message : (isPreviousMode == 'edit' ? editMessage:
          '' )) | translate}}</p>
        <div class="ifp-db__message-btn-sec">
          @if (!dashboardId) {
          <ifp-button class="ifp-db__message-btn" [label]="'Yes'" [buttonClass]="buttonClass.primary"
            (ifpClick)="saveDashboard()"></ifp-button>
          }
        </div>
      </div>
      } -->
      <div class="ifp-db__header-right">
        @if (selectTab == 'Dashboards') {
        <div class="ifp-db__btn-round" (click)="preview('edit'); isEdit =true;">
          <em class="ifp-icon ifp-icon ifp-icon-edit"></em>
          <span class="ifp-db__btn-round-text">{{'Edit' | translate}}</span>
        </div>
        }


        <div class="ifp-db__btn-round" (click)="exportPDF()">
          <em class="ifp-icon ifp-icon-download-line"></em>
          <span class="ifp-db__btn-round-text">{{'Export' | translate}}</span>
        </div>
      </div>
    </div>
    <span class="ifp-db__preview-tag">{{'Preview' | translate}}</span>
    }
    <div class="ifp-db__wrapper" [style.height]="isAiDashboard ? 'auto' : gridsterHeight+'px'" #downloadPrint>
      <!-- header start -->


      <!-- header end -->

      <!-- dashboard start -->
      <!-- @if (mode === 'edit' || _dashboardService.dashboardProperties.logo !== '' ||
      _dashboardService.dashboardProperties.title !== '') { -->
      <div class="ifp-db__header">
        <div class="ifp-db__header-title-container">
          @if (mode === 'edit') {
          <input type="text" (click)="isTitleUpdate =true"
            [value]="_dashboardService.dashboardProperties.title === ''  ? (!isTitleUpdate ? 'Untitled' : '') :  _dashboardService.dashboardProperties.title"
            class="ifp-db__header-title" (keyup)="updateDashboardTitle($event)" #inputTitle>
          <em class="ifp-icon ifp-icon-edit" (click)="inputTitle.focus()"></em>
          <div *ngIf="isTitleLengthOver" class="ifp-db__validation">
            {{'*limit exceeds' | translate}}
          </div>
          <div *ngIf="isWhiteSpaces" class="ifp-db__validation">
            {{'*Remove unwanted white spaces' | translate}}
          </div>
          } @else {
          <p class="ifp-db__header-title">{{_dashboardService?.dashboardProperties?.title}}</p>
          }
        </div>

        <div class="ifp-db__logo-sec">
          @if (mode === 'edit') {
          <p class="ifp-db__logo-text">{{'Your logo' | translate}}</p>
          <app-ifp-db-file-uploader [allowedExtensions]="logoFormat" [isImage]="true" (fileUpload)="uploadLogo($event)"
            [previewUrl]="_dashboardService?.dashboardProperties?.logo ? _dashboardService?.dashboardProperties?.logo : ''"
            (removeFile)="deleteLogo($event)"></app-ifp-db-file-uploader>
          } @else {
          <img [src]="_dashboardService?.dashboardProperties?.logo" class="ifp-db__logo">
          }

        </div>
      </div>



      <!-- card listing start -->
      <!-- @if (showCustomCard) {
        <app-ifp-db-card [isCustom]="true" class="ifp-db__card ifp-db__card--custom"></app-ifp-db-card>
      } @else { -->
      <gridster [options]="options" [style.height]="isAiDashboard ? 'auto' : gridsterHeight+'px'">
        <div class="ifp-db__inner-wrapper" #dbHeader>
          @for (key of getObjectKeys(selectedCards); let i=$index; track key;) {
          @for (card of selectedCards[key]; let j=$index; track card) {
          <gridster-item class="ifp-db__gridster-item" [item]="card" [ngClass]="{'ifp-db__gridster-item--selected': selectedId == card.id && mode == 'edit'}">
            <app-ifp-db-card [id]="card.id" [isSelectMode]="false" class="ifp-db__card"
              [ngClass]="{'ifp-db__card--selected' : selectedId == card && mode == 'edit'}" [isShowchart]="true"
              (click)="selectCard(card.id, card)" (touchend)="selectCard(card.id, card)" [selectedId]="selectedId"
              [cntType]="card.key != dashboardConstants.customCard ? contentType : dashboardConstants.customCard"
              [isDashboardCard]="true" (updatedCardTitle)="updateTitle($event, 'title')"
              (deleteCardEmit)="deletaCard($event)" (openFilter)="openFilterPanel($event)" [mode]="mode"
              (updatedCardDescription)="updateTitle($event, 'cardDescription')" (openTextArea)="openTextArea($event)"
              [accessPending]="card.requestNode" [isCustom]="card.key == dashboardConstants.customCard"
              [customChartType]="card.chartType" (updateCustomChartType)="updateCustomChart($event, j)"
              [dashboardView]="dashboardId"></app-ifp-db-card>
          </gridster-item>
          }
          }


          @if (checkLength() && mode === 'edit') {
          <div class="ifp-db__card ifp-db__card--add-indicator" (click)="toggleDropDown()">
            <em class="ifp-icon ifp-icon-plus"></em>
            <p class="ifp-db__add-text">{{'Add Indicators' | translate}}</p>

            @if (showImportTypes()) {
            <ifp-import-dropdown [showImportTypes]="showImportTypes()" (openImportType)="openImportIndicators($event)"
              class="ifp-db__import-pop-up"></ifp-import-dropdown>
            }

          </div>
          }

        </div>
      </gridster>
      <!-- } -->
      <!-- card listing end -->

      <!-- dashboard start -->
    </div>
  </div>

  @if (!checkLength() && mode === 'edit') {
  <div class="ifp-db__add-indicator" (click)="toggleDropDown()"
    [ngClass]="{'ifp-db__add-indicator--right' : isLeftCut}">
    <div class="ifp-db__add-indicator-relative">
      <em class="ifp-icon ifp-icon-plus"></em><span class="ifp-db__add-indicator-text">{{'Add Indicators' |
        translate}}</span>

      @if (showImportTypes()) {
      <ifp-import-dropdown [showImportTypes]="showImportTypes()" (openImportType)="openImportIndicators($event)"
        class="ifp-db__import-pop-up"></ifp-import-dropdown>
      }
    </div>
  </div>
  }

</div>

<!-- toolbar start -->
@if (checkCardsLength() && mode === 'edit' && selectedId != '') {
<div class="ifp-db__toolbar ifp-db__toolbar--right resizable" #toolbar (mousedown)="onMouseDown($event)"
  (document:mouseup)="onMouseUp()" (document:mousemove)="onMouseMove($event)" [ngClass]="{'ifp-db__toolbar--expand': isToolbarExpanded, 'ifp-db__toolbar--filter': isFilterPanel, 'ifp-db__toolbar--sticky': isSticky,
    'ifp-db__toolbar--resize': isSelectedTool == 'data'
  }" #sideBar (mousedown)="onMouseResizeDown($event)" (document:mouseup)="onResizeMouseUp($event)"
  (document:mousemove)="onResizeMouseMove($event)"
  [ngStyle]="{'max-width': isToolbarExpanded ? toolbarWidth+'px': 30+'px'}">
  <div class="ifp-db__toolbar-inner " [ngClass]="{'ifp-db__toolbar-inner--drag' : isDragging}">
    <em class="ifp-icon ifp-icon-left-arrow ifp-db__toolbar-toggle content"
      [appIfpTooltip]=" (isToolbarExpanded ? 'Collapse' : 'Expand')| translate" (click)="expandOrCollapseToolbar()"
      [zIndex]="2000"></em>
    <ifp-chart-toolbar [isPieDisabled]="isPieDisabled" (changeChartType)="changeChartType($event)"
      [selectedCard]="selectedId" [cntType]="selectedCardData.key !=dashboardConstants.customCard ? contentType :
      dashboardConstants.customCard" class="ifp-db__toolbar-panel" [dataType]="dataType"
      [selectedCardData]="selectedCardData" [allDropDownData]="allDropDownData"
      [selectedAllCardData]="selectedAllCardData" [isFilterPanel]="isFilterPanel"
      (closeToolbar)="closeExpandModel($event)" [isTextareaExpanded]="isTextareaExpanded"
      (dockOptionChanged)="changeToolbarPosition($event)" [isDragged]="isDragged"
      (pinDashboardOutput)="pinDashboard($event)" [openDataTool]="selectedCardData.isOpen"
      (selectTool)="selectTool($event)" [uploadedFile]="uploadedFile"></ifp-chart-toolbar>
  </div>
</div>
}
<!-- toolbar end -->

<app-ifp-modal #importInidcators [overlayType]="'transparent'" [modalClass]="'ifp-modal__import-indicators'">
  @if (isImportDropdown) {
  <app-ifp-import-indicators [heading]="'Select the indicators you want to add'| translate"
    (closeImport)="closeImport()" (addToDashboard)="addAllIndicators($event)" [importType]="importType"
    [displayedCards]="selectedCards" [isDashboardTool]="true"></app-ifp-import-indicators>
  }

</app-ifp-modal>

<app-ifp-modal #loaderModal [modalClass]="'ifp-modal__import-indicators'">
  <div class="ifp-db__loader">+
    <app-ifp-spinner></app-ifp-spinner>
  </div>
</app-ifp-modal>

<app-ifp-modal #uploadData [overlayType]="'transparent'" [modalClass]="'ifp-modal__upload-data'">
  <ifp-sc-upload-model class="ifp-db__upload-model" (closeUploadModel)="closeUploadModel()"
    (uploadOrAddData)="uploadOrAddData($event)"></ifp-sc-upload-model>
</app-ifp-modal>
