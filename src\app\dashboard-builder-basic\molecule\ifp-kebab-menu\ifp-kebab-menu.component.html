<div class="ifp-kebab-menu" [ngClass]="{'ifp-kebab-menu--active': showDropdown, 'ifp-kebab-menu--left': position === 'left', 'ifp-kebab-menu--right': position === 'right', 'ifp-kebab-menu--top': verticalPosition === 'top'}">
  <em class="ifp-icon ifp-icon-kebab-menu ifp-kebab-menu__btn" (click)="showDropdown = !showDropdown"></em>
  <ul class="ifp-kebab-menu__list">
    @for (option of options; track option.name) {
      @if (!option.disabled) {
        <li class="ifp-kebab-menu__item" (click)="onOptionSelect(option.event)">
          @if (option?.icon) {
            <em [class]="'ifp-icon ' + option.icon"></em>
          }
        {{option.name | translate}}
      </li>
    }
    }
  </ul>
</div>
