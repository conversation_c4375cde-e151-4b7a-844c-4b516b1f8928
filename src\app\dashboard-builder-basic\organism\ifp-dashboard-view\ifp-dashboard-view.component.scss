@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-db-view {
  padding: $spacer-5 $spacer-0;
  // background-color: $ifp-color-white;
  & > .ifp-container {
    position: relative;
  }
  &__head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    padding: $spacer-0 $spacer-3;
  }
  &__message {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &__message-text {
    font-size: $ifp-fs-5;
    font-weight: $fw-medium;
  }
  &__message-btn-sec {
    margin: $spacer-0 $spacer-2;
  }
  &__message-btn {
    margin: $spacer-0 $spacer-2;
  }
  &__body {
    position: relative;
    padding: $spacer-0 $spacer-6;
  }
  &__btn-round-text {
    margin-top: $spacer-1;
    display: block;
    color: inherit;
  }
  &__btn-round {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: $ifp-fs-5;
    cursor: pointer;
    .ifp-icon {
      font-size: inherit;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      border: 1px solid $ifp-color-grey-3;
      padding: $spacer-1;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: 0.3s;
    }
    &:hover {
      color: $ifp-color-active-blue;
      .ifp-icon {
        color: $ifp-color-white;
        background-color: $ifp-color-active-blue;
        border: 1px solid $ifp-color-active-blue;
      }
    }
  }
  &__tag {
    display: none;
  }
  &--preview {
    .ifp-db-view {
      &__head {
        position: static;
        margin-bottom: $spacer-4;
      }
      &__tag {
        display: inline-block;
        background-color: $ifp-color-grey-5;
        color: $ifp-color-white;
        padding: $spacer-1 $spacer-4;
        border-radius: 5px 5px 0 0;
        position: relative;
        margin-bottom: -1px;
        text-transform: capitalize;
        &::after {
          content: "";
          position: absolute;
          right: -8px;
          bottom: 0;
          width: 0;
          height: 0;
          border-right: 9px solid transparent;
          border-left: 9px solid transparent;
          border-bottom: 24px solid $ifp-color-grey-5;
        }
      }
      &__body {
        padding: $spacer-5;
        border: 1px solid $ifp-color-grey-3;
        border-radius: 0 5px 5px 5px;
      }
    }
  }
}
