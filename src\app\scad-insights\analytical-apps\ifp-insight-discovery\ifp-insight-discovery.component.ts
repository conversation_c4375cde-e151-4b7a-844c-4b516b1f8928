import { chartConstants } from 'src/app/scad-insights/core/constants/chart.constants';
import { cloneDeep } from 'lodash';
import { CommonModule } from '@angular/common';
import { Component, On<PERSON><PERSON>roy, OnInit, QueryList, ViewChild, ViewChildren, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateModule } from '@ngx-translate/core';
import { classCarousel } from 'src/app/scad-insights/core/constants/caroisel.constants';
import { IfpCarouselItemDirective } from 'src/app/scad-insights/core/directives/Ifp-carousel-item.directive';
import { getIndicatorDetail, getIndicatorFilterDetail } from 'src/app/scad-insights/home/<USER>/Analytical apps/analyticalApps.action';
import { selectAnalyticGetById } from 'src/app/scad-insights/home/<USER>/Analytical apps/analyticalApps.selector';
import { IfpCarouselComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-carousel/ifp-carousel.component';
import { IfpBreadcrumbsComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-breadcrumbs/ifp-breadcrumbs.component';
import { IfpInsightDiscoveryCardComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-insight-discovery-card/ifp-insight-discovery-card.component';
import { IfpWhatsNewCardComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-whats-new-card/ifp-whats-new-card.component';
import { SubSink } from 'subsink';
import { getStatisticsInsights } from 'src/app/scad-insights/store/statistics-insights/statistics-insights-list.action';
import { PageData } from 'src/app/scad-insights/core/interface/molecule/breadcrumb.interface';
import { Title } from '@angular/platform-browser';
import { IfpAnalysisCardComponent } from '../../ifp-widgets/ifp-organism/ifp-analysis-card/ifp-analysis-card.component';
import { classifications } from 'src/app/scad-insights/core/constants/domain.constants';
import { QuotRemove } from 'src/app/scad-insights/core/pipes/quotsRemove.pipe';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { IFPMsalService } from '../../core/services/IFP-msal.service';
import { Security } from '../../core/interface/indicator.interface';
import { IfpIndicatorCardService } from '../../core/services/indicator-card/ifp-indicator-card.service';
import { environment } from 'src/environments/environment';
import { title } from '../../core/constants/header.constants';


@Component({
    selector: 'app-ifp-insight-discovery',
    templateUrl: './ifp-insight-discovery.component.html',
    styleUrls: ['./ifp-insight-discovery.component.scss'],
    imports: [IfpInsightDiscoveryCardComponent, IfpBreadcrumbsComponent, CommonModule, TranslateModule, IfpWhatsNewCardComponent, QuotRemove,
        IfpCarouselComponent, IfpCarouselItemDirective, IfpAnalysisCardComponent]
})
export class IfpInsightDiscoveryComponent implements OnInit, OnDestroy {

  @ViewChild('leftContainer') leftContainer!: IfpCarouselComponent;
  @ViewChildren('leftCard') leftCard!: QueryList<IfpWhatsNewCardComponent>;
  @ViewChild('cardComponent') cardComponent!: IfpInsightDiscoveryCardComponent;

  id: any;
  public subsink = new SubSink();
  public response: any = [];
  public carousalClass = classCarousel;
  public activeLeftSlider = false;
  public activeLeft: number | null = null;
  public active: number | null = null;
  public indicatorsIds: any = [];
  public chartTitle!: string;
  public visualaizationfilterPanel: any = [];
  public visualizationData: any = [];
  public tableData: any = [];
  public visulaizationId!: any;
  public visualaizationValues: any = [];
  public visualaizationFilters: any = [];
  public chartData: any = [];
  public allCharts: any = [];
  public appType!: string;
  public loader: any = false;
  public relatedInsightDiscovery: any = [];
  public isRender: boolean = false;
  public classifications = classifications;
  public dataSource: string = '';
  public disclaimerDetails: string[] = [];
  public chartConstants = chartConstants;
  public logoPath!: string;
  public isFilterSelected: boolean = false;
  public hideSuffix: boolean = false;
  public hideChartTypes: string[] = [];

  public isMultiRelationFilterEnable: boolean = false

  pageData!: PageData[];
  public security!: Security;

  public customOptions = {
    freeMode: true,
    slidesPerView: 'auto',
    updateOnWindowResize: true,
    watchSlidesProgress: true
  };

  constructor(private route: ActivatedRoute, private store: Store, private _titleService: Title, private _cdr: ChangeDetectorRef, private themeService: ThemeService, private _msalService: IFPMsalService, private _cardService: IfpIndicatorCardService) {


  }

  ngOnInit() {
    this.subsink.add(
      this.route.params.subscribe(val => {
        this.isFilterSelected = false;
        this.id = val['id'];
        this.dispatchApp({});
        this.store.select(selectAnalyticGetById(this.id.toString())).subscribe(resp => {
          this.relatedInsightDiscovery = [];
          this.response = cloneDeep(resp.body);
          if (this.response?.component_title) {
            this._titleService.setTitle(`${title.bayaan} | ${this.response?.component_title}`);
            (window as any)?.dataLayer?.push({
              'event': 'page_load',
              'page_title_var': this.response.component_title,
              'userId': this._msalService.convertEmail(this._msalService.getLoginData.account.username)
            });
          }
          this.disclaimerDetails = this.response?.domain_details?.policy_guide;
          this.loader = resp.loader;
          this.isRender = resp.isRender;
          this.dataSource = resp.body?.data_source;
          this.logoPath = this.themeService.defaultTheme == 'light' ? resp.body?.page_light_icon : resp.body?.page_icon;
          if (this.response?.related_insight_discoveries) {
            this.relatedInsightDiscovery = this.response.related_insight_discoveries.split(',');
          }
          this.appType = this.response?.type;
          this.indicatorsIds = this.response?.ifp_indicators;
          if (!this.isFilterSelected) {
            this.chartTitle = this.response?.component_title;
            this.setAllChartData();
          }
          this.callOverview();
          this.createPageData();
          if (!this.isFilterSelected) {
            this.visulaizationId = this.response?.default_visualisation;
          }
          if (this.visulaizationId) {
            this.getVisualizationData();
          }
          if (this.response?.security && environment.env !== 'demo') {
            this.security = this._cardService.setSecurity(this.response.security);
          }
        });
      })
    );
  }

  createPageData() {
    if (this.response?.domain) {
      this.pageData = [
        {
          title: 'Home',
          route: '/home'
        },
        {
          title: this.response.domain,
          route: `/domain-exploration/${this.response.domain}/${this.response.domain_id}`,
          queryParams: { key: 'analytical_apps', tabName: 'Analytical Apps' }
        },
        {
          title: this.response.component_title,
          route: ''
        }
      ];
    }
  }

  callOverview() {
    if (this.response?.ifp_indicators && this.response?.ifp_indicators[0] != '') {
      const indicator: any = [];
      if (this.response?.ifp_indicators?.length > 0) {
        this.response?.ifp_indicators.forEach((element: string) => {
          indicator.push(parseInt(element));
        });
      }
      this.store.dispatch(getStatisticsInsights({ id: indicator, name: classifications.innovativeStatistics }));
    }
  }

  setAllChartData() {
    this.allCharts = [];
    const title = this.response?.visualizations?.find((x: { id: any; }) => x.id == this.response.default_visualisation)?.component_title;

    this.response?.visualizations?.forEach((element: { component_title: any; id: any; }) => {
      if (element.component_title != title) {
        this.allCharts.push({ name: element.component_title });
      }
    });
    this.allCharts.unshift({ name: title });

  }

  getVisualizationData(isFilterReset: boolean = false) {
    this.visualizationData = this.response?.visualizations?.find((x: { id: number; }) => x.id == this.visulaizationId);
    this.hideChartTypes = this.visualizationData.hideChart;
    this.tableData = this.visualizationData?.tableFields;

    if (!this.isFilterSelected || isFilterReset || this.isMultiRelationFilterEnable) {
      this.visualaizationfilterPanel = cloneDeep(this.visualizationData?.filterPanel);
      
      this.cardComponent.isFilterSet = this.isMultiRelationFilterEnable;
      this.cardComponent.isFilterApplied = this.isMultiRelationFilterEnable;
    }
    this.visualaizationValues = this.visualizationData?.indicatorValues?.valuesMeta;
    this.visualaizationFilters = this.visualizationData?.indicatorFilters;

    if (this.visualizationData?.indicatorVisualizations?.visualizationsMeta?.length > 0) {
      this.chartData = this.visualizationData?.indicatorVisualizations?.visualizationsMeta.find((y: { id: any; }) => y.id == this.visualizationData?.indicatorVisualizations?.visualizationDefault);
      this.chartData.type = this.visualizationData.type;
      this.chartData.tableFields = this.visualizationData?.tableFields;
      this.chartData.domain = this.visualizationData?.domain;
      this.chartData.actualValue = this.visualizationData?.tooltipValues?.[0];

      this.hideSuffix = false;
      if (this.visualizationData?.indicatorVisualizations?.visualizationsMeta[0].hideSuffix) {
        this.hideSuffix = this.visualizationData?.indicatorVisualizations?.visualizationsMeta[0].hideSuffix;
      }
    }
  }

  dispatchApp(driver: any = {}) {
    this.store.dispatch(getIndicatorDetail({
      id: this.id.toString(),
      contentType: 'analytical-apps',
      indicatorDrivers: driver
    }));
  }



  slideClicked() {
    if (this.leftCard) {
      this.leftCard.toArray().forEach((element) => {
        element.removeTooltip();
      });
    }
  }

  resizedLeft(index: number | null, realIndex: number) {

    this.activeLeft = index;
    this.leftContainer.setActiveIndex(realIndex);
    setTimeout(() => {
      this.leftContainer.update();
      this.leftContainer.setActiveIndex(realIndex);
    }, 300);
  }

  resizedEvent(event: boolean, index: number) {
    if (event !== null || event !== undefined) {
      this.active = index;
      this._cdr.detectChanges();
    } else if (!event && this.active === index) {
      this.active = null;
    }

    if (event !== null || event !== undefined) {
      setTimeout(() => {
        this._cdr.detectChanges();
        this.leftContainer.swiperTranslateTo(event);
      }, 310);
    } else {
      this._cdr.detectChanges();
      this.leftContainer.swiperTranslateTo(event);
    }
  }

  changeChartData(event: any) {
    this.visulaizationId = this.response?.visualizations.find((x: { component_title: any; }) => x?.component_title == event.event.name).id;
    console.log('event666', event);
    console.log('visulaizationId', this.visulaizationId);
    this.getVisualizationData(true);
  }

  changeFilterData(filter: any) {
    this.isFilterSelected = true;
    let data = {};
    console.log('filter', filter);
    if (filter?.related_filters_viewName) {
      this.isMultiRelationFilterEnable = true;
      data = {
        meta: [
          {
            dbColumn: this.visualizationData.indicatorVisualizations?.visualizationsMeta[0]?.dbColumn,
            dbIndicatorId: this.visualizationData.indicatorVisualizations?.visualizationsMeta[0]?.seriesMeta[0].dbIndicatorId,
            viewName: this.visualizationData.indicatorVisualizations?.visualizationsMeta[0]?.viewName,
            filterBy: filter?.filterBy,
            related_filters_viewName: filter?.related_filters_viewName,
            related_filters_columns: filter?.related_filters_columns,
            selectedOptions: filter?.selectedOptions?.selectedOptions
          }
        ]
      };
    } else {
        this.isMultiRelationFilterEnable = false;
        data = {
        meta: [
          {
            dbColumn: this.visualizationData.indicatorVisualizations?.visualizationsMeta[0]?.dbColumn,
            dbIndicatorId: this.visualizationData.indicatorVisualizations?.visualizationsMeta[0]?.seriesMeta[0].dbIndicatorId,
            viewName: this.visualizationData.indicatorVisualizations?.visualizationsMeta[0]?.viewName,
            filterBy: filter
          }
        ]
      };
    }
    this.store.dispatch(getIndicatorFilterDetail({
      id: this.visualizationData.id.toString(),
      filter: data,
      parentId: this.id.toString()
    }));
  }

  ngOnDestroy(): void {
    this.subsink.unsubscribe();
  }
}
