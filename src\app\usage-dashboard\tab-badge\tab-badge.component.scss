@use "../../../assets/ifp-styles/abstracts/index" as *;
:host{
    position: relative;
}
.ifp-tab-badge {
  display: flex;
  margin: $spacer-0 (-$spacer-2);
  width: 100%;
  padding-bottom: $spacer-2;
  @include ifp-scroll-x(transition, $ifp-color-grey-7, 8px, 8px);
  &__items {
    white-space: nowrap;
    margin: $spacer-0 $spacer-2;
    padding: $spacer-1 $spacer-3 ;
    border-radius: 40px ;
    background-color: $ifp-color-grey-13;
    font-size: $ifp-fs-4;
    font-weight: $fw-semi-bold;
    cursor: pointer;
    &--active{
      background-color: $ifp-color-secondary-blue;
      color: $ifp-color-white;
    }
  }
  &--transparent {
    .ifp-tab-badge {
      &__items {
        border: 1px solid $ifp-color-grey-7;
        font-weight: $fw-regular;
        &:not(.ifp-tab-badge__items--active) {
          color: $ifp-color-grey-14;
          background-color: transparent;
        }
      }
    }
  }

  &--gen-ai {
    padding: $spacer-2;
      @include ifp-scroll-x(transparent, transparent, 4px, 4px);
      &::after {
        top: 0;
        right: -24px;
          border-radius:0 20px 20px 0;
                 background: linear-gradient(90deg, transparent -40%, $ifp-color-gen-ai-shadow 71%, transparent 100%);

      }
      &::before {
        top: 0;
        left: -24px;
          border-radius:20px 0 0 20px ;
   background: linear-gradient(270deg, transparent -40%, $ifp-color-gen-ai-shadow 71%, transparent 100%);
      }
      &::before , &::after{
        content: "";
        width: 80px;
        height: 100%;
        position: absolute;

      }
    .ifp-tab-badge {

      &__items {

        background-color: transparent;
        color: $ifp-color-grey-14;
        border:1px solid $ifp-color-grey-13;
        font-weight: $fw-regular;
        box-shadow: 0px 12px 42px -4 rgba($color: #101828, $alpha: 0.02);
        &:hover {
            border:1px solid $ifp-color-ai-blue;
            color:  $ifp-color-ai-blue;;
        }
          &--active{
            background-color:  rgba($color: $ifp-color-ai-blue, $alpha: 0.02)  ;
            border:1px solid $ifp-color-ai-blue;
            color: $ifp-color-ai-blue;
        }
      }
    }
  }
  &--at-right-end {
  &::after {
        content: unset;
      }
  }
  &--at-left-end {
    &::before {
      content: unset;
    }
  }
}
:host-context(.ifp-dark-theme) {
  .ifp-tab-badge {
    &__items {
      background-color: $ifp-color-grey-7;
      &--active{
        background-color: $ifp-color-secondary-blue;
        color: $ifp-color-white;
      }
    }
  }
}
