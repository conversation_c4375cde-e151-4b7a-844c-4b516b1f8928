@use "../../../assets/ifp-styles/abstracts/index" as *;
.ifp-tab-badge {
  display: flex;
  margin: $spacer-0 (-$spacer-2);
  width: 100%;
  padding-bottom: $spacer-2;
  @include ifp-scroll-x(transition, $ifp-color-grey-7, 8px, 8px);
  &__items {
    white-space: nowrap;
    margin: $spacer-0 $spacer-2;
    padding: $spacer-1 $spacer-3 ;
    border-radius: 40px ;
    background-color: $ifp-color-grey-13;
    font-size: $ifp-fs-4;
    font-weight: $fw-semi-bold;
    cursor: pointer;
    &--active{
      background-color: $ifp-color-secondary-blue;
      color: $ifp-color-white;
    }
  }
  &--transparent {
    .ifp-tab-badge {
      &__items {
        border: 1px solid $ifp-color-grey-7;
        font-weight: $fw-regular;
        &:not(.ifp-tab-badge__items--active) {
          color: $ifp-color-grey-14;
          background-color: transparent;
        }
      }
    }
  }
}
:host-context(.ifp-dark-theme) {
  .ifp-tab-badge {
    &__items {
      background-color: $ifp-color-grey-7;
      &--active{
        background-color: $ifp-color-secondary-blue;
        color: $ifp-color-white;
      }
    }
  }
}
