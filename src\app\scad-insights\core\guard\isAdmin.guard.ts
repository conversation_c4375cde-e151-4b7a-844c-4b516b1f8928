import { Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';
import { AdminService } from '../services/sla/admin.service';
import { catchError, of, tap } from 'rxjs';
import { controlPanelAccess, dgStatus, role } from '../../user-onboarding/control-panel/ifp-access-control/ifp-access-control.constants';
@Injectable({
  providedIn: 'root'
})
export class IsAdminService {
  public apiResponse = signal(false);

  constructor(private readonly _router: Router, private readonly _adminService: AdminService) {
  }

  resolve() {
    if (this.apiResponse()) {
      return of({ status: true });
    }
    return this._adminService.getUserRole().pipe(tap({
      next: (res: Role) => {
        this.apiResponse.set(true);
        let secondaryRole: string[] = [];
        this._adminService.userRole = res.role ?? role.normalUser;
        this._adminService.dgStatus.set(res?.dg_status ?? dgStatus.invitePending);
        this._adminService.isDgRequired.set(res?.dg_required);
        this._adminService.userDesignation = res?.designation;
        this._adminService.generalizedRole.set(res?.generalizedRole ?? res.role);
        if (res?.secondary_role.length) {
          secondaryRole = typeof res.secondary_role === 'string' ? [res.secondary_role] : res.secondary_role;
        }
        this._adminService.secondaryRole.set(secondaryRole);
        if (res.entity) {
          this._adminService.userEntity = res.entity;
        }
        this._adminService.hasControlpanelAccess = controlPanelAccess.includes(res.role);
      },
      error: () => {
        this.apiResponse.set(true);
        this._adminService.userRole = role.normalUser;
      }
    }),
      catchError(() => {
        this.apiResponse.set(true);
        return of({ status: true });
      })
    );
  }


  canActivate(): boolean {
    if (this._adminService?.userRole !== role.normalUser) {
      return true;
    }
    this._router.navigate(['/404']);
    return false;
  }


}


interface Role {
  role: string;
  designation: string;
  entity: Entity;
  dg_required: boolean;
  dg_status?: string;
  generalizedRole: string;
  secondary_role: string;
}

interface Entity {
  id: string;
  name: string;
}
