import { animate, style, transition, trigger } from '@angular/animations';
import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { buttonClass, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { IfpButtonComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';

@Component({
  selector: 'ifp-tools-page-landing',
  templateUrl: './ifp-tools-page.component.html',
  styleUrls: ['./ifp-tools-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [TranslateModule, IfpButtonComponent, AsyncPipe],
  animations: [trigger('fadeIn', [
    transition(':enter', [
      style({ opacity: 0 }),
      animate('400ms', style({
        opacity: 1
      }))
    ])
  ])]
})

export class IfpToolsPageComponent {
  @Input() heading: string = 'Bayaan Store';
  @Input() description: string = 'The Bayaan Store feature permits users to access their saved indicators within my bookmarks and facilitates the creation of personalized dashboards.';
  @Input() cards: CardsList[] = [];

  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public currentIndex: number = 0;
  public timeInterval!: NodeJS.Timer;
  public descIndex: number = 0;

  constructor(private _cdr: ChangeDetectorRef, private _router: Router, public themeService: ThemeService) { }


  route(card: CardsList) {
    if (card.external) {
      window.open(card.url);
      return;
    }
    this._router.navigateByUrl(card.url);
  }

  openUrl(event: MouseEvent, card: CardsList) {
    if (card.external) {
      window.open(card.secondUrl);
      return;
    }
    event.stopPropagation();
    this._router.navigate([card.secondUrl]);
  }

}

export interface CardsList {
  title: string;
  description: string;
  img: string;
  url: string;
  buttonLabel: string;
  buttonSecondLabel: string;
  isSecondButton?: boolean;
  secondUrl: string;
  visible?: boolean;
  beta?: boolean;
  external?: boolean;
}
