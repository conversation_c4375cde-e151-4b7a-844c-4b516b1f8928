<ifp-bg-page>
  @if (loader()) {
  <div class=" ifp-page-bg__content  ifp-dxp-admin__loader">
    <app-ifp-spinner></app-ifp-spinner>
  </div>
  } @else {

  <div class="ifp-page-bg__content ifp-dxp-admin" [ngClass]="{'ifp-dxp-admin--collapse':( hideSideBar() )|| (generalizedRoles.explorer === role() )}">
    <div class="ifp-container">
      <app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs>
      <div class="ifp-node ifp-node--dxp-admin">
        <div class="ifp-node__card-left">
          <div class="ifp-dxp-admin__header-wrapper" >
               <em [class]="'ifp-dxp-admin__icon ifp-icon ' + data()?.icon"></em>
          <h1 class="ifp-node__tiltle">{{data()?.title}}</h1>
          </div>
          <p class="ifp-node__subtitle">{{data()?.subTitle}}</p>
          @if(data()?.legendPanel?.length) {
          <div class="ifp-node__filters">
            <app-ifp-dropdown (dropDownItemClicked)="legendChanges($event)" [formDisable]="true" [key]="'column'"
              [searchEnable]="true" [isMulti]="false" [showTitle]="true" class="ifp-node__filter-item"
              [title]="'Legends'" [dropDownItems]="data()?.legendPanel ?? []" [placeHolder]="'Select'"
              [selectedValue]="defaultLegend()"></app-ifp-dropdown>
            <app-ifp-dropdown [formDisable]="true" [searchEnable]="true" [isMulti]="true" [showTitle]="true"
              class="ifp-node__filter-item" [title]="defaultLegend()?.column ?? ''" [minLimit]="1" [placeHolder]="'Select'"
              [dropDownItems]="defaultLegend()?.options ?? []" [selectedValues]="defaultLegend()?.selected_values ?? []"
              (dropDownItemMultiClicked)="changeSubFilter($event)" [limit]="10"></app-ifp-dropdown>
          </div>
          }

          <div class="ifp-node__filters">
            @for (filter of data()?.filterPanel; track i; let i = $index) {

            <app-ifp-dropdown [formDisable]="true" [searchEnable]="true" [isMulti]="true" [showTitle]="true"
              class="ifp-node__filter-item" [title]="filter.label" [dropDownItems]="filter.options"
              [selectedValues]="filter.defaultArray ? filter.defaultArray: []" [placeHolder]="'Select'"
               [enableSingleValueSelection]="false"
              (dropDownItemMultiClicked)="changeFilter($event,filter)"></app-ifp-dropdown>
            }
            @if(currentChartType() === chartConst.pie &&  data()?.series?.series && data()?.series?.series?.length !== 1) {
            <app-ifp-dropdown [formDisable]="true" [searchEnable]="true" [isMulti]="false" [showTitle]="true"
              class="ifp-node__filter-item" [title]="'Series'" [dropDownItems]="data()?.series?.xAxis?.categories ?? []"
              [selectedValue]="data()?.series?.xAxis?.categories?.[0]" [placeHolder]="'Select'"
              (dropDownItemClickedWithIndex)="seriesClick($event)"></app-ifp-dropdown>
            }

          </div>

          @if(xAxis() && data()?.series?.xAxis?.categories?.length && data()?.series?.series?.length ) {
          @if(chartLoader()) {
          <app-ifp-card-loader class="ifp-dxp-admin__loader-chart" [type]="'chart'"></app-ifp-card-loader>
          } @else {


          <ifp-highcharts [yAxisStyle]=" {
                    fontSize: '1.5rem',
                    fontWeight: '600',
                  }" [legends]="legends" [comparisonEnable]="false" [xAxis]="xAxis()" class="ifp-dxp-admin__chart"
            [chartType]="currentChartType()" [height]="400" [category]="
            data()?.series?.xAxis?.categories ?? []" [donutCenterFunc]="centerValue()"
            [yaxisLabel]="data()?.visualizationConfig?.chart_configuration?.y_axis?.label  + (data()?.visualizationConfig?.chart_configuration?.unit ? ' (' + this.data()?.visualizationConfig?.chart_configuration?.unit+ ')':'')"
            [data]="(currentChartType() === chartConst.pie ? [seriesDonutChart()]:data()?.series?.series) ?? []"
            [innerSize]="data()?.visualizationConfig?.chart_configuration?.default_chart_type === chartConst.pie? '0%': '80%'"
          [chartName]="chartTypeConfigValue()" [plotOptions]="plotOptions()"></ifp-highcharts>



          }

          } @else {
          <app-ifp-no-data [isTransparent]="true"></app-ifp-no-data>
          }

          <div class="ifp-node__txt-icon">
            <ifp-icon-text class="ifp-node__source" [icon]="'ifp-icon-text'"  [text]="data()?.product?.displayName ?? ''"
              [key]="'Product'"></ifp-icon-text>
            <ifp-icon-text class="ifp-node__source" [icon]="'ifp-icon-text'" [text]="data()?.asset?.displayName ?? ''"
              [key]="'Asset'"></ifp-icon-text>
          </div>

          <div class="ifp-dxp-admin__access-wrapper">
            <div class="ifp-dxp-admin__user-detail">
              <p class="ifp-dxp-admin__user-detail-txt"><em class="ifp-icon ifp-icon-user-normal"></em></p>
              <ifp-user-tag [name]="data()?.createdBy?.name ?? ''"
                class="ifp-dxp-admin__user-detail-tag"></ifp-user-tag>
            </div>

            @if (data()?.userAccess && data()?.userAccess?.length) {
            <div class="ifp-dxp-admin__user-detail">
              <p class="ifp-dxp-admin__user-detail-txt"><em class="ifp-icon ifp-icon-multiple-user"></em>{{'Shared with' | translate}}: </p>
              <ifp-user-tag-group [moreText]="'Others'" [tagList]="data()?.userAccess ?? []" [limit]="2"
                class="ifp-dxp-admin__user-group"></ifp-user-tag-group>
            </div>
          }
          </div>

          @if(viewBar()) {
          <div class="ifp-dxp-admin__footer">
            @if( data()?.approvalRequest?.approvalActions?.revert && generalizedRoles.builder ===
            role()) {
            <div class="ifp-dxp-admin__footer-toggle">
              {{'Take an action on approver feedback' | translate}}
            </div>
            }
            @if( data()?.approvalRequest?.approvalActions?.claim) {
            <div class="ifp-dxp-admin__footer-toggle">
              {{'Click to assign or unassign this KPI for review' | translate}} <app-ifp-toggle-button
                (toggleChange)="enableReview.set($event);currentStatusUpdate(enableReview()? approveConst.claim: approveConst.unclaim);"
                [enable]="enableReview()" class="ifp-dxp-admin__footer-toggle-btn"></app-ifp-toggle-button>
            </div>
            }

            <div class="ifp-dxp-admin__footer-btn">
              @if( data()?.approvalRequest?.approvalActions?.revert) {
              <ifp-button [label]="'Return'" [iconClass]="'ifp-icon-refresh'"
                [buttonClass]="buttonClass.secondary +' '+(enableReview() ? '' : buttonClass.disabledSecondary)"
                class="ifp-dxp-admin__btn"
                (ifpClick)="currentStatus.set(approveConst.revert);createUpdatePop(approveConst.revert);"></ifp-button>
              }
              @if( data()?.approvalRequest?.approvalActions?.reject) {
              <ifp-button [label]="'Reject'" [iconClass]="'ifp-icon-round-cross'"
                [buttonClass]="buttonClass.secondary+' '+(enableReview() ? '' : buttonClass.disabledSecondary)"
                class="ifp-dxp-admin__btn"
                (ifpClick)="currentStatus.set(approveConst.reject);createUpdatePop(approveConst.reject);"></ifp-button>
              }
              @if( data()?.approvalRequest?.approvalActions?.approve) {
              <ifp-button [label]="'Approve'" [iconClass]="'ifp-icon-tick-round'"
                [buttonClass]="buttonClass.primary+' '+(enableReview() ? '' : buttonClass.disabled)"
                class="ifp-dxp-admin__btn" (ifpClick)="callApproveApi(approveConst.approve);"></ifp-button>
              }
              @if( data()?.approvalRequest?.approvalActions?.revert && generalizedRoles.builder === role()) {
              <ifp-button [label]="'Edit'" [iconClass]="'ifp-icon-edit'" [buttonClass]="buttonClass.primary "
                class="ifp-dxp-admin__btn" (ifpClick)="goToEditPage()"></ifp-button>
              }
            </div>


          </div>
          }

        </div>
        @if(enableChat() ) {
        <div class="ifp-node__card-right">
          @if ( generalizedRoles.explorer !== role() ) {
          <div class="ifp-chart-toolbar__action-box ifp-dxp-admin__tool-bar-header">

            <span class="ifp-dxp-admin__tool-bar-opener" (click)="hideSideBar.set(!hideSideBar())"> <em
                class="ifp-icon ifp-icon-chat-empty ifp-dxp-admin__tool-bar-opener-icon"></em></span>


            <span class="ifp-dxp-admin__revert-inline">{{'Comments' | translate}}</span>
          </div>
          @if (comments().length) {
          <div class="ifp-chart-toolbar__action-box ifp-dxp-admin__comment-wrapper"
            [ngClass]="{'ifp-dxp-admin__comment-wrapper--approved': data()?.approvalRequest?.status !== approveStatus.pending && data()?.approvalRequest?.status !== approveStatus.revert}">
            @for (comment of comments(); track comment; let index = $index; let last = $last) {
            <div class="ifp-dxp-admin__comment-item"
              [ngClass]="{'ifp-dxp-admin__comment-item--self':comment?.userDetails?.email == currentUserEmail}">
              @if(comment.commentType === 'system') {
              <div class="ifp-dxp-admin__comment-name">
                <p class="ifp-dxp-admin__comment-desc ifp-dxp-admin__comment-desc--system">
                  <ifp-mark-down [data]="comment.content"></ifp-mark-down>
                </p>
              </div>

              } @else {
              <div class="ifp-dxp-admin__comment-name">
            <ifp-abbreviation-tag [text]="comment?.userDetails?.name?? ''"
                  class="ifp-dxp-admin__name-tag"></ifp-abbreviation-tag>
                <p class="ifp-dxp-admin__comment-desc">{{comment?.userDetails?.name }}</p>
              </div>
              <p class="ifp-dxp-admin__comment">{{comment.content}}</p>
              <p class="ifp-dxp-admin__comment-time">{{comment.createdAt | date: dateFormat.dateAndTime}}</p>
              }

            </div>
            }
          </div>
          }
          @if( data()?.approvalRequest?.status === approveStatus.pending || data()?.approvalRequest?.status ===
          approveStatus.revert) {
          <div class="ifp-chart-toolbar__action-box ifp-dxp-admin__comment-edit">

            <p class="ifp-dxp-admin__textarea-label">{{'Add a Comment' | translate}}</p>
            <textarea [placeholder]="'Type your comment here' | translate" appIfpInputAutoResize
              class="ifp-input-textarea ifp-dxp-admin__textarea" [(ngModel)]="newComment"></textarea>
            <div class="ifp-dxp-admin__btn-sec">
              <ifp-button [label]="'Cancel'"
                [buttonClass]="newComment.trim() === '' ? buttonClass.disabled : buttonClass.secondary"
                class="ifp-dxp-admin__btn" (ifpClick)="newComment = '';hideSideBar.set(true)"></ifp-button>
              <ifp-button [label]="'Submit'"
                [buttonClass]="newComment.trim() === '' ? buttonClass.disabled : buttonClass.primary"
                class="ifp-dxp-admin__btn" (ifpClick)="addComment()"></ifp-button>
            </div>

          </div>
          }
          }
        </div>
      }
      </div>
    </div>
  </div>
  }

</ifp-bg-page>
<app-ifp-modal #modalUpdate [overlayType]="'transparent'" [modalClass]="'ifp-modal__small-template'">
  <ifp-dxp-validation-pop-up (close)="closeModal()" [textEnable]="enableCommentBox()" [desc]="updateDec()"
    [icon]="updateIcon()" [secondaryBtnLabel]="'Cancel'" [heading]="updateTitle()"
    [enableDoubleButton]="enableCommentBox()" (secondaryBtn)="closeModal()" [successPopup]="!enableCommentBox()"
    (primaryBtn)="enableCommentBox() ? currentStatusUpdate(currentStatus(),$event) : popupBtnClick()"
    [primaryBtnLabel]="enableCommentBox() ?  'Submit': 'View Published KPIs'"></ifp-dxp-validation-pop-up>
</app-ifp-modal>
