import { generalizedRoles } from './../../../../dxp/dxp.constants';
import { dgStatus, role, roleList } from './../../../user-onboarding/control-panel/ifp-access-control/ifp-access-control.constants';
import { Injectable, signal, WritableSignal } from '@angular/core';
import { commonApi } from '../../apiConstants/common-api.constants';
import { HttpService } from '../http/http.service';

@Injectable({
  providedIn: 'root'
})
export class AdminService {

  constructor(private _http: HttpService) {}

  // public isPEUser: boolean = false;
  // public isSuperUser: boolean = false;

  public userRole: string = role.normalUser;
  public userEntity!: Enitity;
  public hasControlpanelAccess: boolean = false;
  public isDgRequired: WritableSignal<boolean> = signal(false);
  public dgStatus: WritableSignal<string> = signal(dgStatus.invitePending);
  public userDesignation: string = roleList[role.normalUser];
  public generalizedRole: WritableSignal<string> = signal(generalizedRoles.builder);
  public secondaryRole: WritableSignal<string[]> = signal([generalizedRoles.builder]);


  getUserRole() {
    return this._http.get(commonApi.role);
  }

}

export interface Role {
  role: string;
}

export interface Enitity {
  id: string;
  name: string;
  domains?: string[];
}
