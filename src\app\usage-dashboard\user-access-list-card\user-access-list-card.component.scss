@use "../../../assets/ifp-styles/abstracts/index" as *;

.ifp-db-users {
  display: flex;
  &__chart-sec,
  &__table-sec {
    width: 50%;
  }
  &__chart-sec {
    padding-inline-end: $spacer-5 ;
  }
  &__table-sec {
    display: flex;
    flex-direction: column;
    padding: $spacer-4;
    border-radius: 20px;
    background-color: $ifp-color-grey-18;
  }
  &__name {
    margin-inline-end: $spacer-3;
  }
  &__head-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: $spacer-3;
  }
  &__table {
    display: block;
    margin-bottom: $spacer-4;
  }
  &__loader {
    &--chart {
      min-height: 450px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

:host::ng-deep {
  .ifp-db-users {
    &__table {
      .ifp-data-table__no-data {
        width: 100%;
      }
    }
  }
}

:host::ng-deep {
  .ifp-db-users {
    &__table {
      .ifp-data-table__no-data {
        width: 100%;
      }
    }
  }
}
