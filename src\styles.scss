@use "sass:meta";
@use './assets/ifp-styles/abstracts/index' as *;
@use './assets/ifp-styles/base/index' as *;


/* Importing Bootstrap SCSS file. */
// @import 'bootstrap/scss/bootstrap';

@import "https://js.arcgis.com/4.26/@arcgis/core/assets/esri/themes/light/main.css";
@import "node_modules/codemirror/lib/codemirror.css";
// imports
@include meta.load-css('assets/ifp-styles/base/ifp-icon-style-v1');
@include meta.load-css('assets/ifp-styles/base/direction');
@include meta.load-css('assets/ifp-styles/components');

.zoom-tool-div {
  border-radius: 2px;
  position: absolute;
}
//this comment is added to replicate the test branch changes into production
.new-tagline {
  font-size:1.4rem;
}
.new-tagline > * {
  font-size:1.2rem;
}

.zoom-tool-div-alone .esri-widget--button,
.zoom-tool-div .esri-widget--button {
  border-radius: inherit;
}

.zoom-tool-div-alone .esri-widget--button:first-child,
.zoom-tool-div .esri-widget--button:first-child {
  border-bottom: 1px solid $ifp-color-black;
}

.geom-tool-div {
  position: absolute;
  display: inline-flex;
}

.geom-tool-div .esri-widget--button {
  border-radius: 0;
}

.geom-tool-div .esri-widget--button:not(:first-child) {
  border-left: 1px solid $ifp-color-black;
}

//
//
///* Importing Bootstrap SCSS file. */
//
//.calcite-icon {
//  //font-size: 32px; /* Adjust the width to your desired size */
//}
//calcite-icon {
//  padding: 2px !important;
//}
//.esri-widget--button p {
//  font-size: 18px;
//}
#esri-full-screen-btn {
  background-color: $ifp-color-secondary-blue !important;
}

.esri-widget--button {
  background: rgba(255, 255, 255, 0.6)!important;
  -webkit-backdrop-filter: blur(8px) saturate(180%)!important;
  backdrop-filter: blur(8px) saturate(180%)!important;
  border: 1px solid rgba(255, 255, 255, 0.2)!important;
  color: #333 !important;
  border-radius: 8px !important;
  width: 28px !important;
  height: 28px !important;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transition: all 0.2s ease !important;

  &:hover {
    background: rgba(255, 255, 255, 0.25)!important;
    border: 1px solid rgba(255, 255, 255, 0.3)!important;
    transform: translateY(-1px) scale(1.02) !important;
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  }

  &:active {
    background: rgba(255, 255, 255, 0.3)!important;
    border: 1px solid rgba(255, 255, 255, 0.4)!important;
    transform: translateY(0) scale(1) !important;
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.1),
      inset 0 2px 4px rgba(255, 255, 255, 0.2) !important;
  }

  &:focus {
    outline: none !important;
    border: 1px solid rgba(74, 144, 226, 0.6)!important;
    box-shadow:
      0 0 0 2px rgba(74, 144, 226, 0.2),
      0 4px 16px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  }

  // Support for older browsers without backdrop-filter
  @supports not (backdrop-filter: blur(8px)) {
    background: rgba(255, 255, 255, 0.3)!important;

    &:hover {
      background: rgba(255, 255, 255, 0.4)!important;
    }

    &:active {
      background: rgba(255, 255, 255, 0.5)!important;
    }
  }
}
.esri-widget--button[title="Zoom in"] {
  border-radius: 5px 5px 0px 0px !important;
}
.esri-widget--button[title="Zoom out"] {
  border-radius: 0px 0px 5px 5px !important;
}
.esri-widget--button[title="تكبير"] {
  border-radius: 5px 5px 0px 0px !important;
}
.esri-widget--button[title="تصغير"] {
  border-radius: 0px 0px 5px 5px !important;
}


.esri-ui-corner .esri-component {
  position: relative;
  background: transparent !important;
  box-shadow: none !important;
  z-index: 2;
}

// .esri-expand--auto .esri-expand__content {
//   position: absolute;
//   top: -25px;
//   left: 10px;
// }

.esri-component.level-selection {
  width: 160px;
  position: absolute;
  background: rgba(255, 255, 255, 0.6)!important;
  -webkit-backdrop-filter: blur(8px) saturate(180%)!important;
  backdrop-filter: blur(8px) saturate(180%)!important;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  color: #333!important;
  cursor: pointer;
  border-radius: 8px;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;

  &:hover {
    // background: rgba(255, 255, 255, 0.25)!important;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  &:active {
    background: rgba(255, 255, 255, 0.3)!important;
    border: 1px solid rgba(255, 255, 255, 0.4);
    transform: translateY(0);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.1),
      inset 0 2px 4px rgba(255, 255, 255, 0.2);
  }

  &:focus {
    outline: none;
    border: 1px solid rgba(74, 144, 226, 0.6);
    box-shadow:
      0 0 0 2px rgba(74, 144, 226, 0.2),
      0 4px 16px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  // Support for older browsers without backdrop-filter
  @supports not (backdrop-filter: blur(8px)) {
    background: rgba(255, 255, 255, 0.3)!important;

    &:hover {
      background: rgba(255, 255, 255, 0.4)!important;
    }

    &:active {
      background: rgba(255, 255, 255, 0.5)!important;
    }
  }
}

.esri-component.map-type {
  width: 160px;
  min-width: fit-content;
  position: absolute;
  background: rgba(255, 255, 255, 0.6)!important;
  -webkit-backdrop-filter: blur(8px) saturate(180%)!important;
  backdrop-filter: blur(8px) saturate(180%)!important;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  color: #333!important;
  cursor: pointer;
  border-radius: 8px;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;

  &:hover {
    border: 1px solid rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  &:active {
    background: rgba(255, 255, 255, 0.3)!important;
    border: 1px solid rgba(255, 255, 255, 0.4);
    transform: translateY(0);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.1),
      inset 0 2px 4px rgba(255, 255, 255, 0.2);
  }

  &:focus {
    outline: none;
    border: 1px solid rgba(74, 144, 226, 0.6);
    box-shadow:
      0 0 0 2px rgba(74, 144, 226, 0.2),
      0 4px 16px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  // Support for older browsers without backdrop-filter
  @supports not (backdrop-filter: blur(8px)) {
    background: rgba(255, 255, 255, 0.3)!important;

    &:hover {
      background: rgba(255, 255, 255, 0.4)!important;
    }

    &:active {
      background: rgba(255, 255, 255, 0.5)!important;
    }
  }
}

.esri-ui-corner .esri-component.esri-zoom {
  position: absolute;
  background: transparent !important;
  box-shadow: none !important;
}

.esri-component.download-btn {
  position: absolute;
  background: rgba(255, 255, 255, 0.6)!important;
  -webkit-backdrop-filter: blur(8px) saturate(180%)!important;
  backdrop-filter: blur(8px) saturate(180%)!important;
  border: 1px solid rgba(255, 255, 255, 0.5);
  padding: 4px 8px;
  color: #333;
  cursor: pointer;
  border-radius: 8px;
  z-index: 1;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.5)!important;
    border: 1px solid rgba(255, 255, 255, 0.6);
    transform: translateY(-1px) scale(1.02);
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.5);
  }

  &:active {
    background: rgba(255, 255, 255, 0.6)!important;
    border: 1px solid rgba(255, 255, 255, 0.7);
    transform: translateY(0) scale(1);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.1),
      inset 0 2px 4px rgba(255, 255, 255, 0.3);
  }

  &:focus {
    outline: none;
    border: 1px solid rgba(74, 144, 226, 0.6);
    box-shadow:
      0 0 0 2px rgba(74, 144, 226, 0.2),
      0 4px 16px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }

  // Support for older browsers without backdrop-filter
  @supports not (backdrop-filter: blur(8px)) {
    background: rgba(255, 255, 255, 0.7)!important;

    &:hover {
      background: rgba(255, 255, 255, 0.75)!important;
    }

    &:active {
      background: rgba(255, 255, 255, 0.8)!important;
    }
  }
}


.esri-component.map-location{
  position: absolute;
  font-size: 1.4rem;
  color: #000000e0;
  z-index: 3;
  display: inline-block;
  max-width: 90vw;
  //align-items: center;
}

.esri-component.map-location span {
  padding: 0px 10px;
  font-weight:bolder;
  font-style: normal;
  position: relative;

  &::after {
    content: '+';
    position: absolute;
    right: -5px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 14px;
    font-weight: bold;
    color: inherit;
  }

  &:last-child::after {
    content: '';
  }
}

.esri-component.map-location-right span {
  &::after {
    left: -5px;
    right: auto;
  }

}



// Map Widgets Position
.esri-ui-corner .esri-component {left: 77rem; bottom: 4.8rem;}
.esri-component.level-selection {left: 43.5rem; bottom: 6.7rem;}
.esri-component.map-type {left: 61rem; bottom: 6.7rem;}
.esri-component.download-btn{left: 82.8rem;bottom: 6.5rem;}
.esri-ui-corner .esri-component.esri-zoom {left: 38rem;bottom: 5.5rem;}
.esri-component.map-location{top: 25rem;left: 20.5%;}
.esri-component.map-location-right{right: 20.5%; left: 0;}

@media screen and (max-width: 1600px) { 
  .esri-component.map-location{top: 22rem!important; left: 7%!important;}
  .esri-component.map-location-right{top: 22rem!important;right: 7%!important;}
  .esri-ui-corner .esri-component.esri-zoom {left: 41rem!important;bottom: 0rem!important;}
  .esri-ui-corner .esri-component {left: 82rem!important; bottom: -0.8rem!important;}
  .esri-component.level-selection {left: 46.5rem!important; bottom: 1rem!important;}
  .esri-component.map-type {left: 63rem!important; bottom: 1rem!important;}
  .esri-component.download-btn{left: 79.5rem!important;bottom: 1rem!important;}
}

@media screen and (min-width: 1921px) {
  .esri-component.map-location{left: 18.5%;}
  .esri-component.map-location-right{right: 18.5%;}
}

// @media screen and (max-width: 1400px) {
//   .esri-ui-corner .esri-component.esri-zoom {left: 7rem!important;bottom: 2.5rem!important;}
//   .esri-ui-corner .esri-component {left: 47.5rem!important; bottom: -0.8rem!important;}
//   .esri-component.level-selection {left: 8.5rem!important; bottom: 1rem!important;}
//   .esri-component.map-type {left: 25rem!important; bottom: 1rem!important;}
//   .esri-component.download-btn{left: 44rem!important;bottom: 1rem!important;}
// }

// @media only screen and (min-width: 1800px) {
//   // .esri-ui-corner .esri-component {left: 54rem;}
//   .esri-ui-corner .esri-component {left: 70rem;}

//   .esri-component.level-selection {left: 37rem;}
//    .esri-component.download-btn{left: 76.8rem}

//   .esri-ui-corner .esri-component.esri-zoom {left: 35.5rem}

//   .esri-component.map-location{top: 25rem;left: 20%;}
//   .esri-component.map-location-right{right: 20%; left: 0;}
// }

// @media screen and (max-width: 1250px) {
//   .esri-component.map-location{left: 26%;}
//   .esri-component.map-location-right{right: 26%;}
// }

.geom-tool-div>.esri-widget--button,
.zoom-tool-div>.esri-widget--button,
.zoom-tool-div-alone>.esri-widget--button,
.tool-div>.esri-widget--button {
  border-radius: 0 !important;
}

.zoom-tool-div-alone>.esri-widget--button:first-child,
.zoom-tool-div>.esri-widget--button:first-child {
  border-radius: 5px 5px 0 0 !important;
}

.zoom-tool-div-alone>.esri-widget--button:last-child,
.zoom-tool-div>.esri-widget--button:last-child {
  border-radius: 0 0 5px 5px !important;
}

.geom-tool-div>.esri-widget--button:first-child {
  border-radius: 5px 0 0 5px !important;
}

.geom-tool-div>.esri-widget--button:last-child {
  border-radius: 0 5px 5px 0 !important;
}

.tool-div>.esri-widget--button:first-child {
  border-radius: 5px 5px 0 0 !important;
}

.tool-div>.esri-widget--button:last-child {
  border-radius: 0 0 5px 5px !important;
}

.tool-div>.esri-widget--button {
  background: $ifp-color-white !important;
  color: $ifp-color-black !important;
}

#tool-div-measurement-line {
  background-color: $ifp-color-secondary-blue !important;
  color: $ifp-color-white !important;
}

#tool-div-measurement-polygon {
  background-color: $ifp-color-secondary-blue !important;
  color: $ifp-color-white !important;
}

#tool-div-measurement-delete {
  background-color: $ifp-color-secondary-blue !important;
  color: $ifp-color-white !important;
}

#standalone-share {
  background-color: $ifp-color-secondary-blue !important;
  color: $ifp-color-white !important;
}

#standalone-meta {
  background-color: $ifp-color-secondary-blue !important;
  color: $ifp-color-white !important;
}

#standalone-save {
  background-color: $ifp-color-secondary-blue !important;
  color: $ifp-color-white !important;
}

#standalone-maximize {
  background-color: $ifp-color-secondary-blue !important;
  color: $ifp-color-white !important;
}

.measure-tool-div-icon::before {
  font-family: "BootstrapIcons";
  /* Specify the font-family for Bootstrap icons */
  content: "\F4FD";
  /* Replace '\F56D' with the Unicode representation of the desired Bootstrap icon */
}

.modal-dialog {
  max-width: 95% !important;
  height: 90% !important;
}

.modal-content {
  height: 100% !important;
}

.btn-outline-primary {
  --bs-btn-color: $ifp-color-secondary-blue;
  --bs-btn-border-color: $ifp-color-secondary-blue;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: $ifp-color-secondary-blue;
  --bs-btn-hover-border-color: $ifp-color-secondary-blue;
  --bs-btn-focus-shadow-rgb: 13, 110, 253;
  --bs-btn-active-color: #fff;
  --ifp-color-secondary-blue: $ifp-color-secondary-blue;
  --bs-btn-active-border-color: $ifp-color-secondary-blue;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: $ifp-color-secondary-blue;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: $ifp-color-secondary-blue;
  --bs-gradient: none;
}

.selection-tool-div {
  background: #FFFFFF;
  width: 160%;
  padding: 5px;
  line-height: 2.5rem;
}

.selection-tool-div a {
  text-decoration: underline;
  color: blue;
}

.selectDistrict {
  padding: 5px;
  max-width: 150px;
  text-overflow: ellipsis;
  overflow: hidden !important;
  white-space: nowrap;
}

[data-title]:hover:after {
  opacity: 1;
  transition: all 0.1s ease 0.5s;
  visibility: visible;
}

[data-title]:after {
  content: attr(data-title);
  background-color: #00FF00;
  color: #111;
  font-size: 150%;
  position: absolute;
  padding: 1px 5px 2px 5px;
  bottom: -1.6em;
  left: 100%;
  white-space: nowrap;
  box-shadow: 1px 1px 3px #222222;
  opacity: 0;
  border: 1px solid #111111;
  z-index: 99999;
  visibility: hidden;
}

[data-title] {
  position: relative;
}

.next-button {
  background-color: $ifp-color-secondary-blue;
}

.shepherd-title {
  font-size: 1.8rem;
}

.shepherd-text {
  font-size: 1.4rem;
}

#customLayersContainer {
  background: $ifp-color-white;
  color: $ifp-color-black !important;
  width: 150px;
  padding: 10px;
  line-height: 2.5rem;
  //for elayzia map poi's
  max-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
}
#customLayersContainer>.layer-item {
  display: flex;
}
#customLayersContainer input[type="radio"] {
  margin-right: 10px;
}

#customLayersContainer>.checkbox-container {
  display: flex;
  align-items: center;
}

#customLayersContainer>.checkbox-container>label {
  margin-left: 5px;
}

// search widget
.map-district-search-select{
  background: #ffffff99;
  color: $ifp-color-black !important;
  padding: 10px;
  border: unset;
  position: absolute;
  overflow-y: auto!important;
  overflow-x: hidden!important;
  top: -70px;
  left: -20px;
}


// *******POI
#customLayersContainerPoi {
  background: #ffffff99;
  color: $ifp-color-black !important;
  width: 250px;
  padding: 10px;
  line-height: 2.5rem;
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  border-radius: 5px;
  margin-top: -240px;
}

#customLayersContainerBaseMap_controls_content{
  position: absolute;
  top: -175px;
}

.esri-ui-bottom-left .esri-expand__content, .esri-ui-bottom-right .esri-expand__content{
  bottom: auto!important;
}

.esri-basemap-gallery{
  background: #ffffff99;
  border-radius: 5px;
}

/* For WebKit browsers (Chrome, Safari) */
#customLayersContainerPoi::-webkit-scrollbar {
  width: 0px;
}

#customLayersContainerPoi::-webkit-scrollbar-track {
  background: transparent;
}


#customLayersContainerPoi>.layer-item {
  display: flex;
}
#customLayersContainerPoi input[type="radio"] {
  margin-right: 10px;
}

#customLayersContainerPoi>.checkbox-container {
  display: flex;
  align-items: center;
}

#customLayersContainerPoi>.checkbox-container>label {
  margin-left: 5px;
}


.zoom-tool-div-alone {
  /*border: 1px solid $ifp-color-black;*/
  border-radius: 2px;
  position: absolute;
  //bottom: 40px;
}

.text-danger {
  color: $ifp-color-red;
}


@media print {
  .isPrinting {
    display: none;
  }
}





// ** for dashboard

// :root {
//   --border-color: #4ea9ff;
//   --color: #eee;
//   --background-color: #333;
//   --background-box-title: #444;
// }

// html,
// body {
//   margin: 0px;
//   padding: 0px;
//   width: 100vw;
//   height: 100vh;
//   overflow: hidden;
//   font-family: "Roboto", sans-serif;
//   background-color: var(--background-color);
//   color: var(--color);
// }

// header {
//   height: 66px;
//   border-bottom: 1px solid var(--border-color);
//   padding-left: 20px;
// }

// header h2 {
//   margin: 0px;
//   line-height: 66px;
// }

// header a {
//   color: black;
// }

// .them-edit-link {
//   position: absolute;
//   top: 10px;
//   right: 100px;
//   color: black;
//   font-size: 40px;
// }

// .them-edit-link a {
//   text-decoration: none;
// }

// .github-link {
//   position: absolute;
//   top: 10px;
//   right: 20px;
//   color: black;
// }

// .wrapper {
//   width: 100%;
//   height: calc(100vh - 67px);
//   display: flex;
// }

// .col {
//   overflow: auto;
//   width: 300px;
//   height: 100%;
//   border-right: 1px solid var(--border-color);
// }

// .drag-drawflow {
//   line-height: 50px;
//   /* border-bottom: 1px solid var(--border-color); */
//   padding-left: 20px;
//   cursor: move;
//   user-select: none;
// }

// .menu {
//   position: absolute;
//   height: 40px;
//   display: block;
//   background: var(--color);
//   width: 100%;
//   background-color: var(--background-color);
// }

// .menu ul {
//   padding: 0px;
//   margin: 0px;
//   line-height: 40px;
// }

// .menu ul li {
//   display: inline-block;
//   margin-left: 10px;
//   /* border-right: 1px solid var(--border-color); */
//   padding-right: 10px;
//   line-height: 40px;
//   cursor: pointer;
// }

// .menu ul li.selected {
//   font-weight: bold;
// }

// .btn-clear {
//   float: right;
//   position: absolute;
//   top: 10px;
//   right: 10px;
//   color: var(--color);
//   font-weight: bold;
//   border: 1px solid #0e5ba3;
//   background: var(--border-color);
//   padding: 5px 10px;
//   border-radius: 4px;
//   cursor: pointer;
//   z-index: 5;
// }

// .btn-lock {
//   float: right;
//   position: absolute;
//   bottom: 10px;
//   right: 140px;
//   display: flex;
//   font-size: 24px;
//   color: var(--color);
//   padding: 5px 10px;
//   background: #555555;
//   border-radius: 4px;
//   border-right: 1px solid var(--border-color);
//   z-index: 5;
//   cursor: pointer;
// }

// .bar-zoom {
//   float: right;
//   position: absolute;
//   bottom: 10px;
//   right: 10px;
//   display: flex;
//   font-size: 24px;
//   color: var(--color);
//   padding: 5px 10px;
//   background: #555555;
//   border-radius: 4px;
//   border-right: 1px solid var(--border-color);
//   z-index: 5;
// }

// .bar-zoom svg {
//   cursor: pointer;
//   padding-left: 10px;
// }

// .bar-zoom svg:nth-child(1) {
//   padding-left: 0px;
// }

// #drawflow {
//   position: relative;
//   width: calc(100vw - 301px);
//   height: calc(100% - 50px);
//   top: 40px;
//   background: var(--background-color);
// }

// @media only screen and (max-width: 768px) {
//   .col {
//     width: 50px;
//   }
//   .col .drag-drawflow span {
//     display: none;
//   }
//   #drawflow {
//     width: calc(100vw - 51px);
//   }
// }

// /* Editing Drawflow */
// .drawflow .drawflow-node {
//   display: flex;
//   align-items: center;
//   position: absolute;
//   z-index: 2;
//   background: var(--background-color);
//   border: 1px solid var(--border-color);
//   -webkit-box-shadow: 0 2px 15px 2px var(--border-color);
//   box-shadow: 0 2px 15px 2px var(--border-color);
//   padding: 0px;
//   width: 200px;
//   color: var(--color);
// }

// .drawflow .drawflow-node.selected {
//   background: var(--color);
//   border: 1px solid var(--border-color);
//   -webkit-box-shadow: 0 2px 20px 2px var(--border-color);
//   box-shadow: 0 2px 20px 2px var(--border-color);
// }

// .drawflow .drawflow-node.selected .title-box {
//   color: var(--background-color);
//   background: var(--border-color);
//   border-bottom: 1px solid var(--border-color);
// }

// .drawflow .connection .main-path {
//   stroke: var(--border-color);
//   stroke-width: 3px;
// }

// .drawflow .drawflow-node .input,
// .drawflow .drawflow-node .output {
//   height: 15px;
//   width: 15px;
//   border: 2px solid var(--border-color);
// }

// .drawflow .drawflow-node .input:hover,
// .drawflow .drawflow-node .output:hover {
//   background: var(--border-color);
// }

// .drawflow .drawflow-node .output {
//   right: 10px;
// }

// .drawflow .drawflow-node .input {
//   left: -10px;
//   background: var(--color);
// }

// .drawflow > .drawflow-delete {
//   border: 2px solid #43b993;
//   background: var(--color);
//   color: #43b993;
//   -webkit-box-shadow: 0 2px 20px 2px #43b993;
//   box-shadow: 0 2px 20px 2px #43b993;
// }

// .drawflow-delete {
//   border: 2px solid var(--border-color);
//   background: var(--color);
//   color: var(--border-color);
//   -webkit-box-shadow: 0 2px 20px 2px var(--border-color);
//   box-shadow: 0 2px 20px 2px var(--border-color);
// }

// .drawflow-node .title-box {
//   height: 50px;
//   line-height: 50px;
//   background: var(--background-box-title);
//   /* border-bottom: 1px solid var(--border-color); */
//   border-radius: 4px 4px 0px 0px;
//   padding-left: 10px;
// }

// .drawflow .title-box svg {
//   position: initial;
// }

// .drawflow-node .box {
//   padding: 10px 20px 20px 20px;
//   font-size: 14px;
//   color: var(--color);
// }

// .drawflow-node .box p {
//   margin-top: 5px;
//   margin-bottom: 5px;
// }

// .drawflow .drawflow-node.selected .box {
//   color: var(--background-color);
// }

// .drawflow-node.welcome {
//   width: 250px;
// }

// .drawflow-node.slack .title-box {
//   border-radius: 4px;
// }

// .drawflow-node input,
// .drawflow-node select,
// .drawflow-node textarea {
//   border-radius: 4px;
//   border: 1px solid var(--border-color);
//   height: 30px;
//   line-height: 30px;
//   font-size: 16px;
//   width: 158px;
//   color: #555555;
// }

// .drawflow-node textarea {
//   height: 100px;
// }

// .drawflow-node.personalized {
//   background: red;
//   height: 200px;
//   text-align: center;
//   color: var(--color);
// }

// .drawflow-node.personalized .input {
//   background: yellow;
// }

// .drawflow-node.personalized .output {
//   background: green;
// }

// .drawflow-node.personalized.selected {
//   background: blue;
// }

// .drawflow .connection .point {
//   stroke: var(--border-color);
//   stroke-width: 2;
//   fill: var(--color);
//   transform: translate(-9999px, -9999px);
// }

// .drawflow .connection .point.selected,
// .drawflow .connection .point:hover {
//   fill: var(--border-color);
// }

// /* Modal */
// .modal {
//   display: none;
//   position: fixed;
//   z-index: 7;
//   left: 0;
//   top: 0;
//   width: 100vw;
//   height: 100vh;
//   overflow: auto;
//   /* background-color: rgb(0, 0, 0);
// 		background-color: rgba(0, 0, 0, 0.7); */
//   background-color: #333;
//   color: #eee;
// }

// .modal-content {
//   position: relative;
//   /* background-color: #fefefe; */
//   background-color: #333;
//   color: #eee;
//   margin: 15% auto;
//   /* 15% from the top and centered */
//   padding: 20px;
//   border: 1px solid #888;
//   width: 400px;
//   /* Could be more or less, depending on screen size */
// }

// /* The Close Button */

// .modal .close {
//   color: #aaa;
//   float: right;
//   font-size: 28px;
//   font-weight: bold;
//   cursor: pointer;
// }

// @media only screen and (max-width: 768px) {
//   .modal-content {
//     width: 80%;
//   }
// }

// /* -------------------------------------------------------------------------------- */
// /* -------------------------------------------------------------------------------- */
// /* -------------------------------------------------------------------------------- */
// /* -------------------------------------------------------------------------------- */

// .drawflow,
// .drawflow .parent-node {
//   position: relative;
// }

// .parent-drawflow {
//   display: flex;
//   overflow: hidden;
//   touch-action: none;
//   outline: 0;
// }

// .drawflow {
//   width: 100%;
//   height: 100%;
//   user-select: none;
// }

// .drawflow .drawflow-node:hover {
//   cursor: move;
// }

// .drawflow .drawflow-node .inputs,
// .drawflow .drawflow-node .outputs {
//   width: 0;
// }

// .drawflow .drawflow-node .drawflow_content_node {
//   width: 100%;
//   display: block;
// }

// .drawflow .drawflow-node .input,
// .drawflow .drawflow-node .output {
//   position: relative;
//   width: 20px;
//   height: 20px;
//   background: #fff;
//   border-radius: 50%;
//   border: 2px solid #000;
//   cursor: crosshair;
//   z-index: 1;
//   margin-bottom: 5px;
// }

// .drawflow .drawflow-node .input {
//   left: -27px;
//   top: 2px;
//   background: #ff0;
// }

// .drawflow .drawflow-node .output {
//   right: -3px;
//   top: 2px;
// }

// .drawflow svg {
//   z-index: 0;
//   position: absolute;
//   overflow: visible !important;
// }

// .drawflow .connection {
//   position: absolute;
//   transform: translate(9999px, 9999px);
// }

// .drawflow .connection .main-path {
//   fill: none;
//   stroke-width: 5px;
//   stroke: var(--border-color);
//   transform: translate(-9999px, -9999px);
// }

// .drawflow .connection .main-path:hover {
//   stroke: #1266ab;
//   cursor: pointer;
// }

// .drawflow .connection .main-path.selected {
//   stroke: #43b993;
// }

// .drawflow .connection .point {
//   cursor: move;
//   stroke: #000;
//   stroke-width: 2;
//   fill: #fff;
//   transform: translate(-9999px, -9999px);
// }

// .drawflow .connection .point.selected,
// .drawflow .connection .point:hover {
//   fill: #1266ab;
// }

// .drawflow .main-path {
//   fill: none;
//   stroke-width: 5px;
//   stroke: var(--border-color);
// }

// .drawflow .selectbox {
//   z-index: 3;
//   position: absolute;
//   transform: translate(9999px, 9999px);
// }

// .drawflow .selectbox rect {
//   fill: #00f;
//   opacity: 0.5;
//   stroke: #ff0;
//   stroke-width: 5;
//   stroke-opacity: 0.5;
//   transform: translate(-9999px, -9999px);
// }

// .drawflow-delete {
//   position: absolute;
//   display: block;
//   width: 30px;
//   height: 30px;
//   background: #000;
//   color: #fff;
//   z-index: 4;
//   border: 2px solid #fff;
//   line-height: 30px;
//   font-weight: 700;
//   text-align: center;
//   border-radius: 50%;
//   font-family: monospace;
//   cursor: pointer;
// }

// .drawflow > .drawflow-delete {
//   margin-left: -15px;
//   margin-top: 15px;
// }

// .parent-node .drawflow-delete {
//   right: -15px;
//   top: -15px;
// }
