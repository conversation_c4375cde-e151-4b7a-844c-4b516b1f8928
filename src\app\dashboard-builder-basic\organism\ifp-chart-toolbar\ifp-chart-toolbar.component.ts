import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpAccordionComponent } from '../../molecule/ifp-accordion/ifp-accordion.component';
import { DbToolbarIcon, ToolbarTabMenu, Content, DockItem, AxisDropDown, AxisOptions } from './ifp-chart-toolbar.interface';
import { NgClass } from '@angular/common';
import { axisDropDowns, chartSettingsOptions, chartTypes, dockOptions, singleDimentionData, tabMenu } from './ifp-chart-toolbar.constants';
import { IfpIconSelectorComponent } from '../../atom/ifp-icon-selector/ifp-icon-selector.component';
import { IfpDbIconLibraryComponent, TagItem } from '../../molecule/ifp-db-icon-library/ifp-db-icon-library.component';
import { iconLibrary, tagList } from '../../molecule/ifp-db-icon-library/ifp-db-icon-library.constants';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';
import { IfpDbChartStyleComponent } from '../../molecule/ifp-db-chart-style/ifp-db-chart-style.component';
import { IfpChartLegendPropsComponent } from '../../molecule/ifp-chart-legend-props/ifp-chart-legend-props.component';
import { chartConstants } from 'src/app/scad-insights/core/constants/chart.constants';
import { IfpDbTextComponent } from '../../molecule/ifp-db-text/ifp-db-text.component';
import { IfpChartSettingsComponent } from '../../molecule/ifp-chart-settings/ifp-chart-settings.component';
import { contentTypeDashboard } from 'src/app/scad-insights/core/constants/contentType.constants';
import { IfpChartCardFilterComponent } from '../ifp-chart-card-filter/ifp-chart-card-filter.component';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { IfpDbUploadDataComponent } from '../../molecule/ifp-db-upload-data/ifp-db-upload-data.component';
import { IfpTableComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-table/ifp-table.component';
import { fileFormats } from '../../molecule/ifp-db-file-uploader/ifp-db-file-uploader.constants';
import { buttonClass, buttonIconPosition } from 'src/app/scad-insights/core/constants/button.constants';
import { dashboardConstants } from 'src/app/scad-insights/core/constants/dashboard.constants';

@Component({
    selector: 'ifp-chart-toolbar',
    templateUrl: './ifp-chart-toolbar.component.html',
    styleUrl: './ifp-chart-toolbar.component.scss',
    imports: [TranslateModule, IfpAccordionComponent, NgClass, IfpIconSelectorComponent, IfpDbIconLibraryComponent, IfpDbChartStyleComponent, IfpChartLegendPropsComponent,
    IfpDbTextComponent, IfpChartSettingsComponent, IfpChartCardFilterComponent, IfpTooltipDirective, IfpDbUploadDataComponent, IfpTableComponent]
})
export class IfpChartToolbarComponent implements OnChanges {


  @Input() isPieDisabled: boolean = false;
  @Input() selectedCard: any = [];
  @Input() cntType!: string;
  @Input() selectedTextColor!: string;
  @Input() selectedDescriptionColor!: string;
  @Input() dataType!: string;
  @Input() selectedCardData: any = [];
  @Input() allDropDownData: any = [];
  @Input() selectedAllCardData: any = [];
  @Input() isDragged: boolean = false;
  @Input() isFilterPanel: boolean = false;
  @Input() isTextareaExpanded: boolean = false;
  @Input() openDataTool: boolean = false;
  @Input() uploadedFile: any;
  @Output() changeChartType: EventEmitter<DbToolbarIcon> = new EventEmitter<DbToolbarIcon>();
  @Output() closeToolbar: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() dockOptionChanged: EventEmitter<string> = new EventEmitter<string>();
  @Output() pinDashboardOutput: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() selectTool: EventEmitter<string> = new EventEmitter<string>();

  public tabMenuTypes = tabMenu;

  public selectedTab: number = 0;
  public prevTab: number = this.selectedTab - 1;
  public selectedTabItem: ToolbarTabMenu = this.tabMenuTypes[this.selectedTab];
  public chartTypes: DbToolbarIcon[] = chartTypes;
  public selectedChartType!: DbToolbarIcon;
  public selectedVisualization!: string;

  public iconLibrary: DbToolbarIcon[] = iconLibrary;
  public chartSettingsOption = chartSettingsOptions;
  public tagList: TagItem[] = tagList;
  public chartSeries!: number;
  public seriesTitles: string[] = [];
  public seriesColors: string[] = [];
  public cardTitle!: string;
  public selectedDockItem?: DockItem = dockOptions[this._themeService.defaultLang == 'ar' ? 0 : 1];
  public dockItems: DockItem[] = dockOptions;
  public selectedTextSize!: number;
  public selectedDescriptionTextSize!: number;
  public cardDescription!: string;
  public defualtLang: string | null = 'light';
  public isPinned: boolean = false;
  public axisOptions: AxisOptions[] = axisDropDowns;
  public buttonClass = buttonClass;
  public buttonIconPosition = buttonIconPosition;
  public selectedRows: { value: number, index: number } = { value: 1, index: 0 };
  public addRow: boolean = false;
  public selectedXaxis: AxisDropDown = axisDropDowns[0].options[0];
  public selectedYaxis: AxisDropDown[] = [axisDropDowns[0].options[1]];
  public isDockDropdDown: boolean = false;
  public dashboardConstants = dashboardConstants;
  public chartSettings = {
    isDatalabel: true,
    isPrecise: false
  };

  public tabledata = singleDimentionData;

  public allowedExcelExtensions = fileFormats.excelFormats;
  public rowCount = [
    {
      value: 1
    },
    {
      value: 5
    },
    {
      value: 10
    },
    {
      value: 20
    },
    {
      value: 50
    }
  ];

  constructor(private _dashboardService: DashboardService, private _themeService: ThemeService) {
    this._dashboardService.titleUpdated.subscribe(resp => {
      if (resp) {
        this.getCardTitle(resp);
      }
    });
    this.defualtLang = this._themeService.defaultLang;
  }


  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isPieDisabled'] || this.cntType == dashboardConstants.customCard) {
      const pieIndex = this.chartTypes.findIndex(x => x.key == 'pie');
      const doughnutIndex = this.chartTypes.findIndex(x => x.key == 'doughnut');
      this.chartTypes[pieIndex].disabled = this.cntType == dashboardConstants.customCard ? false : this.isPieDisabled;
      this.chartTypes[doughnutIndex].disabled = this.cntType == dashboardConstants.customCard ? false : this.isPieDisabled;
    }
    if ((changes['cntType'] && this.cntType == chartConstants.COMPARE_STATISTICS)) {
      const horizontalIndex = this.chartTypes.findIndex(x => x.key == 'bar');
      const circularIndex = this.chartTypes.findIndex(x => x.key == 'circular');
      this.chartTypes[horizontalIndex].disabled = this.isPieDisabled;
      this.chartTypes[circularIndex].disabled = this.isPieDisabled;
    }

    if (this.chartTypes?.length > 0) {
      this.selectedVisualization = this.cntType ? 'line' : '';
      if (this._dashboardService.chartSettings[this.cntType]?.find((x: { id: any; }) => x.id == this.selectedCard)) {
        const selectedChartType = this._dashboardService.chartSettings[this.cntType].find((x: { id: string; }) => x.id == this.selectedCard).chartType;
        this.selectedVisualization = selectedChartType ? selectedChartType : this.selectedVisualization;
      }
      this.chartTypes.map(x => x.selected = false);
      if (this.selectedVisualization) {
        this.chartTypes[this.chartTypes.findIndex(x => x.key == this.selectedVisualization)].selected = true;
      }
    }

    if (changes['selectedCard']) {
      this.changeSeriesLength();
      setTimeout(() => {
        this.getCardTitle('title');
      }, 500);
      this.selectedTextColor = this.getTitleColor('textColor');
      this.selectedDescriptionColor = this.getTitleColor('descriptionColor');
      this.selectedTextSize = this.getTextSize('textSize');
      this.selectedDescriptionTextSize = this.getTextSize('descriptionFontSize');
      this.getCardDescription();

      const horizontalIndex = this.chartTypes.findIndex(x => x.key == 'bar');
      const circularIndex = this.chartTypes.findIndex(x => x.key == 'circular');
      const columnIndex = this.chartTypes.findIndex(x => x.key == 'column');
      const treeIndex = this.chartTypes.findIndex(x => x.key == 'tree');
      const lineIndex = this.chartTypes.findIndex(x => x.key == 'line');
      this.chartTypes[treeIndex].disabled = true;
      this.chartTypes[lineIndex].disabled = false;

      this.chartTypes[horizontalIndex].disabled = (this.dataType?.toLowerCase() == 'coi' || this.dataType?.toLowerCase() == 'internal') ? true : false;
      this.chartTypes[circularIndex].disabled = (this.dataType?.toLowerCase() == 'coi' || this.dataType?.toLowerCase() == 'internal') ? true : false;
      this.chartTypes[columnIndex].disabled = (this.dataType?.toLowerCase() == 'coi' || this.dataType?.toLowerCase() == 'internal') ? true : false;

      const ischartType: string = this.selectedCardData?.indicatorVisualizations?.visualizationsMeta?.[0].type;

      if (ischartType == chartConstants.TREECHART) {
        this.chartTypes[treeIndex].disabled = false;
        this.chartTypes[lineIndex].disabled = true;
      }
      if (this.cntType == dashboardConstants.customCard) {
        this.getCusomcardData();
      } else if (this.selectedTabItem.key == 'data') {
        this.selectedTab = this.tabMenuTypes.findIndex(x => x.key == 'chart');
        this.selectedTabItem = this.tabMenuTypes[this.selectedTab];
      }
    }


    const filterTabIndex = this.tabMenuTypes.findIndex(x => x.key == 'filter');
    const filter = this.selectedCardData?.filterPanel?.properties?.length;
    this.tabMenuTypes[filterTabIndex].disabled = (filter > 0 && filter != undefined) ? false : true;

    if (changes['isFilterPanel'] && this.isFilterPanel) {
      this.selectedTab = this.tabMenuTypes.findIndex(x => x.key == 'filter');
      this.selectedTabItem = this.tabMenuTypes[this.selectedTab];
    }

    if (changes['isTextareaExpanded'] && this.isTextareaExpanded) {
      this.selectedTab = this.tabMenuTypes.findIndex(x => x.key == 'text');
      this.selectedTabItem = this.tabMenuTypes[this.selectedTab];
      const contentIndex = this.selectedTabItem.content.findIndex(x => x.key == 'textArea');
      this.selectedTabItem.content[contentIndex].isExpand = true;
    }

    if (this.openDataTool) {
      this.openDataSettings();
    }
  }

  openDataSettings() {
    this.selectedTab = this.tabMenuTypes.findIndex(x => x.key == 'data');
    this.selectedTabItem = this.tabMenuTypes[this.selectedTab];
  }

  getCusomcardData() {
    const index = this._dashboardService.chartSettings[this.cntType]?.findIndex((x: { id: any; }) => x.id == this.selectedCard);
    if (index >= 0) {
      this.tabledata = this._dashboardService.chartSettings[this.cntType][index].data;
      if (!this._dashboardService.chartSettings[this.cntType][index].chartType) {
        this.chartTypes.map(x => x.selected = false);
      }
      this.selectedXaxis = this._dashboardService.chartSettings[this.cntType][index].Xaxis ?? this.selectedXaxis;
      this.selectedYaxis = this._dashboardService.chartSettings[this.cntType][index].Yaxis ?? this.selectedYaxis;
    }
  }

  toolbarTabSelect(item: ToolbarTabMenu, index: number) {
    this.selectedTab = index;
    this.prevTab = index - 1;
    this.selectedTabItem = item;
    this.selectTool.emit(this.selectedTabItem.key);
  }

  selectChartType(item: DbToolbarIcon, index: number) {
    // this.selectedChart = index;
    this.selectedChartType = item;

    this.changeChartType.emit(item);
  }

  selectIconFromLibrary(_icon: DbToolbarIcon) {
    //
  }

  updateValues(event: any, type: string) {
    if (this._dashboardService.chartSettings[this.cntType]?.length == 0 || !this._dashboardService.chartSettings[this.cntType]) {
      this._dashboardService.chartSettings[this.cntType] = [];
      const data = {
        id: this.selectedCard
      };
      this._dashboardService.chartSettings[this.cntType].push(data);
    }
    const index = this._dashboardService.chartSettings[this.cntType]?.findIndex((x: { id: any; }) => x.id == this.selectedCard);
    if (index >= 0) {
      if (type == 'chartType') {
        this._dashboardService.chartSettings[this.cntType][index].chartType = event.key;
      } else if (type == 'chartStyle') {
        this._dashboardService.chartSettings[this.cntType][index][event.key] = event.value;
      } else if (type == 'xAxisPos') {
        this._dashboardService.chartSettings[this.cntType][index].xAxisPos = event;
      } else if (type == 'legend') {
        this._dashboardService.chartSettings[this.cntType][index].legendPos = event.value;
      } else if (type == 'legendEnable') {
        this._dashboardService.chartSettings[this.cntType][index].isLegend = event.value;
      } else if (type == 'icon') {
        this._dashboardService.chartSettings[this.cntType][index].icon = event;
      } else if (type == 'lineColor') {
        let lineColors = this._dashboardService.chartSettings[this.cntType][index].lineColors;
        if (lineColors?.length <= 0 || !lineColors) {
          lineColors = [];
        }
        const colorIndex = lineColors.findIndex((x: { index: any; }) => x.index == event.index);
        if (colorIndex >= 0) {
          lineColors[colorIndex].color = event.value;
        } else {
          const colorData = {
            index: event.index,
            color: event.value
          };
          lineColors.push(colorData);
        }

        this._dashboardService.chartSettings[this.cntType][index].lineColors = lineColors;
      } else if (type == 'cardTitle') {
        this._dashboardService.chartSettings[this.cntType][index].title = event;
        this._dashboardService.chartSettings[this.cntType][index].isTitleEdit = true;
      } else if (type == 'textColor') {
        this.selectedTextColor = event;
        this._dashboardService.chartSettings[this.cntType][index].textColor = event;
      } else if (type == 'descriptionColor') {
        this.selectedDescriptionColor = event;
        this._dashboardService.chartSettings[this.cntType][index].descriptionColor = event;
      } else if (type == 'textSize') {
        this.selectedTextSize = event.value;
        this._dashboardService.chartSettings[this.cntType][index].textSize = event.value;
      } else if (type == 'descriptionFontSize') {
        this.selectedDescriptionTextSize = event.value;
        this._dashboardService.chartSettings[this.cntType][index].descriptionFontSize = event.value;
      } else if (type == 'cardDescription') {
        this.cardDescription = event;
        this._dashboardService.chartSettings[this.cntType][index].cardDescription = event;
      } else if (type == 'chartSettings') {
        this._dashboardService.chartSettings[this.cntType][index][event.key] = event.value;
      } else if (type == 'Xaxis') {
        this._dashboardService.chartSettings[this.cntType][index][type] = event;
      } else if (type == 'Yaxis') {
        this._dashboardService.chartSettings[this.cntType][index][type] = event;
      } else if (type == 'dataUpdate') {
        if (event.collapse) {
          this.closeToolbar.emit(event);
        }
        this._dashboardService.chartSettings[this.cntType][index].data = event.data;
      } else if (type == 'axisUpdate') {
        this.axisOptions = event;
        const selectedXAxisIndex = this.axisOptions[0].options.findIndex(x => x.checked);
        this.selectedXaxis = this.axisOptions[0].options[selectedXAxisIndex];
        this._dashboardService.chartSettings[this.cntType][index]['Xaxis'] = this.selectedXaxis;
        const selectedYAxes = this.axisOptions[1]?.options?.filter(element => element.checked) || [];
        this.selectedYaxis = selectedYAxes;
        this._dashboardService.chartSettings[this.cntType][index]['Yaxis'] = selectedYAxes;
      }
    } else {
      const data: any = {
        id: this.selectedCard
      };
      if (type == 'chartType') {
        data['chartType'] = event.key;
      } else if (type == 'chartStyle') {
        data[event.key] = event.value;
      } else if (type == 'xAxisPos') {
        data.xAxisPos = event;
      } else if (type == 'legend') {
        data.legendPos = event.value;
      } else if (type == 'legendEnable') {
        data.isLegend = event.value;
      } else if (type == 'icon') {
        data.icon = event;
      } else if (type == 'cardTitle') {
        data.title = event;
        data.isTitleEdit = true;
      } else if (type == 'textColor') {
        this.selectedTextColor = event;
        data.textColor = event;
      } else if (type == 'descriptionColor') {
        this.selectedDescriptionColor = event;
        data.descriptionColor = event;
      } else if (type == 'textSize') {
        this.selectedTextSize = event.value;
        data.textSize = event.value;
      } else if (type == 'descriptionFontSize') {
        this.selectedDescriptionTextSize = event.value;
        data.descriptionFontSize = event.value;
      } else if (type == 'cardDescription') {
        this.cardDescription = event;
        data.cardDescription = event;
      } else if (type == 'chartSettings') {
        data[event.key] = event.value;
      } else if (type == 'Xaxis') {
        data[type] = event;
      } else if (type == 'Yaxis') {
        data[type] = event;
      } else if (type == 'dataUpdate') {
        if (event.collapse) {
          this.closeToolbar.emit(event);
        }
        data.data = event.data;
      } else if (type == 'axisUpdate') {
        this.axisOptions = event;
        const selectedXAxisIndex = this.axisOptions[0].options.findIndex(x => x.checked);
        this.selectedXaxis = this.axisOptions[0].options[selectedXAxisIndex];
        data[this.cntType][index]['Xaxis'] = this.selectedXaxis;
        const selectedYAxes = this.axisOptions[1]?.options?.filter(element => element.checked) || [];
        this.selectedYaxis = selectedYAxes;
        data[this.cntType][index]['Yaxis'] = selectedYAxes;
      }
      this._dashboardService.chartSettings[this.cntType].push(data);
    }
    this._dashboardService.settingsChanged.next({ type: (this.dataType == 'coi' ? contentTypeDashboard['scad_official_indicator'] : this.cntType), id: this.selectedCard, tools: type });
    if (type == 'chartType') {
      this.changeSeriesLength();
    }
  }

  changeSeriesLength() {
    this.chartSeries = this._dashboardService.chartSettingsSeries?.[this.cntType]?.find((x: { id: any; }) => x.id == this.selectedCard)?.seriesLength;
    this.seriesTitles = this._dashboardService?.chartSettingsSeries?.[this.cntType]?.find((x: { id: any; }) => x.id == this.selectedCard)?.titles;
    this.seriesColors = this._dashboardService.chartSettingsSeries?.[this.cntType]?.find((x: { id: any; }) => x.id == this.selectedCard)?.colors;
  }

  getCardTitle(key: string) {
    let title = '';
    const dataTypeArray = this._dashboardService.chartSettings[this.cntType];
    if (dataTypeArray?.find((x: { id: any; }) => x.id == this.selectedCard)) {
      title = dataTypeArray.find((x: { id: any; }) => x.id == this.selectedCard)[key];
    }
    if (key == 'title') {
      this.cardTitle = title;
    }
    if (key == 'cardDescription') {
      this.cardDescription = title;
    }
  }

  getTitleColor(key: string) {
    let color = '#000000';
    const dataTypeArray = this._dashboardService.chartSettings[this.cntType];
    if (dataTypeArray?.find((x: { id: any; }) => x.id == this.selectedCard)) {
      color = dataTypeArray.find((x: { id: any; }) => x.id == this.selectedCard)?.[key];
      color = color ? color : '#000000';
    }
    return color;
  }

  getTextSize(key: string) {
    let size = 16;
    const dataTypeArray = this._dashboardService.chartSettings[this.cntType];
    if (dataTypeArray?.find((x: { id: any; }) => x.id == this.selectedCard)) {
      size = dataTypeArray.find((x: { id: any; }) => x.id == this.selectedCard)?.[key];
      size = size ? size : 16;
    }
    return size;
  }

  getCardDescription() {
    let title = '';
    const dataTypeArray = this._dashboardService.chartSettings[this.cntType];
    if (dataTypeArray?.find((x: { id: any; }) => x.id == this.selectedCard)) {
      title = dataTypeArray.find((x: { id: any; }) => x.id == this.selectedCard).cardDescription;
      title = title ? title : '';
    }
    this.cardDescription = title;
  }

  getChartSettings() {
    this.chartSettingsOption[0].value = true;
    this.chartSettingsOption[1].value = false;
    const dataTypeArray = this._dashboardService.chartSettings[this.cntType];
    if (dataTypeArray?.find((x: { id: any; }) => x.id == this.selectedCard)) {
      this.chartSettingsOption[0].value = dataTypeArray.find((x: { id: any; }) => x.id == this.selectedCard).dataLabel;
      this.chartSettingsOption[1] = dataTypeArray.find((x: { id: any; }) => x.id == this.selectedCard).preciseValue;
    }
  }

  closeModelToolbar(event: boolean) {
    this.closeToolbar.emit(event);
  }



  expandAccordian(_event: boolean, content: Content) {
    this.selectedTabItem.content.map(x => x.title == content.title ? x.isExpand = !x.isExpand : x.isExpand = false);
  }

  selectDockItem(item: DockItem) {
    this.dockOptionChanged.emit(item.key);
    this.selectedDockItem = item;
    if (this.selectedDockItem.key == 'drag') {
      this.isPinned = false;
    }
    setTimeout(() => {
      this.isDockDropdDown = false;
    }, 100);

  }

  pinDashboard() {
    if (this.selectedDockItem?.key == 'drag') {
      this.isPinned = !this.isPinned;
      this.pinDashboardOutput.emit(true);
    }
  }

  // addNewRows() {
  //   this.addRow = true;
  // }

  // onSelectRowCount(event: any) {
  //   this.selectedRows = event;
  // }
}
