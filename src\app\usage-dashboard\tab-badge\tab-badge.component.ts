import { NgClass } from '@angular/common';
import { Component, EventEmitter, input, Input, Output, signal, ElementRef, ViewChild } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'ifp-tab-badge',
  imports: [TranslateModule, NgClass],
  templateUrl: './tab-badge.component.html',
  styleUrl: './tab-badge.component.scss'
})
export class TabBadgeComponent {
  @Input() data: BadgeTabInterface[] = [];
  @Input() selectedValue!: string;
  @Input() isTransparent: boolean = false;
  @ViewChild('scrollContainer', { static: false }) scrollContainer!: ElementRef;

  public genAi = input();
  @Output() selectedEvent = new EventEmitter<BadgeTabInterface>();
  public isAtLeftEnd = signal(true);
  public isAtRightEnd = signal(false);

  // Drag functionality variables
  private isDragging = false;
  private startX = 0;
  private scrollLeft = 0;
  private animationFrame: number | null = null;

  selectData(item: BadgeTabInterface) {
    // Only emit if not dragging to prevent accidental clicks during drag
    if (!this.isDragging) {
      this.selectedEvent.emit(item);
    }
  }

  checkScrollPosition(event: Event) {
    const element = event.target as HTMLElement;
    const scrollLeft = element.scrollLeft;
    const scrollWidth = element.scrollWidth;
    const clientWidth = element.clientWidth;
    this.isAtLeftEnd.set(scrollLeft === 0);
    this.isAtRightEnd.set(Math.round(scrollLeft + clientWidth) >= scrollWidth);
  }

  // Mouse drag functionality
  onMouseDown(event: MouseEvent) {
    if (!this.genAi()) {
      return;
    }
    if (!this.scrollContainer) {
      return;
    }

    this.isDragging = true;
    this.startX = event.pageX - this.scrollContainer.nativeElement.offsetLeft;
    this.scrollLeft = this.scrollContainer.nativeElement.scrollLeft;

    // Prevent text selection during drag
    event.preventDefault();

    // Add cursor style
    this.scrollContainer.nativeElement.style.cursor = 'grabbing';
    this.scrollContainer.nativeElement.style.userSelect = 'none';
  }

  onMouseMove(event: MouseEvent) {
    if (!this.genAi()) {
      return;
    }
    if (!this.isDragging || !this.scrollContainer) {
      return;
    }

    event.preventDefault();

    const x = event.pageX - this.scrollContainer.nativeElement.offsetLeft;
    const walk = (x - this.startX) * 2; // Multiply by 2 for faster scrolling

    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
    }

    this.animationFrame = requestAnimationFrame(() => {
      this.scrollContainer.nativeElement.scrollLeft = this.scrollLeft - walk;
    });
  }

  onMouseUp() {
    if (!this.genAi()) {
      return;
    }
    if (!this.scrollContainer) {
      return;
    }

    // Add a small delay before allowing clicks again
    setTimeout(() => {
      this.isDragging = false;
    }, 50);

    this.scrollContainer.nativeElement.style.cursor = 'grab';
    this.scrollContainer.nativeElement.style.userSelect = '';

    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }
  }

  onMouseLeave() {
    if (!this.genAi()) {
      return;
    }
    this.onMouseUp();
  }

  // Touch events for mobile support
  onTouchStart(event: TouchEvent) {
    if (!this.genAi()) {
      return;
    }
    if (!this.scrollContainer) {
      return;
    }

    this.isDragging = true;
    this.startX = event.touches[0].pageX - this.scrollContainer.nativeElement.offsetLeft;
    this.scrollLeft = this.scrollContainer.nativeElement.scrollLeft;

    event.preventDefault();
  }

  onTouchMove(event: TouchEvent) {
    if (!this.genAi()) {
      return;
    }
    if (!this.isDragging || !this.scrollContainer) {
      return;
    }

    event.preventDefault();

    const x = event.touches[0].pageX - this.scrollContainer.nativeElement.offsetLeft;
    const walk = (x - this.startX) * 1.5; // Slightly slower for touch

    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
    }

    this.animationFrame = requestAnimationFrame(() => {
      this.scrollContainer.nativeElement.scrollLeft = this.scrollLeft - walk;
    });
  }

  onTouchEnd() {
    if (!this.genAi()) {
      return;
    }
    setTimeout(() => {
      this.isDragging = false;
    }, 50);

    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }
  }
}

export interface BadgeTabInterface {
  name: string;
  key?: string;
  tab?: boolean;
  node?: any;
}
