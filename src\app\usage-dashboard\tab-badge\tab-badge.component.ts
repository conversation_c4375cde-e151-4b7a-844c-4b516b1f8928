import { NgClass } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'ifp-tab-badge',
    imports: [TranslateModule, NgClass],
    templateUrl: './tab-badge.component.html',
    styleUrl: './tab-badge.component.scss'
})
export class TabBadgeComponent {
  @Input() data:BadgeTabInterface[] = [];
  @Input() selectedValue!:string;
  @Input() isTransparent: boolean = false;
  @Output() selectedEvent =  new EventEmitter<BadgeTabInterface>();

  selectData(item: BadgeTabInterface) {
    this.selectedEvent.emit(item);
  }


}

export interface BadgeTabInterface   {name: string; key?: string; tab?: boolean, node?: any}
