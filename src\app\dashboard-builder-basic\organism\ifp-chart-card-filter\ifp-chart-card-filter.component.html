<div class="ifp-card-filter">
  <!-- <div class="ifp-card-filter__header-wrapper">
    <em class="ifp-icon ifp-icon-filter-2"></em>
    <h4 class="ifp-card-filter__header-title">{{'Filter' | translate}}</h4>
  </div> -->

  <div class="ifp-card-filter__body-wrapper">

    @if (visualDataNames?.length > 0) {
    <app-ifp-db-dropdown class="ifp-card-filter__filter-item" [options]="visualDataNames" [title]="'Views'"
      [isMultiSelect]="false" (singleSelected)="selectViews($event)" [defaultSelect]="false"
      [selectedSingleItem]="getDefualtView()" [key]="'name'"></app-ifp-db-dropdown>
    }



    @for (filter of filters; let i=$index; track filter) {
    <app-ifp-db-dropdown class="ifp-card-filter__filter-item" [options]="filter.filterOptions" [title]="filter.label"
      [isMultiSelect]="checkRadio()  || checkMultiSelectIndex() === i || filter.path === chartConstants.TIME_PERIOD"
      (singleSelected)="applyFilter($event, filter.label)" [defaultSelect]="!getSelectedSingleItem(i)" [key]="'name'"
      (multiSelected)="applyFilter($event, filter.label)" [selectedSingleItem]="getSelectedSingleItem(i)"
      [multipleSelectedItems]="getSelectedMultipleItem(i)"></app-ifp-db-dropdown>
    }


  </div>

  <div class="ifp-card-filter__footer-wrapper">
    <!-- <ifp-button [label]="'Cancel' | translate" (ifpClick)="closeModel()" [buttonClass]="buttonClass.secondary"
      class="ifp-card-filter__cancel-btn"></ifp-button> -->

    <ifp-button [label]="'Apply' | translate" (ifpClick)="apply()" [buttonClass]="buttonClass.primary"
      class="ifp-card-filter__apply-btn"></ifp-button>
  </div>
</div>
