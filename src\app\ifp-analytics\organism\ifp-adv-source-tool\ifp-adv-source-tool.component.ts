import { ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnChanges, OnDestroy, Output, ViewChild, WritableSignal, effect, inject, signal } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { dateFormat } from 'src/app/scad-insights/core/constants/date.constants';
import { DatePipe, DecimalPipe, NgClass, NgStyle } from '@angular/common';
import { IfpDropdownComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { IfpProgressBarComponent } from '../../atom/ifp-progress-bar/ifp-progress-bar.component';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { IFPMsalService } from 'src/app/scad-insights/core/services/IFP-msal.service';
import { FileData, IfpDbFileUploaderComponent } from '../../../dashboard-builder/molecule/ifp-db-file-uploader/ifp-db-file-uploader.component';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpImportIndicatorsComponent } from '../../../dashboard-builder/organism/ifp-import-indicators/ifp-import-indicators.component';
import { IfpPrepUploadService } from '../../data-prep/ifp-data-prep/ifp-data-prep-upload/services/ifp-prep-upload.service';
import { SubscriptionLike } from 'rxjs';
import { ApiStatus } from 'src/app/scad-insights/core/constants/api-status.constants';
import { FileResponePrep, NodeData } from '../../data-prep/ifp-data-prep/interface/ifp-data-prep.interface';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { connectionType, prepsApiEndpoints, storageType } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';
import { SubSink } from 'subsink';
import { workFlowState } from '../../data-prep/ifp-data-prep/constants/ifp-state.contants';
import { IfpPrepLibraryComponent } from '../../data-prep/ifp-data-prep/ifp-prep-library/ifp-prep-library.component';
import { IfpAdvancePrepService } from '../../data-prep/ifp-advance-prep/service/ifp-advance-prep.service';
import { selectNodeStoreAdvance } from '../../data-prep/ifp-advance-prep/store/advance-node-store/advance-node.store';
import { fileFormats } from '../../../dashboard-builder/molecule/ifp-db-file-uploader/ifp-db-file-uploader.constants';
import { IfpSpinnerComponent } from '../../../scad-insights/ifp-widgets/ifp-molecules/ifp-spinner/ifp-spinner.component';

@Component({
    selector: 'ifp-adv-source-tool',
    templateUrl: './ifp-adv-source-tool.component.html',
    styleUrl: './ifp-adv-source-tool.component.scss',
    providers: [IfpPrepUploadService],
    imports: [TranslateModule, IfpButtonComponent, DatePipe, DecimalPipe, IfpDropdownComponent, IfpProgressBarComponent, IfpDbFileUploaderComponent, IfpModalComponent, IfpImportIndicatorsComponent, IfpPrepLibraryComponent, IfpSpinnerComponent, NgClass]
})
export class IfpAdvSourceToolComponent implements OnChanges, OnDestroy {

  @ViewChild('fileUpload') fileUpload!: ElementRef;
  @ViewChild('indicatorModal') indicatorModal!: IfpModalComponent;
  @ViewChild('libraryListModal') libraryListModal!: IfpModalComponent;
  @Input() nodeData!: NodeData;
  @Input() workFlowRunning = signal(false);
  @Output() selectData = new EventEmitter();
  @Output() goToLibrary = new EventEmitter();
  @Output() runWorkflow = new EventEmitter();
  @Output() resetToolConfigs = new EventEmitter();

  public isDataset: boolean = true;
  public buttonClass = buttonClass;
  public allowedExtensions = fileFormats.excelFormats;
  public fileName!: string;
  public selectedFile!: File | null;
  public files: File[] = [];
  public dateFormat = dateFormat;
  public showUploadScreen: boolean = false;
  public fileType: string = '';
  public sheetList!: string[];
  public color = ifpColors;
  public success: boolean = false;
  public userName: string = this._msal?.getLoginData?.account?.name;
  public downloadSubscribe!: SubscriptionLike;
  public uploadDataProgess: WritableSignal<number> = signal(0);
  public libraryId!: string;
  public uploadResponse!: FileResponePrep;
  public disableProcesed: boolean = true;
  public subs: SubSink = new SubSink();
  public loaderProcess = false;
  public isImportModelOpen: WritableSignal<boolean> = signal(false);
  public isLaibraryModelOpen: WritableSignal<boolean> = signal(false);
  public readonly advanceStore = inject(selectNodeStoreAdvance);
  public filename!: string;
  public fileDetails!: FileDetails | null;
  private cancelUploads = false;
  public fileError: boolean = false;

  public allowedSheetType: string = 'xlsx';
  public s3Path!: string;
  public selectedWorksheet: string = '';
  public nodeDetails: any;
  private sourceDeleteEffect: any;

  constructor(private _router: Router, private _msal: IFPMsalService, private _prepService: IfpPrepUploadService, private _prepCommonService: IfpAdvancePrepService, private _toaster: ToasterService, private _cdr: ChangeDetectorRef,
    private _advancePrepService: IfpAdvancePrepService
  ) {

    this.sourceDeleteEffect = effect(() => {
      const nodeData = this.advanceStore?.selectNodeValue().nodeValue(this.nodeData.objectId);
      if (this._advancePrepService.deleteDataSet() && this._advancePrepService.deleteDataSet() == nodeData?.configuration?.connection?.path) {
        this.fileDetails = null;
        this._cdr.detectChanges();
        setTimeout(() => {
          this.advanceStore?.addUploadData(this.nodeData.objectId, '', connectionType.ifp, this.fileDetails);
          this._advancePrepService.currentNodeId.set(null);
        }, 300);

      }
    });
  }




  ngOnChanges(): void {
    this.showUploadScreen = false;
    this.fileDetails = null;
    const nodeData = this.advanceStore.selectNodeValue()?.nodeValue(this.nodeData.objectId);
    if (nodeData?.settings?.name && nodeData?.configuration?.connection?.path != '') {
      this.fileDetails = nodeData.settings;
    }
  }

  uploadData() {
    this.fileUpload.nativeElement.click();
  }

  onBrowse(event: FileData) {
    this.fileError = false;
    this.success = false;
    this.selectedFile = event.file[0];
    this.filename = this.selectedFile.name.replace(/^.*[\\/]/, '');
    this.fileDetails = {
      name: this.filename,
      type: this.filename.split('.').pop() ?? '',
      addedBy: this.userName,
      addedOn: new Date(),
      lastModified: new Date(this.selectedFile.lastModified),
      size: this.selectedFile.size
    };
    if (this.fileDetails.type === this.allowedSheetType) {
      this.getSheetList(event.file[0]);
    } else {
      this.sheetList = [];
      this.showUploadScreen = true;
      this.success = true;
    }
    const interVellId = setInterval(() => {
      if (this.uploadDataProgess() <= 100) {
        this.uploadDataProgess.set(this.uploadDataProgess() + 1);
      } else {
        clearInterval(interVellId);
        this._cdr.detectChanges();
      }
    }, 10);
  }


  createNewFile(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const uploadData = new FormData();
        const fileName = this.fileDetails?.type === this.allowedSheetType ? this.s3Path : this.filename;
        uploadData.append('name', fileName);
        uploadData.append('storage_backend', storageType.s3);

        if (this.fileDetails?.type === this.allowedSheetType) {
          uploadData.append('sheet_name', this.selectedWorksheet);
        } else {
          uploadData.append('file', this.selectedFile ?? '');
        }
        this.downloadSubscribe = this._prepService.getUploadData(uploadData).subscribe({
          next: data => {
            if (data?.type == 4) {
              if (data?.status == ApiStatus.created) {
                this.success = true;
                this.fileError = false;
                this.showUploadScreen = false;
                this.libraryId = data?.body?.id;
                this._prepCommonService.uploadedFileResponse.set(data.body);
                this.uploadResponse = data.body;
                this.disableProcesed = false;
                resolve(data);
              }
            }
          }, error: err => {
            const error = err?.error;
            this.showUploadScreen = true;
            // this.fileDetails = null;
            this.errorHandler(error);
            resolve();
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  errorHandler(error: any) {
    this.success = false;
    if (typeof error === 'string') {
      if (error !== '') {
        this._toaster.error(error, 10000);
        this.fileError = true;
      }
    } else {
      for (const key in error) {
        if (Object.prototype.hasOwnProperty.call(error, key)) {
          const element = error[key];
          let data = '';
          if (typeof element === 'string') {
            data = element;
          } else {
            element.forEach((elementValue: string) => {
              data = `${data} ${elementValue}`;
            });
          }
          if (data !== '') {
            this._toaster.error(data, 10000);
            this.fileError = true;
          }
        }
      }
    }

  }

  getSheetList(file: File) {
    const uploadData = new FormData();
    uploadData.append('file', file);
    this.subs.add(
      this._prepService.getSheetList(uploadData).subscribe({
        next: data => {
          this.showUploadScreen = true;
          if (data?.status === ApiStatus.ok) {
            this.success = true;
            this.fileError = false;
            this.sheetList = data?.body?.sheet_names;
            this.s3Path = data?.body?.s3_path;
            this.selectedWorksheet = data?.body?.sheet_names[0];
            this._cdr.detectChanges();
          }
        }, error: err => {
          this.errorHandler(err?.error);
        }
      })
    );
  }

  deleteFile() {
    this.selectedFile = null;
    this.downloadSubscribe?.unsubscribe();
    if (this.uploadResponse) {
      this.deleteLibrary(this.uploadResponse.id ?? '');
      this._prepCommonService.uploadedFileResponse.set(null);
    }
    this.disableProcesed = true;
  }

  deleteLibrary(id: string) {
    this.subs.add(
      this._prepCommonService.getDeleteRequest(`${prepsApiEndpoints.libraryDelete}${id}`).subscribe({
        next: () => {
          this._toaster.success('Removed successfully!');
          this.fileError = false;
        }, error: err => {
          const error = err?.error;
          this.errorHandler(error);
        }
      })
    );
  }

  async saveFile() {
    await this.createNewFile().then(() => {
      this.uploadProccessed(this._prepCommonService.uploadedFileResponse()?.id);
    });
    this.uploadDataProgess.set(0);
    this.selectData.emit();
  }

  selectWorkSheet(sheet: string) {
    this.selectedWorksheet = sheet;
  }

  cancelUpload() {
    this.showUploadScreen = false;
    this.disableProcesed = true;
    this.fileDetails = null;
    this.uploadDataProgess.set(0);
  }

  closeImport() {
    this.loaderProcess = false;
    this.cancelUploads = true;
    this._prepCommonService.uploadedFileResponse.set(null);
    this.fileDetails = null;
    this.isImportModelOpen.set(false);
    this.indicatorModal.removeModal();
  }

  addAllIndicators(indicatorId: { id: string; cols: number; rows: number; y: number; x: number; key: string; type: string; indicatorId: string; title: string; }) {
    this.cancelUploads = false;
    this._prepCommonService.uploadedFileResponse.set({
      name: indicatorId.title,
      file: indicatorId.type,
      owner: '',
      storage_backend: '',
      id: indicatorId.indicatorId ?? indicatorId.id,
      type: 'ifp'
    });
    this.filename = indicatorId.title.replace(/^.*[\\/]/, '');
    this.fileDetails = {
      name: this.filename,
      type: this.filename.split('.').pop() ?? '',
      addedBy: this.userName,
      addedOn: new Date(),
      lastModified: new Date(),
      size: 500
    };
    const uploadData = {
      name: indicatorId.title,
      indicator_id: indicatorId.indicatorId ?? indicatorId.id,
      storage_backend: storageType.s3
    };
    this._prepService.getIndicatorUpload(uploadData).subscribe({
      next: data => {
        this.libraryId = data?.id;
        this._prepCommonService.uploadedFileResponse.set(data);
        this.uploadResponse = data;
        this.uploadProccessed(this.libraryId, false);
        this.selectData.emit();
        this.isImportModelOpen.set(false);
        this.indicatorModal.removeModal();
      },
      error: error => {
        error?.error?.non_field_errors?.forEach((element: string) => {
          this._toaster.error(element);
        });

      }
    });

  }

  openImportIndicatorModal() {
    this.isImportModelOpen.set(true);
    setTimeout(() => {
      this.indicatorModal.createElement();
      this._cdr.detectChanges();
    }, 300);

  }

  openLibrary() {
    // this._router.navigate(['/ifp-analytics/prep-library'], {queryParams: {source: 'advance'}});
    this.isLaibraryModelOpen.set(true);
    setTimeout(() => {
      this.libraryListModal.createElement();
      this._cdr.detectChanges();
    }, 300);

  }

  getFromLibrary(value: FileResponePrep) {
    this.uploadResponse = {
      id: value.id,
      name: value.name,
      file: value.file,
      owner: value.owner,
      storage_backend: value.storage_backend
    };
    this._prepCommonService.uploadedFileResponse.set(this.uploadResponse);
    this.fileDetails = {
      name: value.name ?? '',
      type: value.type ?? '',
      addedBy: value.owner ?? '',
      addedOn: new Date(value.created_at ?? ''),
      lastModified: new Date(value.created_at ?? ''),
      size: 0
    };
    this.selectData.emit();
    this.isLaibraryModelOpen.set(false);
    this.libraryListModal.removeModal();
    this.uploadProccessed(this.uploadResponse?.id);
  }

  closeLibrary() {
    this.isLaibraryModelOpen.set(false);
    this.libraryListModal.removeModal();
  }

  uploadProccessed(id?: string, ifp?: boolean) {
    if (!id) {
      this.fileError = true;
    }
    this.loaderProcess = true;
    // this._prepCommonService.processActive.set(true);
    // const nodeData = this.advanceStore.selectNodeValue().nodeValue(this.nodeData.objectId);
    const currentNode = this.advanceStore.selectNodeValue().nodeValue(this.nodeData.objectId ?? '');
    if (currentNode?.configuration?.connection?.path && currentNode?.configuration?.connection?.path != '') {
      this.checkColumnsMismatch();
    }
    this.advanceStore.addUploadData(this.nodeData.objectId, id ?? '', ifp ? connectionType.ifp : connectionType.dataset, this.fileDetails);
    this._prepCommonService.nodeChangeDetect.next('Upload Data');
    this.loaderProcess = false;
    const subsValue = this._prepCommonService.processStatus.subscribe(data => {
      if (this.cancelUploads) {
        subsValue?.unsubscribe();
        return;
      }
      if (data?.workflow_status === workFlowState.completed) {
        if (ifp) {
          this.isImportModelOpen.set(false);
          this.indicatorModal.removeModal();
        }
      }
    });
    subsValue?.unsubscribe();
  }

  checkColumnsMismatch() {
    if (this._prepCommonService.streamId()) {
      this._prepCommonService.getMethodRequest(`${prepsApiEndpoints.workflow}${this._prepCommonService.streamId()}/node/${this.nodeData.objectId}/dataset/${this.uploadResponse?.id}/check-mismatch/`).subscribe(resp => {
        if (resp?.reset_config) {
          this.resetToolConfigs.emit(resp.message);
        }
      });
    }
  }



  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }
}

interface FileDetails {
  name: string;
  type: string;
  addedBy: string;
  addedOn: Date;
  lastModified: Date;
  size: number;
}
