import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IfpCardLoaderComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-card-loader/ifp-card-loader.component';
import { IfpNoDataComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { IfpDropdownComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';

@Component({
  selector: 'ifp-hies-census-most-used-filters',
  imports: [CommonModule, TranslateModule, IfpNoDataComponent, IfpCardLoaderComponent, IfpDropdownComponent],
  templateUrl: './hies-census-most-used-filters.component.html',
  styleUrl: './hies-census-most-used-filters.component.scss'
})
export class HiesCensusMostUsedFiltersComponent {
  @Input() regionData: any[] = [];
  @Input() yearData: any[] = [];

  // trackBy for ngFor to avoid re-rendering rows unnecessarily
  trackByIndex(_: number, __: any) {
    return _;
  }

}
