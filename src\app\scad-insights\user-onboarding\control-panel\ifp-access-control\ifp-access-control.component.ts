import { AccessRe<PERSON>s, <PERSON><PERSON><PERSON>ist, EditAccess, RequestData } from './../../user-onboarding.interface';
import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit, signal, WritableSignal } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IfpTabComponent } from '../../../ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component';
import { IfpAccessControlCardComponent } from '../../../ifp-widgets/ifp-molecules/ifp-access-control-card/ifp-access-control-card.component';
import { Title } from '@angular/platform-browser';
import { AccessList, FilterItems, RequestList, SubmitUserData } from '../../user-onboarding.interface';
import { SubSink } from 'subsink';
import { RadioGroupItems } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-radio-group-progress/ifp-radio-group-progress.interface';
import { accessLevels, accessTabs, accessTabsConfidential, accessTabsDg, accessTabsPE, apiParams, dataClassification, dgStatus, peUserRoles, role, superUserRoles } from './ifp-access-control.constants';
import { LabelData } from 'src/app/scad-insights/core/interface/atom/ifp-category-label.interface';
import { NgClass } from '@angular/common';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { IfpDropdownComponent } from 'src/app/scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';
import { dg, productEngagement, superUser, user, userGuide } from '../../user-onboarding.constants';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { IfpNoDataComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/ifp-no-data/ifp-no-data.component';
import { AdminService } from 'src/app/scad-insights/core/services/sla/admin.service';
import { PaginationComponent } from 'src/app/scad-insights/ifp-widgets/ifp-molecules/pagination/pagination.component';
import { IfpInviteUserFormComponent } from '../../ifp-invite-user-form/ifp-invite-user-form.component';
import { title } from 'src/app/scad-insights/core/constants/header.constants';
import { IfpSearchComponent } from '../../../ifp-widgets/ifp-atoms/ifp-search/ifp-search.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { DownLoadService } from 'src/app/scad-insights/core/services/download-service/download.service';
import { environment } from 'src/environments/environment';
import { IfpDeletedInvitesComponent } from '../../ifp-deleted-invites/ifp-deleted-invites.component';
import { IfpKebabMenuComponent } from '../../../../dashboard-builder/molecule/ifp-kebab-menu/ifp-kebab-menu.component';
import { KebabMenuOption } from 'src/app/dashboard-builder/molecule/ifp-kebab-menu/ifp-kebab-menu.interface';
import { FormGroup } from '@angular/forms';

@Component({
    selector: 'ifp-access-control',
    imports: [TranslateModule, IfpTabComponent, IfpAccessControlCardComponent, NgClass, IfpDropdownComponent, IfpTooltipDirective, IfpNoDataComponent, PaginationComponent, IfpInviteUserFormComponent, IfpSearchComponent, IfpDeletedInvitesComponent, IfpKebabMenuComponent],
    templateUrl: './ifp-access-control.component.html',
    styleUrl: './ifp-access-control.component.scss'
})

export class IfpAccessControlComponent implements OnInit, OnDestroy {

  public accessType!: string;
  public newRequestList: RequestList[] = [];
  public completedRequestList: RequestList[] = [];
  public approvedList: RequestList[] = [];
  public subs: SubSink = new SubSink();
  public newReqCount: number = 0;
  public pendingReqCount: number = 0;
  public accessTabs: LabelData[] = accessTabs;
  public selectedEntity: string = '';
  public selectedTabView!: { event: { key: string }; index: number };
  public classificationList = accessLevels;
  public pageHeading: string = 'Users access dashboard';
  public accessPolicyList: AccessList[] = [];

  public newRequestAccess: RadioGroupItems[] = [];
  public approvedAccess: RadioGroupItems[] = [];
  public domainList: string[] = [];
  public apiParams = apiParams;
  public page: number = 1;
  public limit: number = 10;
  public size!: number;
  public entity!: FilterItems | any;
  public exisitingUserType: string[] = ['Existing', 'New'];
  public selectedUserType: string = '';
  public role = role;
  public superUserRoles = superUserRoles;
  public sort = [
    {
      label: 'Latest',
      value: 'desc',
      key: 'requestDate'
      // icon: 'ifp-rotate-90 ifp-icon-rightarrow'
    },
    {
      label: 'Oldest',
      value: 'asc',
      key: 'requestDate'
      // icon: 'ifp-rotate-270 ifp-icon-rightarrow'
    }
  ];

  public sortValue = {
    label: 'Date',
    value: 'desc',
    key: 'requestDate'
    // icon: 'ifp-rotate-90 ifp-icon-rightarrow'
  };

  public entityList: FilterItems[] = [];
  public dataClassification = dataClassification;
  public isSubmitted: boolean = false;
  public isDg: boolean = false;
  public existingUserEmails: string[] = [];
  public searchUser: string = '';
  public buttonClass = buttonClass;
  public deletedUsersList: DeletedList[] = [];
  public kebabOptionsExisting: KebabMenuOption[] = [
    {
      name: 'Export Users',
      event: 'export',
      icon: 'ifp-icon-user-download',
      disabled: false
    },
    {
      name: 'Download User Manual',
      event: 'downloadUserGuide',
      icon: 'ifp-icon-guide-download',
      disabled: false
    }
  ];

  public kebabOptions: KebabMenuOption[] = [
    {
      name: 'Download User Manual',
      event: 'downloadUserGuide',
      icon: 'ifp-icon-guide-download',
      disabled: false
    }
  ];
  public excludeSearch = ['invite', 'deleted'];
  public excludePagination = ['invite', 'deleted'];
  public inviteFormValue!: Record<string, FormGroup>;
  // public saveForm: WritableSignal<boolean> = signal(true);

  constructor(private _titleService: Title, private _apiService: ApiService, private _cdr: ChangeDetectorRef, private _toastrService: ToasterService, public _adminService: AdminService, private _translate: TranslateService, private _downloadService: DownLoadService) {
    this.accessType = this._adminService.userRole;
    this.isDg = this.accessType === role.dg;
    switch (this.accessType) {
    // case this.role.suPrimary:
    //   this.accessTabs = accessTabs;
    //   break;
    // case this.role.suSecondary:
    //   this.accessTabs = accessTabs;
    //   break;
    case this.role.pePrimary:
      this.accessTabs = accessTabsPE;
      break;
    case this.role.dg:
      this.accessTabs = accessTabsDg;
      break;
    default:
      if (!this._adminService.isDgRequired()) {
        this.accessTabs = accessTabsConfidential;
      } else {
        this.accessTabs = accessTabs;
      }
      break;
    }
    if (this.accessTabs.length) {
      this.selectedTabView = {
        event: {
          key: this.accessTabs[0].key ?? this.accessTabs[0].name
        },
        index: 0
      };
    }
    this._titleService.setTitle(`${title.bayaan} | Controlpanel`);
  }

  ngOnInit() {
    if (this.superUserRoles.includes(this.accessType)) {
      this.pageHeading = `${this._translate.instant('Superuser Access Panel')}`;
      this.getAccessPolicy();
    } else if (this.accessType === this.role.dg) {
      this.pageHeading = `${this._translate.instant(this._adminService.userDesignation)} ${this._translate.instant('User Access Panel')}`;
      this.getAccessPolicy();
    } else {
      this.pageHeading = 'Product Engagement - User Access Panel';
      this.entity = '';
      this.subs.add(
        this._apiService.getMethodRequest(productEngagement.entityList).subscribe((res: any) => {
          if (res) {
            this.entityList = res;
          }
        })
      );
    }
    if (this.accessType !== this.role.normalUser) {
      if (!this._adminService.isDgRequired()) {
        const sensitiveIndex = this.classificationList.findIndex((classification: any) => classification.value === dataClassification.sensitive.toLowerCase());
        if (sensitiveIndex >= 0) {
          this.classificationList.splice(sensitiveIndex, 1);
        }
      }
      this.getNewRequestsList();
      this.getCompletedList();
      if (this.accessType !== this.role.pePrimary) {
        this.getPendingRequestList();
      }
    }
  }

  changeTabView(event: SelectedTabView) {
    // this.saveForm.set(this.selectedTabView.event.key !== 'invite');
    this.selectedTabView = event;
    this.page = 1;
    this.limit = 10;
    this.getSelectedTabData(event.event.key);
    // if (this.selectedTabView.event.key === 'invite') {
    //   this.inviteFormValue =
    // }
  }

  getSelectedTabData(tabName: string) {
    if (this.accessType) {
      this.apiParams.page = this.page;
      this.apiParams.limit = this.limit;
      switch (tabName) {
      case 'new': this.getNewRequestsList();
        break;
      case 'existing': this.getCompletedList();
        break;
      case 'pending': this.getPendingRequestList();
        break;
      case 'deleted': this.getDeletedUsersList();
        break;
      default: return;
      }
      this._cdr.detectChanges();
    }
  }

  // Get new requests
  getNewRequestsList() {
    const isDgEndPoint = this.isDg ? dg.newRequests : productEngagement.newRequests;
    const endPoint = this.superUserRoles.includes(this.accessType) ? superUser.newRequests : isDgEndPoint;
    this.subs.add(
      this._apiService.getMethodRequest(endPoint, this.apiParams).subscribe((res: RequestData) => {
        this.newRequestList = res.data;
        this.size = res.total;
        this.setTabCount('new', res.total);
      })
    );
  }

  // Get approved requests
  getPendingRequestList() {
    const isDgEndPoint = this.isDg ? dg.pendingRequests : productEngagement.pendingRequests;
    const endPoint = this.superUserRoles.includes(this.accessType) ? superUser.pendingRequests : isDgEndPoint;
    this.subs.add(
      this._apiService.getMethodRequest(endPoint, this.apiParams).subscribe((res: RequestData) => {
        this.approvedList = res.data;
        this.size = res.total;
        this.setTabCount('pending', res.total);
      })
    );
  }

  // Get completed/existing users
  getCompletedList() {
    const isDgEndPoint = this.isDg ? dg.completedRequests : productEngagement.completedRequests;
    const endPoint = this.superUserRoles.includes(this.accessType) ? superUser.completedRequests : isDgEndPoint;
    this.subs.add(
      this._apiService.getMethodRequest(endPoint, this.apiParams).subscribe((res: RequestData) => {
        this.existingUserEmails = [];
        this.completedRequestList = res.data;
        this.size = res.total;
        this.existingUserEmails = this.completedRequestList?.filter((item) => item.role === role.normalUser).map((existingUser: RequestList) => existingUser.email);
      })
    );
  }

  // Get deleted users list
  getDeletedUsersList() {
    let endPoint;
    let params = {};
    if (this.superUserRoles.includes(this.accessType)) {
      endPoint = superUser.deletedList;
      params = {entityId: this._adminService.userEntity.id};
    } else {
      endPoint = productEngagement.deletedList;
      params = {entityId: this.apiParams.entity ?? ''};
    }
    this.subs.add(
      this._apiService.getMethodRequest(endPoint, params).subscribe((res: DeletedList[]) => {
        this.deletedUsersList = res;
        // this.size = res.total;
      })
    );
  }

  // Manage Access

  setUserAccess(userData: SubmitUserData, index: number = 0) {
    const isDgEndPoint = this.isDg ? dg.manageAccess : productEngagement.manageAccess;
    const endPoint = this.superUserRoles.includes(this.accessType) ? superUser.manageAccess : isDgEndPoint;
    this.subs.add(
      this._apiService.postMethodRequest(endPoint, userData).subscribe((res) => {
        if (userData.action !== 'revoke') {
          this._toastrService.success(res.message);
          const currentList = this.newRequestList;
          currentList.forEach((item: RequestList, i: number) => {
            if (item.id === userData.userId) {
              currentList.splice(i, 1);
              this.size -= 1;
              this.accessTabs.forEach((tabItem: LabelData) => {
                if (tabItem.key === 'new') {
                  tabItem.count = tabItem.count ? tabItem.count - 1 : 0;
                }
                if (tabItem.key === 'pending' && userData.action !== 'reject') {
                  tabItem.count = tabItem.count ? tabItem.count + 1 : 0;
                }
              });
            }
          });
        } else {
          this.completedRequestList[index].access.forEach((item: AccessRequests) => {
            if ((item.classification?.classification.toLowerCase() === 'sensitive') && item.isSelected) {
              item.isSelected = false;
            }
          });
          this._toastrService.success('All sensitive access has been removed');
          this.completedRequestList[index].maxClassification = dataClassification.confidential;
        }
      })
    );
    this._cdr.detectChanges();
  }

  inviteUser(data: any) {
    this.subs.add(
      this._apiService.postMethodRequest(superUser.shareLink, data).subscribe({
        next: (res)=> {
          this._toastrService.success(res.message);
          this.isSubmitted = !this.isSubmitted;
        },
        error: error => {
          this._toastrService.error(error.error.message);
        }
      })
    );
  }

  inviteDg(data: any) {
    this.subs.add(
      this._apiService.postMethodRequest(superUser.inviteDg, data).subscribe({
        next: (res)=> {
          this._toastrService.success(res.message);
          this.isSubmitted = !this.isSubmitted;
          this._adminService.dgStatus.set(dgStatus.inviteSent);
        },
        error: error => {
          this._toastrService.error(error.error.message);
        }
      })
    );
  }

  getAccessList(domainList: string[], classification: string) {
    const access = domainList.map((item: string) => {
      return {domain: item, classification: classification};
    });
    return access;
  }

  setTabCount(tabName: string, count: number) {
    this.accessTabs.forEach((tabItem: LabelData) => {
      if (tabItem.key === tabName) {
        tabItem.count = count;
      }
    });
  }

  sortClick(event: any) {
    this.sortValue = event;
    this.page = 1;
    this.limit = 10;
    this.apiParams = {
      page: this.page,
      limit: this.limit,
      // sortBy: event.key,
      // order: event.value,
      entity: this.selectedEntity,
      search: this.searchUser,
      userType: this.selectedUserType
    };
    this.getSelectedTabData(this.selectedTabView.event.key);
  }

  filterCards(item: any) {
    this.selectedEntity = item?.['ID'] ?? '';
    this.entity = item;
    this.apiParams.entity = this.selectedEntity;
    this.getSelectedTabData(this.selectedTabView.event.key);
  }

  onSelectUserType(item: string) {
    this.selectedUserType = item;
    this.apiParams.userType = item.toLowerCase();
    this.getSelectedTabData(this.selectedTabView.event.key);
  }

  onPageChange(page: number) {
    this.page = (page / this.limit) + 1;
    this.apiParams.page = this.page;
    this.getSelectedTabData(this.selectedTabView.event.key);
  }

  limitChanged(limit: number) {
    this.page = 1;
    this.limit = limit;
    this.apiParams.limit = limit;
    this.getSelectedTabData(this.selectedTabView.event.key);
  }

  inviteSuperUser(data: Record<string, any>) {
    this.subs.add(
      this._apiService.postMethodRequest(productEngagement.shareLink, data).subscribe({
        next: (res: any) => {
          if (res) {
            this._toastrService.success(res.message);
            this.isSubmitted = !this.isSubmitted;
          }
        },
        error: ((error: any) => {
          this._toastrService.error(error.error.message);
        })
      })
    );
  }

  getAccessPolicy() {
    this.subs.add(
      this._apiService.getMethodRequest(superUser.accessPolicy).subscribe((res: any) => {
        if (res) {
          this.accessPolicyList = res;
          // this.domainList = this.accessLevels.map((access: AccessList) => access.name);
        }
      })
    );
  }

  onDeleteUser(id: string, i: number, userDetails: RequestList) {
    const selectedTab = this.selectedTabView.event.key;
    this.subs.add(
      this._apiService.deleteWithoutQuery(user.delete+id).subscribe({
        next: () => {
          switch (selectedTab) {
          case 'new':
            this.newRequestList.splice(i, 1);
            break;
          case 'existing': this.completedRequestList.splice(i, 1);
            break;
          case 'pending': this.approvedList.splice(i, 1);
            break;
          default: return;
          }
          this.size -= 1;
          this.accessTabs.forEach((tabItem: LabelData) => {
            if (tabItem.key === selectedTab) {
              tabItem.count = tabItem.count ? tabItem.count - 1 : 0;
            }
          });
          if (userDetails.role === role.dg) {
            this._adminService.dgStatus.set(dgStatus.invitePending);
          }
          this._toastrService.success('Deleted successfully');
        },
        error: (error: any) => {
          this._toastrService.error(error.error.message);
        }
      })
    );
  }

  onLinkUser(data: any, index: number) {
    const body = {
      designation: data.designation,
      emiratesId: data.emiratesId
    };
    this.subs.add(
      this._apiService.patchMethodRequest(superUser.linkUser+data.userId, body).subscribe({
        next: () => {
          this.getCompletedList();
          this.completedRequestList[index].isLinked = true;
          if(data.emiratesId && !data.designation) {
            this._toastrService.success('Emirates ID has been linked with Bayaan successfully');
          }
          if(!data.emiratesId && data.designation) {
            this._toastrService.success('Job title has been updated with Bayaan successfully');
          }
          if(data.emiratesId && data.designation) {
            this._toastrService.success('Emirates ID and Job title has been updated with Bayaan successfully');
          }
        },
        error: (error) => {
          this._toastrService.success(error.error.message);
        }
      })
    );
  }

  // onManageAllRequests(requestList: RequestList[], status: string) {

  // }

  onSearchUser(keyWord: string) {
    this.apiParams.search = keyWord;
    this.getSelectedTabData(this.selectedTabView.event.key);
  }

  updateUserAccess(data: EditAccess) {
    this.subs.add(
      this._apiService.postMethodRequest(superUser.editAccess, data).subscribe({
        next: (res) => {
          this.getCompletedList();
          if (res) {
            this._toastrService.success(res?.message);
          }
        },
        error: (res) => {
          if (res) {
            this._toastrService.error(res?.error?.message);
          }
        }
      })
    );
  }

  exportUsers() {
    let endPoint;
    let params;
    if (peUserRoles.includes(this.accessType)) {
      endPoint = productEngagement.exportUsers;
      params = {entityId: this.selectedEntity, userType: this.selectedUserType.toLowerCase()};
    } else {
      endPoint = superUser.exportUsers;
      params = {entityId: this._adminService.userEntity.id, userType: this.selectedUserType.toLowerCase()};
    }
    this.subs.add(
      this._apiService.getDownloadRequest(`${environment.baseUrl}${environment.apiVersion}${endPoint}`, params).subscribe( {next: value=> {
        const matches = (value.headers.get('Content-Disposition'))?.match(/"(.*?)"/);
        const nameValue = matches ? matches[1] : 'Bayaan_Users_Export';
        this._downloadService.downloadFiles(value.body, nameValue);
      },
      error: (error) => {
        this._toastrService.error(error.error.message);
      }
      })
    );
  }

  downloadUserManual() {
    const type = this._adminService.isDgRequired() ? 'sensitive' : 'confidential';
    this.subs.add(
      this._apiService.getDownloadRequest(`${environment.baseUrl}${environment.apiVersion}${userGuide}`, {type: type}).subscribe( {next: value=> {
        const matches = (value.headers.get('Content-Disposition'))?.match(/"(.*?)"/);
        const nameValue = matches ? matches[1] : 'Bayaan_User_Onboarding_Manual';
        this._downloadService.downloadFiles(value.body, nameValue);
      },
      error: (error) => {
        this._toastrService.error(error.error.message);
      }
      })
    );
  }

  onSelectOptions(event: string) {
    switch (event) {
    case 'export':
      this.exportUsers();
      break;
    case 'downloadUserGuide':
      this.downloadUserManual();
      break;
    default:
      break;
    }
  }

  setInviteFormData(formData: Record<string, FormGroup>) {
    this.inviteFormValue = formData;
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}

interface SelectedTabView {
  event: { key: string };
  index: number;
}
