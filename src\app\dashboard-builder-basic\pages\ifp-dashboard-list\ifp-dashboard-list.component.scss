@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host::ng-deep {
  .ifp-db-list-card {
    &__options {
      display: inline-block;

      .ifp-kebab-menu__btn {
        background-color: $ifp-color-black-50;
        backdrop-filter: blur(24);
        color: $ifp-color-white-global;
      }
    }
  }
}

.ifp-db-list {
  // padding: $spacer-6 $spacer-0;

  &__container {
    min-height: calc(100vh - 395px);
    display: flex;
    flex-direction: column;
  }

  &__bg {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: top;
    top: 0;
    left: 0;
  }

  &__head {
    display: flex;
    align-items: flex-start;
    margin-bottom: $spacer-5;
  }

  &__action {
    display: flex;
    align-items: center;
  }

  &__head-item {
    flex: 1;

    &--middle {
      text-align: center;
      margin: $spacer-0 $spacer-3;
      flex: 2;
    }

    &--right {
      text-align: right;
      display: flex;
      align-items: center;
    }
  }

  &__heading {
    font-size: $ifp-fs-12;
    font-weight: $fw-bold;
    margin-bottom: $spacer-3;
  }

  &__desc {
    font-size: $ifp-fs-4;
    font-weight: $fw-medium;
    color: $ifp-color-grey-9;
    margin-bottom: $spacer-3;
  }

  &__manage-list {
    display: flex;
    align-items: center;
    margin: $spacer-5 (
      -$spacer-2) $spacer-0;
  }

  &__wrapper {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-0 (
      -$spacer-2) $spacer-5;
  }

  &__card {
    margin: $spacer-2;
    width: calc(25% - (2 * $spacer-2)
    );
  cursor: pointer;
}

&__search {
  margin: $spacer-0 $spacer-3;
}

&__filter-text,
&__filter-icon {
  font-size: $ifp-fs-4;
}

&__filter-text {
  color: $ifp-color-secondary-grey;
  font-weight: $fw-semi-bold;
  width: 100%;
  transition: 0.3s;
}

&__filter-icon {
  width: 35px;
  height: 35px;
  min-width: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: 0.3s;
}

&__head-btn {
  margin: $spacer-0 $spacer-2;
}

&__filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: $spacer-2 $spacer-2 $spacer-2 $spacer-3;
  border: 1px solid $ifp-color-grey-3;
  background-color: $ifp-color-white;
  border-radius: 40px;
  cursor: pointer;
  transition: 0.3s;

  &:hover {
    border: 1px solid $ifp-color-active-blue;

    .ifp-db-list {

      &__filter-text,
      &__filter-icon {
        color: $ifp-color-active-blue;
      }
    }
  }

  &--active {
    background-color: $ifp-color-active-blue;

    .ifp-db-list {

      &__filter-text,
      &__filter-icon {
        color: $ifp-color-white-global;
      }
    }

    &:hover {
      .ifp-db-list {

        &__filter-text,
        &__filter-icon {
          color: $ifp-color-white-global;
        }
      }
    }
  }

  &--disabled {
    background-color: $ifp-color-grey-7;
    border: 1px solid $ifp-color-grey-7;
    pointer-events: none;
  }
}

&__pagination {
  margin-top: auto;
}

&__no-data {
  width: 100%;
}

&__tab {
  margin-bottom: $spacer-3;
  align-self: flex-start;
}

&__header-back {
  display: flex;
}

&__header-back-text {
  line-height: 1;
  font-weight: 600;
  margin-left: 4px;
}

&__hr-tab-wrapper {
  display: flex;
}

&__action-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: $spacer-3;
}

&__filter-item-wrapper {
  display: flex;
  align-items: center;
  margin-inline-start: auto;
}

&__filter-item-title {
  margin-inline-end: $spacer-2;
}

&__loader-wrapper {
  display: flex;
  width: 100%;
}

&__htab {
  width: 15%;
  margin-inline-end: $spacer-4;
}

&__ifp-loader {
  margin: $spacer-2;
}

&__card-wrapper {
  width: 85%;
}
}

@include desktop-sm {
  .ifp-db-list {
    &__card {
      width: calc(33.33% - (2 * $spacer-2));
    }
  }
}

@include mobile-tablet {
  .ifp-db-list {
    &__head {
      flex-wrap: wrap;
    }

    &__head-item {

      &--left,
      &--right {
        width: 50%;
      }

      &--left {
        order: 1;
      }

      &--right {
        order: 2;
      }

      &--middle {
        order: 3;
        margin: $spacer-3 $spacer-0 $spacer-0;
      }
    }

    &__card {
      width: calc(50% - (2 * $spacer-2));
    }
  }
}


:host-context([dir="rtl"]) {
  .ifp-db-list {
    &__bg {
      right: 0;
      left: auto;
    }

    &__head-item {
      &--right {
        text-align: left;
      }
    }
  }
}

:host::ng-deep {
  .ifp-db-list {
    &__tab {
      .ifp-category-label__txt {
        font-size: $ifp-fs-4;
      }
    }
  }
}