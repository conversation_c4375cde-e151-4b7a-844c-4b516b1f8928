.geospatial-revamp-container {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 0 60px 10px 60px;
  margin: 20px 0px;
  background-image: url("../../../../assets/images/geospatial-ravamp/bg.png");
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 10px;
}
 
.geospatial-revamp-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(53, 130, 198, 0.5); 
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 10px; 
  pointer-events: none;
}

.geospatial-revamp-header{
  position: relative;
  z-index: 2;
  top: 30px;
  width: 60%;
}

.geospatial-revamp-header h1{
  font-size: 30px;
  color: #FFF;
  font-weight: bold;
}

.geospatial-revamp-container-dark {
  .geospatial-revamp-header{
    h1, p{
      color: #000;
    }
  }
  .geospatial-revamp-box {
    background: #17191e;
    p{
      color: #888!important;
    }
  }
}

.geospatial-revamp-header p{
  font-size: 18px;
  color: #FFF;
  padding-top: 15px;
}

.geospatial-revamp-body {
  height: 250px;
  margin-top: -70px;
  border-radius: 20px;
  position: relative;
}

.revamp-box-container{
  position: relative;
  display: flex;
  gap: 30px;
  margin-top: 125px;
  z-index: 4;
}

.geospatial-revamp-box {
  display: flex;
  z-index: 1000;
  width: 100%;
  max-width: 250px;
  border-radius: 20px;
  height: 80px;
  background: #FFFFFF;
  transition: height 0.5s ease, background 0.5s ease;
  z-index: 1;
}

.loading{
  align-items: center;
  margin-top: 100px!important;
  color: #FFF;
}

// Value container and label styles
.value-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.value-number {
  margin: 0;
  line-height: 1.2;
}

.value-label {
  font-size: 10px;
  color: #888;
  font-weight: normal;
  margin-top: 2px;
  // text-transform: uppercase;
  letter-spacing: 0.3px;
}

@media only screen and (max-width: 900px) {
  .revamp-box-container{
    width: 90%;
  }

  .geospatial-revamp-header{
      width: 100%;
    }

  .geospatial-revamp-body {
    height: 300px;
  }

  .geospatial-revamp-box{
    height: 110px;
  }

  .geospatial-revamp-box-content{
    display: block!important;
  }

  .geospatial-revamp-box-checkbox{
    display: block!important;
  }

  .geospatial-revamp-box-text{   
    display: block!important; 
    text-align: start;  
  }

  .domain-name{
    color: #4b452d!important;
  }

  .estate-box img{
    width: 40px;
  }

  // Mobile adjustments for labels
  .value-label {
    font-size: 9px;
  }
}

.geospatial-revamp-box:nth-child(n) {
    margin-top: 10px;
}

/* Hover state */
.geospatial-revamp-box:hover {
  height: 80px; 
}

.pop-box:hover {
  background: linear-gradient(129deg, #C3DCE5, #97C2D3); 
}
.lab-box:hover {
  background: linear-gradient(129deg, #BAD2CA, #84B0A2); 
}
.estate-box:hover {
  background: linear-gradient(129deg, #DFD3AD, #C5B16B); 
}
.addb-box:hover {
  background: linear-gradient(129deg, #AEC4DD, #6E94C1); 
}

.geospatial-revamp-box {
  display: flex;
  justify-content: space-between;
  padding: 15px;
}

.geospatial-revamp-box-checkbox {
  display: flex;
}

.geospatial-revamp-box-checkbox .arrow-icon{
  display: none;
}

.geospatial-revamp-box-content {
  display: flex;
  justify-content: space-evenly;
}

.geospatial-revamp-box-text{
  display: flex;
  flex-direction: column;
  align-items: start;
  padding: 0px 10px;
  font-weight: bold;
}

.hidden {
  display: none!important;
}

.show{
  display: block!important;
}

.map-maintenanace{
  position: relative;
  img{border-radius: 15px!important;}
  .map-maintenanace-content{
    position: absolute;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;  
    align-items: center;    
    height: 100%;   
    width: 100%;
    color: #000;
    span{font-size: 2rem; width: 60%;}
    h3{
      font-size: 2.4rem; 
      margin-bottom: 25px;
      font-weight: bold;
    }
  }
  .dark-theme{
    color: #FFF;
  }
}