import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { IfpDbChartPropsComponent } from '../ifp-db-chart-props/ifp-db-chart-props.component';
import { IfpToggleButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-toggle-button/ifp-toggle-button.component';
import { DashboardService } from 'src/app/scad-insights/core/services/create-dashboard/dashboard.service';

@Component({
    selector: 'ifp-ifp-chart-settings',
    templateUrl: './ifp-chart-settings.component.html',
    styleUrl: './ifp-chart-settings.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        CommonModule,
        IfpDbChartPropsComponent,
        IfpToggleButtonComponent
    ]
})
export class IfpChartSettingsComponent implements OnChanges {


  @Input() options: { name: string, key: string, value: boolean }[] = [];
  @Input() selectedCard!: string;
  @Input() cntType!: string;
  @Output() updateSetingsValue: EventEmitter<{ key: string, value: boolean }> = new EventEmitter<{ key: string, value: boolean }>();


  constructor(private _dashboardService: DashboardService) { }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedCard'] && this.options?.length > 0) {
      const chartSettings = this._dashboardService.chartSettings[this.cntType];
      if (chartSettings) {
        const index = chartSettings.findIndex((x: { id: string; }) => x.id === this.selectedCard);
        if (index >= 0) {
          const indexObject = chartSettings[index];
          this.options.forEach((option, i) => {
            const key = this.options[i].key;
            option.value = indexObject[key] ?? (this.options[i].key == 'preciseValue' ? false: true);
          });
          return;
        }
      }
    }
    this.options.forEach((option, i) => {
      option.value = (i === 1) ? false : true;
    });
  }


  changeValues(event: any, opt: any) {
    opt.value = event;
    this.updateSetingsValue.emit({ key: opt.key, value: event });
  }
}
