<div 
  class="ifp-tab-badge" 
  #scrollContainer
  [ngClass]="{
    'ifp-tab-badge--transparent': isTransparent, 
    'ifp-tab-badge--gen-ai': genAi(), 
    'ifp-tab-badge--at-left-end': isAtLeftEnd(), 
    'ifp-tab-badge--at-right-end': isAtRightEnd()
  }" 
  (scroll)="checkScrollPosition($event)"
  (mousedown)="onMouseDown($event)"
  (mousemove)="onMouseMove($event)"
  (mouseup)="onMouseUp()"
  (mouseleave)="onMouseLeave()"
  (touchstart)="onTouchStart($event)"
  (touchmove)="onTouchMove($event)"
  (touchend)="onTouchEnd()">
  @for (item of data; track item.name) {
    <span 
      class="ifp-tab-badge__items" 
      (click)="selectData(item)" 
      [ngClass]="{'ifp-tab-badge__items--active': item.key == selectedValue}">
      {{item.name | translate}}
    </span>
  }
</div>
