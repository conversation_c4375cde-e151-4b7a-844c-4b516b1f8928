@use "../../../../assets/ifp-styles/abstracts/index" as *;

:host {
  display: flex;
  align-items: flex-start;
  position: relative;
}

.ifp-db__breadcrumb-section {
  background-color: $ifp-color-section-white;
  padding: $spacer-2 $spacer-0;
  border-bottom: 1px solid $ifp-color-grey-13;
  position: relative;
  z-index: 999;
}

.ifp-db {
  $toolbar-expand-width: 480px;
  // width: calc(100% - $spacer-5);
  width: 100%;
  background-size: contain;
  background-position: center;
  min-height: 100vh;
  background-image: url("../../../../assets/images/dashboard-builder/dashboard-builder-bg.png");

  &__wrapper {
    &--bg {
      background-color: $ifp-color-grey-bg;
    }
  }
  &__page-header,
  &__header,
  &__footer-box {
    background-color: $ifp-color-section-white;
  }

  &__page-header {
    padding: $spacer-3 $spacer-0;
  }

  &__outer {
    position: relative;
  }

  &__header-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin: $spacer-0 (
    //   -$spacer-2
    // );
  }

  &__header-right,
  &__header-left {
    display: flex;
    align-items: center;
  }

  &__header-back {
    margin-inline-end: $spacer-5;
    display: flex;
  }

  &__header-back-label {
    margin: $spacer-1 $spacer-5;
    display: flex;
    font-size: $ifp-fs-4;
    align-items: center;
    white-space: nowrap;
  }

  &__header-back-link {
    color: $ifp-color-blue-hover;
    margin-left: $spacer-1;
  }

  &__page-title {
    font-size: $ifp-fs-7;
    font-weight: $fw-semi-bold;
  }

  &__header-btn,
  &__footer-btn {
    margin-inline-start: $spacer-3;
  }

  &__header,
  &__footer-box {
    padding: $spacer-3 $spacer-4;
    border: 1px solid $ifp-color-grey-13;
    display: flex;
    align-items: center;
  }

  &__header {
    // border-radius: 8px;
    font-size: $ifp-fs-6;
    font-weight: $fw-semi-bold;
    justify-content: space-between;
  }

  /* Align user abbreviation chips to the extreme right of the header */
  &__header-users {
    display: flex;
    align-items: center;
    margin-inline-end: $spacer-3;
  }

  &__footer-box {
    justify-content: space-between;
    border-radius: 10px;
    position: sticky;
    bottom: 0;
    left: 0;
    z-index: 1;
    // Override for government affairs dashboard

    // When only revert button is shown (no toggle), align content to the right
    &--revert-only {
      justify-content: flex-end !important;
    }
  }

  &__footer-toggle {
    display: flex;
    align-items: center;
    margin-inline-end: $spacer-4;
  }

  &__footer-toggle-btn {
    margin-inline-start: $spacer-2;
  }

  &__footer-btn-sec {
    display: flex;

    // When only one button (revert) is shown, keep it aligned to the right
    &--single {
      justify-content: flex-end !important;
      margin-left: auto;
      width: auto;

      .ifp-db__footer-btn {
        margin-left: auto;
      }
    }
  }

  // &__header-title-container {
  //   position: relative;

  //   .ifp-icon {
  //     font-size: $ifp-fs-5;
  //     color: $ifp-color-black;
  //     opacity: 0;
  //     visibility: hidden;
  //     transition: 0.3s;
  //     margin-inline-start: (-$spacer-1);
  //     transform: translateX(-100%);
  //   }

  //   &:hover {
  //     .ifp-icon {
  //       opacity: 1;
  //       visibility: visible;
  //     }
  //   }
  // }

  &__header-title {
    font-size: $ifp-fs-6;
    font-weight: $fw-medium;
    transition: 0.3s;
    border-radius: 3px;
    padding: $spacer-1 $spacer-2;
    padding-inline-end: $spacer-5;
    background-color: transparent;
    color: $ifp-color-black;
    min-width: 200px;
    field-sizing: content;
    &:focus {
      background-color: $ifp-color-violet-light;
      border: 1px solid $ifp-color-blue-med;
      padding-right: $spacer-5;

      &+.ifp-icon {
        opacity: 1;
        visibility: visible;
      }
    }
  }

  &__inner-wrapper {
    display: flex;
    flex-wrap: wrap;
  }

  &__card {
    display: flex;
    height: 100%;

    // margin: $spacer-3;
    // width: calc(25% - (2 * $spacer-3));
    &--active {
      border: 1px solid $ifp-color-secondary-blue-dark;
    }

    &--custom,
    &--add-indicator {
      margin: $spacer-3;
      width: calc(25% - (2 * $spacer-3));
    }

    &--add-indicator {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border: 2px dashed $ifp-color-blue-hover;
      background-color: $ifp-color-violet-light;
      border-radius: 10px;
      color: $ifp-color-blue-hover;
      max-width: 300px;
      min-height: 300px;
      font-size: $ifp-fs-7;
      position: relative;
      z-index: 1;

      &>.ifp-icon {
        font-size: $ifp-fs-13;
        margin-bottom: $spacer-1;
      }
    }

    &--template-switch {
      background-color: $ifp-color-section-white !important;
      color: $ifp-color-black !important
    }

    &--template {
      &::ng-deep {
        .ifp-kpi-temp {
          width: 100%;
        }
      }
    }
  }

  &__add-text {
    font-size: $ifp-fs-7;
    font-weight: $fw-medium;
  }

  &__add-type {
    box-shadow: 0 32px 96px -12px $ifp-color-black-16;
    border-radius: 24px;
    padding: $spacer-0 $spacer-3;
    background-color: $ifp-color-white;
    opacity: 0;
    visibility: hidden;
    z-index: 1;
    position: absolute;
    top: 100%;
    left: 100%;
    margin: (-$spacer-4) $spacer-0 $spacer-0 (
      -$spacer-4
    );
  transition: 0.3s;

  &--show {
    opacity: 1;
    visibility: visible;
  }
}

&__logo-sec {
  display: flex;
  align-items: center;
}

&__logo {
  max-height: 40px;
  width: auto;
  margin-inline-start: $spacer-2;
}

&__add-type-item {
  font-size: $ifp-fs-6;
  color: $ifp-color-secondary-grey;
  display: flex;
  align-items: center;
  padding: $spacer-3 $spacer-0;
  border-bottom: 1px solid $ifp-color-grey-7;
  white-space: nowrap;
  transition: 0.3s;

  &:last-child {
    border-bottom: none;
  }

  .ifp-icon {
    color: $ifp-color-blue-hover;
    margin-right: $spacer-2;
    font-size: inherit;
  }

  &:hover {
    color: $ifp-color-blue-hover;
  }
}

&__logo-text {
  color: $ifp-color-grey-9;
  margin: $spacer-0 $spacer-3;
}

&__header-action-sec {
  display: flex;
  margin: $spacer-0 (
    -$spacer-2
  );
}

&__header-action-btn {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 1px solid $ifp-color-grey-7;
  padding: $spacer-1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: $ifp-fs-5;
  transition: 0.3s;
  color: $ifp-color-active-blue;
  margin: $spacer-0 $spacer-2;
  cursor: pointer;

  .ifp-icon {
    font-size: inherit;
  }

  &:hover {
    background-color: $ifp-color-active-blue;
    color: $ifp-color-white;
    border: 1px solid $ifp-color-active-blue;
  }

  &--disabled {
    pointer-events: none;
    color: $ifp-color-grey-disabled;
  }
}

&__header-btn-sec {
  margin-left: $spacer-6;
  padding-left: $spacer-6;
  border-left: 1px solid $ifp-color-grey-7;
}

&__toolbar-inner {
  position: relative;
  padding-left: $spacer-5;
  transition: 0.5s;

  &--drag {
    cursor: move;
  }
}

&__toolbar-toggle {
  width: 25px;
  height: 25px;
  text-align: center;
  color: $ifp-color-white-global;
  border-radius: 50%;
  background-color: $ifp-color-secondary-blue-dark;
  position: absolute;
  top: 60px;
  left: 0;
  transform: translateX(-50%);
  transition: 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;

  &::before {
    margin: 2px $spacer-1 $spacer-0 $spacer-0;
  }

  .ifp-icon {
    font-size: $ifp-fs-6;
  }
}

&__import-item {
  display: block;
  margin-bottom: $spacer-2;
}

&__toolbar {
  min-height: calc(100vh - $ifp-header-height-inner);
  width: 100%;
  max-width: 30px;
  // position: fixed;
  // right: 0;
  // top: $ifp-header-height-inner;
  // z-index: 1;
  transition: max-width 0.3s;
  background-color: $ifp-color-section-white;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.16);
  &--sticky {
    position: sticky;
    // position: fixed;
    top: $ifp-header-height-sticky;
    animation: slide-down 0.5s forwards;
    // .ifp-db__toolbar-inner {
    //   animation: slide-down 0.5s forwards;
    // }
  }

  &--right {
    right: 0;
  }

  &--left {
    .ifp-db__toolbar-toggle {
      left: auto;
      right: 0;
      transform: rotate(180deg) translateX(-50%);
    }

    &:not(.ifp-db__toolbar--expand) {
      left: 0;
    }
  }

  &--expand {
    // transform: translateX(0);
    max-width: $toolbar-expand-width;
    padding-left: $spacer-0;

    .ifp-db {
      &__toolbar-toggle {
        transform: rotate(180deg) translateX(50%);
      }

      &__toolbar-inner {
        padding-left: $spacer-0;
      }
    }

    &.ifp-db__toolbar--left {
      .ifp-db__toolbar-toggle {
        transform: translateX(50%);
      }
    }
  }

  &--filter {
    display: flex;

    .ifp-db {
      &__toolbar-inner {
        width: 100%;
      }
    }
  }



}

// view mode start
&__head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  // position: absolute;
  // top: 0;
  // left: 0;
  padding: $spacer-4 $spacer-0;
}

&__message {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

&__message-text {
  font-size: $ifp-fs-5;
  font-weight: $fw-medium;
}

&__message-btn-sec {
  margin: $spacer-0 $spacer-2;
}

&__message-btn {
  margin: $spacer-0 $spacer-2;
}

&__body {
  position: relative;
  padding: $spacer-0 $spacer-6;
}

&__btn-round-text {
  margin-top: $spacer-1;
  display: block;
  color: inherit;
  margin-left: $spacer-3;
  margin-right: $spacer-3;
}

&__btn-round {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: $ifp-fs-5;
  cursor: pointer;

  .ifp-icon {
    font-size: inherit;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 1px solid $ifp-color-grey-3;
    padding: $spacer-1;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.3s;
  }

  &:hover {
    color: $ifp-color-active-blue;

    .ifp-icon {
      color: $ifp-color-white;
      background-color: $ifp-color-active-blue;
      border: 1px solid $ifp-color-active-blue;
    }
  }
}

&__preview-tag {
  display: none;
}

&__add-indicator {
  color: $ifp-color-blue-hover;
  position: fixed;
  top: 50%;
  left: -1px;
  background-color: $ifp-color-violet-light;
  padding: $spacer-3;
  border: 1px dashed $ifp-color-blue-hover;
  z-index: 1;
  border-radius: 0 30px 30px 0;
  cursor: pointer;
  transition: 0.4s;
  text-align: center;

  .ifp-icon {
    font-size: inherit;
    margin-right: $spacer-2;
    display: inline-block;
  }

  &:hover {
    color: $ifp-color-white;
    background-color: $ifp-color-blue-hover;

    .ifp-db {
      &__add-indicator-text {
        max-width: 500px;
        color: $ifp-color-white;
      }
    }
  }

  &--right {
    left: auto;
    right: -1px;
    border-radius: 30px 0 0 30px;

    .ifp-db__import-pop-up {
      left: auto;
      right: 100%;
      margin: (-$spacer-2) (-$spacer-2) $spacer-0 $spacer-0;
    }
  }
}

&__add-indicator-relative {
  position: relative;
  display: flex;
  font-size: $ifp-fs-5;
}

&__add-indicator-text {
  color: $ifp-color-blue-hover;
  white-space: nowrap;
  overflow: hidden;
  font-size: inherit;
}

&__head-left {
  display: flex;
  align-items: center;
}

&__recieved-detail {
  display: flex;
  align-items: center;
}

&__text {
  color: $ifp-color-tertiary-text;
  margin-inline-end: $spacer-2;
  .ifp-icon {
    color: inherit;
    display: inline-block;
    margin-inline-end: $spacer-1;
  }
}

&__db-name-wrapper {
  display: flex;
  align-items: center;
  .ifp-icon {
    font-size: $ifp-fs-5;
    color: $ifp-color-black;
    opacity: 0;
    visibility: hidden;
    transition: 0.3s;
    margin-inline-start: (-$spacer-1);
    transform: translateX(-100%);
  }
  &:hover {
    .ifp-icon {
      opacity: 1;
      visibility: visible;
    }
  }
}

&--preview {
  .ifp-db {
    background-image: none;

    &__wrapper {
      position: relative;
      padding: $spacer-5;
      border: 1px solid $ifp-color-grey-3;
      border-radius: 0 5px 5px 5px;
      margin: $spacer-0;
    }

    &__preview-tag {
      display: inline-block;
      background-color: $ifp-color-grey-5;
      color: $ifp-color-white;
      padding: $spacer-1 $spacer-4;
      border-radius: 5px 5px 0 0;
      position: relative;
      margin-bottom: -1px;
      text-transform: capitalize;

      &::after {
        content: "";
        position: absolute;
        right: -8px;
        bottom: 0;
        width: 0;
        height: 0;
        border-right: 9px solid transparent;
        border-left: 9px solid transparent;
        border-bottom: 24px solid $ifp-color-grey-5;
      }
    }
  }
}

&--detail,
&--preview {
  background-image: none;
}

&__loader {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

&__import-pop-up {
  position: absolute;
  top: 100%;
  left: 100%;
  margin: (-$spacer-4) $spacer-0 $spacer-0 (
    -$spacer-4
  );
}

&__validation {
  font-size: $ifp-fs-3;
  color: $ifp-color-red;
  margin-top: $spacer-1;
}

&__upload-model {
  width: 100%;
}

&__tabs-wrapper {
  display: flex;
  border-bottom: 1px solid $ifp-color-grey-13;
  margin-bottom: $spacer-4;
  background-color: $ifp-color-grey-bg;
  position: relative;

  &::after {
    content: "";
    width: 100%;
    height: 15px;
    background-color: $ifp-color-grey-bg-2;
    position: absolute;
    top: 0;
    left: 0;
  }

  // &::before,
  // &::after {
  //   content: "";
  //   position: absolute;
  //   left: 0;
  //   width: 100%;
  //   height: 25px;
  // }
  // &::before {
  //   background-color: $ifp-color-grey-bg;
  // }
  // &::after {
  //   background-color: $ifp-color-grey-bg-2;
  // }
}

&__tab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: $ifp-color-grey-9;
  background-color: $ifp-color-grey-bg-2;
  padding: ($spacer-2 + 2px) $spacer-4;
  width: 12.5%;
  cursor: pointer;
  position: relative;
  z-index: 1;

  &--add {
    width: auto;
    border-radius: 0;
    border-inline-start: 1px solid $ifp-color-grey-13;

    &::before {
      content: none;
    }

    .ifp-db__tab-icon {
      font-size: $ifp-fs-2;
      font-weight: $fw-bold;
      margin: $spacer-0;
    }
  }

  &--spacer {
    width: 100%;
    padding: $spacer-0;
    cursor: default;
  }

  &--active {
    border-radius: 20px 20px 0 0;
    background-color: $ifp-color-grey-bg;
    z-index: 1;
    min-width: 200px;
    &::before {
      content: none;
    }

    .ifp-db__tab-title {
      &:focus {
        box-shadow: 0 0 0 2px $ifp-color-blue-med;
      }
    }

    &+.ifp-db__tab {
      border-bottom-left-radius: 20px;

      &--add {
        border-inline-start: 0;

        &-disabled {
          pointer-events: none;

          .ifp-icon {
            opacity: 0.5;
          }
        }
      }
    }

    &:first-child {
      border-radius: 0 20px 0 0;
    }
    &--disabled {
      pointer-events: none;
      opacity: 0.3;
    }
  }

  &--active-prev {
    border-bottom-right-radius: 20px;
  }
}

&__tab-icon {
  margin-inline-start: $spacer-3;
  font-size: $ifp-fs-1;
  cursor: pointer;
  color: inherit;

  &--plus {
    font-weight: $fw-bold;
    font-size: $ifp-fs-2;
  }
}

&__tab-title {
  font-weight: $fw-medium;
  font-size: $ifp-fs-5;
  color: inherit;
  text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 100%;
  border: 0;
  padding: $spacer-1;
  border-radius: 7px;
  background-color: transparent;
  transition: 0.3s;
  &:read-only {
    cursor: pointer;
    pointer-events: all;
    box-shadow: none !important;
  }
}

// view mode end

// Comments start
&__revert-inline {
  font-size: $ifp-fs-5;
  font-weight: $fw-bold;
  display: inline-flex;
  align-items: center;
  transition: 0.3s;
}

&__tool-bar-header {
  position: relative;
}

&__tool-bar-opener {
  cursor: pointer;
  position: absolute;
  top: 40px;
  left: 0;
  padding: $spacer-2;
  background-color: $ifp-color-white;
  transform: translate(-100%, -50%);
  color: $ifp-color-secondary-blue;
  border-start-start-radius: 33px;
  border-end-start-radius: 33px;
  padding-inline-end: $spacer-0;
  border: 1px solid $ifp-color-grey-7;
}

&__tool-bar-opener-icon {
  padding-inline-start: $spacer-2;
  padding-inline-end: $spacer-3;
  border-right: 1px solid $ifp-color-grey-7;
}

&__revert-desc {
  margin-bottom: $spacer-0;
}

&__textarea-label {
  color: $ifp-color-grey-6;
  margin-bottom: $spacer-2;
}

&__textarea {
  min-height: 120px;
  margin-bottom: $spacer-3;
}

&__comment-wrapper {
  padding-top: $spacer-0;
  max-height: 100%;
  border-bottom: none;
  @include ifp-scroll-y(transparent, $ifp-color-grey-1, 8px, 8px);

  &--approved {
    max-height: 784px;
  }
}

&__comment-name {
  display: flex;
  align-items: center;
  margin-bottom: $spacer-2;
}

&__name-tag {
  margin-inline-end: $spacer-2 + 2px;
}

&__btn-sec {
  display: flex;
  justify-content: flex-end;
}

&__comment-btn {
  display: block;
  margin-inline-start: $spacer-3;
}

&__comment {
  display: inline-block;
  color: $ifp-color-white-global;
  white-space: pre-wrap;
  background-color: $ifp-color-blue-hover;
  border-radius: 20px;
  padding: $spacer-3 $spacer-3;
  margin-bottom: $spacer-2;
  border-end-start-radius: 0;
}

&__comment-item {
  padding-top: $spacer-3;
  margin-bottom: $spacer-2;

  &:last-child {
    margin-bottom: $spacer-0;
  }

  &--self {
    text-align: right;

    .ifp-db {
      &__comment-name {
        flex-direction: row-reverse;
      }

      &__name-tag {
        margin-inline-end: $spacer-0;
        margin-inline-start: $spacer-2 + 2px;
      }

      &__comment {
        color: $ifp-color-black;
        background-color: $ifp-color-white;
        border-end-end-radius: 0;
        border-end-start-radius: 20px;
      }
    }
  }
}

&__comment-time {
  color: $ifp-color-grey-14;
}

&__comment-edit {
  margin-top: auto;
  padding-bottom: $spacer-4;
  border-top: 1px solid $ifp-color-grey-3;
}

&__comment-panel {
  display: flex;
  width: 25%;
  min-width: 320px;
  transition: 0.3s;
  position: sticky;
  height: calc(100vh - $ifp-header-height-inner);
  top: 0;
  right: 0;
  .ifp-node {
    &__card-right {
      width: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  &--collapse {
    max-width: 0;
    min-width: 0;
    overflow: visible;

    .ifp-node {
      &__card-right {
        transform: translateX(100%);
        position: absolute;
        right: 0;
        top: 0;
      }
    }
  }
}

// Comments end
}

.ifp-node {
  &__card-right {
    // overflow: hidden;
    transition: 0.3s;

    &--collapse {
      max-width: 1000px;
      min-width: 0;
      overflow: visible;
      // transform: translateX(100%);
      // position: absolute;
      // right: 0;
      // top: 0;
    }
  }
}

:host::ng-deep {
  .ifp-db {
    &__card {
      .ifp-db-card {
        width: 100%;
        height: 100%;
      }
    }

    &__gridster-item {
      .gridster-item-resizable-handler {
        border: transparent;
        display: none;
      }

      &--selected {
        .gridster-item-resizable-handler {
          display: block;
        }
      }

      &--preview {
        ifp-kebab-menu {
          display: none;
        }
        .ifp-db-card {
          border-color: $ifp-color-grey-7;
        }
      }
      &--hide {
        visibility: hidden;
        opacity: 0;
      }
    }

    /* Ensure abbreviation chips align to the far right within the header context */
    &__header-users {
      .ifp-panel-dropdown__select-box--abbr {
        margin-inline-start: auto;
      }
    }

    &__comment-desc {
      .customMarkdown p {
        text-align: inherit;
      }
    }
    &__user-detail-tag {
      .ifp-name-tag {
        background-color: $ifp-color-section-white;
      }
    }
    &--saving {
      .ifp-db-card {
        &__chart-title,
        &__description {
          display: block;
          overflow: visible;
        }
      }
    }
  }
}

:host-context(.ifp-header__fixed) {
  .ifp-db {
    &__toolbar,
    &__comment-panel {
      height: calc(100vh - $ifp-header-height-sticky);
      top: $ifp-header-height-sticky;
    }
    &__toolbar {
      max-width: 480px;
      position: sticky;
    }
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-db {
    background-image: url("../../../../assets/images/dashboard-builder/dashboard-builder-bg-dark.png");

    &--detail,
    &--preview {
      background-image: none;
    }
  }
}


// Gridster reset start
gridster {
  background-color: transparent;

  &.fixed {
    overflow: visible;
  }
}

gridster-item {
  overflow: visible;
  background-color: transparent;

  .ifp-db__card--add-indicator {
    margin: $spacer-0;
    width: 100%;
    max-width: none;
  }

  &.gridster-item-moving,
  &.gridster-item-resizing {
    box-shadow: none;
    // background-color: transparent;
  }

  &::ng-deep {

    .handle-ne,
    .handle-nw,
    .handle-se,
    .handle-sw {
      display: none;
    }
  }
}

@keyframes slide-down {
  0% {
    transform: translateY(-$ifp-header-height-sticky - 16px);
  }

  100% {
    transform: translateY(0);
  }
}

:host-context([dir="rtl"]) {
  .ifp-db {
    &__header-title-container {
      // .ifp-icon {
      //   left: $spacer-2;
      //   right: auto;
      // }
    }

    &__header-title {
      &:focus {
        padding-left: $spacer-5;
        padding-right: $spacer-0;
      }
    }

    &__add-type {
      right: 100%;
      left: auto;
    }

    &__add-type-item {
      .ifp-icon {
        margin-left: $spacer-2;
        margin-right: $spacer-0;
      }
    }

    &__header-btn-sec {
      margin-right: $spacer-6;
      padding-right: $spacer-6;
      border-right: 1px solid $ifp-color-grey-7;
      margin-left: $spacer-0;
      padding-left: $spacer-0;
      border-left: none;
    }

    &__toolbar-inner {
      padding-right: $spacer-5;
      padding-left: $spacer-0;
    }

    &__toolbar-toggle {
      right: 0;
      transform: translateX(50%);
      left: auto;
    }

    &__toolbar {
      left: 0;
      right: auto;
      box-shadow: -2px 0 10px rgba(0, 0, 0, 0.16);

      // &--right:not(.ifp-db__toolbar--expand) {
      //   left: 0;
      //   right: auto;
      // }

      &--left {
        right: auto;
        left: 0;
      }

      // &:not(.ifp-db__toolbar--expand) {
      //   right: 0;
      //   left: auto;
      // }

      &--expand {
        padding-right: $spacer-0;

        ifp-db {
          &__toolbar-toggle {
            transform: rotate(180deg) translateX(-50%);
          }

          &__toolbar-inner {
            padding-right: $spacer-0;
          }

          &.ifp-db__toolbar--left {
            .ifp-db__toolbar-toggle {
              transform: translateX(-50%);
            }

          }
        }
      }
    }

    &__btn-round-text {
      margin-left: $spacer-3;
      margin-right: $spacer-0;
    }

    &__add-indicator {
      right: -1px;
      left: auto;
      border-radius: 30px 0 0 30px;

      .ifp-icon {
        margin-left: $spacer-2;
        margin-right: $spacer-0;
      }

      &--right {
        right: auto;
        left: -1px;
        border-radius: 0 30px 30px 0;

        .ifp-db__import-pop-up {
          right: auto;
          left: 100%;
        }
      }
    }

    &--preview {
      &__wrapper {
        border-radius: 5px 0 5px 5px;
      }

      &::after {
        left: -8px;
        right: auto;
        border-right: 9px solid transparent;
      }
    }

    &__loader {
      right: 50%;
      left: auto;
      transform: translate(50%, -50%);
    }

    &__import-pop-up {
      right: 100%;
      left: auto;
    }

    &__tool-bar-opener {
      left: unset;
      right: 0;
      transform: translate(100%, -50%);
    }
  }

  .ifp-db__toolbar--right.ifp-db__toolbar--expand .ifp-db__toolbar-toggle {
    transform: rotate(180deg) translateX(-50%);
  }
}

@include ipad {
  .ifp-db {
    gridster-item {
      &::ng-deep {
        .gridster-item-resizable-handler {

          &.handle-e,
          &.handle-w {
            width: 80px;
          }

          &.handle-n,
          &.handle-s {
            height: 80px;
          }

          &.handle-e {
            right: -7px;
          }

          &.handle-w {
            left: -7px;
          }

          &.handle-n {
            top: -7px;
          }

          &.handle-s {
            bottom: -7px;
          }
        }
      }
    }
  }
}

@include mobile-tablet {
  .ifp-db__toolbar {
    top: 140px;
  }
}

::ng-deep body {
  &.ifp-demo {
    .ifp-db__toolbar {
      top: 144px;
    }
  }

  &.ifp-header__fixed-demo {
    .ifp-db__toolbar {
      top: 110px;
    }
  }
}

.ifp-add-data__selected-list {
  max-height: calc(100vh - 300px);
  @include ifp-scroll-y(transparent, $ifp-color-grey-7, 10px, 10px);
}
