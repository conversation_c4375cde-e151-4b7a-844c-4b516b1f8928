@use "../../../assets/ifp-styles/abstracts/index" as *;
:host {
  display: block;
  background-color: $ifp-color-pale-grey-50;
  border-radius: 10px;
  padding: $spacer-4;
}
// Use design tokens directly (no CSS var fallbacks)
$muted-color: rgba($ifp-color-black-global, 0.65);
$header-bg: $ifp-color-grey-bg-2;
$divider: $ifp-color-grey-13;
$link-color: $ifp-color-blue-hover;

.ifp-hies-visits {
  border-radius: 8px;
  // color: $text-color;

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: $spacer-3;
  }

  &__title {
    margin: 0;
    font-size: $ifp-fs-6;
    font-weight: $fw-semi-bold;
    // color: $text-color;
  }

  &__download-btn {
    background: transparent;
    border: 1px solid rgba($ifp-color-black-global, 0.06);
    border-radius: $spacer-1;
    padding: $spacer-1;
    cursor: pointer;
  }

  &__total-label { font-size: $ifp-fs-2; color: $muted-color; }
  &__total-value { font-size: $ifp-fs-11; font-weight: $fw-semi-bold; margin-bottom: $spacer-4; }
  &__small { font-size: $ifp-fs-1; color: $muted-color; margin-left: (-$spacer-1); vertical-align: baseline; }

  &__list-wrapper { border-top: 1px solid rgba($ifp-color-black-global, 0.04); padding-top: $spacer-3; }

  &__list-header {
    display: flex;
    justify-content: space-between;
    background: $header-bg;
    padding: $spacer-2 $spacer-3;
    border-radius: 999px;
    margin-bottom: $spacer-2;
    font-weight: $fw-semi-bold;
    color: rgba($ifp-color-black-global, 0.7);
    align-items: center;
  }

  &__col-indicator { flex: 1; }

  /* Table */
  &__table-scroll {
    display: block;
    max-height: 360px;
    overflow-y: auto;
    overflow-x: auto;
    border-radius: $spacer-1;
    -webkit-overflow-scrolling: touch;
      @include ifp-scroll($ifp-color-dropdown-select, $ifp-color-grey-1, 10px, 10px);

    &.is-empty { overflow: hidden !important; }
  }

  &__table {
    width: 100%;
    border-collapse: collapse;
    min-width: 100%;
    font-size: $ifp-fs-6;
  }

  &__table thead th {
    text-align: left;
    padding: $spacer-2 $spacer-2;
    font-weight: $fw-semi-bold;
    // color: $text-color;
     background-color: $ifp-color-grey-bg-2;
    position: sticky;
    top: 0;
    z-index: 3;
    border-bottom: 1px solid $divider;
    border: none;
  }
  &__table thead th:first-child { border-top-left-radius: 8px; border-bottom-left-radius: 8px; }
  &__table thead th:last-child { border-top-right-radius: 8px; border-bottom-right-radius: 8px; }

  &__item { border-bottom: 1px solid var(--ifp-color-grey-7); }

  &__item td {
    padding: $spacer-3 $spacer-2;
   border-bottom: 1px solid var(--ifp-color-grey-7);
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
     color: $ifp-color-grey-9;
    
    text-align: left;
  }

  &__item-label { width: 100%; }
  &__item-visits { width: 10%; cursor: pointer; color: $link-color !important; }

  &__no-data-row td { padding: $spacer-4; }

  // // Scrollbar (WebKit)
  // &__table-scroll::-webkit-scrollbar { width: 12px; }
  // &__table-scroll::-webkit-scrollbar-track { background: rgba($ifp-color-black-global, 0.02); border-radius: 10px; }
  // &__table-scroll::-webkit-scrollbar-thumb { background: rgba($link-color, 0.20); border-radius: 10px; }
  // &__table-scroll.is-empty::-webkit-scrollbar { display: none; }

  // // Firefox scrollbar
  // &__table-scroll { scrollbar-width: thin; scrollbar-color: rgba($link-color, 0.20) rgba($ifp-color-black-global, 0.02); }
  // &__table-scroll.is-empty { scrollbar-width: none; }

  // @include mobile-tablet {
  //   &__table thead th { padding: $spacer-2 $spacer-2; }
  //   &__item td { padding: $spacer-3 $spacer-2; }
  //   &__table-scroll { max-height: 260px; }
  // }

  &__summary-head { display: flex; align-items: center; justify-content: space-between; gap: $spacer-3; margin-bottom: $spacer-2; }
  &__summary-left { border-radius: 8px; }
  &__summary-right { width: 33.33%; }
  &__census-right { width: auto; }
  &__metrics { display:flex; gap: $spacer-4; align-items:center; }
  &__metric { text-align: left; }
  &__metric-label { font-size: $ifp-fs-2; color: $muted-color; }
  &__metric-value { font-size: $ifp-fs-11; font-weight: $fw-semi-bold;}
  &__metric-unit { font-size: $ifp-fs-2; color: $muted-color; }
}