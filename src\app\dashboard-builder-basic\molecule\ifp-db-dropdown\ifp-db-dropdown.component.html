<div class="ifp-db-dropdown" [ngClass]="{'ifp-db-dropdown--show': showList}" #drop>
  @if (title) {
  <p class="ifp-db-dropdown__title"  >{{disableTranslation ? (title | translate) : (title| translate)}}</p>
  }
  <div class="ifp-db-dropdown__selected" (click)="showList = !showList" [ngClass]="{'ifp-db-dropdown__selected--disabled' : options?.length ==1 && defaultSelect && (selectedSingleItem || multipleSelectedItems?.length > 0)}">
    @if (iconEnable) {
      <em class="ifp-icon ifp-db-dropdown__selected-icon" [ngClass]="selectedSingleItem['iconClass']"></em>
    }
    <p class="ifp-db-dropdown__selected-item" >
      {{ disableTranslation  ?
       (!isMultiSelect? ((selectedSingleItem && selectedSingleItem?.[key] ? (!optionAlias ? (selectedSingleItem?.[key] ) : (selectedSingleItem?.[key] ) +' ('+(commmonDataTypes[selectedSingleItem?.[key]] )+')') : (placeholder | translate) )) : multipleSelectedItems?.[0]?.[key] + (multipleSelectedItems.length > 1 ? ((' + '+
      (multipleSelectedItems.length- 1)) +' more'): ''))
      :
      (!isMultiSelect? ((selectedSingleItem && selectedSingleItem?.[key] ? (!optionAlias ? (selectedSingleItem?.[key] | translate) : (selectedSingleItem?.[key] | translate) +' ('+(commmonDataTypes[selectedSingleItem?.[key]] | translate)+')') : placeholder | translate) |
      translate) : multipleSelectedItems?.[0]?.[key] + (multipleSelectedItems.length > 1 ? ((' + '+
      (multipleSelectedItems.length- 1)) +' more'): ''))}}</p>
    @if((options &&  options.length > 1) || !defaultSelect) {
    <em class="ifp-icon ifp-icon-down-arrow"></em>
    }
  </div>
  <ul class="ifp-db-dropdown__list" >
    @for (item of options; track item[key]; let i=$index) {
      @if (!item.isHide) {
        <li class="ifp-db-dropdown__list-item" [ngClass]="{'ifp-db-dropdown__list-text--disabled': item.disabled}">
          @if(isMultiSelect) {
          <app-ifp-check-box [translation]="!disableTranslation" class="ifp-db-dropdown__list-text" [label]="disableTranslation ? item?.[key] : (item?.[key] | translate)" [checkedData]="item.checked"
            (checked)="setChecked($event, item)"
            [ngClass]="{'ifp-db-dropdown__list-text--selected' : getMutiselectedItem(item) }"></app-ifp-check-box>
          } @else {
          <p class="ifp-db-dropdown__list-text" (click)="selectSingleItem(item,i)"
            [ngClass]="{'ifp-db-dropdown__list-text--selected' : selectedSingleItem && item?.[key] == selectedSingleItem?.[key]}">
            {{ disableTranslation  ?
            ( !optionAlias ? (item?.[key]) : item?.[key] ? (item?.[key] )+' ('+(commmonDataTypes[item?.[key]])+')' : (item?.[key]+' ('+commmonDataTypes[item?.[key]]+')' )):
           ( !optionAlias ? (item?.[key] | translate) : item?.[key] ? (item?.[key] | translate)+' ('+(commmonDataTypes[item?.[key]] | translate)+')' : (item?.[key]+' ('+commmonDataTypes[item?.[key]]+')' | translate ))}}</p>
          }
        </li>
      }

    }
  </ul>
</div>
