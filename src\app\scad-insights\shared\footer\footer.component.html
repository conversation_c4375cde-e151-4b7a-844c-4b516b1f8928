@if (footerData$ | async; as footer) {
  <footer class="ifp-footer">
    <div class="ifp-container">
      <div class="ifp-footer__logo-sec">
        <a href="https://www.scad.gov.ae" title="SCAD" class="ifp-footer__logo" target="_blank">
          <ifp-img class="ifp-footer__link-icon" [darkIcon]="footer.site_slogan_light" [lightIcon]="footer.site_slogan" [width]="140" [height]="60"></ifp-img>
        </a>
        <div class="ifp-footer__copyright-links">
          @for(menuItem of footer.footer_menu_first; track $index) {
            <a [routerLink]="[menuItem.menu_link]" [title]="(menuItem.menu_label.trim()) | translate" class="ifp-footer__link" [routerLinkActive]="'ifp-footer__link--active'">{{menuItem.menu_label.trim() | translate}}</a>
          }
          <!-- <a [routerLink]="['/products']" title="Product" class="ifp-footer__link" [routerLinkActive]="'ifp-footer__link--active'">{{'Product' | translate}}</a>
          <a [routerLink]="['/contact']" title="Contact" class="ifp-footer__link" [routerLinkActive]="'ifp-footer__link--active'">{{'Contact' | translate}}</a> -->
          <!-- <ng-container *ngFor="let linkItem of footer.bottom_menu">
            <a [routerLink]="[linkItem.menu_link]" title="linkItem.menu_label" class="ifp-footer__link">{{linkItem.menu_label}}</a>
          </ng-container> -->
        </div>
      </div>
      <div class="ifp-footer__copyright">
        <p class="ifp-footer__copyright-text">{{('copyrightGeneral' | translate:{year: currentYear})}}</p>
        <div class="ifp-footer__copyright-links">
          @for(linkItem of footer.footer_menu_second; track $index) {
            <a [routerLink]="[linkItem.menu_link]" [routerLinkActive]="'ifp-footer__link--active'" [attr.data-text]="linkItem.menu_link" [title]="(linkItem.menu_label.trim()) | translate" class="ifp-footer__link">{{linkItem.menu_label.trim() | translate}}</a>
          }
        </div>
      </div>
    </div>
  </footer>
}
