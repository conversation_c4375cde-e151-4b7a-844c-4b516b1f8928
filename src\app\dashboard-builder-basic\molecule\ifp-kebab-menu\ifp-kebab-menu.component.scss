@use "../../../../assets/ifp-styles/abstracts/index" as *;

.ifp-kebab-menu {
  $size: 24px;
  position: relative;
  &__btn {
    color: $ifp-color-tertiary-text;
    display: inline-block;
    width: $size;
    height: $size;
    line-height: ($size + 4px) !important;
    text-align: center;
    border-radius: 50%;
    transition: 0.3s;
    cursor: pointer;
    &:hover {
      background-color: $ifp-color-violet-light;
      color: $ifp-color-blue-hover;
    }
  }
  &__list {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 20px 60px -8px $ifp-color-black-16;
    position: absolute;
    top: 100%;
    opacity: 0;
    visibility: hidden;
    margin-top: $spacer-1;
    background-color: $ifp-color-white;
    min-width: 100px;
    transition: 0.3s;
    z-index: 1;
  }
  &__item {
    display: flex;
    padding: $spacer-2;
    border-bottom: 1px solid $ifp-color-dropdown-select;
    color: $ifp-color-secondary-grey;
    transition: 0.3s;
    white-space: nowrap;
    cursor: pointer;
    &:hover {
      background-color: $ifp-color-dropdown-select;
    }
    .ifp-icon {
      margin: $spacer-0 $spacer-1;
    }
  }
  &--right {
    .ifp-kebab-menu {
      &__list {
        left: 0;
      }
    }
  }
  &--left {
    .ifp-kebab-menu {
      &__list {
        right: 0;
      }
    }
  }
  &--top {
    .ifp-kebab-menu {
      &__list {
        top: auto;
        bottom: 100%;
      }
    }
  }
  &--active {
    .ifp-kebab-menu {
      &__btn {
        background-color: $ifp-color-violet-light;
        color: $ifp-color-blue-hover;
      }
      &__list {
        opacity: 1;
        visibility: visible;
      }
    }
  }
}
:host-context([dir="rtl"]) {

  .ifp-kebab-menu {
    &--right {
      .ifp-kebab-menu {
        &__list {
          right: 0;
          left: auto;
        }
      }
    }
    &--left {
      .ifp-kebab-menu {
        &__list {
          left: 0;
          right: auto;
        }
      }
    }
  }
}
