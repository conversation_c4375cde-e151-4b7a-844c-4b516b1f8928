import { Component, inject, input, InputSignal, linkedSignal, OnInit, output, OutputEmitterRef, signal, effect } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { IfpButtonComponent } from '../../../scad-insights/ifp-widgets/ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { TranslateModule } from '@ngx-translate/core';
import { IfpDbDropdownComponent } from 'src/app/dashboard-builder/molecule/ifp-db-dropdown/ifp-db-dropdown.component';
import { SelectedProductWithoutId } from '../../widgets/dxp-accordian/dxp-accordian.component';
import { DistinctValuesStore } from '../../store/dxp-distinct-value.store';
import { JsonPipe } from '@angular/common';
@Component({
  selector: 'ifp-dxp-visualization-filter-form',
  imports: [ReactiveFormsModule, IfpButtonComponent, TranslateModule, IfpDbDropdownComponent, JsonPipe],
  templateUrl: './dxp-visualization-filter-form.component.html',
  styleUrl: './dxp-visualization-filter-form.component.scss'
})
export class DxpVisualizationFilterFormComponent implements OnInit {
  private readonly _formBuilder = inject(FormBuilder);
  public columnList: InputSignal<DxpKpiCustomFilterColumnList[]> = input([{name: '', values: [{id: '', value: ''}]}]);
  public buttonClass = buttonClass;
  public selectedColumn = linkedSignal(() => {
    return this.columnList()[0];
  });

  public addFilter: OutputEmitterRef<DxpKpiCustomFilter> = output();
  public cancelFilter: OutputEmitterRef<void> = output();
  public selectedProductDetails = input<SelectedProductWithoutId>();
  public columnfiltervalues = signal<any[]>([]);
  public columnFilterValues = signal<string[]>([]);
  // Edit mode
  public isEdit = input<boolean>(false);
  public editFilter = input<DxpKpiCustomFilter | null>(null);
  public loader = signal<boolean>(false);
  public enableCancel = input(false);
  public addFilterForm!: FormGroup<AddFilterForm>;
  readonly storeDistinctValue = inject(DistinctValuesStore);

  constructor() {
    effect(() => {
      const resp = this.storeDistinctValue.getDistinctList()(
        `${this.selectedColumn().name + (this.selectedProductDetails()?.sourceProductId ?? '') + (this.selectedProductDetails()?.sourceAssetId ?? '')}source`);
      this.loader.set(resp.loader);
      let selectedValues: string[] | null = null;
      const currentSelectedValues: string[] = this.addFilterForm?.controls?.default?.value ?? [];
      if (currentSelectedValues.every(val => resp.data.includes(val))) {
        selectedValues = currentSelectedValues;
      }
      if (!resp.loader){
        this.columnFilterValues.set(resp.data);
        this.columnfiltervalues.set(resp.dataMap ?? []);
        this.addFilterForm.patchValue({
          values: selectedValues ? selectedValues.map(val => ({id: val, value: val, checked: true})) : null,
          default: selectedValues ?? null,
        });
      }


    });
  }

  ngOnInit(): void {
    this.initForm();
    // Prefill in edit mode
    if (this.isEdit() && this.editFilter()) {
      this.prefillFromEdit();
    }
  }

  initForm() {
    this.addFilterForm = this._formBuilder.group({
      column: [null, Validators.required],
      label: [null, Validators.required],
      values: [null],
      default: [null],
    });
  }

  private prefillFromEdit() {
    if (!this.editFilter()) {
      return;
    }
    const colList = this.columnList();
    const columnItem = colList.find(c => (c?.name ?? c) === ( this.editFilter()?.column ?? ''));
    if (columnItem) {
      this.selectedColumn.set(columnItem as any);
    }
    // Use saved filter options if present to avoid extra API call
    if (Array.isArray(this.editFilter()?.filterOptions) && this.editFilter()?.filterOptions.length) {
      this.columnfiltervalues.set(this.editFilter()?.filterOptions ?? []);
      this.columnFilterValues.set(this.editFilter()?.filterOptionsString ?? []);
    }
    this.getColumnFilterValue();
    // Patch form values (set both 'values' and 'default' for dropdown + payload)
    this.addFilterForm.setValue({
      column: columnItem ?? null,
      label: this.editFilter()?.filterLabel ?? (columnItem?.name ?? ''),
      values: this.editFilter()?.defaultValue ?? null,
      default: this.editFilter()?.defaultValueOnly ?? null,
    });
  }

  onSelectColumn(column: DxpKpiCustomFilterColumnList) {
    this.selectedColumn.set(column);
    this.addFilterForm.patchValue({
      values: null,
      default: null
    });
    this.getColumnFilterValue();
  }


  onAddFilter() {
    const values = this.addFilterForm.value;
    const data:DxpKpiCustomFilter = {
      column: values.column.name,
      filterOptions: this.columnfiltervalues(),
      filterOptionsString: this.columnfiltervalues().map(v => v.value),
      filterLabel: values.label && values.label !== '' ? values.label : values.column.name,
      defaultValue: values?.values,
      defaultValueOnly: values?.values.map((v: {id: string; value: string; index: number}) => v.value) ?? '',
      selectedValueArray: values?.values.map((v: {id: string; value: string; index: number}) => v.value) ?? '',
      data_type: values?.column?.data_type
    };
    if (values?.column?.inferred_dtb_dt_format) {
      data['inferred_dtb_dt_format'] = values?.column?.inferred_dtb_dt_format ?? '';
    }
    this.addFilterForm.reset();
    this.addFilter.emit(data);
  }

  onCancelFilter() {
    this.addFilterForm.reset();
    this.cancelFilter.emit();
  }



  getColumnFilterValue() {
    this.loader.set(true);
    this.storeDistinctValue.loadByQuery({ columnName: this.selectedColumn().name, search: '', product: this.selectedProductDetails(),  sourceFilterEnabled: true });
  }

}

interface AddFilterForm {
  column: FormControl;
  label: FormControl;
  values: FormControl;
  default: FormControl;
}

export interface DxpKpiCustomFilterColumnList {
  name: string;
  values: {id: string; value: string}[];
}

export interface DxpKpiCustomFilter {
  column: string;
  filterLabel: string;
  filterOptions: {id: string; value: string}[];
  filterOptionsString: string[];
  inferred_dtb_dt_format?:string;
  defaultValue: {
  id: string;
  value: string;
  index?: number;
}[] | null;
value?: string[];
  data_type: string
  defaultValueOnly: string[];
  selectedValueArray: string[];
}
