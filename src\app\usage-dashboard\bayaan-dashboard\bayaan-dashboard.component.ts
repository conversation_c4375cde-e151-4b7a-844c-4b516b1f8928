import { DownloadNodeData } from './../download-insight-analysis/download-insight-analysis.component';
import { UserListWithAccess } from './../user-access-list-card/user-access-list-card.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Component, inject, Input, OnDestroy, OnInit, signal, ViewChild, WritableSignal } from '@angular/core';
import { UserCardBarComponent } from '../user-card-bar/user-card-bar.component';
import { PeakTimeUsageComponent } from '../peak-time-usage/peak-time-usage.component';
import { SubSink } from 'subsink';
import { ApiService } from '../../scad-insights/core/services/api.service';
import { initDataNew, userAccessKpi, userApi, userCountInitial } from '../usage-dashboard.constant';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { CommonModule, DatePipe } from '@angular/common';
import { IfpPanelDropdownComponent, PanelDropdownOptions } from '../../ifp-analytics/molecule/ifp-panel-dropdown/ifp-panel-dropdown.component';
import { UsageDashboardUserlistComponent } from '../usage-dashboard-userlist/usage-dashboard-userlist.component';
import { IfpModalComponent } from '../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpModalService } from '../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';
import { SelfServiceUserData } from '../ifp-user-list-dashboard/ifp-user-list-dashboard.component';
import { IfpTooltipDirective } from '../../scad-insights/core/directives/ifp-tooltip.directive';
import { ArrowClick, UsageDashboardGroupListComponent, UserListWithGroup } from '../usage-dashboard-group-list/usage-dashboard-group-list.component';
import { IfpPillsTabComponent, PillsTabData } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-pills-tab/ifp-pills-tab.component";
import { EntityDetails } from "../user-detail-card/user-detail-card.component";
import { Tags } from "../../scad-insights/ifp-widgets/ifp-molecules/tag-group/tag-group.component";
import { cloneDeep } from 'lodash';
import { TotalUserDonutComponent } from "../total-user-donut/total-user-donut.component";
import { IfpSearchComponent } from '../../scad-insights/ifp-widgets/ifp-atoms/ifp-search/ifp-search.component';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { EntitiesOverview, StatsDbNavigationMenuComponent, UserAccessParams } from "../stats-db-navigation-menu/stats-db-navigation-menu.component";
import { DashboardDownloadService } from '../shared/services/dashboard-download.service';
import { UserAccessTotal, UserOverviewComponent } from "../user-overview/user-overview.component";
import { ClassificationTab } from "../domain-insights/domain-insights.component";
import { DownloadInsightAnalysisComponent } from "../download-insight-analysis/download-insight-analysis.component";
import { IfpDropdownComponent } from "../../scad-insights/ifp-widgets/ifp-atoms/ifp-dropdown/ifp-dropdown.component";
import { IfpDataTableComponent } from "../../ifp-analytics/organism/ifp-data-table/ifp-data-table.component";
import { PaginationComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/pagination/pagination.component";
import { IfpTabComponent } from "../../scad-insights/ifp-widgets/ifp-molecules/ifp-tab/ifp-tab.component";
import { ActivatedRoute, Router } from '@angular/router';
import { dataClassification, roleList } from 'src/app/scad-insights/user-onboarding/control-panel/ifp-access-control/ifp-access-control.constants';
import { DownloadService } from 'src/app/scad-insights/core/services/download.service';
import { IfpTitleCasePipe } from 'src/app/scad-insights/core/pipes/title-case.pipe';
import { UserDesginationSummaryComponent } from '../user-desgination-summary/user-desgination-summary.component';
import { UsersVisitsComponent } from '../users-visits/users-visits.component';
import { hiesVisitNodeData, HiesCensusVisitsComponent } from '../hies-census-visits/hies-census-visits.component';
import { HiesCensusMostUsedFiltersComponent } from '../hies-census-visits/hies-census-most-used-filters/hies-census-most-used-filters.component';
@Component({
  selector: 'ifp-bayaan-dashboard',
  imports: [UsersVisitsComponent, CommonModule, TranslateModule, IfpSearchComponent, UserCardBarComponent, PeakTimeUsageComponent, MatDatepickerModule, ReactiveFormsModule, DatePipe, IfpPanelDropdownComponent, UsageDashboardUserlistComponent, IfpModalComponent, IfpTooltipDirective, UsageDashboardGroupListComponent, IfpPillsTabComponent, TotalUserDonutComponent, StatsDbNavigationMenuComponent, UserOverviewComponent, DownloadInsightAnalysisComponent, IfpDropdownComponent, IfpDataTableComponent, PaginationComponent, IfpTabComponent,UserDesginationSummaryComponent,HiesCensusVisitsComponent,HiesCensusMostUsedFiltersComponent],
  templateUrl: './bayaan-dashboard.component.html',
  styleUrl: './bayaan-dashboard.component.scss',
  providers: [provideNativeDateAdapter(), IfpTitleCasePipe]
})
export class BayaanDashboardComponent implements OnInit, OnDestroy {
  @ViewChild('modal') modal!: IfpModalComponent;
  @ViewChild('usersDetailesModal') usersDetailesModal!: IfpModalComponent;
  @ViewChild('navigationMenu') sideBarCmp!: StatsDbNavigationMenuComponent;
  @Input() search = '';
  @Input() boxTypeSearch = false;
  @Input() searchPlaceHolder = 'Search...';
  @Input() isSort: boolean = false;

  private readonly _router: Router = inject(Router);
  private readonly _ifpTitleCasePipe: IfpTitleCasePipe = inject(IfpTitleCasePipe);

  private readonly _dashboardDownloadService = inject(DashboardDownloadService);
  public downloadTypes = downloadTypes;
  
  public userApi = userApi;
  public subs = new SubSink();
  public data: UsageDashboard = initDataNew;
  public domain: ClassificationTab[] = [];
  public activeUsers = initDataNew.activeInactiveUsers;
  public totalUser: TotalUsers = initDataNew.totalUsersNew;
  public totalEntities: TotalEntity[] = [initDataNew.totalEntitiesNew];
  public topUsers: TopUser[] = [];
  public topEntities: TopEntity[] = [];
  public peakValueChart: HourlySplit[] = [];
  public columListEntityDropdown: PanelDropdownOptions[] = [];
  public overviewColumListEntityDropdown: PanelDropdownOptions[] = [];
  public externalEntitiesOptions: PanelDropdownOptions[] = [];
  public selectedEntities: WritableSignal<PanelDropdownOptions[]> = signal([]);
  public multiSelectedEntities: WritableSignal<PanelDropdownOptions[]> = signal([]);
  public entitiesIds: string = '';
  public callActiveAndInactiveUsersLoader = true;
  public callTotalUsersLoader = true;
  public callTopUserUsageLoader = true;
  public callPeakValueUsageLoader = true;
  public callTopEntitiesLoader = true;
  public callTotalEntityLoader = true;
  public callDomainsLoader = true;
  public averageUsage: AvgUsage = initDataNew.peakTimeOfUsage.avgUsage;
  public overallDomains: OverallDomain[] = [];
  public entities: Entity[] = [];
  public officialStatistics: OfficialStatistic[] = [];
  public officialStaticsMap: Record<string, number> = {};
  public experimentalStatistics: OfficialStatistic[] = [];
  public analyticalApps: AnalyticalApp[] = [];
  public reports: OfficialStatistic[] = [];
  public selfServiceData: SelfService[] = [];
  public currentSelfService: string = '';
  public usageSelfServiceUser: SelfServiceUserData[] = [];
  public currentParams!: ApiParams;
  public geoSpecialCategory = {
    domain: 'domain',
    district: 'district'
  };

  public userOverviewTabData: PillsTabData[] = [{
    value: 'Entities List', key: 'entity',
    icon: 'ifp-icon-entities'
  }, {
    value: 'External Users', key: 'external',
    icon: 'ifp-icon-multiple-user'
  }, {
    value: 'SCAD Users', key: 'internal',
    icon: 'ifp-icon-multiple-user'
  }
  ];
  
  public usersDetailes: any[] = [];

  public selectedUserTypeItem: PillsTabData = this.userOverviewTabData[0];

  public geoSpecialDistrict: WritableSignal<Record<string, SpecialAnalytics[]>> = signal({});
  public geoSpecialDomain: SpecialAnalytics[] = [];
  public userListOffset = 0;
  public userListSize = 10;
  public domainSize = 10;
  public geoSpecialUserSize = 10;
  public userGroupListSize = 0;
  public userGroupListOffset = 0;
  public geoSpecialCurrentCategory = '';
  public geoSpecialCurrentHeader = '';
  public loaderSelfService = true;
  public loaderUser = true;
  public maxDate = new Date();
  public minDate = new Date(2024, 0, 1);
  public startDate = new FormControl(new Date(new Date().setFullYear(new Date().getFullYear() - 1)));
  public endDate = new FormControl(new Date());
  public activeIndexDomain = 0;
  public userGroups = signal<UserListWithGroup>({});
  public userListWithGroup = signal<UserListWithGroup[]>([]);
  public userGroupLoader = signal<boolean>(true);
  public userAccessLoader = signal<boolean>(true);
  public userCountLoader = signal<boolean>(true);
  public entitiesOverviewLoader = signal<boolean>(true);
  public downloadInsightsLoader = signal<boolean>(true);
  public notificationEnabledInsightsLoader = signal<boolean>(true);
  public hiesVisitLoader = signal<boolean>(true);
  public hiesDurationLoader = signal<boolean>(true);
  public userDetailLoader = signal<boolean>(true);
  public isAllEntitySelected: WritableSignal<boolean> = signal(false);
  public selectAllUserColumn: WritableSignal<boolean> = signal(true);
  public userTableHead: string[] = [];
  public userTableTitleData: PanelDropdownOptions[] = [];
  public userDetailTableData: TableView[][] = [];
  public filteredUserDetailTableData: TableView[][] = [];
  public mainTabs: PillsTabData[] = [
    {
      key: 'userOverview',
      value: 'Overview User and Access',
      icon: 'ifp-icon-user-normal'
    },
    {
      key: 'utilization',
      value: 'Utilization Analysis',
      icon: 'ifp-icon-hand-money'
    }
  ];

  public platforms = [
    {
      name: 'Web',
      value: 'web'
    },
    {
      name: 'Mobile',
      value: 'mobile'
    }
  ];

  public selectedPlatform = 'Web';

  public selectedMainTab = this.mainTabs[0].key;
  public selectedEntity!: PanelDropdownOptions;
  public userDetails: EntityDetails[] = [];

  public userCount = signal(userCountInitial);
  public userListWithAccess = signal<UserListWithAccess[]>([]);
  public userListWithAccessParams: {
    classification?: string;
    domain?: string;
    entityId?: string;
  } = {};
  public userListWithAccessSize = signal(0);

  public entitiesOverView: EntitiesOverview[] = [];
  public entityProduct: string[] = [];
  public userAccessListTableHead: string[] = ['User', 'Entity', 'Designation', 'Permission'];

  public selectedUserType: string = 'external';
  public selectedEntityAccessDetail!: EntitiesOverview;
  public downloadInsights: DownloadNodeData[] = [];
  public downloadInsightsTabData: DownloadInsightsTab[] = [];
  public notificationEnabledInsights: DownloadNodeData[] = [];
  public hiesVisitsData: hiesVisitNodeData[] = [];
  public hiesDurationData: hiesVisitNodeData[] = [];
  public isMobile: boolean = false;

  public userOverviewOffset: number = 0;
  public userOverviewPerPage: number = 5;
  public userOverviewPage: number = 1;
  public userOverviewTotalRow: number = 0;
  public userOverviewData: EntityUserOverview[] = [];
   public allUsersOverviewData: EntityUserOverview[] = [];
  public userOverviewEntityData: EntityUserOverview[] = [];
  public allUsersOverviewEntityData: EntityUserOverview[] = [];
  public userOverviewTableData: TableView[][] = [];
  public userOverviewTableHead: string[] = [];
  public userOverviewEntityTableData: TableView[][] = [];
  public userOverviewEntityTableHead: string[] = [];

  public topUsersTableHeader: string[] = [];
  public topUsersTableData: TableView[][] = [];
  public topEntitiesTableHeader: string[] = [];
  public topEntitiesTableData: TableView[][] = [];
  public selectedTopKey: string = 'users';
  public entityTableUserListColumns: PanelDropdownOptions[] = [];
  public entityTableColumns: PanelDropdownOptions[] = [];
  public topUserTableTitles: PanelDropdownOptions[] = [];
  public topEntityTableTitles: PanelDropdownOptions[] = [];
  private entitySelectedColumn: string[] = [];
  private entityUsersSelectedColumn: string[] = [];
  private performerSelectedColumn: string[] = [];
  private topEntitySelectedColumn: string[] = [];
  public userSelectedColumn: string[] = [];
  public userListTableTitles: PanelDropdownOptions[] = [];
  private userListSelectedColumn: string[] = [];
  public userListTableData: UserListWithGroup[] = [];
  public allUserListTableData: UserListWithGroup[] = [];
  public headingSettings: { [key: string]: { sort: boolean } } = {};

  public tabData: PillsTabData[] = [
    {
      key: 'users',
      value: 'Users',
      icon: ''
    },
    {
      key: 'entities',
      value: 'Entities',
      icon: ''
    }
  ];

  public indicatorTabData: PillsTabData[] = [
    {
      key: 'Official',
      value: 'Official statistics',
      icon: ''
    },
    {
      key: 'Experimental',
      value: 'Experimental Statistics',
      icon: ''
    },
    {
      key: 'analyticalApps',
      value: 'Analytical Apps',
      icon: ''
    },
    {
      key: 'reports',
      value: 'Reports',
      icon: ''
    }
  ];
  public tabDataCensus: PillsTabData[] = [
    {
      key: 'population',
      value: 'Population',
      icon: ''
    },
    {
      key: 'labourForce',
      value: 'Labour Force',
      icon: ''
    },
     {
      key: 'realEstate',
      value: 'Real Estate',
      icon: ''
    }
  ];

  public selectedIndicatorTab: string = 'Official';
  public indicatorTableData: TableView[][] = [];
  public indicatorTableHead: string[] = [];

  public toolsTab: PillsTabData[] = [
    {
      key: 'self_service__Gen_AI',
      value: 'Gen Ai',
      icon: ''
    },
    {
      key: 'self_service__auto_ml',
      value: 'Auto ML',
      icon: ''
    },
    {
      key: 'self_service__exploratory',
      value: 'Exploratory',
      icon: ''
    },
    {
      key: 'self_service__advance_prep',
      value: 'Advance Prep',
      icon: ''
    },
    {
      key: 'self_service__basic_prep',
      value: 'Basic Prep',
      icon: ''
    },
    {
      key: 'self_service__dashboard',
      value: 'Dashboard',
      icon: ''
    },
    {
      key: 'self_service__my_bookmark',
      value: 'Bookmarks',
      icon: ''
    }
  ];

  public userStatusOffset: number = 0;
  public userStatusPage: number = 1;
  public selectedUserStatus: string = 'active';
  public userTypeEntity!: PanelDropdownOptions;
  public selectedUserGroupEntity!: PanelDropdownOptions;
  public userStatusTab: PillsTabData[] = [
    {
      value: 'Active',
      key: 'active',
      icon: ''
    },
    {
      value: 'Inactive',
      key: 'inactive',
      icon: ''
    },
    {
      value: 'Superusers',
      key: 'superusers',
      icon: ''
    }
  ];

  public selectedToolsTab: string = 'self_service__Gen_AI';
  public toolsTableData: TableView[][] = [];
  public allToolsTableData: SelfServiceUserData[] = [];
  public toolsTableHead: string[] = [];
  public toolsPage: number = 1;
  public toolsPer_page: number = 10;
  public toolsOffset: number = 0;
  public selectedDomain: string = '';
  public selectedEntityDomain: string = '';
   
  // entity data overview
  public dataClassificationOverview: WritableSignal<UsageDashboardDataClassification> = signal({
    classification: [],
    domain: [],
    products: [],
    indicator: [{
      contentClassification: undefined,
      dataClassification: undefined,
      domain: '',
      nid: '',
      pageCategory: '',
      title: ''
    }]
  });
  public dataClassificationOverviewLoader: boolean = true;
  public selectedEntityClassificationLevel: string = dataClassification.confidential.toUpperCase();
  public userAndEntityListLoader: boolean = true
  public userDesignationSummary = signal<{ name: string; user_count: number }[]>([]);
  public userDesignationKey = signal<string>('initial');
  public usersDetailsParmData!: ApiParams;
  public modalTitle: string = '';
  public isUsersDetailsModalOpen = false;
  public tableUsersDetailesComponent!: { data: any[]; tableHead: string[]; };
  public usersTableData: TableView[][] = []; 
  public usersTableHead: string[] = [];

  constructor(private _translate: TranslateService, private readonly _api: ApiService, private readonly _datePipe: DatePipe, private readonly _modalService: IfpModalService, private readonly _toasterService: ToasterService, private readonly _activatedParams: ActivatedRoute,
    private readonly downloadService: DownloadService
  ) {
    this.subs.add(this._activatedParams.queryParams.subscribe((params) => {
      this.selectedMainTab = params['tab'] ?? this.mainTabs[0];
    }));
  }


  ngOnInit(): void {
    // entityList
    this.callEntitiesList();
  }


  multiSelection(event: any) {
    this.selectedEntities.set(event);
    // if (this.selectedEntities()?.length <= 0) {
    //   this.columListEntityDropdown[0].checked = true;
    //   this.selectedEntities.set([this.columListEntityDropdown[0]])
    // }
    this.callApis();
  }

  removeFilter(item: Tags) {
    const removedFilterIndex = this.columListEntityDropdown.findIndex((tag: PanelDropdownOptions) => tag.key === item.key);
    if (removedFilterIndex >= 0) {
      this.columListEntityDropdown[removedFilterIndex].checked = false;
      this.isAllEntitySelected.set(false);
      this.callApis();
    }
  }

  modalView() {
    this.modal.createElement();
  }

  usersModalView() {
    this.isUsersDetailsModalOpen = true;
    this.usersDetailesModal.createElement();
  }

  closeVisitsModal() {
    this.isUsersDetailsModalOpen = false;
    this.usersDetailesModal.removeModal();
    this._modalService.removeAllModal();
  }

 callApis() {
    const parmData: ApiParams = {
      startDate: this._datePipe.transform(this.startDate.value, 'y-MM-dd'),
      endDate: this._datePipe.transform(this.endDate.value, 'y-MM-dd'),
      mobile: this.isMobile
    };
    parmData['entityNames'] = this.selectedEntities().map((item: Tags) => item?.shortValue ?? '');
    parmData['entityId'] = this.selectedEntities().map((item: Tags) => item?.key ?? '').toString();
    this.currentParams = parmData;
    this.userListSize = 0;
    this.userListOffset = 0;
    // User API
    this.callActiveAndInactiveUsers(parmData);
    this.callTotalUsage(parmData);
    this.callPeakValueUsage(parmData);

    // Entity APIs
    this.callTopEntityUsage(parmData);
    this.callTotalEntities(parmData);


    this.callDomains(parmData);
    this.callGeopSpatialOverView(parmData);
    this.callUsersGroup();

    this.getMostDownloadedIndicators(parmData);
    this.getMostNotificationEnabledIndicators(parmData);
    this.getHiesVisits(parmData);
    this.getHiesDuration(parmData,userApi.hiesDuration);
    this.callSelfServiceUser(parmData, { limit: this.toolsPer_page, offset: this.toolsOffset });
    // User Access APIs
    this.callTotalUsersCount({ entityId:  parmData['entityId'] });
    this.entitiesIds = this.selectedEntities().map((item: Tags) => item?.key ?? '').toString();
    this.callEntityAdminDetail(this.entitiesIds);
    this.callUsersandEntitiesList();
    const params = { entityId: parmData['entityId'] }
    this.callEntityClassification(params);
  }

  // Total Users Count
  callTotalUsersCount(parmData: { entityId: string }) {
    this.userCountLoader.set(true);
    const subs = this._api.getMethodRequest(userApi.usersCount, parmData).subscribe(
      {
        next: ((data: UserAccessCount) => {
          this.userCount.set(data);
          this.userCountLoader.set(false);
          subs.unsubscribe();
        }),
        error: () => {
          this.userCountLoader.set(false);
          subs.unsubscribe();
        }
      }

    );
  }

  callUsersandEntitiesListAsync(ids: string[] = []): Promise<void> {
    return new Promise((resolve, reject) => {
      let selectedEntityIds = ids.length ? ids : this.userTypeEntity?.key;
      const isEntity = this.selectedUserTypeItem.key === 'entity';
      const subs = this._api.getMethodRequest(
        isEntity ? userAccessKpi.entitiesUserCount : userAccessKpi.userDetails,
        {
          entity_id: selectedEntityIds,
          external: this.selectedUserTypeItem.key == 'external',
          selectedColumns: this.selectedUserTypeItem.key === 'entity' ?
            this.entitySelectedColumn : this.entityUsersSelectedColumn,
          isPaginated: 'false'
        }
      ).subscribe({
        next: (resp: any) => {
          if (isEntity) {
            this.allUsersOverviewEntityData = resp.data;
          }
          if (!isEntity) {
            this.allUsersOverviewData = resp.data;
          }
          subs.unsubscribe();
          resolve();
        },
        error: (error) => {
          this._toasterService.error(error?.message);
          subs.unsubscribe();
          reject(error);
        }
      });
      this.subs.add(subs);
    });
  }

  async downloadOverviewReport(fileType: any, fileName: string) {
    const dataFetcher = async () => {
      if(this.selectedUserTypeItem.key === 'entity'){
        await this.callUsersandEntitiesListAsync();
        return this.allUsersOverviewEntityData;
      } else {
        await this.callUsersandEntitiesListAsync([this.userTypeEntity.key + '']);
        return this.allUsersOverviewData;
      }
    };
    await this._dashboardDownloadService.downloadWithAsyncData(
      fileType,
      fileName,
      dataFetcher
    );
  }

  async downloadGroupListReport(fileType: any, fileName: string) {
    const dataFetcher = async () => {
      await this.getAllUsersGroupAsync(this.userListSelectedColumn, this.userTypeEntity.key ?? '');
      return this.allUserListTableData;
    };
    const dataTransformer = (data: any[]) => {
      if(this.userListSelectedColumn.includes('group') || this.userListSelectedColumn.length === 0){
        return data.map((item: any) => ({
          ...item,
          group: item.group.join(', ')
        }));
      }
      return data;
    };
    await this._dashboardDownloadService.downloadWithAsyncData(
      fileType,
      fileName,
      dataFetcher,
      dataTransformer
    );
  }

  getAllUsersGroupAsync(selectedColumns : string[], entityId: string = ''): Promise<void> {
    return new Promise((resolve, reject) => {
      const pageInfo: { isPaginated: boolean } = { isPaginated: false };
      const selectedEntity = entityId === '' ? this.columListEntityDropdown[0].key : entityId;
      const query = this._api.getParams(pageInfo);
      const subs = this._api.postMethodRequest(`${userApi.userGroupList}?${query}`, {entityId: selectedEntity, selectedColumns }).subscribe({
        next: ((data: { totalCount: number, result: UserListWithGroup[] }) => {
          this.allUserListTableData = data.result;
          subs.unsubscribe();
          resolve();
        }),
        error: (err) => {
          subs.unsubscribe();
          reject(err);
        }
      });
    });
  }

  async downloadtoolsTableReport(fileType: any, fileName: string) {
    const dataFetcher = async () => {
      await this.getAllToolsTableAsync(this.createParamData(), { isPaginated: 'false' });
      return this.allToolsTableData;
    };
    await this._dashboardDownloadService.downloadWithAsyncData(
      fileType,
      fileName,
      dataFetcher
    );
  }

  getAllToolsTableAsync(parmData: ApiParams, pageInfo: { isPaginated: string }): Promise<void> {
    return new Promise((resolve, reject) => {
      const params = { ...parmData, sessionType: this.selectedToolsTab };
      const query = this._api.getParams(pageInfo);
      const subs = this._api.postMethodRequest(`${userApi.selfServiceUser}?${query}`, params).subscribe({
        next: ((data: { count: number, results: SelfServiceUserData[] }) => {
          this.allToolsTableData = data.results;
          subs.unsubscribe();
          resolve();
        }),
        error: (err) => {
          subs.unsubscribe();
          reject(err);
        }
      });
    });
  }

  downloadReport(type: any, data: any, downloadFileName: string): void {
    this._dashboardDownloadService.downloadReport({
      type,
      data,
      fileName: downloadFileName
    });
  }

  downloadUsersAndEntitiesReport(type: any, data: any, downloadFileName: string): void {
    this._dashboardDownloadService.downloadReport({
      type,
      data,
      fileName: downloadFileName
    });
  }

  getTopPerformersFileName(): string {
    const entityType = this.selectedTopKey === 'users' ? 'Users' : 'Entities';
    return `Top 10 Performers ${entityType}`;
  }

  userListWithAccessParamsOutput(value: UserAccessParams) {
    this.userListWithAccessParams = value;
    this.callSelfServiceUserList();
  }

  getSelectedEntityAccessDetails(entityId: string) {
    const details = this.entitiesOverView.find((item: EntitiesOverview) => item.entity_id === entityId);
    if (details) {
      this.selectedEntityAccessDetail = details;
    }
  }

  // User list for Entity users overview
  callSelfServiceUserList(offset: { page: number; offset: number; } = { page: 1, offset: 0 }) {
    const pageInfo: { limit: number, offset: number } = { limit: 10, offset: offset.offset }
    this.userAccessLoader.set(true);
    const query = this._api.getParams(pageInfo);
    const subs = this._api.postMethodRequest(`${userApi.userAccessList}?${query}`, this.userListWithAccessParams).subscribe({
      next: ((data: { userData: UserListWithAccess[], total_count: number }) => {
        this.userListWithAccess.set(data.userData);
        this.userListWithAccessSize.set(data.total_count);
        this.userAccessLoader.set(false);
        subs.unsubscribe();
      }),
      error: () => {
        this.userAccessLoader.set(false);
        subs.unsubscribe();
      }
    });
  }


  callIndicator(parmData: ApiParams) {
    this.callDomainsLoader = true;
    const subs = this._api.postMethodRequest(userApi.dashboardData, parmData).subscribe(
      {
        next: (data => {
          this.data = data;
          this.setDomain();
          this.callDomainsLoader = false;
          this.columListEntityDropdown = this.data.entities.map(dataValue => {
            return {
              key: dataValue.name, value: dataValue.name, checked: true
            };
          });
          this.selectedEntities.set(cloneDeep(this.columListEntityDropdown));
          this.userTypeEntity = this.selectedEntities()[0];
          subs.unsubscribe();
        }),
        error: () => {
          this.callDomainsLoader = false;
          subs.unsubscribe();
        }
      }

    );
  }


  // dateChangeValue() {
  //   const today = new Date(this.startDate.value ?? '');
  //   // Add 365 days to the current date
  //   today.setDate(today.getDate() + 365);

  //   // Display the new date in YYYY-MM-DD format
  //   const futureDate = today.toISOString().split('T')[0];
  //   this.maxDate = new Date(futureDate ?? '') < new Date() ? new Date(futureDate ?? '') : new Date();

  // }

  // self service api
  callSelfService(parmData: ApiParams) {
    this.usageSelfServiceUser = [];
    this.loaderSelfService = true;
    const subs = this._api.postMethodRequest(userApi.selfService, parmData).subscribe({
      next: ((data: SelfService[]) => {
        this.selfServiceData = data;
        const index = this.selfServiceData?.findIndex(selfServiceData => selfServiceData.sessionType === this.currentSelfService);
        if (index !== -1) {
          this.activeIndexDomain = index;
        } else {
          this.currentSelfService = data?.[0]?.sessionType ?? '';
          this.activeIndexDomain = 0;
        }
        subs.unsubscribe();
        this.callSelfServiceUser(parmData, { limit: 10, offset: 0 });
        this.loaderSelfService = false;
      }),
      error: () => {
        this.loaderSelfService = false;
        subs.unsubscribe();
      }
    });
  }

  callSelfServiceUsers(data: PillsTabData) {
    this.selectedToolsTab = data.key;

  }

  // call self service users
  callSelfServiceUser(parmData: ApiParams, pageInfo: { limit: number, offset: number } = { limit: 10, offset: 0 }) {
    this.loaderUser = true;
    const params = { ...parmData, sessionType: this.selectedToolsTab };
    const query = this._api.getParams(pageInfo);
    const subs = this._api.postMethodRequest(`${userApi.selfServiceUser}?${query}`, params).subscribe({
      next: ((data: { count: number, results: SelfServiceUserData[] }) => {
        this.usageSelfServiceUser = data.results;
        this.userListSize = data.count;
        const tableAttributes = this.setUserTableData(this.usageSelfServiceUser);
        this.toolsTableData = tableAttributes?.data ?? [];
        this.toolsTableHead = tableAttributes?.tableHead ?? [];
        subs.unsubscribe();
        this.loaderUser = false;
      }),
      error: () => {
        this.loaderUser = false;
        subs.unsubscribe();
      }
    });

  }


  toolsPageChange(event: number) {
    this.toolsOffset = event + 1;
    this.toolsPage = (event / this.toolsPer_page) + 1;
    this.callSelfServiceUser(this.createParamData(), { limit: this.toolsPer_page, offset: this.toolsOffset });
  }

  toolslimitChanged(limit: number) {
    this.toolsPer_page = limit;
    this.callSelfServiceUser(this.createParamData(), { limit: this.toolsPer_page, offset: this.toolsOffset });
  }

  selectToolsTab(event: PillsTabData) {
    this.selectedToolsTab = event.key;
    this.toolsPage = 1;
    this.toolsOffset = 0;
    this.callSelfServiceUser(this.createParamData(), { limit: this.toolsPer_page, offset: this.toolsOffset });
  }

  createParamData() {
    const parmData: ApiParams = {
      startDate: this._datePipe.transform(this.startDate.value, 'y-MM-dd'),
      endDate: this._datePipe.transform(this.endDate.value, 'y-MM-dd'),
      mobile: this.isMobile
    };
    parmData['entityNames'] = this.selectedEntities().map((item: Tags) => item?.shortValue ?? '');
    parmData['entityId'] = this.selectedEntities().map((item: Tags) => item?.key ?? '').toString();
    return parmData;
  }

  callGeopSpatialOverView(parmData: ApiParams) {
    const subs = this._api.postMethodRequest(userApi.geoSpatialOverview, parmData).subscribe({
      next: (data) => {
        this.geoSpecialDistrict.set(data);
        subs.unsubscribe();
      },
      error: (error) => {
        this._toasterService.error(error?.error?.message)
        subs.unsubscribe();
      }
    })
  }

  // call  users group list
  callUsersGroup(entityId: string = '', pageInfo: { limit: number, offset: number, isPaginated? : boolean } = { limit: 10, offset: 0, isPaginated : true }) {
    this.userGroupLoader.set(true);
    const selectedEntity = entityId === '' ? this.columListEntityDropdown[0].key : entityId;
    const query = this._api.getParams(pageInfo);
    const subs = this._api.postMethodRequest(`${userApi.userGroupList}?${query}`, {entityId: selectedEntity}).subscribe({
      next: ((data: { totalCount: number, result: UserListWithGroup[] }) => {
        this.userListWithGroup.set(data.result);
        this.userListTableData = data.result;
        this.userGroupListSize = data.totalCount;
        this.userListTableTitles = Object.keys(this.userListWithGroup()[0]).map(key => ({key: key.toLowerCase(), value: this._ifpTitleCasePipe.transform(key), checked: true}));
        this.userGroupLoader.set(false);
        subs.unsubscribe();
      }),
      error: () => {
        this.userGroupLoader.set(false);
        subs.unsubscribe();
      }
    });
  }


  // pagination for user changes
  pageChangeUserGroup(event: { page: number, offset: number }) {
    this.userGroupListOffset = event.offset;
    this.callUsersGroup(this.userTypeEntity.key ?? '', { limit: 10, offset: event.offset });
  }

  // pagination for user changes
  pageChange(event: { page: number, offset: number }) {
    this.userListOffset = event.offset;
    this.callSelfServiceUser(this.currentParams, { limit: 10, offset: event.offset });
  }

  // To call entities list dropdown
  callEntitiesList() {
    const subs = this._api.getMethodRequest(userApi.entitiesList).subscribe({
      next: (data => {
        this.selectedEntities.set([]);
        this.columListEntityDropdown = data
          .filter((item: Record<string, string>) => item['name'] !== null)
          .map((item: Record<string, string>, index: number) => ({
            key: item['entity_id'],
            value: item['name'],
            shortValue: item['entity_domain'],
            checked: true
          }));
        this.overviewColumListEntityDropdown = cloneDeep(this.columListEntityDropdown);
        this.externalEntitiesOptions = cloneDeep(this.columListEntityDropdown.filter(x => x.shortValue != 'SCAD'))
        this.selectedEntities.set(cloneDeep(this.overviewColumListEntityDropdown))
        // this.multiSelectedEntities().push(this.overviewColumListEntityDropdown[0]);
        this.multiSelectedEntities.set(cloneDeep(this.overviewColumListEntityDropdown));
        this.isAllEntitySelected.set(true);
        // this.selectedEntity = this.selectedEntities()[0];
        // this.userTypeEntity = this.selectedEntities()[0];
        // this.userTypeEntity = this.selectedEntities()[0];
        this.callApis();
        subs.unsubscribe();
      }),
      error: () => {
        subs.unsubscribe();
      }
    });
  }

  // close user list modal
  closeModal() {
    this.modal.removeModal();
    this._modalService.removeAllModal();
  }

  // call domain  list
  callDomains(parmData: ApiParams) {
    this.callDomainsLoader = true;
    const subs = this._api.postMethodRequest(userApi.domains, parmData).subscribe({
      next: async (data) => {
        this.overallDomains = data;
        this.selectedDomain = this.overallDomains[0]?.domain ?? '';
        const [
          officialStatistics,
          experimentalStatistics,
          analyticalApps,
          reports
        ] = await Promise.all([
          this.callOfficialStatistics(parmData),
          this.callExperimentalStatistics(parmData),
          this.callAnalyticalApps(parmData),
          this.callReports(parmData)
        ]);
        this.officialStatistics = officialStatistics;
        this.experimentalStatistics = experimentalStatistics;
        this.analyticalApps = analyticalApps;
        this.reports = reports;
        this.prepareOfficialStatisticsMap();
        this.setDomain();
        this.callDomainsLoader = false;
        subs.unsubscribe();
      },
      error: () => {
        subs.unsubscribe();
        this.callDomainsLoader = false;
      }
    });
  }

  prepareOfficialStatisticsMap() {
    [...this.officialStatistics, ...this.experimentalStatistics, ...this.reports].forEach((officialStatistic: OfficialStatistic) => {
      officialStatistic.nodes.forEach((node: Node) => {
        this.officialStaticsMap[officialStatistic.domain + '_' + node.name] = node.id;
      });
    });
    this.analyticalApps.forEach((analyticalApp: AnalyticalApp) => {
      analyticalApp.category.forEach((cat: Category) => {
        cat.nodes.forEach((node: Node) => {
          this.officialStaticsMap[analyticalApp.domain + '_' + node.name] = node.id;
        })
      })
    })
  }


  callOfficialStatistics(parmData: ApiParams) {
    return new Promise<OfficialStatistic[]>((res) => {
      const subs = this._api.postMethodRequest(userApi.officialStatistics, parmData).subscribe({
        next: (data => {
          res(data);
          this.officialStatistics = data;
          subs.unsubscribe();
        }),
        error: () => {
          res([]);
          subs.unsubscribe();
        }
      });
    });
  }

  callExperimentalStatistics(parmData: ApiParams) {
    return new Promise<OfficialStatistic[]>((res) => {
      const subs = this._api.postMethodRequest(userApi.experimentalStatistics, parmData).subscribe({
        next: (data => {
          res(data);
          subs.unsubscribe();
        }),
        error: () => {
          res([]);
          subs.unsubscribe();
        }
      });
    });
  }

  callAnalyticalApps(parmData: ApiParams) {
    return new Promise<AnalyticalApp[]>((res) => {
      const subs = this._api.postMethodRequest(userApi.analyticalApps, parmData).subscribe({
        next: (data => {
          res(data);
          subs.unsubscribe();
        }),
        error: () => {
          res([]);
          subs.unsubscribe();
        }
      });
    });
  }

  callReports(parmData: ApiParams) {
    return new Promise<OfficialStatistic[]>((res) => {
      const subs = this._api.postMethodRequest(userApi.reports, parmData).subscribe({
        next: (data => {
          res(data);
          subs.unsubscribe();
        }),
        error: () => {
          res([]);
          subs.unsubscribe();
        }
      });
    });
  }

  callActiveAndInactiveUsers(parmData: ApiParams) {
    this.callActiveAndInactiveUsersLoader = true;
    const subs = this._api.postMethodRequest(userApi.activeUsers, parmData).subscribe(
      {
        next: (data => {
          this.activeUsers = data;
          subs.unsubscribe();
          this.callActiveAndInactiveUsersLoader = false;
        }),
        error: () => {
          subs.unsubscribe();
          this.callActiveAndInactiveUsersLoader = false;
        }
      }

    );
  }

  callTotalUsers(parmData: ApiParams) {
    this.callTotalUsersLoader = true;
    const subs = this._api.postMethodRequest(userApi.totalUsers, parmData).subscribe(
      {
        next: (data => {
          this.totalUser = data;
          subs.unsubscribe();
          this.callTotalUsersLoader = false;
        }),
        error: () => {
          subs.unsubscribe();
          this.callTotalUsersLoader = false;
        }
      }

    );
  }

  callTotalUsage(parmData: ApiParams) {
    this.callTopUserUsageLoader = true;
    const subs = this._api.postMethodRequest(userApi.totalUsage, parmData).subscribe(
      {
        next: (resp => {
          this.callTopUserUsageLoader = false;
          this.topUsers = resp;
          const filteredData = this.setColumnTitles(this.performerSelectedColumn, this.topUsers);
          const { data = [], tableHead = [] } = this.setUserTableData(filteredData);
          [this.topUsersTableData, this.topUsersTableHeader] = [data, tableHead];
          this.topUserTableTitles = Object.keys(this.topUsers[0]).map(key => ({key: key.toLowerCase(), value: this._ifpTitleCasePipe.transform(key), checked: true}));
          subs.unsubscribe();
        }),
        error: () => {
          this.callTopUserUsageLoader = false;
          subs.unsubscribe();
        }
      }

    );
  }

  callPeakValueUsage(parmData: ApiParams) {
    this.callPeakValueUsageLoader = true;
    const subs = this._api.postMethodRequest(userApi.peakValueUsage, parmData).subscribe(
      {
        next: ((data: PeakTimeOfUsage) => {
          this.peakValueChart = data.hourlySplit;
          subs.unsubscribe();
          this.callPeakValueUsageLoader = false;
          this.averageUsage = data.avgUsage;
        }),
        error: () => {
          subs.unsubscribe();
          this.callPeakValueUsageLoader = false;
        }
      }

    );
  }

  callTopEntityUsage(parmData: ApiParams) {
    this.callTopEntitiesLoader = true;
    const subs = this._api.postMethodRequest(userApi.topEntityUsage, parmData).subscribe(
      {
        next: (resp => {
          this.callTopEntitiesLoader = false;
          this.topEntities = resp;
          const filteredData = this.setColumnTitles(this.topEntitySelectedColumn, this.topEntities);
          const { data = [], tableHead = [] } = this.setUserTableData(filteredData);
          [this.topEntitiesTableData, this.topEntitiesTableHeader] = [data, tableHead];
          this.topEntityTableTitles = Object.keys(this.topEntities[0]).map(key => ({key: key.toLowerCase(), value: this._ifpTitleCasePipe.transform(key), checked: true}));
          subs.unsubscribe();
        }),
        error: () => {
          subs.unsubscribe();
          this.callTopEntitiesLoader = false;
        }
      }

    );
  }

  callTotalEntities(parmData: ApiParams) {
    this.callTotalEntityLoader = true;
    const subs = this._api.postMethodRequest(userApi.topEntity, parmData).subscribe(
      {
        next: (data => {
          this.totalEntities = data;
          this.totalEntities.forEach((entity: TotalEntity) => {
            entity.count = entity.active_domains.length;
          });
          subs.unsubscribe();
          this.callTotalEntityLoader = false;
        }),
        error: () => {
          subs.unsubscribe();
          this.callTotalEntityLoader = false;
        }
      }

    );
  }

  dateChange() {
    if (this.endDate.value === null) {
      this.endDate.setValue(this.startDate.value);
    }
    this.callApis();
  }

  setNodesList(data: any): any[] {
    if (data?.length <= 0) { return [] }
    let nodesList: any[] = [];
    data.forEach((domain: { nodes: { name: string; count: number; }[]; domain: string; }) => {
      if (domain?.nodes?.length > 0) {
        domain.nodes.forEach((node: { name: string; count: number; }) => {
          nodesList.push({
            indicator: node.name,
            domain: domain?.domain,
            'number of visits': node.count
          });
        });
      }
    });
    return nodesList;
  }

  setAnalyticalAppNodesList(data: any): any[] {
    if (data?.length <= 0) { return [] }
    let nodesList: any[] = [];
    data.forEach((domain: { category: any[]; domain: string; }) => {
      if (domain?.category?.length > 0) {
        domain.category.forEach((cat: any) => {
          if (cat.nodes?.length > 0) {
            cat.nodes.forEach((node: { name: string; count: number; }) => {
              nodesList.push({
                indicator: node.name,
                domain: domain?.domain,
                product: cat.name,
                'number of visits': node.count
              });
            });
          }
        });
      }
    });
    return nodesList;
  }

  setDomain() {
    this.domain = [
      {
        name: 'Official',
        key: 'Official',
        tab: false,
        node: this.setNodesList(this.officialStatistics)
      },
      {
        name: 'Experimental',
        key: 'Experimental',
        tab: false,
        node: this.setNodesList(this.experimentalStatistics)
      },
      {
        name: 'Analytical Apps',
        key: 'analyticalApps',
        tab: true,
        node: this.setAnalyticalAppNodesList(this.analyticalApps)
      },
      { name: 'Reports', key: 'reports', tab: false, node: this.setNodesList(this.reports) }
    ];
    const selectedDomainIndex = this.domain.findIndex((domain: ClassificationTab) => domain.key === this.selectedIndicatorTab);
    if (selectedDomainIndex >= 0) {
      const tableAttributes = this.setUserTableData(this.domain[selectedDomainIndex].node);
      this.indicatorTableHead = tableAttributes?.tableHead ?? [];
      this.indicatorTableData = tableAttributes?.data ?? [];
    }
  }

  selectIndicatorType(event: PillsTabData) {
    this.selectedIndicatorTab = event.key;
    const selectedDomainIndex = this.domain.findIndex((domain: ClassificationTab) => domain.key === event.key);
    if (selectedDomainIndex >= 0) {
      const tableAttributes = this.setUserTableData(this.domain[selectedDomainIndex].node);
      this.indicatorTableHead = tableAttributes?.tableHead ?? [];
      this.indicatorTableData = tableAttributes?.data ?? [];
    }
  }

  // user list arrow click event
  arrowClickEvent(event: ArrowClick) {
    this.userGroups.set(event.value);
    // this.enableUserGroup.set(true);
    this.modalView();
  }

  selectedFilter(item: any) {
    this.selectedEntity = item;
  }

  callEntityAdminDetail(entityId: any, status: string = 'active') {
    this.userDetailLoader.set(true);
    this.subs.add(
      this._api.getMethodRequest(userAccessKpi.entityDetails, { entity_id: entityId, status: status }).subscribe({
        next: (resp: any) => {
          this.userDetails = resp.user;
          // Directly set the new data - the trackBy function will handle animation triggering
          if(resp.designationSummary?.length > 0){
            this.userDesignationSummary.set(resp.designationSummary);
          } else {
            this.userDesignationSummary.set([]);
          } 
          if (this.userTableTitleData.length === 0) {
            this.userTableTitleData = Object.keys(this.userDetails[0]).map(key => ({key: key.toLowerCase(), value: this._ifpTitleCasePipe.transform(key), checked: true}));
          }
          const filteredData = this.setColumnTitles(this.userSelectedColumn, this.userDetails);
          const { data = [], tableHead = [] } = this.setUserTableData(filteredData);
          [this.userDetailTableData, this.userTableHead] = [data, tableHead];
           // Add keys from userTableHead to headingSettings if not present
            this.userTableHead.forEach((key: string) => {
            if (!this.headingSettings.hasOwnProperty(key)) {
              this.headingSettings[key] = { sort: true };
            }
            });
          this.filteredUserDetailTableData = this.userDetailTableData;
          this.search = '';
          this.userDetailLoader.set(false);
        },
        error: (error) => {
          this._toasterService.error(error?.message);
          this.userDetailLoader.set(false);
        }
      })
    );
  }

  setUserTableData(data: any) {
    if (data?.length <= 0) {
      return { data: [], tableHead: [] };
    }
    let tableComponents: {
      data: any[],
      tableHead: string[]
    } = {
      data: [],
      tableHead: []
    };
    tableComponents.tableHead = Object.keys(data[0]).map(key => key.replace(/_/g, ' '))
    data.forEach((item: EntityDetails) => {
      let tableData: any[] = [];
      Object.entries(item).forEach(([key, value]) => {
        let renderComponentValue = () => import('../../scad-insights/data-governance-new/ifp-status-tag/ifp-status-tag.component').then(mode => mode.IfpStatusTagComponent)
        const multiComponents = key === 'permission' ? (value as any[]).map((val: any) => ({
          key,
          title: key,
          value: val,
          type: 'custom',
          renderComponent: renderComponentValue,
          inputValues: { name: val, color: this._api.checkColor(val), enableDot: false }
        })) : [];
        let role;
        if (key === 'user_type') {
          role = value.toLowerCase() === 'user' ? 'Normal User' : roleList[value];
        }
        tableData.push({
          key: key.replace(/_/g, ' '),
          value: key === 'user_type' ? role : value,
          type: key != 'permission' ? 'defualt' : 'custom',
          multiComponents: key != 'permission' ? undefined : multiComponents,
          title: key.replace(/_/g, ' '),
          customKey: key === 'user' ? 'name' : undefined,
          customValue: key === 'user' ? value?.name : undefined,
          click: key === this._translate.instant('number of visits')
        })
      })
      tableComponents.data.push(tableData);
    })
    return tableComponents;
  }

  async productVisitsClicked(event: any) {
    const selected_productVisits_nodeId = this.officialStaticsMap[event.row[1].value + '_' + event.row[0].value];
    this.modalTitle = `${event.row[0].value}`;

    this.usersDetailsParmData = {
      startDate: this._datePipe.transform(this.startDate.value, 'y-MM-dd'),
      endDate: this._datePipe.transform(this.endDate.value, 'y-MM-dd'),
      nodeId: selected_productVisits_nodeId,
      mobile: this.isMobile
    };
    
    await this.getUsersDetails(userApi.usersVisits, this.usersDetailsParmData);
    
    this.prepareModalTable();
    
    this.usersModalView();
  }

  async usersModalClicked(paramsObject: {downloadType?: string; nodeId?: string}, indicatorName: string, subTitle: string, url: string) {
    this.modalTitle = `${subTitle}${indicatorName}`;

    this.usersDetailsParmData = {
      startDate: this._datePipe.transform(this.startDate.value, 'y-MM-dd'),
      endDate: this._datePipe.transform(this.endDate.value, 'y-MM-dd'),
      entityNames: this.selectedEntities().map((item: Tags) => item?.shortValue ?? ''),
      mobile: this.isMobile,
      indicatorName: indicatorName,
      ...paramsObject
    };
    
    await this.getUsersDetails(url, this.usersDetailsParmData);
    
    this.prepareModalTable();
    
    this.usersModalView();
  }
  async hiesVisitDetails(paramsObject: {sessionType: string},subTitle: string, url: string) {
    this.modalTitle = `${subTitle}`;

    let hiesVisitDetails = {
      startDate: this._datePipe.transform(this.startDate.value, 'y-MM-dd'),
      endDate: this._datePipe.transform(this.endDate.value, 'y-MM-dd'),
      mobile: this.isMobile,
      ...paramsObject
    };
    
    await this.getUsersDetails(url,hiesVisitDetails);
    
    this.prepareModalTable();
    
    this.usersModalView();
  }
  OnSelectedTabHies(event: string){
     const parmData: ApiParams = {
      startDate: this._datePipe.transform(this.startDate.value, 'y-MM-dd'),
      endDate: this._datePipe.transform(this.endDate.value, 'y-MM-dd'),
      mobile: this.isMobile
    };
    parmData['entityNames'] = this.selectedEntities().map((item: Tags) => item?.shortValue ?? '');
    parmData['entityId'] = this.selectedEntities().map((item: Tags) => item?.key ?? '').toString();
    const apiUrl:string = event === 'users' ? userApi.hiesDuration : userApi.hiesDurationEntity;
   this.getHiesDuration(parmData, apiUrl)
  }


  prepareModalTable() {
    this.setUsersDetailsTableData();
    this.usersTableData = this.tableUsersDetailesComponent.data; 
    this.usersTableHead  = this.tableUsersDetailesComponent.tableHead;
  }



  setUsersDetailsTableData() {
    if (this.usersDetailes?.length <= 0) {
      this.tableUsersDetailesComponent = { data: [], tableHead: [] };
      return;
    }
    this.tableUsersDetailesComponent = {
      data: [],
      tableHead: []
    };
    this.tableUsersDetailesComponent.tableHead = Object.keys(this.usersDetailes[0]).map(key => key.replace(/_/g, ' '))
    this.usersDetailes.forEach((item) => {
      let tableData: any[] = [];
      Object.entries(item).forEach(([key, value]) => {
        let renderComponentValue = () => import('../../scad-insights/data-governance-new/ifp-status-tag/ifp-status-tag.component').then(mode => mode.IfpStatusTagComponent)
        const multiComponents = key === 'permission' ? (value as any[]).map((val: any) => ({
          key,
          title: key,
          value: val,
          type: 'custom',
          renderComponent: renderComponentValue,
          inputValues: { name: val, color: this._api.checkColor(val), enableDot: false }
        })) : [];
        tableData.push({
          key: key.replace(/_/g, ' '),
          value: value,
          type: key != 'permission' ? 'defualt' : 'custom',
          title: key.replace(/_/g, ' ')
        })
      })
      this.tableUsersDetailesComponent.data.push(tableData);
    })
  }

  getUsersDetails(url: string, parmData: ApiParams) {
    return new Promise((res) => {
      const subs = this._api.postMethodRequest(url, parmData).subscribe({
        next: (data => {
          res(data);
          this.usersDetailes = data;
          subs.unsubscribe();
        }),
        error: () => {
          res([]);
          subs.unsubscribe();
        }
      });
    });
  }

  callEntityClassification(params: any = {}) {
    this.subs.add(
      this._api.getMethodRequest(userAccessKpi.entityOverview, params).subscribe({
        next: (data) => {
          this.dataClassificationOverviewLoader = false;

          this.dataClassificationOverview().classification = data.map((x: { name: string; }) => {
            if (x.name.toLowerCase() === dataClassification.confidential.toLowerCase()) {
              this.selectedEntityClassificationLevel = x.name;
            }
            return { key: x.name, value: x.name, color: this.setAccessLevelColor(x.name.toLowerCase()) }
          });

          this.callEntityDomains({ entityId: params.entityId, classification: this.selectedEntityClassificationLevel });

          this.setInitialActiveIndex();
        },
        error: (error) => {
          this.dataClassificationOverviewLoader = false;
          this._toasterService.error(error?.error?.message);
        }
      })
    )
  }

  setAccessLevelColor(level: string) {
    switch (level) {
      case 'open':
        return '#249F00';
      case 'confidential':
        return '#FF6600';
      case 'sensitive':
        return '#FF0000';
      case 'secret':
        return '#8E0000';
      default: return '#FF6600'
    }
  }

  callEntityDomains(params: any = {}) {
    this.subs.add(
      this._api.getMethodRequest(userAccessKpi.entityDomain, params).subscribe({
        next: (data) => {
          this.dataClassificationOverview().domain = data;
          if (data.length) {
            this.selectedEntityDomain = this.dataClassificationOverview().domain[0].name ?? '';
            this.callEntityProducts({ classification: params.classification, domain: this.dataClassificationOverview().domain[0].name });
            this.setInitialActiveIndex();
          }
        },
        error: (error) => {
          this._toasterService.error(error?.error?.message);
        }
      })
    )
  }

  callEntityProducts(params: any = {}) {
    this.subs.add(
      this._api.getMethodRequest(userAccessKpi.entityProducts, params).subscribe({
        next: (data) => {
          this.dataClassificationOverview().products = data;
          if (data.length) {
            this.getProductNodes({
              classification: this.selectedEntityClassificationLevel, domain: this.selectedEntityDomain,
              product: this.dataClassificationOverview().products[0]
            });
          } else {
            this.dataClassificationOverview().indicator = [];
          }
        },
        error: (error) => {
          this._toasterService.error(error?.error?.message);
        }
      })
    )
  }

  getProductNodes(params: any = {}) {
    this.subs.add(
      this._api.postMethodRequest(userAccessKpi.nodeList, params).subscribe({
        next: (data) => {
          this.dataClassificationOverview().indicator = data;
        },
        error: (error) => {
          this._toasterService.error(error?.error?.message);
        }
      })
    )
  }

  setInitialActiveIndex() {
    if (!this.sideBarCmp) { return; }
    this.sideBarCmp.selectedDomainIndex = 0;
    this.sideBarCmp.selectedProductIndex = 0;
  }

  getMostDownloadedIndicators(parmData: ApiParams) {
    this.downloadInsightsLoader.set(true);
    this.subs.add(
      this._api.postMethodRequest(userApi.mostDownloadedIndicators, parmData).subscribe({
        next: (data: DownloadInsights[]) => {
          if (data?.length > 0) {
            this.downloadInsightsTabData = data.map((item: DownloadInsights) => {
              return { name: item.type.toUpperCase(), key: item.type, node: item.indicators }
            });
            this.downloadInsights = this.downloadInsightsTabData?.[0]?.node;
          }
          this.downloadInsightsLoader.set(false);
        },
        error: (error) => {
          this._toasterService.error(error?.error?.message);
          this.downloadInsightsLoader.set(false);
        }
      })
    )
  }

  getMostNotificationEnabledIndicators(parmData: ApiParams) {
    this.notificationEnabledInsightsLoader.set(true);
    this.subs.add(
      this._api.postMethodRequest(userApi.mostEnabledIndicators, parmData).subscribe({
        next: (data) => {
          this.notificationEnabledInsights = data.map((item: NotificationEnabled) => {
            this.notificationEnabledInsightsLoader.set(false);
            return { id: item.NODE_ID, name: item.name, key: item.name, count: item.OCCURRENCE_COUNT }
          });
        },
        error: (error) => {
          this._toasterService.error(error?.error?.error);
          this.notificationEnabledInsightsLoader.set(false);
        }
      })
    )
  }

    getHiesVisits(parmData: ApiParams) {
    this.hiesVisitLoader.set(true);
    this.subs.add(
      this._api.postMethodRequest(userApi.hiesVisits, parmData).subscribe({
        next: (data) => {
          this.hiesVisitsData = data.data.map((item: HiesVisits) => {
            this.hiesVisitLoader.set(false);
            return { id: item.page_path, indicator: this._ifpTitleCasePipe.transform(item.page_path), key: item.page_path, visits: item.visits, total:data.total }
          });
        },
        error: (error) => {
          this._toasterService.error(error?.error?.error);
          this.hiesVisitLoader.set(false);
        }
      })
    )
  }

  getHiesDuration(parmData: ApiParams,apiUrl:string) {
    this.hiesDurationLoader.set(true);
    this.subs.add(
      this._api.postMethodRequest(apiUrl, parmData).subscribe({
        next: (data) => {
          this.hiesDurationData = data;
          this.hiesDurationLoader.set(false);
          // .map((item: NotificationEnabled) => {
          //   this.hiesDurationLoader.set(false);
          //   return { id: item.NODE_ID, name: item.name, key: item.name, count: item.OCCURRENCE_COUNT }
          // });
        },
        error: (error) => {
          // this._toasterService.error(error?.error?.error);
          this.hiesDurationLoader.set(false);
        }
      })
    )
  }

  onSelectPlatform(event: { name: string; value: string }) {
    this.isMobile = event.value === 'mobile';
    this.selectedPlatform = event.name;
    this.callApis();
  }



  callUsersandEntitiesList(ids: string[] = []) {
    let selectedEntityIds = ids.length ? ids : this.userTypeEntity?.key;
    this.userAndEntityListLoader = true;
    const isEntity = this.selectedUserTypeItem.key === 'entity';
    this.subs.add(
      this._api.getMethodRequest(isEntity ? userAccessKpi.entitiesUserCount : userAccessKpi.userDetails, {
        limit: this.userOverviewPerPage,
        offset: this.userOverviewOffset, entity_id: selectedEntityIds, external: this.selectedUserTypeItem.key == 'external'
      }).subscribe({
        next: (resp: any) => {
          let tableData: any;
          if (isEntity && this.entityTableColumns.length === 0) { //  Entities List tab
            this.userOverviewEntityData = resp.data;
            tableData = this.setUserTableData(this.userOverviewEntityData);
            this.userOverviewEntityTableData = tableData?.data ?? [];
            this.entityTableColumns = Object.keys(this.userOverviewEntityData[0]).map(key => ({key: key.toLowerCase(), value: this._ifpTitleCasePipe.transform(key), checked: true}));
            this.userOverviewEntityTableHead = tableData?.tableHead;
          }
          if (!isEntity){ // External User and SCAD Users tab
            this.userOverviewData = resp.data;
            if (this.entityTableUserListColumns.length === 0) {
              this.entityTableUserListColumns = Object.keys(this.userOverviewData[0]).map(key => ({key: key.toLowerCase(), value: this._ifpTitleCasePipe.transform(key), checked: true}));
            }
            const filteredData = this.setColumnTitles(this.entityUsersSelectedColumn, this.userOverviewData);
            const { data = [], tableHead = [] } = this.setUserTableData(filteredData);
            [this.userOverviewTableData, this.userOverviewTableHead] = [data, tableHead];
          }
          this.userOverviewTotalRow = resp.totalCount;
          this.userAndEntityListLoader = false;
        },
        error: (error) => {
          this.userAndEntityListLoader = false;
          this._toasterService.error(error?.message);
        }
      })
    );
  }




  userOverviewTabSelection(event: PillsTabData) {
    this.selectedUserTypeItem = event;
    this.userOverviewOffset = 0;
    this.userOverviewPage = 1;
    this.callUsersandEntitiesList();
  }

  onUserOverviewPageChange(event: number) {
    this.userOverviewOffset = event + 1;
    this.userOverviewPage = (event / this.userOverviewPerPage) + 1;
    this.callUsersandEntitiesList();
  }

  onUserOverviewlimitChanged(limit: number) {
    this.userOverviewPerPage = limit;
  }

  changeDataEntity(event: any) {
    this.userTypeEntity = event;
    this.callEntityClassification({ entityId: event.key })
    this.callTotalUsersCount({ entityId: event.key });
    this.callEntityAdminDetail(event.key);
    this.callUsersGroup(this.userTypeEntity.key);
    this.callUsersandEntitiesList([event.key + '']);
  }

  changeDataClassification(event: PillsTabData) {
    this.selectedEntityClassificationLevel = event.key;
    this.callEntityDomains({ classification: this.selectedEntityClassificationLevel, entityId: this.columListEntityDropdown[0].key })
  }

  changeDomainOrProduct(event: { name: string, type: string }) {
    if (event.type === 'domain') {
      this.selectedEntityDomain = event.name;
      this.callEntityProducts({ classification: this.selectedEntityClassificationLevel, domain: this.selectedEntityDomain });
    } else {
      this.getProductNodes({
        classification: this.selectedEntityClassificationLevel, domain: this.selectedEntityDomain,
        product: event.name
      });
    }
  }

  changeEntityUsersOverview(event: any) {
    this.userTypeEntity = event;
    this.callUsersandEntitiesList([event.key + '']);
    this.callEntityClassification({ entityId: event.key })
    this.callTotalUsersCount({ entityId: event.key });
    this.callEntityAdminDetail(event.key);
    this.callUsersGroup(this.userTypeEntity.key);
  }
  
  entitiesMultiSelection(event: any) {
    this.multiSelectedEntities.set(event);
    // if (this.multiSelectedEntities()?.length <= 0) {
    //   this.overviewColumListEntityDropdown[0].checked = true;
    //   this.multiSelectedEntities.set([this.overviewColumListEntityDropdown[0]])
    // }
    this.entitiesIds = this.multiSelectedEntities().map((item: Tags) => item?.key ?? '').toString();
    this.callTotalUsersCount({ entityId: this.entitiesIds });
    this.callEntityAdminDetail(this.entitiesIds, this.selectedUserStatus);
    this.callEntityClassification({ entityId: this.entitiesIds })
    this.callUsersGroup(this.entitiesIds);
    this.callUsersandEntitiesList([this.entitiesIds + '']);
  }
  
  onSelectUserStatusTab(event: PillsTabData) {
    this.selectedUserStatus = event.key;
    this.userStatusOffset = 0;
    this.userStatusPage = 1;
    this.callEntityAdminDetail(this.entitiesIds, this.selectedUserStatus);
  }

  onSearch(event: string) {
    const searchResult = event.trim();
    this.search = event;
    this.filteredUserDetailTableData = this.userDetailTableData.filter((prod: any) => {
      return (prod[0].value).toLowerCase().includes(searchResult.toLowerCase()) || (prod[1].value).toLowerCase().includes(searchResult.toLowerCase());
    });
    
  }

  onSelectEntityUserGroup(event: any) {
    this.userTypeEntity = event;
    this.callUsersGroup(this.userTypeEntity.key);
    this.callTotalUsersCount({ entityId: event.key });
    this.callEntityAdminDetail(event.key);
    this.callEntityClassification({ entityId: event.key })
  }

  selectUserColumns(event: any): void {
    this.userSelectedColumn = event.map((e: any) => e.key);
    const filteredData = this.setColumnTitles(this.userSelectedColumn, this.userDetails);
    const { data = [], tableHead = [] } = this.setUserTableData(filteredData);
    [this.userDetailTableData, this.userTableHead] = [data, tableHead];
     this.filteredUserDetailTableData = this.userDetailTableData;
    this.userDetailLoader.set(false);
    this.setSelectedColumsList(event, this.userTableTitleData);
  }

  selectTopTableColumn(event: any): void {
    if (this.selectedTopKey == 'users') {
      this.performerSelectedColumn = event.map((cols: any) => cols.key);
      const filteredData = this.setColumnTitles(this.performerSelectedColumn, this.topUsers);
      const { data = [], tableHead = [] } = this.setUserTableData(filteredData);
      [this.topUsersTableData, this.topUsersTableHeader] = [data, tableHead];
      this.setSelectedColumsList(event, this.topUserTableTitles);
    } else {
      this.topEntitySelectedColumn = event.map((cols: any) => cols.key);
      const filteredData = this.setColumnTitles(this.topEntitySelectedColumn, this.topEntities);
      const { data = [], tableHead = [] } = this.setUserTableData(filteredData);
      [this.topEntitiesTableData, this.topEntitiesTableHeader] = [data, tableHead];
      this.setSelectedColumsList(event, this.topEntityTableTitles);
    }
    const filteredData = this.setColumnTitles(this.userSelectedColumn, this.userDetails);
    const { data = [], tableHead = [] } = this.setUserTableData(filteredData);
    [this.userDetailTableData, this.userTableHead] = [data, tableHead];
    this.userDetailLoader.set(false);
  }

  setSelectedColumsList(selectedCols: PanelDropdownOptions[], actualCols: PanelDropdownOptions[]): void {
    actualCols.forEach((title: PanelDropdownOptions) => {
      const selectedColIndex = selectedCols.findIndex((selectedCol: PanelDropdownOptions) => selectedCol.key === title.key);
      title.checked = selectedColIndex >= 0;
    });
  }

  setColumnTitles(selectedColumns: any, sourceData: any) {
    let filteredData = cloneDeep(sourceData)
    if (selectedColumns?.length > 0) {
      const allKeys = Object.keys(sourceData[0] || {});
      this.selectAllUserColumn.set(selectedColumns.length === allKeys.length);

      filteredData = sourceData
        .map((user: { [s: string]: unknown; } | ArrayLike<unknown>) => Object.fromEntries(
          Object.entries(user).filter(([key]) => selectedColumns.includes(key.toLowerCase()))
        ))
        .filter((user: {}) => Object.keys(user).length);
    }
    return filteredData;
  }

  selectEntityColumns(event: any): void {
    if (this.selectedUserTypeItem.key === 'entity') {
      this.entitySelectedColumn = event.map((e: any) => e.key);
      const filteredData = this.setColumnTitles(this.entitySelectedColumn, this.userOverviewEntityData);
      const { data = [], tableHead = [] } = this.setUserTableData(filteredData);
      [this.userOverviewEntityTableData, this.userOverviewEntityTableHead] = [data, tableHead];
      this.setSelectedColumsList(event, this.entityTableColumns);
    } else {
      this.entityUsersSelectedColumn = event.map((e: any) => e.key);
      const filteredData = this.setColumnTitles(this.entityUsersSelectedColumn, this.userOverviewData);
      const { data = [], tableHead = [] } = this.setUserTableData(filteredData);
      [this.userOverviewTableData, this.userOverviewTableHead] = [data, tableHead];
      this.setSelectedColumsList(event, this.entityTableUserListColumns);
    }
    this.userDetailLoader.set(false);
  }

  selectUserListColumns(event: any): void {
    this.userListSelectedColumn = event.map((e: any) => e.key);
    const filteredData = this.setColumnTitles(this.userListSelectedColumn, this.userListWithGroup());
    this.userListTableData = filteredData;
    const initialHead = Object.keys(this.userListWithGroup()[0]).map(key => ({key: key.toLowerCase(), value: this._ifpTitleCasePipe.transform(key), checked: true}));
    initialHead.forEach((title: PanelDropdownOptions) => {
      const selectedColIndex = event.findIndex((selectedCol: PanelDropdownOptions) => selectedCol.key === title.key);
      title.checked = selectedColIndex >= 0;
    });
    this.userListTableTitles = initialHead;
  }

  selectedKey(event: any) {
    this.selectedTopKey = event.key;
  }

  onSelectMainTab(event: PillsTabData) {
    this.selectedMainTab = event.key;
    this._router.navigate(['/bayaan-dashboard'], { queryParams: { tab: this.selectedMainTab } });
  }

  downloadUserStatus() {
    this.downloadService.exportToExcel(
      this.activeUsers.monthly_users_data,
      'users'
    );
  }

  downloadEntitiesStatus() {
    this.downloadService.exportToExcel(
      this.totalEntities,
      'entities'
    );
  }
    sortEvent(data: {sortValue:string, sortEventName: string, sortEvent: boolean }) {
        const index = this.filteredUserDetailTableData[0]?.findIndex((col: any) => col.key === data.sortEventName);
        if (index === -1) return;
        const isString = typeof this.filteredUserDetailTableData[0][index].value === 'string';
        const sortFn = isString
          ? (a: any[], b: any[]) => {
              const aValue = a[index].value ?? '';
              const bValue = b[index].value ?? '';
              if (aValue === '' && bValue === '') return 0;
              if (aValue === '') return data.sortEvent ? 1 : -1;
              if (bValue === '') return data.sortEvent ? -1 : 1;
              return data.sortEvent
                ? bValue.localeCompare(aValue)
                : aValue.localeCompare(bValue);
            }
          : (a: any[], b: any[]) => {
              const aValue = a[index].value ?? 0;
              const bValue = b[index].value ?? 0;
              if ((aValue === '' || aValue === null || aValue === undefined) && (bValue === '' || bValue === null || bValue === undefined)) return 0;
              if (aValue === '' || aValue === null || aValue === undefined) return data.sortEvent ? 1 : -1;
              if (bValue === '' || bValue === null || bValue === undefined) return data.sortEvent ? -1 : 1;
              return data.sortEvent
                ? +bValue - +aValue
                : +aValue - +bValue;
            };
        this.filteredUserDetailTableData.sort(sortFn);
    }

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }
}

interface UsageDashboard {
  totalUsersNew: TotalUsers;
  totalEntitiesNew: TotalEntity;
  activeInactiveUsers: ActiveInactiveUsers;
  topUsers: TopUser[];
  topEntities: TopEntity[];
  peakTimeOfUsage: PeakTimeOfUsage;
  overallDomains: OverallDomain[];
  officialStatistics: OfficialStatistic[];
  experimentalStatistics: OfficialStatistic[];
  reports: OfficialStatistic[];
  analyticalApps: AnalyticalApp[];
  entities: Entity[];
}

interface Entity {
  id: number;
  name: string;
  percentage?: number;
}

interface AnalyticalApp {
  domain: string;
  count: number;
  percentage: number;
  category: Category[];
}

interface Category {
  name: string;
  nodes: Node[];
}

interface OfficialStatistic {
  domain: string;
  count: number;
  percentage: number;
  nodes: Node[];
}

interface Node {
  id: number;
  name: string;
  count: number;
  percentage: number;
}

interface OverallDomain {
  domain: string;
  count: number;
  percentage: number;
}




export interface TopEntity {
  entity: string;
  duration: string;
  percentageShare?: number;
}


interface ActiveInactiveUsers {
  active: number;
  inActive: number;
  total: number;
}

interface TotalUsers {
  total: number;
  active: number;
  inActive: number;
  monthly_users_data: MonthlySplit[];
}

interface TotalEntity {
  active_domains: string[];
  month: string;
  count: number;
}

interface MonthlySplit {
  month: string;
  total_active_users: number;
  total_users: number;
}

export interface ApiParams {
  startDate?: string | null;
  endDate?: string | null;
  entityNames?: string[] | undefined;
  entityId?: string;
  indicatorName?: string;
  downloadType?: string;
  classification?: string;
  domain?: string;
  mobile?: boolean;
  nodeId?: number | string;
}

interface PeakTimeOfUsage {
  avgUsage: AvgUsage;
  hourlySplit: HourlySplit[];
}

interface HourlySplit {
  hour: number;
  count: string;
}

interface AvgUsage {
  value: number;
  unit: string;
}
export interface TopUser {
  email: string;
  name: string;
  type: string;
  duration: string;
  percentageShare?: number;
}
export interface SelfService {
  sessionType: string;
  name: string;
  averageUsageHrsValue: number;
  averageUsageValueUnit: string;
  percentageShare: number;
  sessionCount: string;
}
export interface UserSelfServiceUser {
  email: string;
  duration: string;
  percentageShare: number;
}
export interface SpecialAnalytics {
  district?: string;
  user_count: string;
  duration: string;
  domain?: string;
  entity?: string;
}

export interface UserAccessCount {
  total: UserAccessTotal;
  entity_count: CountData;
}

export interface CountData {
  total: number;
  inActive: number;
  active: number;
}

interface DownloadInsights {
  type: string;
  indicators: Indicator[];
}
interface Indicator {
  id: string;
  name: string;
  key: string;
  count: string;
}
interface NotificationEnabled {
  NODE_ID: string;
  CONTENT_TYPE: string;
  OCCURRENCE_COUNT: number;
  name?: null | string;
}
interface HiesVisits {
  page_path: string;
  visits:string;
}

interface DownloadInsightsTab {
  name: string;
  key: string;
  node: Indicator[]
}

interface EntityUserOverview {
  entity_id?: string,
  entity_name?: string,
  total_users?: number,
  superusers_count?: number,
  onboarded_users?: number,
  waiting_to_onboarded_users?: number
}

export interface TableView {
  key: string;
  value: string;
  type: string;
  title: string;
  customKey?: string;
  customValue?: string;
  multiComponents?: any[];
}

export interface UsageDashboardDataClassification {
  classification: { key: string; value: string; icon: string }[];
  domain: { name: string }[];
  products: string[];
  indicator: {
    contentClassification: any;
    dataClassification: any;
    domain: string;
    nid: string;
    pageCategory: string;
    title: string;
  }[]
}

// export const downloadTypes: any = [{id: "csv", label: "Export CSV"}, {id: "xls", label: "Export XLS"}];
export const downloadTypes: any = [{id: "xls", label: "Export XLS"}];
