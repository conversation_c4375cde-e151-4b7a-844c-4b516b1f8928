<app-ifp-db-file-uploader #uploader (removeFile)="removeFile()"  (fileUpload)="uploadFile($event)" [hideDelete]="true" class="ifp-gen-ai-dashboard-upload__browse" [dragOnly]="true" [iconBackgroung]="'ifp-file-upload__blue'" [supportText]="'Supported file formats'" [dragAndDropText]="'Drag & Drop File Or'"   [isImage]="false" [allowedExtensions]="allowedExtensions" [enableInstruction]="true"  >


    <div class="ifp-gen-ai-dashboard-upload__info-qus  instruction" (click)="downloadFile($event)">
      {{'Make sure that the uploaded file aligns with the predefined template ,else'  | translate}}
         <u  class="ifp-gen-ai-dashboard-upload__info-under-line">{{'download from here'  | translate}}</u>
    </div>
</app-ifp-db-file-uploader>
@if (fileData().name && fileData.name !=='') {
  <ifp-upload-bar  (closeEvent)="removeFile();uploader.deleteFile()" [fileName]="fileName" [error]="fileError()" [width]="fileProgress" [size]="fileData().size" class="ifp-gen-ai-dashboard-upload__upload-bar" ></ifp-upload-bar>
}

