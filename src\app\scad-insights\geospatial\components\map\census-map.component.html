<div id="viewDivCensus" >
    <div *ngIf="!isCustomizeOpen">
        <div *ngIf="!isPreviewOpen" class="esri-component map-location" [ngClass]="{'map-location-right': language == 'ar', 'map-location--dark': theme == 'dark'}">
            <em class="ifp-icon ifp-icon-location"></em>
            @if(mapLocations.length == 0 ){
                <span>{{ language == 'ar' ? defaultLocation.REGION_AR : defaultLocation.REGION_EN}}</span>
            } @else {
                <span *ngFor="let location of mapLocations"> {{ location }} </span>
            }
        </div>

        <div class="map-widgets">
            <ifp-map-zoom  [view]="view"></ifp-map-zoom>
            <div  #selectionLevel class="esri-component level-selection" [ngClass]="{'level-selection--dark': theme == 'dark'}">
                <div class="custom-multiselect" [ngClass]="{'open': isOpen}">
                    <div class="select-trigger" (click)="toggleDropdown()">
                        <span>
                            {{ language == 'ar' ? selectionLevelLabel.AR : selectionLevelLabel.EN }}:
                            {{ language == 'ar' ? currentSelectionLevel?.SELECT_AR :  currentSelectionLevel?.SELECT_EN}}
                        </span>
                        <span> <i class="arrow-down"></i> </span>
                    </div>
                    <div class="options-container" [ngStyle]="{'background-color': 'rgba(255, 255,255,' + ((themeService.geoConfig()['opacity']/100)) + ')'}" *ngIf="isOpen">
                        <div class="options-list">
                            <label *ngFor="let level of selectionLevels" class="option-item">
                                <span (click)="toggleSelection(level)" class="region-name">
                                    {{ language == 'ar' ? level.SELECT_AR : level.SELECT_EN }}
                                </span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div  #mapType class="esri-component map-type" [ngClass]="{'map-type--dark': theme == 'dark'}">
            <div class="custom-multiselect" [ngClass]="{'open': isMapTypeOpen}">
                <div class="select-trigger" (click)="toggleMapTypeDropdown()">
                    <span>
                        {{ language == 'ar' ? mapTypeLabel.AR : mapTypeLabel.EN }}:
                        {{ language == 'ar' ? currentMapType.SELECT_AR :  currentMapType.SELECT_EN}}
                    </span>
                    <span> <i class="arrow-down"></i> </span>
                </div>
                <div class="options-container" [ngStyle]="{'background-color': 'rgba(255, 255,255,' + ((themeService.geoConfig()['opacity']/100)) + ')'}" *ngIf="isMapTypeOpen">
                    <div class="options-list">
                        <label *ngFor="let type of mapLayers" class="option-item">
                            <span (click)="toggleMapLayer(type)" class="region-name">
                                {{ language == 'ar' ? type.SELECT_AR : type.SELECT_EN }}
                            </span>
                        </label>
                    </div>
                </div>
            </div>
            </div>

            <ifp-basemap-gallery [view]="view"></ifp-basemap-gallery>

            <div (click)="download('XL')" class="esri-component download-btn" [ngClass]="{'download-btn--dark': theme == 'dark'}">
                <em class="ifp-icon ifp-icon-download"></em>
            </div>

        </div>
    </div>
</div>
