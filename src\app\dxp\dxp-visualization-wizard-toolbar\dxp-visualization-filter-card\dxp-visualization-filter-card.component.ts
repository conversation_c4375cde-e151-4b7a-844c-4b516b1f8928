import { Component, input, output } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { DxpKpiCustomFilter } from '../dxp-visualization-filter-form/dxp-visualization-filter-form.component';

@Component({
  selector: 'ifp-dxp-visualization-filter-card',
  imports: [TranslateModule],
  templateUrl: './dxp-visualization-filter-card.component.html',
  styleUrl: './dxp-visualization-filter-card.component.scss'
})
export class DxpVisualizationFilterCardComponent {
  public removeCard = output();
  public editCard = output();
  public filter = input<DxpKpiCustomFilter>({
    column: '',
    filterLabel: '',
    defaultValue: null,
    defaultValueOnly: [],
    filterOptions: [],
    filterOptionsString: [],
    data_type: '',
    selectedValueArray: []
  });

  onRemoveCard() {
    this.removeCard.emit();
  }

  onEditCard() {
    this.editCard.emit();
  }
}

