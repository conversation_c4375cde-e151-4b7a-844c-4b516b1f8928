<div class="ifp-upload-model ifp-modal__body">
  <div class="ifp-upload-model__head">
    <!-- <h3 class="ifp-upload-model__title">{{'UPLOAD/ADD-DATA' | translate}}</h3> -->
    <em class="ifp-icon ifp-icon-round-cross" (click)="closeModel()"></em>
  </div>
  <div class="ifp-upload-model__body">
    <app-ifp-tab [tabData]="tabData" [transparent]="true" [showIcon]="false" (selectedTabEvent)="changeTab($event)"
      [selectedTab]="selectedTab.name" [selectionType]="'name'" class="ifp-node__switch-tab"></app-ifp-tab>

      @if (!isCustomInstruction) {
        <div class="ifp-upload-model__cnt">
          @if (selectedTab.name == 'Upload Data') {
          <p class="ifp-upload-model__desc">{{'row_limit' | translate}}</p>
          <p class="ifp-upload-model__desc">{{'colmn_limit' | translate}}</p>
          <p class="ifp-upload-model__desc">{{'file_format' | translate}}</p>

          <app-ifp-db-file-uploader class="ifp-upload-model__data-upload" [allowedExtensions]="allowedExcelExtensions"
            (fileUpload)="uploadFile($event)" [files]="files" (removeFile)="removeFile($event)" [isImage]="false"
            [iconBackgroung]="'ifp-file-upload__blue'" [previewUrl]="previewUrl"></app-ifp-db-file-uploader>
          } @else if (selectedTab.name == 'Insert Data') {
          <p class="ifp-upload-model__desc">{{'add_data' | translate}}</p>
          }

        </div>
      } @else {
        <div class="ifp-upload-model__cnt">
          <p class="ifp-upload-model__desc">{{'row_limit' | translate}}</p>
          <p class="ifp-upload-model__desc">{{'colmn_limit' | translate}}</p>
          <p class="ifp-upload-model__desc">{{'file_format' | translate}}</p>
          <div class="ifp-upload-model__warning-wrapper">
            <em class="ifp-icon ifp-icon-info-round ifp-upload-model__info"></em> <p class="ifp-upload-model__warning">{{'The current row or column count exceeds the maximum limit. Please remove some entries and try again.' | translate}}</p>
          </div>

        </div>
      }


      @if (!isCustomInstruction) {
        <div class="ifp-upload-model__footer">
          <ifp-button class="ifp-upload-model__button" (ifpClick)="goToData()" [label]="buttonLabel | translate"
            [buttonClass]="selectedTab.name == 'Upload Data' && uploadedFile?.result?.length <= 0 ? buttonClass.disabled : buttonClass.primary"
            ></ifp-button>
        </div>
      }

  </div>
</div>
