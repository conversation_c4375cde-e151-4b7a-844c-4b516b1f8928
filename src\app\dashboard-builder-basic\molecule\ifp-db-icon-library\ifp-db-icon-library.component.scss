@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host {
  display: block;
}

.ifp-db-icon-library {
  &__lib-search {
    display: block;
    margin-bottom: $spacer-3;
  }
  &__taglist-nav {
    display: flex;
    justify-content: flex-end;
    margin: $spacer-0 (-$spacer-1) $spacer-2;
    .ifp-icon {
      margin: $spacer-0 $spacer-1;
      cursor: pointer;
    }
  }
  &__taglist-wrapper {
    overflow: hidden;
    position: relative;
    &::after {
      content: "";
      width: 40px;
      height: 100%;
      background: linear-gradient(90deg, transparent 0%, $ifp-color-white 100%);
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  &__taglist-outer {
    display: flex;
    margin: $spacer-0 (-$spacer-2) $spacer-3;
    overflow: hidden;
  }
  &__tag-item {
    border: 1px solid $ifp-color-grey-2;
    margin: $spacer-0 $spacer-2;
    padding: $spacer-1 $spacer-3;
    border-radius: 30px;
    white-space: nowrap;
    cursor: pointer;
    transition: 0.3s;
    &:hover {
      border: 1px solid $ifp-color-active-blue;
      color: $ifp-color-active-blue;
    }
    &--active {
      border: 1px solid $ifp-color-active-blue;
      background-color: $ifp-color-active-blue;
      color: $ifp-color-white-global;
      pointer-events: none;
    }
  }
  &__wrapper {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-0 (-$spacer-2);
  }
  &__chart-icon {
    width: calc(16.66% - (2 * $spacer-2));
    margin: $spacer-2;
  }
  &__pagination {
    margin-top: $spacer-3;
  }
  &__divider {
    color: $ifp-color-grey-9;
    text-align: center;
    position: relative;
    margin: $spacer-3 $spacer-0;
    &::after {
      content: "";
      width: 100%;
      height: 1px;
      background-color: $ifp-color-grey-7;
      position: absolute;
      top: 50%;
      left: 0;
    }
  }
  &__divider-text {
    background-color: $ifp-color-white;
    padding: $spacer-0 $spacer-3;
    position: relative;
    z-index: 1;
  }
  &__no-data {
    width: 100%;
  }
}
