import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, inject, input, Input, InputSignal, model, ModelSignal, OnChanges, OnDestroy, OnInit, Output, Renderer2, signal, SimpleChanges, ViewChild } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { IfpButtonComponent } from '../../ifp-atoms/ifp-button/ifp-button.component';
import { buttonClass, buttonColor } from 'src/app/scad-insights/core/constants/button.constants';
import { IFPHighChartsComponent } from '../../charts/ifp-highcharts.component';
import { IfpTooltipDirective } from 'src/app/scad-insights/core/directives/ifp-tooltip.directive';

import { animate, style, transition, trigger } from '@angular/animations';
import { fadeInOut } from 'src/app/scad-insights/animation/fade.animation';
import { analyticsClasses } from 'src/app/scad-insights/core/constants/analytics-class.constants';

import { IfpSyncButtonComponent, SyncStatus } from '../../ifp-atoms/ifp-sync-button/ifp-sync-button.component';
import { Router } from '@angular/router';
import { chartConst, chartType } from 'src/app/dxp/dxp.constants';
import { ConvertedChartData, ListingPageItem } from 'src/app/dxp/dxp.interface';
import { ifpColors } from 'src/app/scad-insights/core/constants/color.constants';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { SubSink } from 'subsink';
import { dateFormat } from 'src/app/scad-insights/core/constants/date.constants';
import { DxpDonutService } from 'src/app/dxp/dxp-donut.service';
import { numberChartDatePipe } from 'src/app/scad-insights/core/pipes/numberChart.pipe';
import { ShortNumberPipe } from 'src/app/scad-insights/core/pipes/shortNumber.pipe';
import { Chart } from 'highcharts';

// DXP Chart API Response Interface
export interface DxpChartApiResponse {
  chartType: string;
  title: string;
  subtitle: string;
  xAxis: string;
  filterPanel: Array<{
    column: string;
    label: string;
    default: string;
    options: string[];
  }>;
  series: {
    xAxis: {
      categories: string[];
    };
    series: Array<{
      name: string;
      data: number[];
    }>;
  };
  tooltip: {
    enabled: boolean;
    shared: boolean;
  };
  plotOptions: any;
  approvalRequestId: string;
}

// Interface for card action button configuration
export interface CardConfigItem {
  label: string;
  value: string;
  icon: string;
}

// DXP Data Structure Interface
export interface DxpComponentData {
  icon?: string;
  objectId?: string;
  id?: string;
  component_title?: string;
  title?: string;
  component_subtitle?: string;
  subTitle?: string; // Added for new API structure
  sourceAssetId?: string;
  sourceProductId?: string;
  visualizationConfig?: {
    source_filter: any;
    chart_configuration: any;
    chartData?: any[];
  };
  updated?: string;
  updatedAt?: string; // Added for new API structure
  createdAt?: string;
  createdById?: string;
  createdBy?: {
    id?: string;
    name: string;
    designation: string;
    email?: string;
    entity?: {
      id: string;
      name: string;
    };
  };
  approvalRequest?: {
    id: string;
    status: string;
  };
  product?: {
    id: string;
    displayName: string;
    organization?: { id: string; name: string };
  };
  overview?: {
    unit: string;
    value: number;
  };
  // Legacy properties that might still be present
  value?: string | number;
  range?: string;
  domain?: any;
  domains?: any[];
  chartData?: any;
  unit?: string;
  thresholdValue?: number;
  valueFormat?: string;
  compareFilters?: string[];
  baseDate?: string;
  sourceProductSubscription?: {
                hasSubscription: boolean;
                productUrl: string;
}
}

/**
 * DXP What's New Card Component
 *
 * This component displays DXP component data in a card format without making direct API calls.
 * Data should be passed via the @Input() data property.
 *
 * Usage:
 * <ifp-dxp-whats-new-card
 *   [data]="dxpComponentData"
 *   [contentType]="'dxp-component'"
 *   [index]="0"
 *   [small]="true">
 * </ifp-dxp-whats-new-card>
 */
@Component({
  selector: 'ifp-dxp-whats-new-card',
  templateUrl: './ifp-dxp-whats-new-card.component.html',
  styleUrl: './ifp-dxp-whats-new-card.component.scss',
  imports: [
    CommonModule,
    IfpButtonComponent,
    TranslateModule,
    IFPHighChartsComponent,
    IfpTooltipDirective,
    IfpSyncButtonComponent,
    ShortNumberPipe
  ],
  providers: [ShortNumberPipe, numberChartDatePipe, DatePipe, DxpDonutService],
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('slideInOut', [
      transition(':enter', [
        style({ transform: 'translateY(-100%)' }),
        animate('200ms ease-in', style({ transform: 'translateY(0%)' }))
      ]),
      transition(':leave', [
        animate('200ms ease-in', style({ transform: 'translateY(-100%)', opacity: '0' }))
      ])
    ]),
    fadeInOut
  ]
})
export class IfpDxpWhatsNewCardComponent implements OnInit, OnDestroy, OnChanges {
  @ViewChild('chartRef') chartRef!: IFPHighChartsComponent;
  @ViewChild('card') card!: HTMLDivElement;

  // Core inputs - data passed directly instead of fetching via API

  @Input() classification: string = '';
  @Input() index!: number;
  @Input() contentType!: string;
  @Input() remove: boolean = false;
  @Input() isSelected: boolean = false;
  @Input() delay = 300;
  @Input() cardConfig: CardConfigItem[] = []; // Card action button configuration
  public data = input<ListingPageItem | null>(); // Main data object containing DXP component data

  // Derived properties from data object
  public baseDate!: any;

  // DXP specific inputs
  public isDxp: InputSignal<boolean> = input(false);
  public small = model(false);
  public syncStatus: ModelSignal<SyncStatus> = model({status: 'false', action: 'sync'});

  // Events
  @Output() resized = new EventEmitter();
  @Output() crossClick = new EventEmitter();
  @Output() selectIndicator: EventEmitter<{ status: boolean; id: string | number; type: string }> = new EventEmitter<{ status: boolean; id: string | number; type: string }>();
  @Output() cardAction = new EventEmitter<{action: string, cardData: ListingPageItem | null | undefined}>(); // Emit card action button clicks with card data

  // Component properties
  public buttonClass = buttonClass;
  public buttonColor = buttonColor;
  public chart = false;

  public textLimit = 35;
  public analyticsClasses = analyticsClasses;
  public centerValue = signal<((chart: Chart) => void) | undefined>(undefined);


  public chartType: Record<string, string> = chartType;
  public xAxis = signal<Highcharts.XAxisOptions>({});
  public _translate =  inject(TranslateService);
  public _donutService = inject(DxpDonutService);
  public legends: Highcharts.LegendOptions = {
    rtl: this._translate.currentLang === 'ar',
    itemStyle: {
      fontSize: '14px', // set the desired font size here,
      fontFamily: 'Noto Sans',
      fontWeight: '600',
    },
    enabled: false,
    align: 'center',
  };

  public darkColor = signal<string>(ifpColors.primaryGrey);
  public plotOptions = signal<Highcharts.PlotOptions | undefined>(undefined);
  public _themeService = inject(ThemeService);
  public seriesDonutChart = signal<{name:string, colorByPoint: boolean, data: { name: string; y: number }[]}>({name: '', colorByPoint: true, data: []});
  public chartConst = chartConst;
  public subs = new SubSink();
  constructor(
    private _elementRef: ElementRef,
    private _render: Renderer2,
    private _cdr: ChangeDetectorRef,
    private _router: Router,
    private _datePipe: DatePipe,
  ) {}

  ngOnInit(): void {
    this.subs.add(
      this._themeService.defaultTheme$.subscribe((val) => {
        if (val == 'dark') {
          this.darkColor.set(ifpColors.white);
        } else {
          this.darkColor.set(ifpColors.primaryGrey);
        }
        this.updatePlotOptions();
      })
    );
    this.initializeData();
    this.fetchChartData();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['small']) {
      this.handleResize(changes['small'].currentValue);
    }

    if (changes['data']) {
      this.initializeData();
      this.fetchChartData();
    }
  }

  gotTOProduct(value: string) {
    window.open(value, '_blank');
  }



  private updatePlotOptions() {
    const plotUnit =   this.data()?.visualizationConfig?.chart_configuration?.unit &&  this.data()?.visualizationConfig?.chart_configuration?.unit !== '' ? `(${this.data()?.visualizationConfig?.chart_configuration?.unit})` : '';
    this.plotOptions.set(this._donutService.updatePlotOptions(plotUnit, this.data()?.visualizationConfig?.chart_configuration?.default_chart_type ?? '', this.darkColor, false));
  }


  private initializeData(): void {

    if (this.data) {
      if (this.data()?.createdAt) {
        this.baseDate = this._datePipe.transform(new Date(this.data()!.createdAt!), dateFormat.mediumDateTime);
      }
    }
    this._cdr.detectChanges();
  }

  private handleResize(isSmall: boolean): void {
    if (isSmall) {
      setTimeout(() => {
        this.chart = false;
        this._cdr.detectChanges();
      }, this.delay);
      // Remove active class from the host element (ifp-dxp-whats-new-card)
      this._render.removeClass(this._elementRef.nativeElement, 'ifp-active-card');
      this.resized.emit(null);
    } else {
      setTimeout(() => {
        this.chart = true;
        this._cdr.detectChanges();
      }, this.delay);
      // Add active class to the host element (ifp-dxp-whats-new-card)
      this._render.addClass(this._elementRef.nativeElement, 'ifp-active-card');
      this.resized.emit(this.index);
    }
  }

  /**
   * Fetch chart data from DXP API
   */
  private fetchChartData(): void {
    if (
      this.data()?.visualizationConfig?.chart_configuration?.default_chart_type === chartConst.donut ||            this.data()?.visualizationConfig?.chart_configuration?.default_chart_type == chartConst.pie
    ) {
      this.seriesClick( {item: this.data()?.series?.xAxis?.categories?.[0]?? '', index: 0});
    }

    this.xAxis.set({
      lineColor: '#D9DCDD',
      tickColor: '#D9DCDD',
      type: 'category',
      tickLength: 1,
      categories: this.data()?.series?.xAxis?.categories ?? [],
      gridLineWidth: 30,
      lineWidth: 1,
      gridLineColor: '#F8F8F8',
      tickPosition: 'outside',
      startOnTick: false,
      endOnTick: false,
      offset: 0,
      title: {
        text: this.data()?.visualizationConfig?.chart_configuration?.x_axis?.label,
        style: {
          fontSize: '15px',
          fontWeight: '500',
          color: ifpColors.primaryGrey,
        },
      },
      labels: {
        formatter: (value: any) => {
          return value.value;
        },
      },
    });
    this.updatePlotOptions();
    this._cdr.detectChanges();
  }




  // click on series dropdown
  seriesClick(event: { item: string; index: number }) {
    const graphData:ConvertedChartData = {
      category: this.data()?.series?.xAxis?.categories ?? [],
      unit: '',
      series: this.data()?.series?.series ?? [],
      // xAxisLabel:'',
      // yAxisLabel:''
    };
    const result = this._donutService.seriesClick(event, graphData);
    this.seriesDonutChart.set(result.series);
    this.centerValue.set(this._donutService.centerValue(this.data()?.visualizationConfig?.chart_configuration?.default_chart_type  === chartConst.pie ? '' : result.total));
  }


  /**
   * Resize card between small and large views
   */
  resize(value: boolean): void {
    this.small.set(value);
    this.handleResize(value);
  }





  /**
   * Handle card action button clicks
   */
  onCardActionClick(actionValue: string): void {

    // Emit both the action value and the card data (including objectId)
    this.cardAction.emit({
      action: actionValue,
      cardData: this.data()
    });
  }

  /**
   * Open URL in new route
   */
  openUrl(url: string): void {
    const queryParams = {
      contentType: this.contentType
    };
    this._router.navigate([url], { queryParams: queryParams });
  }

  /**
   * Remove chart tooltip
   */
  removeTooltip(): void {
    if (this.chartRef) {
      this.chartRef.removeTooltip();
    }
  }





  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }
}
