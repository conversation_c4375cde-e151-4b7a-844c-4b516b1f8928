<div class="ifp-db-list">
  <div class="ifp-container ifp-db-list__breadcrumb-container">
    <app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs>
  </div>
  <!-- <img src="../../../../assets/images/dashboard-builder/ifp-store-bg.png" alt="" class="ifp-db-list__bg"> -->
  <div class="ifp-container ifp-db-list__container">
    <div class="ifp-db-list__head">
      <div class="ifp-db-list__head-item ifp-db-list__head-item--left">
        <span (click)="goToStore()" class="ifp-link ifp-db-list__header-back" [title]="'Back' | translate"><em
            class="ifp-icon ifp-icon-left-arrow"></em> <span class="ifp-db-list__header-back-text">{{'Back' |
            translate}}</span></span>
      </div>
      <div class="ifp-db-list__head-item ifp-db-list__head-item--middle">
        <h1 class="ifp-db-list__heading">{{'Visulization Builder' | translate}}</h1>
        <!-- <p class="ifp-db-list__desc">{{'Lorem ipsum dolor sit amet consectetur. Sodales proin ac quis ultrices.' |
          translate}}</p> -->
        <!-- <div class="ifp-db-list__manage-list">
          <ifp-search-round class="ifp-db-list__search" [placeholder]="'Search dashboards'| translate"
            (getResults)="onSearch($event)" [searchText]="searchKeyword"></ifp-search-round>

          <div class="ifp-db-list__filter" (click)="onApplyFilter()"
            [ngClass]="{'ifp-db-list__filter--active': recentlyAddedEnabled == 'desc', 'ifp-db-list__filter--disabled': dashboardList.length <= 0}">
            <div (click)="recentlyAdded()" class="ifp-db-list__action">
              <p class="ifp-db-list__filter-text">{{'Recently Added' | translate}}</p>
              <em class="ifp-icon ifp-icon-double-arrow ifp-db-list__filter-icon"></em>
            </div>
          </div>
        </div> -->
      </div>
      <!-- <div class="ifp-db-list__head-item ifp-db-list__head-item--right">
        <ifp-button [label]="'Share'" (ifpClick)="shareSelected()" class="ifp-db-list__head-btn"
          [buttonClass]="(selectedDashboards.length ? buttonClass.primary : buttonClass.disabled) +' '+ buttonIconPosition.left"
          [iconClass]="'ifp-icon-share'"></ifp-button>
        <ifp-button [label]="'Create new dashboard' | translate" [link]="false" (ifpClick)="goToDashboardBuilder()"
          class="ifp-db-list__head-btn" [buttonClass]="buttonClass.primary" [iconClass]="'ifp-icon-plus'"></ifp-button>
      </div> -->
    </div>

    <!-- <app-ifp-tab [tabData]="dashboardTabs" (selectedTabEvent)="changeTabView($event)" [selectionType]="'name'"
      [selectedTab]="selectedTabView.event.name" class="ifp-db-list__tab" [showIcon]="false"
      [isSmall]="true"></app-ifp-tab> -->

    <div class="ifp-db-list__hr-tab-wrapper">
      <ifp-horizontal-tab [selected]="selectedTabView.index" [list]="dashboardTabs" [countKey]="'count'"
        [disableTooltip]="true" [boxMode]="false" class="ifp-db-list__htab"
        (selectionClick)="changeTabView($event)"></ifp-horizontal-tab>

      <div class="ifp-db-list__card-wrapper">
        <div class="ifp-db-list__action-wrapper">
          <ifp-button [label]="'Create' | translate" [link]="false" (ifpClick)="goToDashboardBuilder()"
            class="ifp-db-list__head-btn" [buttonClass]="buttonClass.primaryLight +' '+  buttonClass.normalAplabetic"
            [iconClass]="'ifp-icon-desktop-chart'" [iconPosition]="buttonIconPosition.left"></ifp-button>



          <ifp-button [label]="'share' | translate" (ifpClick)="shareSelected()" class="ifp-db-list__head-btn"
            [buttonClass]="(selectedDashboards.length ? buttonClass.secondary : buttonClass.disabled) +' '+ buttonIconPosition.left"
            [iconClass]="'ifp-icon-share-new'"></ifp-button>


          <div class="ifp-db-list__filter-item-wrapper">
            <p class="ifp-db-list__filter-item-title">
              {{'Sort'| translate}}
            </p>
            <div class="ifp-db-list__filter-item">
              <ifp-panel-dropdown [titleCaseEnable]="false" [enableSearch]="false" [isBoxType]="true"
                [enableSelectAll]="false" [multiSelect]="false" [key]="'value'"
                [selectedValue]="selectedSortOption.value" [options]="sortOptions" [customIcon]="'ifp-icon-sort-arrows'"
                class="ifp-db-list__sort-dropdown" (selected)="onSortOptionChange($event)">
              </ifp-panel-dropdown>
            </div>
          </div>


          <ifp-search class="ifp-db-list__search" [isKeypress]="true" (searchEvent)="onSearch($event)"
            [onSearch]="searchKeyword" [placeholderText]="'Search'| translate"></ifp-search>

        </div>

        <div class="ifp-db-list__wrapper">
          @if (!isLoading) {
          @for (dashboard of dashboardList; track dashboard) {
          <ifp-db-list-card class="ifp-db-list__card" [title]="dashboard.name"
            [date]="(selectedTabView.item.name === 'Dashboards' ? dashboard?.createdDate : (selectedTabView.item.name === 'Sent' ? dashboard.sharedDate :  dashboard.sharedDate)) | date:'MM d, y'"
            [thumb]="_themeService.defaultTheme == 'light' ? dashboard.thumbnailLight
        .content :  dashboard.thumbnailDark.content" [isQuoetsRemove]="true" [thumbType]="_themeService.defaultTheme == 'light' ? dashboard.thumbnailLight
        .type :  dashboard.thumbnailDark.type" (cardClicked)="openDetail($event)" [id]="dashboard.id"
            (cardOptionSelected)="editOrDelete($event, dashboard)" (selectCardShare)="updateSelectDashboard($event)"
            [selectedTab]="selectedTabView.item.name" [recipientsCount]="dashboard?.recepientEmails?.length"
            [selectedNodes]="selectedDashboards"></ifp-db-list-card>
          }
          } @else {
          <app-ifp-card-loader class="ifp-loader ifp-db-list__ifp-loader" [type]="'large'"></app-ifp-card-loader>
          }


          @if (!dashboardList.length && !isLoading) {
          <app-ifp-no-data class="ifp-db-list__no-data"
            [message]="'No Dashboards Are Available' | translate"></app-ifp-no-data>
          }


        </div>
      </div>
    </div>



    @if (dashboardList.length) {
    <app-pagination class="ifp-indicator-tab__pagination" [offset]="offset" [limit]="limit" [limit]="limit"
      [size]="totalCount" (pageChange)="onPageChange($event)" (limitChange)="limitChanged($event)"></app-pagination>
    }
  </div>
</div>

<app-ifp-modal #modal>
  <app-ifp-remove-card [text]="currentRemoveText" (firstButtonEvent)="closeModel($event)"
    (secondButtonEvent)="closeModel($event)">
  </app-ifp-remove-card>
</app-ifp-modal>

<app-ifp-modal #shareAppsModal>
  <app-ifp-share-modal (closeShareModal)="closeShare()" (submitShare)="onShareApps($event)"
    [sharedAppsCount]="selectedDashboards.length" [modelHead]="'Share Dashboards'" [shareItem]="'Dashboards'"
    [isHideTitleColumn]="true"></app-ifp-share-modal>
</app-ifp-modal>