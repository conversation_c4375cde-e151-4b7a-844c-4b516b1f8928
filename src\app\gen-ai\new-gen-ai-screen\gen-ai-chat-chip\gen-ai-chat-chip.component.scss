@use "../../../../assets/ifp-styles/abstracts/index" as *;
:host {
  display: block;
}
.ifp-gen-ai-chat-chip {
  width: 100%;
  font-size: $ifp-fs-4;
  &__icon {
    margin-inline-end: $spacer-2;
  }


  &__ai-wrapper {
    display: flex;
    align-items: flex-start;
  }
  &__ai-image {
    margin-inline-end: $spacer-3;
  }
  &__chart-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin: $spacer-4 (-$spacer-2);
  }
  &__source-pills {
    transition: .3s;
    visibility: hidden;
    opacity: 0;
    max-height: 0px;
    max-width: 0px;

    &--active {
      visibility: visible;
      opacity: 1;
      max-height:none;
      animation: expand-bounce 0.3s;
      max-width: none;
    }
  }
  &__indicator {
    margin-top: $spacer-3;
    display: none;
    &--active {
      display: block;
    }
  }
  &__reason-wrapper {
    max-width: 80%;
  }
  &__reason-wrapper {
    max-width: 80%;
  }
  &__chart {
    margin: $spacer-1;
    width: calc(50% - $spacer-3);
    &--odd {
      &:last-child {
        width: calc(100% - (2 * $spacer-3) ) ;
      }
    }
  }
  &__map {
    animation: expand-bounce 0.3s ease-in-out;
    margin-bottom: $spacer-4;
  }
  &__chart-actions {
    display: flex;
    margin: $spacer-2 (-$spacer-2);
  }
  &__action-icon {
    margin: $spacer-0 $spacer-2;
    font-size: $ifp-fs-6;
    color: $ifp-color-grey-14;
    cursor: pointer;
    transition: all 0.1s ease-in-out;

    &:active {
      transform: scale(0.80);
    }
    &--active {
      color: $ifp-color-blue-hover;
    }
  }
  &__suggestion-wrapper {
    margin-top: $spacer-5;
    text-align: end;
    font-size: $ifp-fs-3;
  }
  &__suggestion {
    margin-top: $spacer-3;
    border-radius: 10px;
    padding: $spacer-2 $spacer-3;
    background-color: rgba($color: $ifp-color-yellow-dark, $alpha: 0.16);
    color: $ifp-color-secondary-grey;
    display: inline-block;
  }
  &__suggestion-icon {
    color:  $ifp-color-yellow-dark;
  }
  &__relatedQuery {
    cursor: pointer;
    margin-top: $spacer-3;
    background-color: $ifp-color-white;
    border-radius: 30px 30px 10px 30px;
    display: inline-block;
    padding: $spacer-3 $spacer-4;
    box-shadow: 0 12px 5px -14px rgba(96, 122, 147, 0.16);
    position: relative;
    border: 1px solid $ifp-color-grey-7;
    &::after {
      position: absolute;
      content: "";
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      filter: blur(141.5px);
    }
  }
  // &__reason-toggle-arrow,&__bulb{
  //  top:-4px;
  //  color: $ifp-color-ai-blue;
  // }
   &__reason-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    padding: $spacer-3;
  }
  &__reason-detail {
    background-color:   $ifp-color-gen-ai-grey;
    border-radius: 30px;
    margin-bottom: $spacer-3;
    &--open {
      padding: $spacer-3;
      .ifp-gen-ai-chat-chip__reason-header {
        padding: $spacer-0;
      }
    }
  }

  &__reason-title {
    font-size: $ifp-fs-5;
    font-weight: $fw-semi-bold;
    display: flex;
    align-items: center;
  }
  &__bulb {

    margin-inline-end: $spacer-1;
        font-size: $ifp-fs-7;
          -webkit-text-fill-color: transparent;
        color:   $ifp-color-gen-ai-grey;
        background-image: linear-gradient(
          217deg,
          #3fbffc 0%,
          #1676e8 59%,
          #0154b8 100%
        );
        -webkit-background-clip: text;
        background-clip: text;
        object-fit: cover;
        background-size: cover;
        background-repeat: no-repeat;
  }
  &__reason-desc {
    margin-top: $spacer-3;
  }
  &--prompt {
    display: flex;
    justify-content: flex-end;
    .ifp-gen-ai-chat-chip {
      &__wrapper {
        animation: expand-bounce 0.3s ease-in-out;
        background-color:   $ifp-color-gen-ai-grey;
        border-radius: 30px 30px 0px 30px;
        display: inline-block;
        padding: $spacer-3 $spacer-4;
        color: $ifp-color-primary-grey;
        max-width: 80%;
      }
    }
  }

  &__animation-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    height: 44px;
  }
  &__animaton-node {
    background: $ifp-color-black;
    height: 6px;
    width: 6px;
    margin: 3px;
    transform: translateY(0);
    border-radius: 50%;
    animation: bounce 600ms infinite alternate;
    &:nth-of-type(2) {
      animation-delay: 250ms;
    }
    :nth-of-type(3) {
      animation-delay: 350ms;
    }
  }
  &__feedback-wrapper {
    z-index: 1300;
  }
}
:host::ng-deep {
  .ifp-ai-card__chart {
    width: 100%;
  }
  .ifp-gen-ai-chat-chip__matrics-markdown {
    .customMarkdown {
      h2,
      h4,
      p,
      ul,
    strong  {
        color: $ifp-color-black;
        line-height: 2.2;
      }
      h2,
      h4 {
        font-weight: $fw-semi-bold;
        margin: $spacer-0 $spacer-0 $spacer-2;
      }
      p {
        margin-top: $spacer-0;
      }
      li {
        text-align: start;
      }
      ul {
        padding-left: $spacer-0;
        margin-right: $spacer-6;
      }
      strong {
        color: $ifp-color-black;
      }
      table {
        display: block;
        @include ifp-scroll(transparent, $ifp-color-grey-2, 4px, 8px);
      }
    }
  }
}

@keyframes expand-bounce {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes bounce {
  to {
    transform: translateY(-1em);
  }
}

@include desktop-xl {
.ifp-gen-ai-chat-chip {
  &__suggestion-wrapper {
    font-size: $ifp-fs-2;
  }
    &__chart {
   width: calc(100% - (2 * $spacer-3) ) ;
  }
  }
}
@include desktop-sm {
 .ifp-gen-ai-switch {
  &__suggestion-wrapper {
    font-size: $ifp-fs-1;
  }
  }
}

:host-context(.ifp-dark-theme) {
  .ifp-gen-ai-chat-chip {
       &__reason-title,&__reason-toggle-arrow {
          color: $ifp-color-white-pure;
          }
      &--prompt {
    .ifp-gen-ai-chat-chip {
      &__wrapper {
         background-color:   $ifp-color-white;
         color: $ifp-color-white-pure;
       }

    }
  }
   &__reason-detail {
    background-color: $ifp-color-section-white;
   }
    &__relatedQuery {
      background-color: $ifp-color-ai-dark-black-3;
      color: $ifp-color-white-pure;
      box-shadow: 0 15px 5px -14px rgba(22, 27, 32, 0.16);
      border: none;
     } }
  :host::ng-deep {
    .ifp-gen-ai-chat-chip__matrics-markdown {
    .customMarkdown {
      h2,
      h4,
      p,
      ul,strong {
        color: $ifp-color-white-pure;
      }
    }
  }

}
}
:host-context([dir="rtl"]) {
 .ifp-gen-ai-chat-chip--prompt {
     .ifp-gen-ai-chat-chip {
      &__wrapper {
                border-radius: 30px 30px 30px 0;
      }
    }
 }
}
