import { Component, Input, ViewChild, OnInit, Output, EventEmitter, inject, viewChild, InputSignal, input, WritableSignal, signal } from '@angular/core';
import { DownLoadService } from 'src/app/scad-insights/core/services/download-service/download.service';
import { HttpService } from 'src/app/scad-insights/core/services/http/http.service';
import { SubSink } from 'subsink';
import { IfpCheckBoxComponent } from '../../ifp-atoms/ifp-check-box/ifp-check-box.component';
import { CommonApiService } from 'src/app/scad-insights/core/services/common-api/commonApi.service';
import { IfpModalComponent } from '../../ifp-organism/ifp-modal/ifp-modal.component';
import { IfpTncModalComponent } from '../../ifp-organism/ifp-tnc-modal/ifp-tnc-modal.component';
import { TranslateModule } from '@ngx-translate/core';
import { UsageDashboardLogService } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard-log.service';
import { logType } from 'src/app/scad-insights/core/services/usage-dashboard-log/usage-dashboard.constants';
import { IfpRemoveCardComponent } from '../../ifp-atoms/ifp-remove-card/ifp-remove-card.component';
import { DomSanitizer } from '@angular/platform-browser';
import { IfpUniversalFileViewerComponent } from '../../ifp-organism/ifp-universal-file-viewer/ifp-universal-file-viewer.component';
import { IfpModalService } from '../../ifp-organism/ifp-modal/ifp-modal.service';
import { IfpTabComponent } from '../../ifp-molecules/ifp-tab/ifp-tab.component';
import { NgClass } from '@angular/common';
import { buttonClass } from '../../../core/constants/button.constants';
import { IfpIconButtonComponent } from '../../ifp-atoms/ifp-icon-button/ifp-icon-button.component';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-ifp-document-download',
  templateUrl: './ifp-document-download.component.html',
  styleUrls: ['./ifp-document-download.component.scss'],
  imports: [IfpCheckBoxComponent, IfpTncModalComponent, IfpModalComponent, TranslateModule, IfpRemoveCardComponent, IfpUniversalFileViewerComponent, IfpTabComponent, NgClass, IfpIconButtonComponent]
})
export class IfpDocumentDownloadComponent implements OnInit {

  @ViewChild('tncModal') tncModal!: IfpModalComponent;
  @ViewChild('previewDocModal') previewDocModal!: IfpModalComponent;

  public previewDocUniversalModal = viewChild<IfpModalComponent>('previewDocUniversalModal');

  @Input() tncState!: boolean;
  @Input() inputId!: string;
  @Input() documentList: any[] = [];
  @Input() custom: boolean = false;
  @Input() isExcel: boolean = true;
  @Input() fileTypes: string[] = ['pdf', 'xl', 'png', 'ppt'];

  public indicatorName: InputSignal<string> = input('');
  public buttonClass = buttonClass;

  public fileTypesData: { type: string; icon: string }[] = [
    { type: 'pdf', icon: '../../../../assets/images/pdf-icon.svg' },
    { type: 'xl', icon: '../../../../assets/images/xls-icon.svg' },
    { type: 'png', icon: '../../../../assets/images/img-icon.svg' }
    // { type: 'ppt', icon: '../../../../assets/images/ppt-icon.svg' }
  ];

  @Output() checkTnC: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() downLoadClicked: EventEmitter<any> = new EventEmitter<any>();

  public docData: any[] = [];
  public subs: SubSink = new SubSink();
  public isTncModalOpen: boolean = false;
  private sessionId!: string;
  public selectedDoc: any;
  public sanitizer = inject(DomSanitizer);
  // private _cdr = inject(ChangeDetectorRef);
  private readonly _modalService = inject(IfpModalService);
  public previewEnabled = false;
  public selectedFileIndex: WritableSignal<number> = signal(0);
  public fileList: any[] = [];

  constructor(private _commonApiService: CommonApiService, private _httpService: HttpService, public _download: DownLoadService, private log: UsageDashboardLogService,
    private http: HttpClient
  ) { }

  ngOnInit() {
    if (this.custom) {
      // this.fileTypesData = this.fileTypesData.filter((item: {type: string; icon: string }) => {
      //   this.fileTypes.forEach((type: string) => {
      //     item.type === type;
      //   });
      // });
    } else {
      this.documentList.forEach((doc: any) => {
        this.docData.push({
          downloadLink: doc.files,
          type: doc.type,
          count: doc.files.length,
          docIcon: doc.type === 'pdf' ? '../../../../assets/images/pdf-icon.svg' : '../../../../assets/images/xls-icon.svg'
        });
      });

      // this.documentList.forEach((doc: string) => {
      //   const filename = doc?.replace(/^.*[\\/]/, '');
      //   const fileType = filename.split('.').pop();
      //   this.docData.push({
      //     downloadLink: doc,
      //     type: fileType,
      //     filename: filename,
      //     docIcon: fileType === 'pdf' ? '../../../../assets/images/pdf-icon.svg' : '../../../../assets/images/xls-icon.svg'
      //   });
      // });
    }
  }

  actionButton(url: any) {
    this.fileList = [];
    this.selectedDoc = url;
    this.selectedFileIndex.set(0);
    if (url.downloadLink.length) {
      this.fileList = url.downloadLink.map((file: any) => {
        return {
          name: decodeURIComponent(file?.replace(/^.*[\\/]/, ''))
        };
      });
    }
    this.previewDocModal.createElement();
  }

  preview() {
    this.previewEnabled = true;
    this.previewDocModal.removeModal();
    this.previewDocUniversalModal()?.createElement();
    // setTimeout(() => {
    //   this._cdr.detectChanges();
    // }, 300);
  }



  download() {
    if (this.selectedDoc && !this.custom) {
      this.selectedDoc.downloadLink.forEach((link: string) => {
        const filename = decodeURIComponent(link?.replace(/^.*[\\/]/, ''));
        this.subs.add(
          this._httpService.fileDownload(link).subscribe((data: any) => {
            this._download.downloadFiles(data, filename);
            if (this.sessionId) {
              this.log.logEnds(this.sessionId, this.log.currentTime);
            }
            this.sessionId = this.log.createUUid;
            this.log.logStart(this.sessionId, logType.indicator, this.log.currentTime, +this.inputId);
          }
          )
        );
      });
      // this.subs.add(
      //   this._httpService.fileDownload(url.downloadLink).subscribe((data: any) => {
      //     this._download.downloadFiles(data, url.filename);
      //   }
      //   )
      // );
    }
    if (this.custom) {
      this.downLoadClicked.emit(this.selectedDoc);
    }
    this.previewDocModal.removeModal();
  }

  termsResponse(response: boolean) {
    if (response) {
      this.subs.add(this._commonApiService.setDownloadTermsStatus(this.inputId).subscribe((res: any) => {
        if (res) {
          this.tncState = true;
          this.checkTnC.emit(response);
        }
      }));
    }
    // else {
    //   this.tncState = false;
    // }
    // this.checkTnC.emit(response);
    if (this.isTncModalOpen) {
      this.tncModal.removeModal();
      this.isTncModalOpen = false;
    }
  }

  showTnC() {
    this.tncModal.createElement();
    this.isTncModalOpen = true;
  }

  checkFiles(type: string) {
    return this.fileTypes.find(x => x == type);
  }

  closePreviewModal() {
    this.selectedFileIndex.set(0);
    this._modalService.removeAllModal();
  }

  fileChange(file: { event: any, index: number }) {
    this.selectedFileIndex.set(file.index);
    // setTimeout(() => {
    //   this._cdr.detectChanges();
    // }, 300);
  }

  convertXltoBase64(url: string) {
    return new Promise((resolve, reject) => {
      this.http.get(url, { responseType: 'blob' }).subscribe({
        next: (blob) => {
          const reader = new FileReader();
          reader.readAsDataURL(blob);
          reader.onloadend = () => {
            const base64data = reader.result as string;
            resolve(base64data);
          };
          reader.onerror = (error) => reject(error);
        },
        error: (err) => reject(err)
      });
    });
  }
}
