@use '../../../../../assets/ifp-styles/abstracts' as *;
.ifp-screening {
  &__filter {
    display: flex;
    margin: $spacer-2 (-$spacer-2);
    flex-wrap: wrap;
    align-items: flex-end ;
  }
  &__filter-dropdown {
    margin: $spacer-0 $spacer-2;
  }
  &__btn {
    margin: $spacer-2  $spacer-2 $spacer-0;
  }
  &__search {
    margin-top: $spacer-2 ;
  }
  &__tag {
    display: flex;
    margin: $spacer-2 (-$spacer-2);
    flex-wrap: wrap;
    align-items: flex-end ;
  }
  &__tag-btn {
    margin: $spacer-1 $spacer-2;
    color: #ffffff;
    border: 1px solid $ifp-color-grey-disabled;
    background-color: $ifp-color-grey-disabled;
    padding: 4px 8px;

    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;

    cursor: pointer;

}
&__tag-btn-icon {
  margin-left: $spacer-1;
  margin-right: $spacer-1;
  font-size: $ifp-fs-2;
}
&__cards {
  display: flex;
  margin: $spacer-2 (-$spacer-2);
  flex-wrap: wrap;
}
&__card {
  margin: $spacer-2 $spacer-2;
  display: block;
  width: calc(25% - $spacer-3);
}
&__nodata {
  display: block;
  margin-bottom: $spacer-2;

}
.ifp-active-card {
  width:calc(50% - $spacer-3);
}

&__btn-sec {
  display: flex;
  justify-content: flex-end;
  margin: $spacer-2 $spacer-0;
}

&__compare-btn{
  margin-left: auto;
}
  }

:host::ng-deep {
  .ifp-screening__title-dropdown {
    .ifp-dropdown {
      min-width: 315px;
    }
    .ifp-dropdown__selected {
      font-size: $ifp-fs-11;
    }
  }
}

// @include desktop-xl{
//   .ifp-screening {
//     &__card {
//       width: calc(33.33% - $spacer-3);
//       margin: $spacer-2;
//     }
//     .ifp-active-card {
//       width:calc(66.66% - $spacer-3);
//     }
//   }
// }
@include desktop-sm {
  .ifp-screening {
    &__card {
      width: calc(33.33% - $spacer-3);
      margin: $spacer-2;
    }
    .ifp-active-card {
      width:calc(66.66% - $spacer-3);
    }
  }
}

@include mobile-tablet{
  .ifp-screening {
    &__card {
      width: calc(50% - $spacer-3);
      margin: $spacer-2;
    }
    .ifp-active-card {
      width:calc(100% - $spacer-3);
    }
  }
}

@include mobile {
  .ifp-screening {
    &__card {
      width: calc(100% - ($spacer-3));
    }
    &__filter-dropdown {
      margin: $spacer-0 $spacer-1;
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-screening__compare-btn{
    margin-left: $spacer-0;
    margin-right: auto;
  }
}
