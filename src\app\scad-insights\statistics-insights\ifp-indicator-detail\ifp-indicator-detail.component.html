<div class="ifp-node-detail" #detailPage>

  <div class="ifp-container">
    @if(globalService.isExternalSite()){
      <ifp-back-button class="ifp-node-detail__back"></ifp-back-button>
    } @else {
      <app-ifp-breadcrumbs [pageData]="pageData"></app-ifp-breadcrumbs>
    }
  </div>

  <div class="ifp-container">
    <app-ifp-indicator-detail-widget [id]="indicatorId" (pageDataEmit)="createPageData($event)"
      (relatedProductDropdownSelected)="updateQueryParams($event)"></app-ifp-indicator-detail-widget>
  </div>
</div>
