// export const requestAccess = 'user/request-access'';
export const uaePassInfo = 'uae-pass/user-info';
export const resendInviteApi = 'user-onboarding/invitations/';
export const deleteInviteApi = 'user-onboarding/invitations/';
export const verifyInviteToken = 'user-onboarding/invitations/validate';
export const uaePassRefreshTokenApi = 'uae-pass/access-token';
export const userGuide = 'user-onboarding/user-guide';

export const user = {
  register: 'user-onboarding/entity/user/register',
  verifyToken: 'user-onboarding/entity/user/verify',
  generateOtp: 'user-onboarding/entity/user/request/otp',
  verifyOtp: 'user-onboarding/entity/user/verify/otp',
  delete: 'user-onboarding/entity/users/'
};
export const productEngagement = {
  allRequests: 'user/ifp/requests/all',
  approve: 'user/ifp/approve',
  pendingRequests: 'user-onboarding/product-engagement/requests/pending',
  approvedRequests: 'user/ifp/requests/approved',
  rejectedRequests: 'user/ifp/requests/rejected',
  newRequests: 'user-onboarding/product-engagement/requests/new',
  completedRequests: 'user-onboarding/product-engagement/requests/existing',
  // entityList: 'user/ifp/entity/list'
  entityList: 'user-onboarding/entity/list',
  shareLink: 'user-onboarding/product-engagement/invitations/create',
  manageAccess: 'user-onboarding/product-engagement/manage-access',
  makePrimary: 'user-onboarding/entity/product-engagement/make-primary',
  invitationsList: 'user-onboarding/product-engagement/invitations',
  exportUsers: 'user-onboarding/product-engagement/users-export',
  deletedList: 'user-onboarding/product-engagement/deleted-users'
};
export const superUser = {
  register: 'user-onboarding/entity/superuser/register',
  // registrationLink: 'user/entity/registration/link',
  allRequests: 'user/entity/requests/all',
  approve: 'user/entity/approve',
  pendingRequests: 'user-onboarding/entity/requests/pending',
  approvedRequests: 'user-onboarding/entity/requests/approved',
  rejectedRequests: 'user/entity/requests/rejected',
  newRequests: 'user-onboarding/entity/requests/new',
  completedRequests: 'user-onboarding/entity/requests/existing',
  shareLink: 'user-onboarding/entity/invitations/create',
  verifyToken: 'user-onboarding/entity/superuser/verify',
  generateOtp: 'user-onboarding/entity/superuser/request/otp',
  accessPolicy: 'user-onboarding/entity/access-policy',
  inviteDg: 'user-onboarding/entity/invitations/create',
  verifyOtp: 'user-onboarding/entity/superuser/verify/otp',
  manageAccess: 'user-onboarding/entity/superuser/manage-access',
  makePrimary: 'user-onboarding/entity/superuser/make-primary',
  linkUser: 'user-onboarding/entity/user/',
  invitationsList: 'user-onboarding/entity/invitations',
  editAccess: 'user-onboarding/entity/superuser/edit-access',
  exportUsers: 'user-onboarding/entity/superuser/users-export',
  deletedList: 'user-onboarding/entity/deleted-users',
  departments: 'user-onboarding/departments',
  jobLevels: 'user-onboarding/job-levels'
};

export const dg = {
  manageAccess: 'user-onboarding/entity/dg/manage-access',
  newRequests: 'user-onboarding/entity/dg/requests/new',
  pendingRequests: 'user-onboarding/entity/dg/requests/pending',
  completedRequests: 'user-onboarding/entity/dg/requests/existing',
  generateOtp: 'user-onboarding/entity/dg/request/otp',
  verifyOtp: 'user-onboarding/entity/dg/verify/otp',
  register: 'user-onboarding/entity/dg/register',
  verifyToken: 'user-onboarding/entity/dg/verify'
};

export const xTokenList = [user.generateOtp, user.verifyOtp, superUser.generateOtp, superUser.verifyOtp, dg.generateOtp, dg.verifyOtp];
