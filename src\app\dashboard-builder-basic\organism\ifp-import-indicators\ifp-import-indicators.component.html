<div class="ifp-import" [ngClass]="{'ifp-import--single-select': isSingleSelect}">
  <div class="ifp-import__inner" [ngClass]="{'ifp-import__loading': loader}">
    <div class="ifp-import__sec-1">
      @if (heading && heading !== '') {
      <h3 class="ifp-import__heading">{{heading | translate}}</h3>
      }
      <!-- search and filter start -->
      <div class="ifp-import__action-wrapper">
        @if (importType == 'browse') {
        <div class="ifp-import__action-sec">
          <!-- <ifp-search class="ifp-import__search" [isKeypress]="true" (searchEvent)="onSearch($event)"
              [onSearch]="search" [boxType]="true" [placeholderText]="'Search indicators'"></ifp-search> -->
          <ifp-button [label]="'Filters' | translate" (ifpClick)="showFilter = !showFilter"
            [buttonClass]="buttonClass.tertiary +' '+ buttonIconPosition.left + (showFilter ? ' ifp-btn--active' : '')"
            [iconClass]="'ifp-icon-filter-2'" class="ifp-import__filter-btn"></ifp-button>
        </div>


        <!-- filters start -->
        <div class="ifp-import__filter-sec" [ngClass]="{'ifp-import__filter-sec--show': showFilter}">
          <div class="ifp-import__filter-inner">
            @if (classificationList && classificationList.length) {
            <app-ifp-db-dropdown class="ifp-import__filter-item" [options]="classificationList"
              [title]="'Classification'" [isMultiSelect]="false" (singleSelected)="selectClassification($event)"
              [key]="'name'"></app-ifp-db-dropdown>
            }
            @if (domainList && domainList.length) {
            <app-ifp-db-dropdown class="ifp-import__filter-item" [options]="domainList" [title]="'Topic'"
              [isMultiSelect]="false" (singleSelected)="selectTopic($event)" [key]="'name'"></app-ifp-db-dropdown>
            }
            @if (themeList && themeList.length) {
            <app-ifp-db-dropdown class="ifp-import__filter-item" [options]="themeList" [title]="'Theme'"
              [isMultiSelect]="false" (singleSelected)="selectTheme($event)" [key]="'name'"></app-ifp-db-dropdown>
            }
            @if (subThemeList && subThemeList.length && selectedClassification.key !=
            classifications.innovativeStatistics) {
            <app-ifp-db-dropdown class="ifp-import__filter-item" [options]="subThemeList" [title]="'Sub theme'"
              [isMultiSelect]="false" (singleSelected)="selectSubTheme($event)"
              [key]="'name'"></app-ifp-db-dropdown>
            }
            @if (productList && productList.length && selectedClassification.key !=
            classifications.innovativeStatistics) {
            <app-ifp-db-dropdown class="ifp-import__filter-item" [options]="productList" [title]="'Product'"
              [isMultiSelect]="false" (singleSelected)="selectProduct($event)" [key]="'title'"
              [defaultSelect]="false"></app-ifp-db-dropdown>
            }

            @if (screenerFilters && screenerFilters.length > 0) {
              @for (item of screenerFilters; track item.id) {
                <app-ifp-db-dropdown class="ifp-import__filter-item" [options]="item.options" [title]="item.name"
                  [isMultiSelect]="true" (multiSelected)="expFilterSelected($event, item.key)"
                  [key]="'name'" [defaultSelect]="false" [multipleSelectedItems]="[item?.default]"></app-ifp-db-dropdown>
                }
            }

          </div>
        </div>
        }
        <!-- filters end -->
      </div>
      <!-- search and filter end -->

      <div class="ifp-import__card-list">
        <div class="ifp-import__card-wrapper">
          <ng-container *ngFor="let item of itemsList">
            <app-ifp-db-card
              *ngIf="(importType == 'browse' ? item.app_type : item.APP_TYPE) !== 'tableau_internal' && (importType == 'browse' ? item.app_type : item.APP_TYPE) !== 'eci_insights' && (importType == 'browse' ? item.app_type : item.APP_TYPE) !== 'basket_insights' && item?.content_type != 'publications'"
              [id]="item.id" [isSelectMode]="true" [enableCheckbox]="!isSingleSelect" [selectedId]="activeCard"
              [title]="item.title" class="ifp-import__card-item" (cardSelected)="selectCards($event)"
              [checked]="getChecked(item.id, item.content_classification_key, item.category)"
              [isCheckboxDisabled]="chkbxDisable && !getChecked(item.id, item.content_classification_key, item.category)"></app-ifp-db-card>
          </ng-container>
          @if (itemsList.length <= 0) {
            <app-ifp-no-data class="ifp-import__no-data-main" [message]="noDataText | translate" [isTransparent]="true"></app-ifp-no-data>
          }
        </div>
      </div>
    </div>
    @if (!isSingleSelect) {
    <div class="ifp-import__sec-2" [ngClass]="{'ifp-import__sec-2--empty': isEmpty}">
      <p class="ifp-import__selected-title">{{'Your selected indicators' | translate}}</p>
      <ng-container *ngFor="let key of getObjectKeys(selectedCards)">
        @if (key != dashboardConstants.customCard) {
        @for (card of selectedCards[key]; track card) {
        <app-ifp-db-card [id]="card.id" [isSelectMode]="true" class="ifp-import__card-item"
          (cardSelected)="selectCards($event)" [checked]="true" [enableCheckbox]="true"></app-ifp-db-card>
        }
        }
      </ng-container>
      @if (isEmpty) {
      <app-ifp-no-data class="ifp-db-list__no-data" [message]="'You have not selected any indicators' | translate"
        [isTransparent]="true"></app-ifp-no-data>
      }
    </div>
    }
  </div>
  <div class="ifp-import__footer">
    @if (selectedClassification?.key !== classifications.analyticalApps && importType == 'browse') {
    <div [ngClass]="{'ifp-import__loading': loader}">
    <app-pagination class="ifp-import__pagination" [offset]="offsetPage" [limit]="limit" [size]="totalCount"
      (pageChange)="onPageChange($event)" (limitChange)="limitChanged($event)"></app-pagination>
    </div>
    }
    <div class="ifp-import__btn-sec">
      <ifp-button [label]="'Cancel' | translate" (ifpClick)="closeModal();cancelOnly();"
        [buttonClass]="buttonClass.secondary" class="ifp-import__modal-btn"></ifp-button>
      <ifp-button [label]="primaryButtonText | translate" (ifpClick)="addAllIndicators(selectedIndicators)"
        [buttonClass]="isEmpty ? buttonClass.disabled : buttonClass.primary" class="ifp-import__modal-btn" [loader]="loader"
        [spinnerClass]="'ifp-round-spinner--primary'"></ifp-button>
    </div>
  </div>

</div>
