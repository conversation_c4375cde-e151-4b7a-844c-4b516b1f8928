@if (!_globalService.isExternalSite()) {
<div class="ifp-container ifp-indicator-tab__tab">
  <app-ifp-tab [inverse]="false" [classification]="true" [tabData]="tab" (selectedTabEvent)="tabClick($event)"
    [selectedTab]="selectedTab" [hideCount]="true"></app-ifp-tab>
</div>
}
<div class="ifp-container" [ngClass]="{'ifp-indicator-tab--list': selectedView === 'list' && currentTab.key === classification.reports}">
  <ifp-indicator-card-filter-and-search
    [themeName]="this.currentTab.key === classification.analyticalApps ? 'category' : 'Theme'"
    [isReportTab]="currentTab.key === classification.reports" [categoryList]="categoryList"
    [selectedCategory]="category" [searchDisable]="!screenerEnable&&(search === '' ? total>1: true)"
    class="ifp-indicator-tab__filter-wrapper" [themeListSingle]="themeListSingle"
    [selectedDomainSingle]="this.currentTab.key === classification.analyticalApps ? category  :selectedDomainSingle"
    [domainKey]="'name'" [multi]="false" [ngClass]="{'ifp-indicator-tab__filter-wrapper--indicator': true}"
    [domainList]="filterOutput" [enableTheme]="filterOutput.length!== 0" [type]="types.indicator" [buttonsClear]="false"
    [selectedTheme]="[themeListSingle]" [enableSubtheme]="this.currentTab.key === classification.officialStatistics"
    [selectedDomain]="this.currentTab.key === classification.analyticalApps ? [category]  :[selectedDomainSingle]"
    [buttonsApply]="false" [(search)]="onSearch" (searchChange)="searchResult($event)"
    (changeDomain)="changeDomain($event)" (changeTheme)="changeTheme($event)" (compareButton)="compareButton($event)"
    [themeList]="themeList" [isCompare]="!screenerEnable && isCompare && isCompared" [enableCompare]="checkLength()"
    (compareButton)="compareButton($event)" [isCompare]="!screenerEnable && isCompare&& isCompared"
    [analyticClass]="analyticClass" [domainDropdownOptions]="domainDropdownOptions"
    [selectedDropdownOption]="currentDropdownSelection" (regularDropdownChange)="onDropdownChange($event)"
    (dateRangeChange)="onDateRangeChange($event)" [(selectedView)]="selectedView" (sortClicked)="sortCliked($event)"></ifp-indicator-card-filter-and-search>
  <ng-container *ngIf="nodeValues | async as nodeData">

    <ng-container *ngIf="currentTab.key === classification.analyticalApps && !loader">
      <div class="ifp-indicator-tab__analytical" *ngIf="nodeData?.results?.length > 0; else noDataAnlytical">
        <div *ngFor="let item of nodeData?.results; let index=index" class="ifp-indicator-tab__analytical-card">
          <ng-container *ngIf="(item.content_type==='analytical_apps')&& !loader; else normalCard">
            <ifp-analysis-card [appTypeData]="item?.app_type" [addMyApps]="addMyApps" [firstLoader]="true"
              [id]="item.id" [contentType]="'analytical-apps'" [delay]="300"
              (myApps)="myappsEvent($event)"></ifp-analysis-card>
          </ng-container>
          <ng-template #normalCard>
            <ifp-whats-new-card [appTypeData]="item?.app_type" [addMyApps]="addMyApps" [domain]="[domain]"
              [id]="item.id" [contentType]="item.content_type" [hybridCard]="true" [index]="index+1"
              (compareCheck)="emitCompare($event)" [enableCompare]="enableCompare" [isCompare]="isCompare"
              [domainId]="id" (myApps)="myappsEvent($event)"></ifp-whats-new-card>
          </ng-template>
        </div>
      </div>
      <ng-template #noDataAnlytical>
        <app-ifp-no-data [message]="'No results found'" class="ifp-indicator-tab__no-data"></app-ifp-no-data>
      </ng-template>
    </ng-container>

    <ng-container
      *ngIf="(currentTab.key === classification.officialStatistics || currentTab.key === classification.innovativeStatistics) && !loader">
      <ng-container *ngIf="screenerEnable;else screener">
        <app-ifp-screener (myApps)="myappsEvent($event)" [screenerSettings]="screenerSettings" [addMyApps]="addMyApps"
          [innovative]="currentTab.key === classification.innovativeStatistics" [domain]="domain" [domainId]="id"
          [selectedTheme]="themeListSingle"></app-ifp-screener>
      </ng-container>
      <ng-template #screener>
        <div class="ifp-indicator-tab__indicator">
          <ifp-horizontal-tab [(selected)]="productSelectIndex" [list]="nodeData?.products"
            class="ifp-indicator-tab__htab" (selectionClick)="selectTab($event)"
            *ngIf="nodeData?.products?.length > 0"></ifp-horizontal-tab>
          <div class="ifp-indicator-tab__search-wrapper" *ngIf="nodeData?.results?.length > 0; else noData">
            <div class="ifp-indicator-tab__indicator-wrapper">
              <div class="ifp-indicator-tab__indicator-card ifp-d-block"
                *ngFor="let item of nodeData?.results; let index =index">
                <ifp-whats-new-card [appTypeData]="item?.app_type" [addMyApps]="addMyApps"
                  [classification]="currentTab.name" [id]="item.id" [title]="item.title"
                  [contentType]="item.content_type" [index]="index+1" (compareCheck)="emitCompare($event)"
                  [enableCompare]="enableCompare" [isCompare]="isCompare && isCompared" [domainId]="id"
                  [domain]="[domain]" (myApps)="myappsEvent($event)"></ifp-whats-new-card>
              </div>
            </div>
            <app-pagination class="ifp-indicator-tab__pagination" [offset]="offsetPage" [limit]="limit"
              [size]="nodeData?.total_count" (pageChange)="onPageChange($event)"
              (limitChange)="limitChanged($event)"></app-pagination>
          </div>
          <ng-template #noData>
            <app-ifp-no-data [message]="'No results found.'" class="ifp-indicator-tab__no-data"></app-ifp-no-data>
          </ng-template>
        </div>
      </ng-template>
    </ng-container>

    <ng-container *ngIf="(currentTab.key === classification.reports) && !loader">
      <div class="ifp-indicator-tab__analytical " *ngIf="nodeData?.results?.length > 0; else noDataAnlytical">
        <ifp-report-card *ngFor="let item of nodeData?.results; let index=index" class="ifp-indicator-tab__analytical-card ifp-indicator-tab__analytical-card--reports" [addMyApps]="addMyApps" [id]="item.id" [contentType]="item.content_type" [dropdownSelection]="currentDropdownSelection?.value || 'publication'" [isListView]="selectedView === 'list'" [isDropdownActive]="true" (myApps)="myappsEvent($event)"></ifp-report-card>
      </div>
      <ng-template #noDataAnlytical>
        <app-ifp-no-data [message]="'No results found.'" class="ifp-indicator-tab__no-data"></app-ifp-no-data>
      </ng-template>
    </ng-container>
  </ng-container>

</div><app-ifp-spinner *ngIf="loader"></app-ifp-spinner>
