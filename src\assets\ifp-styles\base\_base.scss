@use "../abstracts/index" as *;
html,
body {
  font-family: $ff-noto-sans;
  font-weight: $fw-regular;
  font-size: 62.5%;
  line-height: 1.3;
  color: $ifp-color-primary-grey;

}

body {
  width: 100%;
  overflow-x: hidden;
  position: relative;
  background-color: $ifp-color-grey-bg;
  cursor: url(../../images/cursor1.svg), auto;
}

.ifp-main {
  color: $ifp-color-primary-grey;
}

button,
input {
  font-size: $ifp-font-size-md;
  font-family: $ff-noto-sans;
}

a,
button,
input {
  outline: none;
  border: none;
}

input:focus {
  outline: none;
  border: none;
  box-shadow: none;
}

a,
a:hover {
  color: inherit;
  text-decoration: none;
}

img {
  display: block;
  max-width: 100%;
  height: auto;
}

.ifp-disable {
  opacity: .5;
  pointer-events: none;
  cursor: not-allowed;
}

.ifp-container {
  width: 100%;
  max-width: 1920px;
  padding-right: $spacer-6;
  padding-left: $spacer-6;
  margin-right: auto;
  margin-left: auto;
  &--small {
    max-width: 900px;
  }
}

.ifp-font-sm {
  font-size: 50%;
  .ifp-header__overlay--domain {
    top: 242px;
    right: 0;
    bottom: 0;
    left: 0;
  }
}

.ifp-font-md {
  font-size: 62.5%;
}

.ifp-font-lg {
  font-size: 75%;
}

// .ifp-cursor-1 {
//   cursor: url(../../images/cursor1.svg), auto;
// }

.ifp-cursor-2 body {
  cursor: url(../../images/cursor2.svg), auto;
}

.ifp-cursor-3 body {
  cursor: url(../../images/cursor3.svg), auto;
}

.ifp-module {

  &-source {
    font-weight: $fw-bold;
  }
  &-title {
    font-size: 2rem;
    color: $ifp-color-primary-grey;
    font-weight: $fw-bold;
  }

  &-heading {
    font-size: 2rem;
    color: $ifp-color-black;
  }

  &-subtitle {
    font-size: 1.8rem;
    color: $ifp-color-primary-blue;
    font-weight: $fw-bold;
  }
}

.ifp-main {
  background-color: $ifp-color-grey-bg;
}

.ifp-header__count {
  // $ifp-color-white: #fff;
  color: $ifp-color-white-global;
  font-weight: $fw-semi-bold;
}

.ifp-module-spacing{
  margin-top: $spacer-4;
}

.ifp-module-padding {
  padding: $spacer-5 $spacer-0;
}

.ifp-d-block {
  display: block;
}

.ifp-d-none {
  display: none !important;
}

.ifp-modal-open,.ifp-modal-show {
  overflow: hidden;
  height: 100vh;
  padding-right: 20px;
  .ifp-header {
    width: calc(100% + 20px);
  }
}

.ifp-icon-verifyed-tick {
  color: $ifp-color-blue-hover;
}

.ifp-link {
  color: $ifp-color-blue-hover;
  transition: 0.3s;
  cursor: pointer;
  &:hover {
    color: $ifp-color-hover-blue;
  }
}

.ifp-module-heading {
  font-size: $ifp-fs-11;
  font-weight: $fw-bold;
}

.ifp-sub-title {
  font-size: $ifp-fs-6;
  font-weight: $fw-bold;
}

.ifp-header {
  &__fixed {
    padding-top: $ifp-header-height;
  }
  &__fixed-demo {
    padding-top: 314px;
  }
  &__inner {
    &.ifp-header__fixed {
      padding-top: $ifp-header-height-inner;
    }
  }
}

.ifp-alert-container {
  position: absolute;
  z-index: 3;
}

.ifp-input {
  font-size: $ifp-fs-3;
  color: $ifp-color-black;
  background-color: $ifp-color-section-white;
  border-radius: 7px;
  border: 1px solid $ifp-color-grey-7;
  @include placeholder($ifp-color-grey-9);
  display: block;
  width: 100%;
  padding: $spacer-2 $spacer-3;
  @include reset-number-input;
  &:focus {
    border: 1px solid $ifp-color-grey-7;
  }
}

.ifp-input-error,
.ifp-input-info {
  font-size: $ifp-fs-2;
  .ifp-icon {
    margin-inline-end: $spacer-1;
    position: relative;
    top: 2px;
    font-size: $ifp-fs-3;
  }
  strong {
    font-size: inherit;
  }
}

.ifp-input-error {
  color: $ifp-color-red;
}

.ifp-input-info {
  color: $ifp-color-secondary-blue
}

.ifp-collapse {
  border: 1px solid $ifp-color-grey-6;
  height: 16px;
  width: 16px;
  border-radius: 4px;
  border-inline-start: 1px solid $ifp-color-grey-6;
  border-inline-end: 11px solid $ifp-color-grey-6;
  transition: all 0.2s;
  cursor: pointer;
  &--expand {
    border-inline-end: 1px solid $ifp-color-grey-6;
    border-inline-start: 11px solid $ifp-color-grey-6;
  }
}

.ifp-page-external {
  background-color: transparent;
  .ifp-main {
    background-color: transparent;
  }
}

body[dir="rtl"] {
  font-family: $ff-noto-sans-arabic;
  button,
  input {
    font-family: $ff-noto-sans-arabic;
  }
  &.ifp-modal-open,
  &.ifp-modal-show {
    padding-right: 0;
    padding-left: 20px;
  }
}

.ifp-page-break-inside {
  -webkit-column-break-inside: avoid;
  page-break-inside: avoid;
  break-inside: avoid;
  width: 100%;
}
.ifp-required-field {
  color: $ifp-color-red;
  margin-inline-start:  $spacer-1;
}

highcharts-chart {
  .highcharts-root {
    font-family: "Noto sans" !important;
  }
}

.ifp-beta-title {
  display: flex;
  align-items: center;
}

.ifp-beta-icon {
  width: 60px;
  height: auto;
  margin-inline-start: $spacer-2;
}

@include mobile-tablet {
  body {
    // padding-top: $ifp-header-height;
    padding-top: 141px;
    &.ifp-demo {
      padding-top: 165px;
    }
  }

  .ifp-container {
    padding-right: $spacer-3;
    padding-left: $spacer-3;
  }

  .ifp-modal-open,.ifp-modal-show {
    padding-right: $spacer-0;
  }

  // .ifp-header__fixed {
  //   padding-top: 95px;
  // }

  .ifp-module-heading {
    font-size: $ifp-fs-6;
  }

  .ifp-sub-title {
    font-size: $ifp-fs-4;
  }

}

@include mobile {
  .ifp-module-title {
    font-size: $ifp-fs-5;
  }
}


