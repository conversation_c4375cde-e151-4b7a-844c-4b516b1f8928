/// <reference types="@angular/localize" />

import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';
import { environment } from './environments/environment';
import { enableProdMode } from '@angular/core';

// if (environment.production) {
enableProdMode();

// Suppress console.warn in production
console.warn = () => { };

// Optional: Also suppress console.log or console.error if needed
// console.log = () => {};
// console.error = () => {};
// }
platformBrowserDynamic().bootstrapModule(AppModule)
  .catch(err => console.error(err));
