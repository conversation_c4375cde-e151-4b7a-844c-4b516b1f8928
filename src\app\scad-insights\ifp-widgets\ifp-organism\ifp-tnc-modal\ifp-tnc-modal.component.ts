import { CommonModule } from '@angular/common';
import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild, ViewChildren, QueryList } from '@angular/core';
import { NgbNavModule } from '@ng-bootstrap/ng-bootstrap';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { getTerms } from 'src/app/scad-insights/store/footer/terms-n-conditions/terms-n-conditions.action';
import { selectTermsResponse } from 'src/app/scad-insights/store/footer/terms-n-conditions/terms-n-conditions.selector';
import { TermsState } from 'src/app/scad-insights/store/footer/terms-n-conditions/terms-n-conditions.state';
import { buttonClass } from 'src/app/scad-insights/core/constants/button.constants';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { CookieService } from 'src/app/scad-insights/core/services/cookie.service';
import { CommonApiService } from 'src/app/scad-insights/core/services/common-api/commonApi.service';
import { headerKeys } from 'src/app/scad-insights/core/constants/header.constants';
import { SubSink } from 'subsink';
import { TermsNConditionsComponent } from '../../../footer-pages/terms-n-conditions/terms-n-conditions.component';

@Component({
    selector: 'app-ifp-tnc-modal',
    templateUrl: './ifp-tnc-modal.component.html',
    styleUrls: ['./ifp-tnc-modal.component.scss'],
    imports: [CommonModule, TranslateModule, NgbNavModule, TermsNConditionsComponent]
})
export class IfpTncModalComponent implements OnInit {

  @Input() docType!: string;
  @Input() isAccepted: boolean = false;
  @Input() isLanguage: boolean = false;
  @Output() termsResponse: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() tncVersion: EventEmitter<number> = new EventEmitter<number>();
  @ViewChild('tabs') tabs!: ElementRef;
  @ViewChildren('navItem') navItem!: QueryList<ElementRef>;
  public termsData$: Observable<TermsState> = this.store.select(selectTermsResponse);
  buttonClass = buttonClass;
  public termData: any = [];
  public tabItems: ElementRef[] = [];
  public userSettings: any = [];
  active = 0;
  public selectedLanguage: string = 'en';
  public isDataLoaded: boolean = false;
  public subs = new SubSink();
  public isTabComplete: boolean = false;
  public defaultSettings: any;
  constructor(private store: Store, private themeService: ThemeService, private _cookie: CookieService, public _translate: TranslateService,
    private _commonApiService: CommonApiService) { }

  ngOnInit() {
    this._commonApiService.getUserSettings().subscribe(res => {
      this.defaultSettings = {
        settings: [
          {
            name: 'fontSize',
            value: res.fontSize ? res.fontSize : 'md'
          },
          {
            name: 'cursor',
            value: res.cursor ? res.cursor : 'type1'
          },
          {
            name: 'theme',
            value: res.theme ? res.theme : 'light'
          },
          {
            name: 'lang',
            value: res.lang ? res.lang : 'en'
          }
        ]
      };
      this.selectedLanguage = res.lang;
      localStorage.setItem(headerKeys.lang, res.lang);
      this._translate.use(res.lang);
      this.selectedLanguage = res.lang;
      this.themeService.changeLanguage(res.lang);

      this.store.dispatch(getTerms());
      this.termsData$.subscribe((resp) => {
        this.termData = resp;
      });
    });

  }

  nextClicked(index: number) {
    this.tabItems = this.navItem.toArray();
    const scrollWidth = index !== this.tabItems.length - 1 ? this.tabItems[index]?.nativeElement?.clientWidth : (this.tabs.nativeElement?.scrollWidth - this.tabItems[index]?.nativeElement?.clientWidth);
    this.tabs.nativeElement.scrollLeft += scrollWidth / 2;
    if (this.termData?.sections.length - 1 == this.active) {
      this.isTabComplete = true;
    }
  }

  prevClicked(index: number) {
    this.tabItems = this.navItem.toArray();
    const scrollWidth = index !== 0 ? this.tabItems[index + 1]?.nativeElement?.clientWidth : (this.tabs.nativeElement?.scrollWidth - this.tabItems[index]?.nativeElement?.clientWidth);
    this.tabs.nativeElement.scrollLeft -= scrollWidth / 2;
  }

  onButtonClick(response: boolean, version: number = 1) {
    this.termsResponse.emit(response);
    this.tncVersion.emit(version);
  }

  getIndex(index: number) {
    if (this.termData?.sections.length - 1 == this.active) {
      this.isTabComplete = true;
    }
  }


  languageChange(lang: any) {
    document.cookie = `lang=${lang}; path=/;`;
    this.defaultSettings.settings[3].value = lang;

    this.subs.add(
      this._commonApiService.updateUserSettings(this.defaultSettings).subscribe((res: any) => {
        if (res) {
          this._translate.use(lang);
          this.themeService.changeLanguage(lang);
          this.selectedLanguage = lang;
          location.reload();
        }
      })
    );
  }

  termsResponseTrigger(_event: any) {
    this.termsResponse.emit(_event);
  }

  getTermsVersion(_event: any) {
    this.tncVersion.emit(_event);
  }

}
