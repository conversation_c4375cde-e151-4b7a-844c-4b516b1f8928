import { Component, On<PERSON><PERSON>roy, OnInit, signal, ViewChild } from '@angular/core';
import { IfpPrepUploadDataComponent, UploadedFile } from '../../organism/ifp-prep-upload-data/ifp-prep-upload-data.component';
import { ApiService } from 'src/app/scad-insights/core/services/api.service';
import { SubSink } from 'subsink';
import { ApiStatus } from 'src/app/scad-insights/core/constants/api-status.constants';
import { ToasterService } from 'src/app/scad-insights/core/services/tooster/ToastrService.service';
import { prepsApiEndpoints, storageType } from '../../data-prep/ifp-data-prep/constants/if-preps.constants';
import { SubscriptionLike } from 'rxjs';
import { FileResponePrep } from '../../data-prep/ifp-data-prep/interface/ifp-data-prep.interface';
import { IfpModalComponent } from '../../../scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.component';
import { IfpPrepLibraryComponent } from '../../data-prep/ifp-data-prep/ifp-prep-library/ifp-prep-library.component';
import { IfpExploratoryService } from '../services/ifp-exploratory.service';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { IfpModalService } from 'src/app/scad-insights/ifp-widgets/ifp-organism/ifp-modal/ifp-modal.service';
import { ThemeService } from 'src/app/scad-insights/core/services/theme/theme.service';
import { AsyncPipe } from '@angular/common';

@Component({
    selector: 'app-upload-exploratory',
    templateUrl: './upload-exploratory.component.html',
    styleUrls: ['./upload-exploratory.component.scss'],
    imports: [IfpPrepUploadDataComponent, IfpModalComponent, IfpPrepLibraryComponent, AsyncPipe]
})
export class UploadExploratoryComponent implements OnInit, OnDestroy{

  @ViewChild('libraryListModal') library!:IfpModalComponent;
  @ViewChild('uploadData') uploadData!:IfpPrepUploadDataComponent;
  constructor(private _commonService : ApiService, private _toast: ToasterService, public _exploratory: IfpExploratoryService, private _router: Router,
    private _activatedRoute: ActivatedRoute, private _modalService: IfpModalService, public _themeService: ThemeService) {
    // used to get scenario id
    this.subs.add(
      this._activatedRoute.queryParams.subscribe((event: Params) => {
        this.autoMl = event['enableAutoMl'] || this._router.url.includes('auto-ml');
      })
    );
  }

  public subs = new SubSink();
  public uploadDataProgess = signal(0);
  public downloadSubscribe!:SubscriptionLike;
  public notErrorFile = false;
  public uploadResponse?: FileResponePrep;
  public disableProceed = true;
  public fileName = signal('');
  public libraryFile = false;
  public loaderFile = false;
  public error = false;
  public allowedSheetType: string = 'xlsx';
  public autoMl = false;
  public libraryEnable = signal(false);
  public sheet = false;
  public sheetDetails: { file: UploadedFile, path?: string, sheet: string } =  { file: {file: [], url: ''}, path: '', sheet: '' };

  // delete uploaded file
  deleteFile() {
    this. resetUpload();
    this.sheet = false;
    this.downloadSubscribe?.unsubscribe();
    this.error= false;
    if (this.uploadResponse && !this.sheet) {
      if (this.notErrorFile && !this.libraryFile) {
        this.deleteLibrary(this.uploadResponse.id ?? '');
      }
      this.disableProceed = true;
      this._exploratory.processDisable.set(true);
      this.uploadResponse = {};
      this.notErrorFile = true;
    }
  }

  ngOnInit(): void {
    this._exploratory.processIcon.set('ifp-icon-rightarrow');
    this._exploratory.secondaryBtn.set(false);
    this._exploratory.processName.set('Process');
    this._exploratory.autoMlStatus.set(false );
    this._exploratory.processDisable.set(true);
    if (this._exploratory.uploadedFile()?.name || this._exploratory.uploadedFile()?.sheet) {
      this.fileName.set(this._exploratory.uploadedFile().name ?? '');
      this.uploadResponse = this._exploratory.uploadedFile();
      this._exploratory.processDisable.set(false);
      if (this._exploratory.uploadedFile()?.sheet){
        this.sheet = true;
        this.sheetDetails  = this._exploratory.uploadedFile()?.sheetDetails as {
          file: UploadedFile;
          path: string;
          sheet: string;
      };
      }
    }
    this.subs.add(
      this._exploratory.processEvent.subscribe((event:string) => {
        if (event !== '') {
          this.  processed();
        }
      }));
  }

  resetUpload() {
    this._exploratory.uploadedFile.set({});
  }


  // delete file from library
  deleteLibrary(id: string) {
    this.subs.add(
      this._commonService.getDeleteRequest(`${prepsApiEndpoints.libraryDelete}${id}`).subscribe({ next: () => {
        this._toast.success('File deleted successfully');
      }, error: () => {
        // const error = err?.error;
        // this._commonService.errorHandler(error);
      }})
    );
  }

  excelUploaded(event :{ file: UploadedFile, path: string, sheet: string, sheetList?: string[] }) {
    this._exploratory.uploadedFile?.set({sheetDetails: event, sheetList: event.sheetList, sheet: true, name: event?.file?.file[0]?.name, sheetName: event.sheet });
  }

  removeFile(id: string) {
    if (id === this._exploratory.uploadedFile().id) {
      this.fileName.set('');
      this._exploratory.uploadedFile.set({});
      this.deleteFile();
    }
  }

  // event trigger when process button click . it is used to forward request to next page
  processed() {
    if ( this.sheet &&  !this._exploratory.uploadedFile()?.id ) {
      this.onFileUpload(this.sheetDetails.file, this.sheetDetails.path, this.sheetDetails.sheet).then(() => {
        if (this.autoMl) {
          this._router.navigateByUrl(`analytics/auto-ml/run?id=${this.uploadResponse?.id}`);
        } else {
          this._router.navigateByUrl(`analytics/exploratory/data-analysis?id=${this.uploadResponse?.id}`);
        }
      }).catch(()=> {
        return;
      });
    } else if (this.autoMl) {
      this._router.navigateByUrl(`analytics/auto-ml/run?id=${this.uploadResponse?.id}`);
    } else {
      this._router.navigateByUrl(`analytics/exploratory/data-analysis?id=${this.uploadResponse?.id}`);
    }
  }

  // close library modal
  closeLibrary() {
    this.library.removeModal();
    this.libraryEnable.set(false);
  }

  closeModalEvent() {
    this.libraryEnable.set(false);
  }

  // used to  get value from library component
  getFromLibrary(value:FileResponePrep) {
    this.uploadData.deleteFile([]);
    this.uploadData.error = false;
    // this.uploadData.enableProcessBtn = true;
    this.uploadData.disableProcesed = false;
    this.uploadResponse = value;
    this.disableProceed = false;
    this._exploratory.processDisable.set(false);
    this._exploratory.uploadedFile?.set(value);
    this.fileName.set(value.name ?? '');
    this.libraryFile = true;
    this.uploadDataProgess.set(0);
    this.closeLibrary();
  }

  // used to open library modal
  openLibrary() {
    this.libraryEnable.set(true);
    this.library.createElement();
  }

  // event trigger when we select sheet from an excel file
  onSelectWorksheet(event: {file: UploadedFile, path: string, sheet: string}) {
    const currentEvent = event;
    this.sheet = true;
    this.sheetDetails = currentEvent;
    this.error = false;
    this.disableProceed = false;
    this._exploratory.processDisable.set(false);
    this._exploratory.uploadedFile?.set({...this._exploratory.uploadedFile(), sheetName: event.sheet, id: undefined, sheetDetails: currentEvent} );
  }

  excelUploadedError() {
    this.sheet = false;
    this.sheetDetails['path'] = '';
    this.sheetDetails['sheet'] = '';
  }

  // event trigger when upload a file also call to create api or temp api
  onFileUpload(file: UploadedFile, path: string = '', sheet: string = '') {
    this.sheet = false;
    this.uploadDataProgess.set(0);
    this.libraryFile = false;
    const uploadData = new FormData();
    const nameFile = path !== '' ? path : file.file[0].name;
    uploadData.append('name', nameFile);
    uploadData.append('storage_backend', storageType.s3);

    this.fileName.set(file.file[0].name.replace(/^.*[\\/]/, ''));
    const fileType = this.fileName().split('.').pop() ?? '';
    if ( fileType !== this.allowedSheetType) {
      uploadData.append('file', file.file[0]);
    }
    if ( fileType === this.allowedSheetType) {
      this.sheet = false;
      uploadData.append('sheet_name', sheet);
    }
    this.loaderFile = true;
    return new Promise((resolve, reject) => {
      this.downloadSubscribe =this._commonService.getUploadData(prepsApiEndpoints.sourceUpload, uploadData).subscribe({next: data => {
        if (data?.type == 1) {
          this.uploadDataProgess.set(Math.floor((data?.loaded/ data?.total)*100)- 10);
        } else if (data?.type == 4 ) {
          if (data?.status == ApiStatus.created) {
            if ( fileType !== this.allowedSheetType) {
              this._exploratory.uploadedFile?.set({...data.body, name: nameFile});
            } else {
              this._exploratory.uploadedFile?.set({...this._exploratory.uploadedFile(), id: data.body.id });
            }
            this._toast.success('Data uploaded and saved to the Library!');
            this.uploadResponse = data.body;
            this.disableProceed = false;
            this._exploratory.processDisable.set(false);
            this.uploadDataProgess.set(100);
            this.notErrorFile = true;
            this.loaderFile =false;
            resolve(data.body);
          }

        }
      }, error: err => {
        const error = err?.error;
        this._commonService .errorHandler(error);
        this.notErrorFile = false;
        this.disableProceed = true;
        this._exploratory.processDisable.set(true);
        this.uploadResponse = {};
        this.loaderFile =false;
        this.error= true;
        reject();
      }});
    }
    );
  }

  // disable Process button
  disableEvent(event: boolean) {
    this._exploratory.processDisable.set(event);
  }


  ngOnDestroy(): void {
    this._modalService.removeAllModal();
    this.subs?.unsubscribe();
    this.library?.removeModal();
    this.downloadSubscribe?.unsubscribe();
    this._exploratory.processEvent.next('');
  }

}
