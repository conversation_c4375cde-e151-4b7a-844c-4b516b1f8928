import { DOCUMENT } from '@angular/common';
import {  Inject, Injectable, Renderer2, RendererFactory2, signal, WritableSignal } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class IfpModalService {

  private render: Renderer2;

  constructor(
    rendererFactory: RendererFactory2,
  @Inject(DOCUMENT) private document: Document) {
    this.render = rendererFactory.createRenderer(null, null);

  }

  public modalElement: WritableSignal< Element[] >=  signal([]);

  /**
   * used to remove all the element of modal
   * @memberof IfpModalService
   */
  removeAllModal() {

    const container = document.fullscreenElement ? document.fullscreenElement : this.document.body;
    this.modalElement().forEach(element => {
      this.render.removeChild(container, element);
      this.render.removeChild(container, element);
    });
    this.modalElement.set([]);
  }
}
