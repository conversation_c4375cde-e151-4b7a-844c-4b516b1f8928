export const environment = {
  production: true,
  domain: '',
  isEnableRealEstate: true,
  cookieDomain: '',
  baseUrl:  window.location.origin,
  baseVersion: '/v1',
  apiVersion: '/api/',
  protocol: 'https://',
  prepbaseUrl: `${window.location.origin}/api/data-prep/api`,
  genAiBaseUrl: `${window.location.origin}/genai/api`,

  dxpBaseUrl: `${window.location.origin}/dxp-integration/api`,
  zohoUrl: 'https://zohostg.scad.gov.ae:8443',
  dxpApiVersion: '/',
  env: 'staging',
  genAiChatVersion: 'v2',
  storage: 'g42',
  msalConfig: {
    authority:  `https://login.microsoftonline.com/${(window as any)['__env']?.tenantId}/`,
    clientId: (window as any)['__env']?.clientId,
    redirectUri: `${window.location.origin}/auth`,
    postLogoutRedirectUri: window.location.origin+'/login',
    scopes: [
      'User.read'
    ],
    domain: (window as any)['__env']?.domain,
    // userList: (window as any)['__env']?.userlist
  }
};
