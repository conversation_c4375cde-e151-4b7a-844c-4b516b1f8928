@use '../../../../../assets/ifp-styles/abstracts' as *;

.ifp-indicator-tab {
  &__filter-wrapper {
    display: block;
    padding-bottom: $spacer-2;
  }

  &__filter-wrapper--indicator {
    margin-bottom: $spacer-3;
  }

  &__analytical,
  &__indicator-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin: 0px (-$spacer-2);
  }

  &__analytical-card {
    display: block;
    width: calc(25% - $spacer-3);
    margin: $spacer-2;

    &--reports {
      background-color: $ifp-color-white;
      border: 1px solid $ifp-color-grey-7;
      border-radius: 15px 15px 0 0;
    }
  }

  &__indicator-card {
    width: calc(33.33% - $spacer-3);
    margin: $spacer-2;
  }

  &__indicator-card.ifp-active-card {
    width: calc(66.66% - $spacer-3);
  }

  &__analytical-card.ifp-active-card {
    width: calc(50% - $spacer-3);
  }



  &__indicator-wrapper {
    width: calc(100% + 16px);
    margin-top: (-$spacer-2);
    margin-bottom: $spacer-3;

    ::ng-deep {
      .ifp-section {
        border: none
      }
    }
  }

  &__search-wrapper {
    min-height: 440px;
    width: calc(75% - 16px);
    margin: $spacer-0 $spacer-2;
    display: flex;
    flex-direction: column;
  }

  &__htab {
    display: block;
    width: calc(25% - 16px);
    margin: $spacer-0 $spacer-2;
    max-height: 495px;
    overflow: auto;
    @include ifp-scroll-y(transparent, $ifp-color-grey-6, 4px, 8px);
  }

  &__indicator {
    align-items: flex-start;
    display: flex;
    margin: $spacer-0 (
      -$spacer-2
    );
}

&__analytical,
&__htab,
&__indicator-wrapper {
  border-radius: 10px;
}

&__tab {
  margin-bottom: $spacer-6;
}

&__pagination {
  display: block;
  margin-top: auto;
}

&__no-data {
  width: 100%;
  display: block;
}
  &--list {
    .ifp-indicator-tab {
      &__analytical-card--reports {
        border-radius: 15px;
        width: calc(100% - $spacer-3);
      }
    }
  }
}

:host ::ng-deep {
  .ifp-card {
    display: flex;
    flex-direction: column;
    background-color: $ifp-color-section-white;
    padding: $spacer-4 $spacer-3 $spacer-3;
  }

  .ifp-analysis {
    height: 100%;
  }

  .ifp-analysis__left {
    flex-direction: column;
    display: flex;
  }

  .ifp-indicator-tab__filter-wrapper {
    .ifp-dropdown {
      background-color: $ifp-color-section-white;

      &--bottom-border {
        background-color: transparent;
      }
    }
  }

  .ifp-indicator-tab__analytical-card {
    .ifp-report-card {
      border: none;
    }
  }
}

:host-context([dir="rtl"]) {
  .ifp-indicator-tab {
    &__htab {
      margin-left: $spacer-3;
      margin-right: $spacer-0;
    }
  }
}

@include mobile-tablet {
  .ifp-indicator-tab {
    &__indicator-card {
      width: calc(50% - $spacer-3);
      margin: $spacer-2;
    }

    &__analytical-card {
      width: calc(33.33% - $spacer-3);
    }

    &__indicator-card.ifp-active-card {
      width: calc(100% - $spacer-3);
    }

    &__analytical-card.ifp-active-card {
      width: calc(100% - $spacer-3);
    }

    &__htab {
      width: calc(33.33% - 16px);
    }

    &__search-wrapper {
      width: calc(66.66% - 16px);
    }
  }
}

@include mobile {
  .ifp-indicator-tab {

    &__filter-wrapper,
    &__dropdown-wrapper {
      display: block;
    }

    &__filter-wrapper {
      &--indicator {
        margin-bottom: $spacer-4;
      }
    }

    &__dropdown-wrapper,
    &__dropdown {
      margin: $spacer-0;
    }

    &__tab {
      margin-bottom: $spacer-3;
    }

    &__indicator {
      display: block;
    }

    &__htab {
      margin-right: $spacer-0;
    }

    &__analytical,
    &__indicator-wrapper {
      display: block;
    }

    &__search-wrapper,
    &__htab {
      width: auto;
    }

    &__indicator-wrapper {
      margin-top: $spacer-2;
    }

    &__indicator-card {
      width: calc(100% - $spacer-3);
      margin: $spacer-2;
    }

    &__analytical-card {
      width: calc(100% - $spacer-3);
    }
  }
}